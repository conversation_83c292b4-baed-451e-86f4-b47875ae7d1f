# 🗄️ **دليل إعداد قاعدة البيانات - نظام ERP**

## ❌ **المشكلة:**
```
خطأ في الاتصال بقاعدة البيانات: SQLSTATE[HY000] [1049] Unknown database 'erp_accounting'
```

## ✅ **الحلول المتاحة:**

---

### **🚀 الحل الأول: الإعداد التلقائي (الأسهل)**

#### **الخطوة 1: افتح صفحة الإعداد**
```
http://localhost/acc/setup_database.php
```

#### **الخطوة 2: اتبع التعليمات**
1. ✅ تحقق من المتطلبات
2. ✅ راجع إعدادات قاعدة البيانات
3. ✅ انقر على "بدء إعداد قاعدة البيانات"
4. ✅ انتظر حتى اكتمال العملية

#### **الخطوة 3: ابدأ استخدام النظام**
```
اسم المستخدم: admin
كلمة المرور: admin123
```

---

### **🛠️ الحل الثاني: الإعداد اليدوي**

#### **الخطوة 1: افتح phpMyAdmin**
```
http://localhost/phpmyadmin
```

#### **الخطوة 2: إنشاء قاعدة البيانات**
```sql
CREATE DATABASE erp_accounting CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

#### **الخطوة 3: استيراد الجداول**
1. اختر قاعدة البيانات `erp_accounting`
2. انقر على تبويب "استيراد" (Import)
3. اختر ملف `database_setup.sql`
4. انقر على "تنفيذ" (Go)

---

### **⚡ الحل الثالث: استخدام سطر الأوامر**

#### **افتح Command Prompt وانتقل لمجلد XAMPP:**
```bash
cd C:\xampp\mysql\bin
```

#### **اتصل بـ MySQL:**
```bash
mysql -u root -p
```

#### **أنشئ قاعدة البيانات:**
```sql
CREATE DATABASE erp_accounting CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE erp_accounting;
SOURCE D:\xampp\htdocs\acc\database_setup.sql;
EXIT;
```

---

## 📋 **محتويات قاعدة البيانات:**

### **🗂️ الجداول الرئيسية:**

#### **1. جدول المستخدمين (users)**
```sql
- id: معرف المستخدم
- name: الاسم الكامل
- username: اسم المستخدم
- email: البريد الإلكتروني
- password: كلمة المرور المشفرة
- role: الدور (admin, manager, accountant, user)
- active: نشط/غير نشط
```

#### **2. جدول الشركات (companies)**
```sql
- id: معرف الشركة
- name: اسم الشركة
- legal_name: الاسم القانوني
- tax_number: الرقم الضريبي
- email, phone, website: بيانات الاتصال
- address: العنوان
- currency: العملة
```

#### **3. جدول الشركاء (partners)**
```sql
- id: معرف الشريك
- name: الاسم
- is_company: شركة أم فرد
- email, phone, mobile: بيانات الاتصال
- customer_rank: ترتيب العميل
- supplier_rank: ترتيب المورد
- address fields: حقول العنوان
```

#### **4. جدول المنتجات (products)**
```sql
- id: معرف المنتج
- name: اسم المنتج
- default_code: الكود المرجعي
- list_price: سعر البيع
- standard_price: سعر التكلفة
- type: نوع المنتج (product, consu, service)
- categ_id: فئة المنتج
```

#### **5. جدول دليل الحسابات (chart_of_accounts)**
```sql
- id: معرف الحساب
- code: كود الحساب
- name: اسم الحساب
- type: نوع الحساب (asset, liability, equity, revenue, expense)
- parent_id: الحساب الأب
```

#### **6. جدول قيود اليومية (journal_entries)**
```sql
- id: معرف القيد
- journal_id: دفتر اليومية
- reference: المرجع
- date: التاريخ
- state: الحالة (draft, posted, cancelled)
```

#### **7. جدول بنود القيود (journal_items)**
```sql
- id: معرف البند
- entry_id: معرف القيد
- account_id: معرف الحساب
- debit: المدين
- credit: الدائن
```

---

## 🔧 **البيانات الافتراضية:**

### **👤 المستخدم الافتراضي:**
```
اسم المستخدم: admin
كلمة المرور: admin123
الدور: مدير النظام
```

### **🏢 الشركة الافتراضية:**
```
الاسم: الشركة الافتراضية
البريد: <EMAIL>
الهاتف: +966 11 123 4567
العملة: SAR
```

### **👥 الشركاء الافتراضيون:**
```
1. شركة التقنية المتقدمة (عميل)
2. أحمد محمد العلي (عميل)
3. فاطمة أحمد السالم (عميل)
4. شركة الإنشاءات الحديثة (عميل ومورد)
5. مؤسسة الخدمات التجارية (مورد)
```

### **📦 المنتجات الافتراضية:**
```
1. لابتوب Dell Inspiron 15 (منتج)
2. خدمة الصيانة الشهرية (خدمة)
3. طابعة HP LaserJet Pro (منتج)
4. ورق A4 - علبة 500 ورقة (استهلاكي)
5. استشارة تقنية (خدمة)
```

### **📚 دليل الحسابات الافتراضي:**
```
1000 - الأصول
  1100 - النقد وما يعادله
    1101 - الصندوق
    1102 - البنك
2000 - الخصوم
  2100 - الذمم الدائنة
3000 - حقوق الملكية
  3100 - رأس المال
4000 - الإيرادات
  4100 - إيرادات المبيعات
5000 - المصروفات
  5100 - مصروفات تشغيلية
```

---

## 🔍 **التحقق من نجاح الإعداد:**

### **1. تحقق من وجود قاعدة البيانات:**
```sql
SHOW DATABASES LIKE 'erp_accounting';
```

### **2. تحقق من الجداول:**
```sql
USE erp_accounting;
SHOW TABLES;
```

### **3. تحقق من البيانات:**
```sql
SELECT COUNT(*) FROM users;
SELECT COUNT(*) FROM partners;
SELECT COUNT(*) FROM products;
SELECT COUNT(*) FROM chart_of_accounts;
```

### **4. اختبر تسجيل الدخول:**
```
http://localhost/acc/login.php
اسم المستخدم: admin
كلمة المرور: admin123
```

---

## 🚨 **حل المشاكل الشائعة:**

### **مشكلة: "Access denied for user"**
```
الحل: تأكد من صحة اسم المستخدم وكلمة المرور في config/database.php
```

### **مشكلة: "Can't connect to MySQL server"**
```
الحل: تأكد من تشغيل MySQL في XAMPP
```

### **مشكلة: "Table doesn't exist"**
```
الحل: أعد تشغيل setup_database.php أو استورد database_setup.sql يدوياً
```

### **مشكلة: "Character set issues"**
```
الحل: تأكد من استخدام utf8mb4 في إعدادات MySQL
```

---

## ⚙️ **إعدادات متقدمة:**

### **تغيير إعدادات قاعدة البيانات:**
```php
// في ملف config/database.php
define('DB_HOST', 'localhost');
define('DB_NAME', 'erp_accounting');
define('DB_USER', 'root');
define('DB_PASS', '');
```

### **إنشاء مستخدم مخصص:**
```sql
CREATE USER 'erp_user'@'localhost' IDENTIFIED BY 'erp_password';
GRANT ALL PRIVILEGES ON erp_accounting.* TO 'erp_user'@'localhost';
FLUSH PRIVILEGES;
```

### **نسخ احتياطي:**
```bash
mysqldump -u root -p erp_accounting > backup.sql
```

### **استعادة من نسخة احتياطية:**
```bash
mysql -u root -p erp_accounting < backup.sql
```

---

## 🎯 **الخطوات التالية بعد الإعداد:**

### **1. تسجيل الدخول:**
```
http://localhost/acc/login.php
admin / admin123
```

### **2. تغيير كلمة المرور:**
```
اذهب إلى الإعدادات → تغيير كلمة المرور
```

### **3. إعداد بيانات الشركة:**
```
اذهب إلى الإعدادات → معلومات الشركة
```

### **4. بدء استخدام النظام:**
```
http://localhost/acc/start_here.php
```

---

## 📞 **للمساعدة:**

### **الملفات المهمة:**
- `setup_database.php` - إعداد تلقائي
- `database_setup.sql` - ملف SQL للإعداد اليدوي
- `config/database.php` - إعدادات الاتصال

### **الروابط المفيدة:**
- [صفحة الإعداد](http://localhost/acc/setup_database.php)
- [phpMyAdmin](http://localhost/phpmyadmin)
- [صفحة البداية](http://localhost/acc/start_here.php)

**🎉 بعد إكمال الإعداد، ستكون قاعدة البيانات جاهزة والنظام يعمل بشكل مثالي!** ✨
