# 👨‍💻 دليل المطورين - نظام ERP المحاسبي

## 🏗️ هيكل النظام

### 📁 هيكل المجلدات
```
acc/
├── api/                    # واجهات البرمجة
├── assets/                 # الملفات الثابتة
│   ├── css/               # ملفات التصميم
│   ├── js/                # ملفات JavaScript
│   └── images/            # الصور
├── config/                # ملفات التكوين
├── includes/              # الملفات المشتركة
├── models/                # نماذج البيانات (بأسلوب Odoo)
├── pages/                 # صفحات النظام
├── scripts/               # سكريبتات مساعدة
└── sql/                   # ملفات قاعدة البيانات
```

## 🗄️ النماذج (Models) - بأسلوب Odoo

### BaseModel - النموذج الأساسي

```php
<?php
require_once 'models/BaseModel.php';

class MyModel extends BaseModel {
    protected function init() {
        $this->table = 'my_table';
        $this->fields = array('id', 'name', 'active');
        $this->required_fields = array('name');
        $this->default_values = array('active' => true);
    }
}
?>
```

### دوال النموذج الأساسي

#### إنشاء سجل جديد
```php
$model = new MyModel();
$id = $model->create(array(
    'name' => 'اسم السجل',
    'description' => 'وصف السجل'
));
```

#### قراءة السجلات
```php
// قراءة سجل واحد
$record = $model->read(array($id));

// قراءة عدة سجلات
$records = $model->read(array(1, 2, 3));
```

#### البحث في السجلات
```php
// البحث البسيط
$ids = $model->search(array(
    array('active', '=', true),
    array('name', 'like', '%test%')
));

// البحث مع القراءة
$records = $model->search_read(
    array(array('active', '=', true)),
    array('id', 'name', 'description'),
    array('limit' => 10, 'order' => 'name ASC')
);
```

#### تحديث السجلات
```php
$model->write(array(1, 2, 3), array(
    'description' => 'وصف محدث'
));
```

#### حذف السجلات
```php
$model->unlink(array(1, 2, 3));
```

#### عد السجلات
```php
$count = $model->search_count(array(
    array('active', '=', true)
));
```

## 🏢 نموذج الشركات (ResCompany)

### الاستخدام الأساسي
```php
require_once 'models/ResCompany.php';

$company_model = new ResCompany();

// إنشاء شركة جديدة
$company_id = $company_model->create(array(
    'name' => 'شركة جديدة',
    'code' => 'NEW',
    'tax_number' => '123456789012345',
    'email' => '<EMAIL>',
    'phone' => '+966501234567'
));

// جلب الشركة الافتراضية
$default_company = $company_model->get_default_company();

// جلب شركات المستخدم
$user_companies = $company_model->get_user_companies($user_id);
```

## 👥 نموذج الشركاء (ResPartner)

### إدارة العملاء والموردين
```php
require_once 'models/ResPartner.php';

$partner_model = new ResPartner();

// إنشاء عميل جديد
$customer_id = $partner_model->create(array(
    'name' => 'عميل جديد',
    'email' => '<EMAIL>',
    'customer_rank' => 1,
    'credit_limit' => 10000.00
));

// تحويل إلى مورد
$partner_model->make_supplier(array($customer_id));

// البحث عن العملاء فقط
$customers = $partner_model->search_customers();

// الحصول على رصيد الشريك
$balance = $partner_model->get_partner_balance($customer_id);

// التحقق من الحد الائتماني
$can_sell = $partner_model->check_credit_limit($customer_id, 5000.00);
```

## 👤 نموذج المستخدمين (ResUsers)

### إدارة المستخدمين
```php
require_once 'models/ResUsers.php';

$user_model = new ResUsers();

// إنشاء مستخدم جديد
$user_id = $user_model->create(array(
    'name' => 'مستخدم جديد',
    'login' => '<EMAIL>',
    'email' => '<EMAIL>',
    'password' => 'password123',
    'company_id' => 1,
    'groups_id' => json_encode(array(2)) // مجموعة المستخدمين
));

// تسجيل الدخول
$user_data = $user_model->authenticate('<EMAIL>', 'admin123');

// الحصول على المستخدم الحالي
$current_user = $user_model->get_current_user();

// التحقق من الصلاحيات
$has_permission = $user_model->has_permission('manage_companies');

// تغيير كلمة المرور
$user_model->change_password($user_id, 'old_password', 'new_password');
```

## 📦 نموذج المنتجات (ProductTemplate)

### إدارة المنتجات
```php
require_once 'models/ProductTemplate.php';

$product_model = new ProductTemplate();

// إنشاء منتج جديد
$product_id = $product_model->create(array(
    'name' => 'منتج جديد',
    'categ_id' => 1,
    'list_price' => 100.00,
    'type' => 'product',
    'uom_id' => 1,
    'uom_po_id' => 1
));

// البحث عن المنتجات القابلة للبيع
$saleable_products = $product_model->search_saleable();

// الحصول على معلومات المخزون
$stock_info = $product_model->get_stock_info($product_id);

// البحث بالاسم أو الكود
$search_results = $product_model->name_search('منتج');
```

## 🔧 إعداد قاعدة البيانات

### إنشاء نموذج جديد

1. **إنشاء الجدول في SQL**
```sql
CREATE TABLE my_custom_table (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    active BOOLEAN DEFAULT TRUE,
    company_id INT NOT NULL DEFAULT 1,
    create_uid INT DEFAULT 1,
    write_uid INT DEFAULT 1,
    create_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    write_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_name (name),
    INDEX idx_active (active),
    FOREIGN KEY (company_id) REFERENCES res_company(id) ON DELETE RESTRICT
);
```

2. **إنشاء النموذج**
```php
<?php
require_once 'BaseModel.php';

class MyCustomModel extends BaseModel {
    protected function init() {
        $this->table = 'my_custom_table';
        
        $this->fields = array(
            'id', 'name', 'description', 'active', 'company_id',
            'create_uid', 'write_uid', 'create_date', 'write_date'
        );
        
        $this->required_fields = array('name');
        
        $this->default_values = array(
            'active' => true,
            'company_id' => 1
        );
    }
    
    // دوال مخصصة
    public function search_active($domain = array()) {
        $active_domain = array(array('active', '=', true));
        return $this->search_read(array_merge($active_domain, $domain));
    }
    
    // hooks للتخصيص
    protected function post_create($id, $values) {
        // منطق بعد الإنشاء
    }
    
    protected function post_write($id, $values) {
        // منطق بعد التحديث
    }
    
    protected function pre_unlink($id) {
        // التحقق قبل الحذف
    }
}
?>
```

## 🎨 إضافة صفحة جديدة

### إنشاء صفحة إدارة
```php
<?php
session_start();
require_once '../config/database_config.php';
require_once '../config/database_odoo.php';
require_once '../models/MyCustomModel.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$model = new MyCustomModel();
$records = $model->search_read();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>إدارة السجلات المخصصة</title>
    <link href="../assets/css/odoo-style.css" rel="stylesheet">
</head>
<body>
    <!-- محتوى الصفحة -->
</body>
</html>
```

## 🔌 إنشاء API جديد

```php
<?php
header('Content-Type: application/json; charset=utf-8');
session_start();

require_once '../config/database_config.php';
require_once '../config/database_odoo.php';
require_once '../models/MyCustomModel.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(array('error' => 'غير مصرح'));
    exit();
}

$model = new MyCustomModel();
$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'GET':
            $records = $model->search_read();
            echo json_encode(array('success' => true, 'data' => $records));
            break;
            
        case 'POST':
            $input = json_decode(file_get_contents('php://input'), true);
            $id = $model->create($input);
            echo json_encode(array('success' => true, 'id' => $id));
            break;
            
        // المزيد من الطرق...
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(array('error' => $e->getMessage()));
}
?>
```

## 🧪 الاختبار والتطوير

### اختبار النماذج
```php
// اختبار إنشاء وقراءة
$model = new MyCustomModel();
$id = $model->create(array('name' => 'اختبار'));
$record = $model->read(array($id));
assert($record[0]['name'] === 'اختبار');

// اختبار البحث
$results = $model->search(array(array('name', '=', 'اختبار')));
assert(in_array($id, $results));

// تنظيف
$model->unlink(array($id));
```

### نصائح للتطوير
1. **استخدم المعاملات** للعمليات المعقدة
2. **تحقق من الصلاحيات** قبل العمليات الحساسة
3. **استخدم hooks** للمنطق المخصص
4. **اتبع تسمية Odoo** للحقول والجداول
5. **أضف فهارس** للحقول المستخدمة في البحث

## 📚 مراجع إضافية

- [Odoo Documentation](https://www.odoo.com/documentation)
- [PHP PDO Documentation](https://www.php.net/manual/en/book.pdo.php)
- [MySQL Documentation](https://dev.mysql.com/doc/)

---

## 🤝 المساهمة

لإضافة ميزات جديدة:
1. أنشئ النموذج في `models/`
2. أضف الجداول في `sql/`
3. أنشئ الصفحات في `pages/`
4. أضف API في `api/`
5. حدث التوثيق

**مبروك! أنت الآن جاهز للتطوير مع النظام** 🚀
