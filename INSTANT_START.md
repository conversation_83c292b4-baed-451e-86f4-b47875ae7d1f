# ⚡ التشغيل الفوري - نظام ERP المحاسبي

## 🚀 تشغيل في 30 ثانية!

### الخطوة الوحيدة:
```
1. شغل XAMPP
2. اذهب إلى: http://localhost/acc/
3. استمتع! 🎉
```

---

## 🎯 طرق التشغيل السريع

### ⚡ الأسرع على الإطلاق
```
http://localhost/acc/launch.php
```
**ماذا يحدث:**
- ✅ تثبيت تلقائي فوري
- ✅ إنشاء قاعدة البيانات
- ✅ إدراج البيانات الأساسية
- ✅ تسجيل دخول تلقائي
- ✅ توجيه مباشر للنظام

### 🏠 الصفحة الرئيسية الذكية
```
http://localhost/acc/
```
**ماذا يحدث:**
- 🔍 يتحقق من حالة النظام
- 🛠️ يثبت تلقائياً إذا لم يكن مثبت
- 🔐 يوجه لتسجيل الدخول إذا لم تكن مسجل
- 🏠 يوجه للنظام إذا كنت مسجل

### 🎮 التشغيل المباشر
```
http://localhost/acc/run.php
```
**ماذا يحدث:**
- 🔐 تسجيل دخول فوري
- 🏠 توجيه مباشر للنظام
- 💨 بدون أي انتظار

### 🔧 التثبيت التفاعلي
```
http://localhost/acc/auto_setup.php
```
**ماذا يحدث:**
- 📊 عرض تقدم التثبيت
- ✅ تثبيت شامل مع جميع الميزات
- 🎯 توجيه تلقائي بعد الانتهاء

---

## 🔑 بيانات الدخول

### المستخدم الافتراضي:
- **البريد الإلكتروني:** `<EMAIL>`
- **كلمة المرور:** `admin123`

### الشركة الافتراضية:
- **الاسم:** `شركتي`
- **الكود:** `MYCO`

---

## 🛠️ إعدادات تلقائية

### قاعدة البيانات:
- **الخادم:** `localhost`
- **اسم قاعدة البيانات:** `erp_accounting`
- **المستخدم:** `root`
- **كلمة المرور:** (فارغة)

### النظام:
- **العملة:** `ر.س` (ريال سعودي)
- **المنطقة الزمنية:** `Asia/Riyadh`
- **اللغة:** `العربية`

---

## 🎯 ما يحدث تلقائياً

### 📁 إنشاء المجلدات:
- `config/` - ملفات التكوين
- `logs/` - ملفات السجلات
- `cache/` - ملفات التخزين المؤقت
- `uploads/` - الملفات المرفوعة
- `tmp/` - الملفات المؤقتة
- `sessions/` - جلسات المستخدمين

### 🗄️ إنشاء الجداول:
- `res_company` - الشركات
- `res_users` - المستخدمين
- `res_partner` - الشركاء (العملاء/الموردين)
- `product_template` - المنتجات
- `account_account` - الحسابات

### 📊 إدراج البيانات:
- شركة افتراضية
- مستخدم إداري
- عملاء تجريبيين
- موردين تجريبيين
- منتجات تجريبية

### 🔐 إعداد الأمان:
- تشفير كلمات المرور
- إعداد الجلسات
- صلاحيات المستخدمين

---

## 🚨 استكشاف الأخطاء

### المشكلة: لا يعمل النظام
**الحل:**
1. تأكد من تشغيل Apache في XAMPP
2. تأكد من وضع الملفات في `htdocs/acc/`
3. جرب: `http://localhost/acc/launch.php`

### المشكلة: خطأ في قاعدة البيانات
**الحل:**
1. تأكد من تشغيل MySQL في XAMPP
2. النظام سيعمل تلقائياً في الوضع التجريبي إذا فشل MySQL

### المشكلة: صفحة فارغة
**الحل:**
1. تحقق من ملفات PHP في مجلد `htdocs/acc/`
2. تأكد من أن PHP يعمل في XAMPP
3. جرب: `http://localhost/acc/test_database.php`

---

## 🎉 مبروك!

النظام الآن جاهز للاستخدام! 

### 🔗 روابط سريعة:
- 🏠 **الصفحة الرئيسية:** `http://localhost/acc/demo.php`
- 🏢 **إدارة الشركات:** `http://localhost/acc/pages/companies.php`
- 👥 **إدارة العملاء:** `http://localhost/acc/pages/customers.php`
- 📄 **إدارة الفواتير:** `http://localhost/acc/pages/invoices.php`
- 📦 **إدارة المنتجات:** `http://localhost/acc/pages/products.php`
- 📊 **التقارير:** `http://localhost/acc/pages/reports.php`

### 🧪 اختبار النظام:
- 🔬 **صفحة الاختبار:** `http://localhost/acc/test_database.php`

---

## 💡 نصائح للاستخدام

1. **ابدأ بالتشغيل الفوري** (`/launch.php`) لأول مرة
2. **استخدم الصفحة الرئيسية** (`/`) للاستخدام العادي
3. **جرب جميع الصفحات** لاستكشاف الميزات
4. **اطلع على التقارير** لفهم إمكانيات النظام

---

## 🎯 الخلاصة

**النظام يعمل تلقائياً 100%!**

لا تحتاج لأي إعداد يدوي - فقط شغل XAMPP واذهب إلى الرابط!

**استمتع بنظام ERP المحاسبي المتكامل!** 🚀
