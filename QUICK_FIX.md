# ⚡ **حل سريع لمشكلة قاعدة البيانات**

## 🚨 **المشكلة:**
```
خطأ في الاتصال بقاعدة البيانات: SQLSTATE[HY000] [1049] Unknown database 'erp_accounting'
```

---

## 🎯 **الحل السريع (دقيقة واحدة):**

### **الخطوة 1: افتح الرابط التالي**
```
http://localhost/acc/setup_database.php
```

### **الخطوة 2: انقر على الزر الأزرق**
```
"بدء إعداد قاعدة البيانات"
```

### **الخطوة 3: انتظر حتى ترى**
```
🎉 تم إعداد قاعدة البيانات بنجاح!
```

### **الخطوة 4: ابدأ استخدام النظام**
```
http://localhost/acc/start_here.php
```

---

## 🔐 **بيانات تسجيل الدخول:**
```
اسم المستخدم: admin
كلمة المرور: admin123
```

---

## 🛠️ **إذا لم يعمل الحل السريع:**

### **تأكد من تشغيل XAMPP:**
1. افتح XAMPP Control Panel
2. تأكد من أن Apache أخضر ✅
3. تأكد من أن MySQL أخضر ✅

### **جرب الحل اليدوي:**
1. افتح: `http://localhost/phpmyadmin`
2. انقر على "New" أو "جديد"
3. اكتب: `erp_accounting`
4. اختر: `utf8mb4_unicode_ci`
5. انقر على "Create" أو "إنشاء"
6. انقر على تبويب "Import" أو "استيراد"
7. اختر ملف: `database_setup.sql`
8. انقر على "Go" أو "تنفيذ"

---

## ✅ **علامات النجاح:**

### **في صفحة الإعداد:**
- ✅ ظهور رسالة "تم إعداد قاعدة البيانات بنجاح"
- ✅ ظهور عدد الجداول المنشأة
- ✅ ظهور بيانات تسجيل الدخول

### **في صفحة البداية:**
- ✅ ظهور "قاعدة البيانات متصلة" باللون الأخضر
- ✅ عمل جميع الروابط بدون أخطاء

### **في تسجيل الدخول:**
- ✅ قبول admin/admin123
- ✅ توجيه للوحة التحكم

---

## 🔗 **الروابط المهمة:**

| الوصف | الرابط |
|--------|---------|
| إعداد قاعدة البيانات | http://localhost/acc/setup_database.php |
| صفحة البداية | http://localhost/acc/start_here.php |
| تسجيل الدخول | http://localhost/acc/login.php |
| phpMyAdmin | http://localhost/phpmyadmin |
| لوحة التحكم | http://localhost/acc/dashboard.php |

---

## 📞 **للمساعدة السريعة:**

### **تحقق من حالة قاعدة البيانات:**
```
http://localhost/acc/check_database.php
```

### **إذا ظهرت أخطاء أخرى:**
1. تأكد من تشغيل XAMPP
2. أعد تشغيل Apache و MySQL
3. جرب الروابط مرة أخرى
4. تحقق من مجلد htdocs

---

## 🎉 **بعد الحل:**

### **ستتمكن من:**
- ✅ تسجيل الدخول للنظام
- ✅ استخدام جميع النماذج
- ✅ إضافة العملاء والمنتجات
- ✅ إنشاء قيود اليومية
- ✅ عرض التقارير

### **النظام سيحتوي على:**
- 👤 مستخدم افتراضي (admin)
- 🏢 شركة افتراضية
- 👥 5 شركاء تجريبيين
- 📦 5 منتجات تجريبية
- 📚 دليل حسابات كامل
- 📊 دفاتر يومية جاهزة

**🚀 النظام جاهز للاستخدام فوراً!** ✨
