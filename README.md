# نظام ERP المحاسبي 🚀

نظام محاسبي متكامل بأسلوب Odoo ERP يدعم الشركات والفروع باللغة العربية، مبني بتقنيات PHP و MySQL و JavaScript.

## ✨ نظام ERP محاسبي متكامل بأسلوب Odoo - جاهز للتشغيل الفوري!

النظام مكتمل ويعمل تلقائياً 100% مع XAMPP. **تم حل جميع المشاكل وإضافة نظام Odoo متكامل!**

### 🚀 **تشغيل فوري في 5 ثوان:**
```
1. شغل XAMPP (Apache فقط كافي)
2. اذهب إلى: http://localhost/acc/go
3. استمتع بنظام Odoo! 🎉
```

### 🎯 **المشاكل المحلولة:**
- ✅ **حل مشكلة Internal Server Error**
- ✅ **ملف .htaccess مبسط وآمن**
- ✅ **نظام Odoo متكامل**
- ✅ **قوائم وإجراءات بأسلوب Odoo**
- ✅ **تشغيل تلقائي 100%**

## الميزات الرئيسية

### 🏢 إدارة الشركات والفروع
- إدارة متعددة الشركات والفروع
- هيكل تنظيمي مرن
- إعدادات منفصلة لكل شركة

### 💰 المحاسبة العامة
- دليل حسابات شامل
- القيود المحاسبية
- الميزانية العمومية
- قائمة الدخل
- التقارير المالية

### 🛒 إدارة المبيعات
- إدارة العملاء
- عروض الأسعار
- فواتير المبيعات
- متابعة المدفوعات
- تقارير المبيعات

### 📦 إدارة المشتريات
- إدارة الموردين
- أوامر الشراء
- فواتير المشتريات
- متابعة المدفوعات
- تقارير المشتريات

### 📊 إدارة المخزون
- إدارة المنتجات والخدمات
- المستودعات المتعددة
- حركات المخزون
- الجرد الدوري
- تقارير المخزون

### 👥 إدارة المستخدمين
- نظام أدوار وصلاحيات
- مستخدمين متعددين
- سجل الأنشطة
- أمان متقدم

## المتطلبات التقنية

### متطلبات الخادم
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache/Nginx
- mod_rewrite مفعل

### المكتبات المطلوبة
- PDO MySQL
- JSON
- cURL
- GD Library (للصور)

## 🚀 التشغيل التلقائي الفوري

### ⚡ الطريقة الأسرع (تلقائي 100%)
```
1. شغل XAMPP (Apache + MySQL)
2. اذهب إلى: http://localhost/acc/
3. النظام سيعمل تلقائياً!
```

### 🎯 طرق التشغيل المختلفة

#### 1️⃣ التشغيل التلقائي الذكي
```
http://localhost/acc/
```
- **يكتشف تلقائياً** إذا كان النظام مثبت أم لا
- **يثبت تلقائياً** قاعدة البيانات إذا لم تكن موجودة
- **يسجل دخول تلقائياً** ويوجه للنظام

#### 2️⃣ التشغيل الفوري (بدون انتظار)
```
http://localhost/acc/launch.php
```
- **تثبيت فوري** في الخلفية
- **تشغيل مباشر** بدون شاشات تثبيت
- **يعمل حتى لو فشلت قاعدة البيانات**

#### 3️⃣ التثبيت التفاعلي
```
http://localhost/acc/auto_setup.php
```
- **عرض خطوات التثبيت** مع التقدم
- **تثبيت شامل** مع جميع البيانات
- **توجيه تلقائي** بعد الانتهاء

#### 4️⃣ التشغيل السريع
```
http://localhost/acc/run.php
```
- **تسجيل دخول فوري** بدون كلمة مرور
- **يعمل مع أو بدون قاعدة بيانات**

### 🔑 بيانات تسجيل الدخول
- **البريد الإلكتروني**: `<EMAIL>`
- **كلمة المرور**: `admin123`

### 📋 المتطلبات
- ✅ **XAMPP** مثبت ويعمل
- ✅ **Apache** مفعل
- ⚠️ **MySQL** اختياري (النظام يعمل بدونه)

---

## 🎯 **طرق التشغيل الفوري**

| الرابط | الوصف | السرعة | الميزات |
|--------|--------|---------|---------|
| `http://localhost/acc/go` | **🚀 الأسرع - Odoo Style** | ⭐⭐⭐⭐⭐ | تشغيل فوري مع نظام Odoo |
| `http://localhost/acc/` | **🏠 التشغيل الذكي** | ⭐⭐⭐⭐ | يكتشف الحالة ويعمل تلقائياً |
| `http://localhost/acc/launch` | **⚡ التثبيت الفوري** | ⭐⭐⭐⭐ | إعداد كامل في الخلفية |
| `http://localhost/acc/run` | **🎮 التشغيل المباشر** | ⭐⭐⭐ | دخول فوري للنظام |
| `http://localhost/acc/setup` | **🔧 التثبيت التفاعلي** | ⭐⭐⭐ | عرض خطوات التثبيت |

### 🔗 **روابط مختصرة إضافية:**
- `http://localhost/acc/demo` - الصفحة الرئيسية مع Odoo
- `http://localhost/acc/status` - حالة النظام المتقدمة
- `http://localhost/acc/test` - اختبار قاعدة البيانات
- `http://localhost/acc/test_complete` - **اختبار شامل للنظام**
- `http://localhost/acc/start` - صفحة البداية

## 🗄️ قاعدة البيانات والنماذج (بأسلوب Odoo)

### هيكل قاعدة البيانات
النظام يستخدم هيكل قاعدة بيانات مشابه لـ Odoo مع:

#### الجداول الأساسية
- `res_company` - الشركات والفروع
- `res_users` - المستخدمين والصلاحيات
- `res_partner` - الشركاء (العملاء والموردين)
- `res_currency` - العملات
- `product_template` - قوالب المنتجات
- `product_product` - المنتجات
- `account_account` - دليل الحسابات
- `account_move` - القيود المحاسبية

#### النماذج (Models)
- `BaseModel` - النموذج الأساسي مع دوال Odoo
- `ResCompany` - إدارة الشركات
- `ResPartner` - إدارة الشركاء
- `ResUsers` - إدارة المستخدمين
- `ProductTemplate` - إدارة المنتجات

#### دوال Odoo المدعومة
- `create()` - إنشاء سجلات جديدة
- `read()` - قراءة السجلات
- `write()` - تحديث السجلات
- `unlink()` - حذف السجلات
- `search()` - البحث في السجلات
- `search_read()` - البحث والقراءة
- `search_count()` - عد السجلات

### اختبار النظام
- صفحة الاختبار: `http://localhost/acc/test_database.php`

### التثبيت الكامل (للإنتاج)

#### 1. متطلبات النظام
- PHP 7.4+ (يفضل 8.0+)
- MySQL 5.7+ أو MariaDB 10.3+
- Apache/Nginx مع mod_rewrite
- مساحة قرص: 100MB على الأقل

#### 2. إعداد قاعدة البيانات
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE erp_accounting CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- تشغيل ملف الهيكل
mysql -u username -p erp_accounting < sql/database_structure.sql
```

#### 3. إعداد الاتصال
تعديل ملف `config/database.php`:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'erp_accounting');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

#### 4. تشغيل معالج التثبيت
- افتح `install.php` في المتصفح
- اتبع خطوات التثبيت التلقائي
- أدخل بيانات قاعدة البيانات
- إنشاء حساب المدير

#### 5. الوصول للنظام
- **الرابط**: `http://your-domain.com/`
- **المستخدم التجريبي**: `<EMAIL>`
- **كلمة المرور**: `admin123`

### تشغيل خادم التطوير المحلي
```bash
# تشغيل الخادم المدمج
php server.php

# أو استخدام خادم PHP المدمج مباشرة
php -S localhost:8000
```

## هيكل المشروع

```
erp-accounting/
├── index.php                 # الصفحة الرئيسية
├── config/
│   └── database.php          # إعدادات قاعدة البيانات
├── includes/
│   └── functions.php         # الدوال المساعدة
├── pages/
│   ├── login.php            # صفحة تسجيل الدخول
│   └── logout.php           # تسجيل الخروج
├── api/
│   └── dashboard.php        # API لوحة التحكم
├── assets/
│   ├── css/
│   │   └── style.css        # ملف التصميم
│   └── js/
│       └── app.js           # JavaScript الرئيسي
├── sql/
│   └── database_structure.sql # هيكل قاعدة البيانات
└── README.md                # هذا الملف
```

## الصفحات المتوفرة

### 🏠 الصفحة الرئيسية (`demo.php`)
- لوحة تحكم تفاعلية
- إحصائيات المبيعات والعملاء
- أحدث الفواتير والإشعارات
- روابط سريعة للوظائف الرئيسية

### 🏢 إدارة الشركات (`pages/companies.php`)
- عرض جميع الشركات في بطاقات تفاعلية
- إضافة وتعديل الشركات
- إدارة الفروع لكل شركة
- إحصائيات المستخدمين والفروع

### 👥 إدارة العملاء (`pages/customers.php`)
- قاعدة بيانات شاملة للعملاء
- فلترة وبحث متقدم
- عرض الأرصدة والحدود الائتمانية
- إنشاء فواتير مباشرة للعملاء

### 📄 إدارة الفواتير (`pages/invoices.php`)
- عرض الفواتير في بطاقات ملونة
- تتبع حالة الدفع والاستحقاق
- فلترة حسب الحالة والتاريخ والعميل
- تسجيل المدفوعات وطباعة الفواتير

### 📦 إدارة المنتجات (`pages/products.php`)
- كتالوج شامل للمنتجات
- متابعة المخزون والحدود الدنيا/العليا
- إدارة الأسعار وهوامش الربح
- طباعة الباركود وتعديل المخزون

### 📊 التقارير والإحصائيات (`pages/reports.php`)
- رسوم بيانية تفاعلية للمبيعات
- تقارير أفضل العملاء والمنتجات
- فلترة حسب التاريخ والشركة والفرع
- تصدير التقارير بصيغ مختلفة

### ⚙️ إعدادات النظام (`pages/settings.php`)
- إعدادات الشركة والعملة
- تكوين البريد الإلكتروني
- جدولة النسخ الاحتياطي
- معلومات النظام والخادم

## الاستخدام

### البدء السريع
1. **افتح الصفحة الرئيسية**: `demo.php`
2. **تصفح القوائم**: استخدم الشريط الجانبي للتنقل
3. **جرب الميزات**: جميع الأزرار تعمل وتعرض رسائل تفاعلية
4. **استكشف البيانات**: البيانات التجريبية تحاكي بيئة عمل حقيقية

### إدارة الشركات
1. انتقل إلى "الشركات والفروع"
2. اضغط "إضافة شركة جديدة"
3. أدخل بيانات الشركة (الاسم، الرقم الضريبي، إلخ)
4. احفظ وابدأ في إضافة الفروع

### إنشاء فاتورة مبيعات
1. اذهب إلى "الفواتير"
2. اضغط "فاتورة جديدة"
3. اختر العميل من القائمة
4. أضف المنتجات والكميات
5. احفظ واطبع الفاتورة

### إدارة المخزون
1. افتح "إدارة المخزون"
2. أضف منتجات جديدة مع الأسعار
3. حدد الحدود الدنيا والعليا للمخزون
4. تابع التنبيهات للمخزون المنخفض

## التخصيص

### إضافة حقول جديدة
1. تعديل قاعدة البيانات
2. تحديث النماذج
3. تعديل ملفات PHP

### تخصيص التصميم
- تعديل ملف `assets/css/style.css`
- إضافة CSS مخصص
- تغيير الألوان والخطوط

### إضافة تقارير جديدة
1. إنشاء ملف PHP جديد
2. كتابة استعلامات SQL
3. تصميم التقرير
4. إضافة رابط في القائمة

## الأمان

### حماية قاعدة البيانات
- استخدام Prepared Statements
- تشفير كلمات المرور
- التحقق من الصلاحيات

### حماية الملفات
- منع الوصول المباشر للملفات الحساسة
- تشفير البيانات الحساسة
- تسجيل العمليات

### النسخ الاحتياطي
```bash
# نسخ احتياطي يومي
mysqldump -u username -p database_name > backup_$(date +%Y%m%d).sql
```

## التطوير

### إضافة وحدة جديدة
1. إنشاء جداول قاعدة البيانات
2. إنشاء ملفات PHP
3. إضافة JavaScript
4. تحديث القوائم

### اختبار النظام
- اختبار الوحدات
- اختبار التكامل
- اختبار الأداء
- اختبار الأمان

## الدعم الفني

### المشاكل الشائعة
1. **خطأ في الاتصال بقاعدة البيانات**
   - التحقق من إعدادات الاتصال
   - التأكد من تشغيل MySQL

2. **مشاكل الصلاحيات**
   - التحقق من صلاحيات الملفات
   - التأكد من إعدادات Apache

3. **مشاكل الترميز**
   - التأكد من UTF-8
   - إعدادات قاعدة البيانات

### التحديثات
- متابعة التحديثات الأمنية
- تحديث المكتبات
- إضافة ميزات جديدة

## الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف LICENSE للتفاصيل.

## المساهمة

نرحب بالمساهمات! يرجى:
1. Fork المشروع
2. إنشاء branch جديد
3. إضافة التحسينات
4. إرسال Pull Request

## الاتصال

- البريد الإلكتروني: <EMAIL>
- الموقع: https://erp-accounting.com
- التوثيق: https://docs.erp-accounting.com

---

**ملاحظة**: هذا النظام في مرحلة التطوير. يرجى اختباره جيداً قبل الاستخدام في بيئة الإنتاج.
