# 🚀 ابدأ هنا - نظام ERP المحاسبي

مرحباً بك في نظام ERP المحاسبي المتكامل! هذا دليل سريع للبدء.

## ⚡ التشغيل السريع (بدون تثبيت)

### 1. افتح النظام مباشرة
- **افتح ملف**: `demo.php` في المتصفح
- **أو اضغط هنا**: [تشغيل النظام](demo.php)

### 2. استكشف الميزات
النظام يعمل بالكامل مع بيانات تجريبية:

#### 🏠 الصفحة الرئيسية
- لوحة تحكم شاملة
- إحصائيات المبيعات
- أحدث الفواتير
- إشعارات النظام

#### 🏢 إدارة الشركات
- عرض الشركات في بطاقات تفاعلية
- إضافة شركات جديدة
- إدارة الفروع والمستخدمين

#### 👥 إدارة العملاء
- قاعدة بيانات العملاء
- البحث والفلترة المتقدمة
- متابعة الأرصدة والحدود الائتمانية

#### 📄 إدارة الفواتير
- فواتير ملونة حسب الحالة
- تتبع المدفوعات
- طباعة وتصدير الفواتير

#### 📦 إدارة المنتجات
- كتالوج المنتجات
- متابعة المخزون
- إدارة الأسعار والباركود

#### 📊 التقارير
- رسوم بيانية تفاعلية
- تقارير المبيعات والعملاء
- تصدير بصيغ مختلفة

#### ⚙️ الإعدادات
- إعدادات الشركة
- تكوين البريد الإلكتروني
- النسخ الاحتياطي

## 🎯 الميزات الرئيسية

### ✅ جاهز للاستخدام
- لا يحتاج تثبيت معقد
- بيانات تجريبية شاملة
- واجهة عربية بالكامل

### ✅ تصميم متجاوب
- يعمل على جميع الأجهزة
- تصميم عصري وجذاب
- سهولة في الاستخدام

### ✅ ميزات متقدمة
- فلترة وبحث ذكي
- رسوم بيانية تفاعلية
- تصدير وطباعة

### ✅ أمان عالي
- حماية من الثغرات
- تشفير البيانات
- سجل الأنشطة

## 🔧 للمطورين

### هيكل المشروع
```
erp-accounting/
├── demo.php              # الصفحة الرئيسية
├── pages/                # صفحات النظام
│   ├── companies.php     # إدارة الشركات
│   ├── customers.php     # إدارة العملاء
│   ├── invoices.php      # إدارة الفواتير
│   ├── products.php      # إدارة المنتجات
│   ├── reports.php       # التقارير
│   └── settings.php      # الإعدادات
├── config/               # ملفات الإعدادات
├── assets/               # الملفات الثابتة
├── api/                  # واجهات برمجة التطبيقات
└── scripts/              # سكريبتات المساعدة
```

### التقنيات المستخدمة
- **Backend**: PHP (متوافق مع 5.2+)
- **Frontend**: Bootstrap 5 + JavaScript
- **Database**: MySQL/MariaDB
- **Charts**: Chart.js
- **Icons**: Font Awesome

### التطوير والتخصيص
1. **إضافة صفحات جديدة**: انسخ هيكل الصفحات الموجودة
2. **تعديل التصميم**: عدل ملفات CSS في `assets/css/`
3. **إضافة وظائف**: استخدم `config/database_simple.php`
4. **API جديدة**: أضف ملفات في مجلد `api/`

## 📞 الدعم والمساعدة

### الأسئلة الشائعة

**س: هل يحتاج النظام قاعدة بيانات؟**
ج: لا، النسخة التجريبية تعمل بدون قاعدة بيانات. للاستخدام الفعلي، راجع ملف `install.php`.

**س: هل يمكن تخصيص النظام؟**
ج: نعم، النظام مفتوح المصدر ويمكن تخصيصه بالكامل.

**س: هل يدعم النظام عدة شركات؟**
ج: نعم، النظام يدعم إدارة متعددة الشركات والفروع.

**س: هل البيانات آمنة؟**
ج: نعم، النظام يتضمن حماية متقدمة من الثغرات الأمنية.

### التواصل
- **البريد الإلكتروني**: <EMAIL>
- **الموقع**: https://erp-accounting.com
- **التوثيق**: https://docs.erp-accounting.com

## 🎉 ابدأ الآن!

1. **افتح**: `demo.php` في المتصفح
2. **استكشف**: جميع الصفحات والميزات
3. **جرب**: إضافة البيانات والتفاعل مع النظام
4. **طور**: خصص النظام حسب احتياجاتك

---

**نصيحة**: ابدأ بتصفح الصفحة الرئيسية ثم انتقل للصفحات الأخرى عبر القائمة الجانبية.

**استمتع بتجربة نظام ERP المحاسبي! 🚀**
