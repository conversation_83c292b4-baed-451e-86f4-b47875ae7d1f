-- =============================================
-- إضافة بيانات تجريبية
-- =============================================

-- بدء المعاملة
START TRANSACTION;

-- =============================================
-- 1. إضافة عملاء
-- =============================================
INSERT INTO `res_partner` (
    `name`, 
    `company_id`, 
    `customer_rank`, 
    `type`, 
    `street`, 
    `city`, 
    `phone`, 
    `email`, 
    `lang`, 
    `create_uid`, 
    `create_date`, 
    `write_uid`, 
    `write_date`
) VALUES 
('أحمد محمد', 1, 1, 'contact', 'شارع الملك فهد', 'الرياض', '0501234567', '<EMAIL>', 'ar_001', 1, NOW(), 1, NOW()),
('سارة عبدالله', 1, 1, 'contact', 'حي العليا', 'جدة', '0557654321', '<EMAIL>', 'ar_001', 1, NOW(), 1, NOW()),
('شركة التقنية المتطورة', 1, 1, 'invoice', 'حي الصحافة', 'الرياض', '0112345678', '<EMAIL>', 'ar_001', 1, NOW(), 1, NOW());

-- =============================================
-- 2. إضافة موردين
-- =============================================
INSERT INTO `res_partner` (
    `name`, 
    `company_id`, 
    `supplier_rank`, 
    `type`, 
    `street`, 
    `city`, 
    `phone`, 
    `email`, 
    `lang`, 
    `create_uid`, 
    `create_date`, 
    `write_uid`, 
    `write_date`
) VALUES 
('مصنع الأجهزة الإلكترونية', 1, 1, 'contact', 'المنطقة الصناعية', 'الدمام', '0137654321', '<EMAIL>', 'ar_001', 1, NOW(), 1, NOW()),
('شركة التوريدات العامة', 1, 1, 'invoice', 'حي النهضة', 'الرياض', '0118765432', '<EMAIL>', 'ar_001', 1, NOW(), 1, NOW());

-- =============================================
-- 3. إضافة منتجات
-- =============================================
-- الحصول على معرف فئة المنتجات
SET @product_category = (SELECT `id` FROM `product_category` WHERE `name` = 'المنتجات العامة' LIMIT 1);
SET @uom_unit = (SELECT `id` FROM `uom_uom` WHERE `name` = 'وحدة' LIMIT 1);

-- إضافة منتجات
INSERT INTO `product_template` (
    `name`, 
    `categ_id`, 
    `list_price`, 
    `standard_price`, 
    `type`, 
    `uom_id`, 
    `uom_po_id`, 
    `sale_ok`, 
    `purchase_ok`, 
    `create_uid`, 
    `create_date`, 
    `write_uid`, 
    `write_date`
) VALUES 
('لابتوب ديل انسبيرون', @product_category, 3500.00, 2800.00, 'product', @uom_unit, @uom_unit, 1, 1, 1, NOW(), 1, NOW()),
('هاتف سامسونج جالكسي', @product_category, 3200.00, 2500.00, 'product', @uom_unit, @uom_unit, 1, 1, 1, NOW(), 1, NOW()),
('طابعة ليزر HP', @product_category, 1200.00, 900.00, 'product', @uom_unit, @uom_unit, 1, 1, 1, NOW(), 1, NOW());

-- الحصول على معرفات المنتجات المضافة
SET @laptop_id = LAST_INSERT_ID();
SET @phone_id = @laptop_id + 1;
SET @printer_id = @laptop_id + 2;

-- إضافة تفاصيل المنتجات
INSERT INTO `product_product` (
    `product_tmpl_id`, 
    `default_code`, 
    `active`, 
    `create_uid`, 
    `create_date`, 
    `write_uid`, 
    `write_date`
) VALUES 
(@laptop_id, 'LAP-DELL-001', 1, 1, NOW(), 1, NOW()),
(@phone_id, 'PHN-SAM-001', 1, 1, NOW(), 1, NOW()),
(@printer_id, 'PRN-HP-001', 1, 1, NOW(), 1, NOW());

-- =============================================
-- 4. إضافة مخزون أولي
-- =============================================
-- الحصول على موقع المخزون
SET @stock_location = (SELECT `id` FROM `stock_location` WHERE `name` = 'الشركة/المستودع الرئيسي' LIMIT 1);

-- إضافة كميات المخزون
INSERT INTO `stock_quant` (
    `product_id`, 
    `location_id`, 
    `quantity`, 
    `create_uid`, 
    `create_date`, 
    `write_uid`, 
    `write_date`
) VALUES 
(@laptop_id, @stock_location, 10, 1, NOW(), 1, NOW()),
(@phone_id, @stock_location, 25, 1, NOW(), 1, NOW()),
(@printer_id, @stock_location, 8, 1, NOW(), 1, NOW());

-- =============================================
-- تأكيد التغييرات
-- =============================================
COMMIT;

-- رسالة نجاح
SELECT 'تمت إضافة البيانات التجريبية بنجاح!' AS message;
