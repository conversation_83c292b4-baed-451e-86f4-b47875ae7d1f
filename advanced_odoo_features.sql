-- Enable foreign key checks
SET FOREIGN_KEY_CHECKS = 0;

-- =============================================
-- Module: Multi-Company
-- =============================================
ALTER TABLE `res_company`
ADD COLUMN IF NOT EXISTS `parent_id` INT AFTER `id`,
ADD COLUMN IF NOT EXISTS `name` VARCHAR(256) NOT NULL AFTER `parent_id`,
ADD COLUMN IF NOT EXISTS `currency_id` INT AFTER `name`,
ADD COLUMN IF NOT EXISTS `create_uid` INT AFTER `currency_id`,
ADD COLUMN IF NOT EXISTS `create_date` DATETIME AFTER `create_uid`,
ADD COLUMN IF NOT EXISTS `write_uid` INT AFTER `create_date`,
ADD COLUMN IF NOT EXISTS `write_date` DATETIME AFTER `write_uid`,
ADD CONSTRAINT `res_company_parent_fk` FOREIGN KEY (`parent_id`) REFERENCES `res_company`(`id`) ON DELETE CASCADE,
ADD CONSTRAINT `res_company_currency_fk` FOREIGN KEY (`currency_id`) REFERENCES `res_currency`(`id`) ON DELETE SET NULL;

-- =============================================
-- Module: Document Management
-- =============================================
CREATE TABLE IF NOT EXISTS `ir_attachment` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(256) NOT NULL,
    `datas` LONGBLOB,
    `datas_fname` VARCHAR(256),
    `res_model` VARCHAR(64),
    `res_id` INT,
    `type` VARCHAR(32) DEFAULT 'binary',
    `mimetype` VARCHAR(128),
    `file_size` INT,
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME,
    `company_id` INT,
    FOREIGN KEY (`company_id`) REFERENCES `res_company`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- =============================================
-- Module: Workflow & Automation
-- =============================================
CREATE TABLE IF NOT EXISTS `base_automation` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(128) NOT NULL,
    `model_id` VARCHAR(64) NOT NULL,
    `trigger` VARCHAR(32) NOT NULL,
    `filter_pre_domain` TEXT,
    `filter_domain` TEXT,
    `state` VARCHAR(32) DEFAULT 'code',
    `code` TEXT,
    `active` BOOLEAN DEFAULT TRUE,
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- =============================================
-- Module: Email & Messaging
-- =============================================
CREATE TABLE IF NOT EXISTS `mail_message` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `model` VARCHAR(64),
    `res_id` INT,
    `message_type` VARCHAR(32) NOT NULL,
    `subject` VARCHAR(256),
    `body` TEXT,
    `date` DATETIME,
    `author_id` INT,
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME,
    FOREIGN KEY (`author_id`) REFERENCES `res_users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `mail_followers` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `res_model` VARCHAR(64) NOT NULL,
    `res_id` INT NOT NULL,
    `partner_id` INT NOT NULL,
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME,
    UNIQUE KEY `mail_followers_res_model_res_id_partner_id_uniq` (`res_model`, `res_id`, `partner_id`),
    FOREIGN KEY (`partner_id`) REFERENCES `res_partner`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- =============================================
-- Module: Calendar & Events
-- =============================================
CREATE TABLE IF NOT EXISTS `calendar_event` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(256) NOT NULL,
    `start` DATETIME,
    `stop` DATETIME,
    `allday` BOOLEAN DEFAULT FALSE,
    `description` TEXT,
    `privacy` VARCHAR(32) DEFAULT 'public',
    `show_as` VARCHAR(32) DEFAULT 'busy',
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- =============================================
-- Module: Website Blog
-- =============================================
CREATE TABLE IF NOT EXISTS `blog_blog` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(128) NOT NULL,
    `subtitle` VARCHAR(256),
    `description` TEXT,
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `blog_post` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(256) NOT NULL,
    `blog_id` INT,
    `author_id` INT,
    `content` LONGTEXT,
    `subtitle` VARCHAR(256),
    `published_date` DATETIME,
    `is_published` BOOLEAN DEFAULT FALSE,
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME,
    FOREIGN KEY (`blog_id`) REFERENCES `blog_blog`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`author_id`) REFERENCES `res_users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- =============================================
-- Module: eCommerce Enhancements
-- =============================================
CREATE TABLE IF NOT EXISTS `product_attribute` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(64) NOT NULL,
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `product_attribute_value` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(64) NOT NULL,
    `attribute_id` INT,
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME,
    FOREIGN KEY (`attribute_id`) REFERENCES `product_attribute`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- =============================================
-- Module: Knowledge Base
-- =============================================
CREATE TABLE IF NOT EXISTS `knowledge_article` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(256) NOT NULL,
    `body` LONGTEXT,
    `parent_id` INT,
    `sequence` INT,
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME,
    FOREIGN KEY (`parent_id`) REFERENCES `knowledge_article`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- =============================================
-- Module: Field Service
-- =============================================
CREATE TABLE IF NOT EXISTS `fsm_location` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(256) NOT NULL,
    `customer_id` INT,
    `contact_id` INT,
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME,
    FOREIGN KEY (`customer_id`) REFERENCES `res_partner`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`contact_id`) REFERENCES `res_partner`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `fsm_order` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(64) NOT NULL,
    `location_id` INT,
    `priority` VARCHAR(16),
    `scheduled_date_start` DATETIME,
    `scheduled_date_end` DATETIME,
    `description` TEXT,
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME,
    FOREIGN KEY (`location_id`) REFERENCES `fsm_location`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;
