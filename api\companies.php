<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

session_start();
require_once '../config/database_simple.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(array('error' => 'غير مصرح بالوصول'), JSON_UNESCAPED_UNICODE);
    exit();
}

// للنسخة التجريبية - تخطي التحقق من الصلاحيات

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    $database = new Database();
    $db = $database->getConnection();
    
    switch ($method) {
        case 'GET':
            handleGet($database);
            break;
            
        case 'POST':
            handlePost($database, $input);
            break;
            
        case 'PUT':
            handlePut($database, $input);
            break;
            
        case 'DELETE':
            handleDelete($database);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'طريقة غير مدعومة'], JSON_UNESCAPED_UNICODE);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'خطأ في الخادم',
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * جلب الشركات
 */
function handleGet($database) {
    $id = $_GET['id'] ?? null;
    
    if ($id) {
        // جلب شركة واحدة
        $sql = "SELECT c.*, 
                       (SELECT COUNT(*) FROM branches WHERE company_id = c.id) as branches_count,
                       (SELECT COUNT(*) FROM users WHERE company_id = c.id) as users_count
                FROM companies c 
                WHERE c.id = ?";
        $company = $database->fetch($sql, [$id]);
        
        if ($company) {
            // جلب الفروع
            $branchesSql = "SELECT * FROM branches WHERE company_id = ? ORDER BY name";
            $branches = $database->fetchAll($branchesSql, [$id]);
            $company['branches'] = $branches;
            
            echo json_encode($company, JSON_UNESCAPED_UNICODE);
        } else {
            http_response_code(404);
            echo json_encode(['error' => 'الشركة غير موجودة'], JSON_UNESCAPED_UNICODE);
        }
    } else {
        // جلب جميع الشركات
        $page = $_GET['page'] ?? 1;
        $limit = $_GET['limit'] ?? 10;
        $search = $_GET['search'] ?? '';
        
        $offset = ($page - 1) * $limit;
        
        $whereClause = "WHERE 1=1";
        $params = [];
        
        if (!empty($search)) {
            $whereClause .= " AND (c.name LIKE ? OR c.tax_number LIKE ? OR c.email LIKE ?)";
            $searchTerm = "%$search%";
            $params = [$searchTerm, $searchTerm, $searchTerm];
        }
        
        // جلب العدد الإجمالي
        $countSql = "SELECT COUNT(*) as total FROM companies c $whereClause";
        $totalResult = $database->fetch($countSql, $params);
        $total = $totalResult['total'];
        
        // جلب البيانات
        $sql = "SELECT c.*, 
                       (SELECT COUNT(*) FROM branches WHERE company_id = c.id) as branches_count,
                       (SELECT COUNT(*) FROM users WHERE company_id = c.id) as users_count
                FROM companies c 
                $whereClause
                ORDER BY c.name 
                LIMIT $limit OFFSET $offset";
        
        $companies = $database->fetchAll($sql, $params);
        
        echo json_encode([
            'data' => $companies,
            'pagination' => [
                'page' => (int)$page,
                'limit' => (int)$limit,
                'total' => (int)$total,
                'pages' => ceil($total / $limit)
            ]
        ], JSON_UNESCAPED_UNICODE);
    }
}

/**
 * إضافة شركة جديدة
 */
function handlePost($database, $input) {
    // التحقق من البيانات المطلوبة
    $required = ['name', 'tax_number', 'phone', 'email'];
    foreach ($required as $field) {
        if (empty($input[$field])) {
            http_response_code(400);
            echo json_encode(['error' => "الحقل $field مطلوب"], JSON_UNESCAPED_UNICODE);
            return;
        }
    }
    
    // التحقق من عدم تكرار الرقم الضريبي
    $checkSql = "SELECT id FROM companies WHERE tax_number = ?";
    $existing = $database->fetch($checkSql, [$input['tax_number']]);
    
    if ($existing) {
        http_response_code(400);
        echo json_encode(['error' => 'الرقم الضريبي موجود بالفعل'], JSON_UNESCAPED_UNICODE);
        return;
    }
    
    // التحقق من صحة البريد الإلكتروني
    if (!filter_var($input['email'], FILTER_VALIDATE_EMAIL)) {
        http_response_code(400);
        echo json_encode(['error' => 'البريد الإلكتروني غير صحيح'], JSON_UNESCAPED_UNICODE);
        return;
    }
    
    // التحقق من صحة الرقم الضريبي
    if (!isValidTaxNumber($input['tax_number'])) {
        http_response_code(400);
        echo json_encode(['error' => 'الرقم الضريبي غير صحيح'], JSON_UNESCAPED_UNICODE);
        return;
    }
    
    $database->beginTransaction();
    
    try {
        // إدراج الشركة
        $companyData = [
            'name' => sanitize($input['name']),
            'tax_number' => sanitize($input['tax_number']),
            'commercial_register' => sanitize($input['commercial_register'] ?? ''),
            'phone' => sanitize($input['phone']),
            'email' => sanitize($input['email']),
            'address' => sanitize($input['address'] ?? ''),
            'city' => sanitize($input['city'] ?? ''),
            'country' => sanitize($input['country'] ?? 'السعودية'),
            'postal_code' => sanitize($input['postal_code'] ?? ''),
            'logo' => $input['logo'] ?? null
        ];
        
        $companyId = $database->insert('companies', $companyData);
        
        // إنشاء دليل حسابات أساسي للشركة الجديدة
        $accounts = [
            ['1000', 'الأصول', 'asset', null, 1],
            ['1100', 'الأصول المتداولة', 'asset', 1, 2],
            ['1110', 'النقدية', 'asset', 2, 3],
            ['1120', 'العملاء', 'asset', 2, 3],
            ['1130', 'المخزون', 'asset', 2, 3],
            ['2000', 'الخصوم', 'liability', null, 1],
            ['2100', 'الخصوم المتداولة', 'liability', 6, 2],
            ['2110', 'الموردين', 'liability', 7, 3],
            ['3000', 'حقوق الملكية', 'equity', null, 1],
            ['4000', 'الإيرادات', 'revenue', null, 1],
            ['5000', 'المصروفات', 'expense', null, 1]
        ];
        
        $accountIds = [];
        foreach ($accounts as $index => $account) {
            $accountData = [
                'company_id' => $companyId,
                'account_code' => $account[0],
                'account_name' => $account[1],
                'account_type' => $account[2],
                'parent_id' => $account[3] ? $accountIds[$account[3] - 1] : null,
                'level' => $account[4]
            ];
            
            $accountIds[] = $database->insert('chart_of_accounts', $accountData);
        }
        
        // إنشاء وحدات قياس أساسية
        $units = [
            ['قطعة', 'قطعة'],
            ['كيلوجرام', 'كجم'],
            ['متر', 'م'],
            ['لتر', 'لتر'],
            ['صندوق', 'صندوق']
        ];
        
        foreach ($units as $unit) {
            $unitData = [
                'company_id' => $companyId,
                'name' => $unit[0],
                'symbol' => $unit[1]
            ];
            
            $database->insert('units', $unitData);
        }
        
        $database->commit();
        
        // تسجيل العملية
        logActivity('إضافة شركة', "تم إضافة شركة جديدة: {$input['name']}");
        
        // جلب الشركة المضافة
        $newCompany = $database->fetch("SELECT * FROM companies WHERE id = ?", [$companyId]);
        
        http_response_code(201);
        echo json_encode([
            'message' => 'تم إضافة الشركة بنجاح',
            'data' => $newCompany
        ], JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        $database->rollback();
        throw $e;
    }
}

/**
 * تحديث شركة
 */
function handlePut($database, $input) {
    $id = $_GET['id'] ?? null;
    
    if (!$id) {
        http_response_code(400);
        echo json_encode(['error' => 'معرف الشركة مطلوب'], JSON_UNESCAPED_UNICODE);
        return;
    }
    
    // التحقق من وجود الشركة
    $existing = $database->fetch("SELECT * FROM companies WHERE id = ?", [$id]);
    if (!$existing) {
        http_response_code(404);
        echo json_encode(['error' => 'الشركة غير موجودة'], JSON_UNESCAPED_UNICODE);
        return;
    }
    
    // التحقق من عدم تكرار الرقم الضريبي
    if (!empty($input['tax_number']) && $input['tax_number'] !== $existing['tax_number']) {
        $checkSql = "SELECT id FROM companies WHERE tax_number = ? AND id != ?";
        $duplicate = $database->fetch($checkSql, [$input['tax_number'], $id]);
        
        if ($duplicate) {
            http_response_code(400);
            echo json_encode(['error' => 'الرقم الضريبي موجود بالفعل'], JSON_UNESCAPED_UNICODE);
            return;
        }
    }
    
    // تحضير البيانات للتحديث
    $updateData = [];
    $allowedFields = ['name', 'tax_number', 'commercial_register', 'phone', 'email', 'address', 'city', 'country', 'postal_code', 'logo', 'is_active'];
    
    foreach ($allowedFields as $field) {
        if (isset($input[$field])) {
            if (in_array($field, ['name', 'tax_number', 'commercial_register', 'phone', 'email', 'address', 'city', 'country', 'postal_code'])) {
                $updateData[$field] = sanitize($input[$field]);
            } else {
                $updateData[$field] = $input[$field];
            }
        }
    }
    
    if (empty($updateData)) {
        http_response_code(400);
        echo json_encode(['error' => 'لا توجد بيانات للتحديث'], JSON_UNESCAPED_UNICODE);
        return;
    }
    
    // التحقق من صحة البريد الإلكتروني
    if (isset($updateData['email']) && !filter_var($updateData['email'], FILTER_VALIDATE_EMAIL)) {
        http_response_code(400);
        echo json_encode(['error' => 'البريد الإلكتروني غير صحيح'], JSON_UNESCAPED_UNICODE);
        return;
    }
    
    // التحقق من صحة الرقم الضريبي
    if (isset($updateData['tax_number']) && !isValidTaxNumber($updateData['tax_number'])) {
        http_response_code(400);
        echo json_encode(['error' => 'الرقم الضريبي غير صحيح'], JSON_UNESCAPED_UNICODE);
        return;
    }
    
    // تحديث الشركة
    $result = $database->update('companies', $updateData, 'id = ?', [$id]);
    
    if ($result) {
        // تسجيل العملية
        logActivity('تحديث شركة', "تم تحديث الشركة: {$existing['name']}");
        
        // جلب البيانات المحدثة
        $updatedCompany = $database->fetch("SELECT * FROM companies WHERE id = ?", [$id]);
        
        echo json_encode([
            'message' => 'تم تحديث الشركة بنجاح',
            'data' => $updatedCompany
        ], JSON_UNESCAPED_UNICODE);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'فشل في تحديث الشركة'], JSON_UNESCAPED_UNICODE);
    }
}

/**
 * حذف شركة
 */
function handleDelete($database) {
    $id = $_GET['id'] ?? null;
    
    if (!$id) {
        http_response_code(400);
        echo json_encode(['error' => 'معرف الشركة مطلوب'], JSON_UNESCAPED_UNICODE);
        return;
    }
    
    // التحقق من وجود الشركة
    $company = $database->fetch("SELECT * FROM companies WHERE id = ?", [$id]);
    if (!company) {
        http_response_code(404);
        echo json_encode(['error' => 'الشركة غير موجودة'], JSON_UNESCAPED_UNICODE);
        return;
    }
    
    // التحقق من وجود بيانات مرتبطة
    $relatedData = [
        'users' => $database->count('users', 'company_id = ?', [$id]),
        'customers' => $database->count('customers', 'company_id = ?', [$id]),
        'suppliers' => $database->count('suppliers', 'company_id = ?', [$id]),
        'products' => $database->count('products', 'company_id = ?', [$id]),
        'invoices' => $database->count('invoices', 'company_id = ?', [$id])
    ];
    
    $hasRelatedData = array_sum($relatedData) > 0;
    
    if ($hasRelatedData) {
        http_response_code(400);
        echo json_encode([
            'error' => 'لا يمكن حذف الشركة لوجود بيانات مرتبطة بها',
            'related_data' => $relatedData
        ], JSON_UNESCAPED_UNICODE);
        return;
    }
    
    // حذف الشركة
    $result = $database->delete('companies', 'id = ?', [$id]);
    
    if ($result) {
        // تسجيل العملية
        logActivity('حذف شركة', "تم حذف الشركة: {$company['name']}");
        
        echo json_encode(['message' => 'تم حذف الشركة بنجاح'], JSON_UNESCAPED_UNICODE);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'فشل في حذف الشركة'], JSON_UNESCAPED_UNICODE);
    }
}
?>
