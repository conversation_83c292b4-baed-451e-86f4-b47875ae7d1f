<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

session_start();
require_once '../config/database_simple.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(array('error' => 'غير مصرح بالوصول'));
    exit();
}

// بيانات تجريبية للشركات
$companies = array(
    array(
        'id' => 1,
        'name' => 'الشركة الرئيسية',
        'tax_number' => '123456789012345',
        'phone' => '+966501234567',
        'email' => '<EMAIL>',
        'address' => 'الرياض، المملكة العربية السعودية',
        'branches_count' => 3,
        'users_count' => 12,
        'is_active' => true
    ),
    array(
        'id' => 2,
        'name' => 'شركة التجارة المتقدمة',
        'tax_number' => '987654321098765',
        'phone' => '+966507654321',
        'email' => '<EMAIL>',
        'address' => 'جدة، المملكة العربية السعودية',
        'branches_count' => 2,
        'users_count' => 8,
        'is_active' => true
    ),
    array(
        'id' => 3,
        'name' => 'مؤسسة الأعمال الحديثة',
        'tax_number' => '456789123456789',
        'phone' => '+966509876543',
        'email' => '<EMAIL>',
        'address' => 'الدمام، المملكة العربية السعودية',
        'branches_count' => 1,
        'users_count' => 5,
        'is_active' => false
    )
);

try {
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGet();
            break;
            
        case 'POST':
            handlePost();
            break;
            
        case 'PUT':
            handlePut();
            break;
            
        case 'DELETE':
            handleDelete();
            break;
            
        default:
            http_response_code(405);
            echo json_encode(array('error' => 'طريقة غير مدعومة'));
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(array(
        'error' => 'خطأ في الخادم',
        'message' => $e->getMessage()
    ));
}

/**
 * جلب الشركات
 */
function handleGet() {
    global $companies;
    
    $id = isset($_GET['id']) ? $_GET['id'] : null;
    
    if ($id) {
        // جلب شركة واحدة
        foreach ($companies as $company) {
            if ($company['id'] == $id) {
                echo json_encode(array('success' => true, 'data' => $company));
                return;
            }
        }
        
        http_response_code(404);
        echo json_encode(array('error' => 'الشركة غير موجودة'));
    } else {
        // جلب جميع الشركات
        echo json_encode(array('success' => true, 'data' => $companies));
    }
}

/**
 * إضافة شركة جديدة
 */
function handlePost() {
    global $companies;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(array('error' => 'بيانات غير صحيحة'));
        return;
    }
    
    // التحقق من البيانات المطلوبة
    if (empty($input['name']) || empty($input['tax_number'])) {
        http_response_code(400);
        echo json_encode(array('error' => 'اسم الشركة والرقم الضريبي مطلوبان'));
        return;
    }
    
    // إنشاء شركة جديدة
    $new_company = array(
        'id' => count($companies) + 1,
        'name' => $input['name'],
        'tax_number' => $input['tax_number'],
        'phone' => isset($input['phone']) ? $input['phone'] : '',
        'email' => isset($input['email']) ? $input['email'] : '',
        'address' => isset($input['address']) ? $input['address'] : '',
        'branches_count' => 0,
        'users_count' => 0,
        'is_active' => true
    );
    
    echo json_encode(array(
        'success' => true,
        'message' => 'تم إضافة الشركة بنجاح',
        'data' => $new_company
    ));
}

/**
 * تحديث شركة
 */
function handlePut() {
    global $companies;
    
    $id = isset($_GET['id']) ? $_GET['id'] : null;
    
    if (!$id) {
        http_response_code(400);
        echo json_encode(array('error' => 'معرف الشركة مطلوب'));
        return;
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(array('error' => 'بيانات غير صحيحة'));
        return;
    }
    
    // البحث عن الشركة وتحديثها
    foreach ($companies as $key => $company) {
        if ($company['id'] == $id) {
            $companies[$key] = array_merge($company, $input);
            echo json_encode(array(
                'success' => true,
                'message' => 'تم تحديث الشركة بنجاح',
                'data' => $companies[$key]
            ));
            return;
        }
    }
    
    http_response_code(404);
    echo json_encode(array('error' => 'الشركة غير موجودة'));
}

/**
 * حذف شركة
 */
function handleDelete() {
    global $companies;
    
    $id = isset($_GET['id']) ? $_GET['id'] : null;
    
    if (!$id) {
        http_response_code(400);
        echo json_encode(array('error' => 'معرف الشركة مطلوب'));
        return;
    }
    
    // البحث عن الشركة وحذفها
    foreach ($companies as $key => $company) {
        if ($company['id'] == $id) {
            unset($companies[$key]);
            echo json_encode(array(
                'success' => true,
                'message' => 'تم حذف الشركة بنجاح'
            ));
            return;
        }
    }
    
    http_response_code(404);
    echo json_encode(array('error' => 'الشركة غير موجودة'));
}
?>
