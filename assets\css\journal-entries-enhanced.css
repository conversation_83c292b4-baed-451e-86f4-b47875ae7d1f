/**
 * تصميم محسن لصفحة القيود اليومية بأسلوب Odoo
 * Enhanced Journal Entries CSS - Odoo Style
 */

/* إعادة تعريف المتغيرات */
:root {
    --odoo-primary: #714B67;
    --odoo-secondary: #875A7B;
    --odoo-success: #00A09D;
    --odoo-danger: #D73502;
    --odoo-warning: #F0AD4E;
    --odoo-info: #00A7E1;
    --odoo-light: #F9F9F9;
    --odoo-dark: #2F3349;
    --odoo-border: #DEE2E6;
    --odoo-text: #495057;
    --odoo-text-muted: #6C757D;
    --border-radius: 4px;
    --box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* تخطيط الصفحة الرئيسي */
.o_main_content {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background: #f5f5f5;
}

.o_content {
    flex: 1;
    display: flex;
}

.o_action_manager {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: white;
    margin: 8px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

/* شريط التحكم العلوي */
.o_control_panel {
    background: white;
    border-bottom: 1px solid var(--odoo-border);
    padding: 12px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 56px;
    flex-shrink: 0;
}

.o_cp_left {
    display: flex;
    align-items: center;
    gap: 12px;
}

.o_cp_right {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* مسار التنقل */
.o_breadcrumb {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
    color: var(--odoo-text-muted);
}

.o_breadcrumb_item {
    color: var(--odoo-text-muted);
    text-decoration: none;
    transition: var(--transition);
}

.o_breadcrumb_item:hover {
    color: var(--odoo-primary);
}

.o_breadcrumb_item.active {
    color: var(--odoo-text);
    font-weight: 500;
}

/* شريط البحث والفلترة */
.o_search_panel {
    background: #f8f9fa;
    border-bottom: 1px solid var(--odoo-border);
    flex-shrink: 0;
}

.o_search_panel .input-group-text {
    background: white;
    border-color: #ced4da;
    color: var(--odoo-text-muted);
}

.o_search_panel .form-control,
.o_search_panel .form-select {
    border-color: #ced4da;
    font-size: 13px;
}

.o_search_panel .form-control:focus,
.o_search_panel .form-select:focus {
    border-color: var(--odoo-primary);
    box-shadow: 0 0 0 0.2rem rgba(113, 75, 103, 0.25);
}

/* عرض القائمة */
.o_list_view {
    flex: 1;
    overflow: auto;
}

.o_list_table {
    width: 100%;
    border-collapse: collapse;
    font-size: 13px;
    margin: 0;
}

.o_list_table thead {
    background: #f8f9fa;
    position: sticky;
    top: 0;
    z-index: 10;
}

.o_list_table th {
    padding: 8px 12px;
    font-weight: 600;
    color: var(--odoo-text);
    border-right: 1px solid #e9ecef;
    border-bottom: 1px solid #e9ecef;
    text-align: right;
    white-space: nowrap;
    position: relative;
    cursor: pointer;
    user-select: none;
    background: #f8f9fa;
    font-size: 12px;
    line-height: 1.4;
}

.o_list_table th:hover {
    background: #e9ecef;
}

.o_list_table th:last-child {
    border-right: none;
}

/* أيقونات الترتيب */
.o_column_sortable::after {
    content: '';
    position: absolute;
    left: 8px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-bottom: 4px solid #ccc;
    opacity: 0.5;
    transition: var(--transition);
}

.o_column_sortable:hover::after {
    opacity: 0.8;
}

.o_column_sortable.o_sort_up::after {
    border-bottom: 4px solid var(--odoo-primary);
    border-top: none;
    opacity: 1;
}

.o_column_sortable.o_sort_down::after {
    border-top: 4px solid var(--odoo-primary);
    border-bottom: none;
    opacity: 1;
}

/* صفوف الجدول */
.o_list_table tbody tr {
    border-bottom: 1px solid #f0f0f0;
    transition: var(--transition);
}

.o_list_table tbody tr:hover {
    background: #f8f9fa;
}

.o_list_table tbody tr.o_selected_row {
    background: #e3f2fd;
}

.o_list_table td {
    padding: 8px 12px;
    border-right: 1px solid #f0f0f0;
    vertical-align: middle;
    color: var(--odoo-text);
    font-size: 13px;
    line-height: 1.4;
}

.o_list_table td:last-child {
    border-right: none;
}

/* خانة الاختيار */
.o_list_record_selector {
    width: 40px;
    text-align: center;
    padding: 8px 4px;
}

.o_list_record_selector input[type="checkbox"] {
    margin: 0;
    cursor: pointer;
    transform: scale(1.1);
}

/* أعمدة خاصة */
.o_list_number {
    text-align: left;
    font-family: 'Courier New', monospace;
    font-weight: 500;
}

.o_list_monetary {
    text-align: left;
    font-family: 'Courier New', monospace;
    font-weight: 500;
    color: var(--odoo-dark);
}

.o_list_date {
    white-space: nowrap;
    font-family: 'Courier New', monospace;
}

/* شارات الحالة */
.o_status_badge {
    display: inline-flex;
    align-items: center;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    line-height: 1;
}

.o_status_badge.draft {
    background: #f8f9fa;
    color: #6c757d;
    border: 1px solid #dee2e6;
}

.o_status_badge.posted {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.o_status_badge.cancelled {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* أزرار الإجراءات */
.btn-group-sm .btn {
    padding: 4px 8px;
    font-size: 12px;
    line-height: 1.2;
}

.btn-outline-primary {
    border-color: var(--odoo-primary);
    color: var(--odoo-primary);
}

.btn-outline-primary:hover {
    background: var(--odoo-primary);
    border-color: var(--odoo-primary);
    color: white;
}

.btn-outline-success {
    border-color: var(--odoo-success);
    color: var(--odoo-success);
}

.btn-outline-success:hover {
    background: var(--odoo-success);
    border-color: var(--odoo-success);
    color: white;
}

.btn-outline-warning {
    border-color: var(--odoo-warning);
    color: #856404;
}

.btn-outline-warning:hover {
    background: var(--odoo-warning);
    border-color: var(--odoo-warning);
    color: #856404;
}

.btn-outline-secondary {
    border-color: #6c757d;
    color: #6c757d;
}

.btn-outline-secondary:hover {
    background: #6c757d;
    border-color: #6c757d;
    color: white;
}

/* عرض البطاقات */
.move-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    border-left: 4px solid transparent;
    overflow: hidden;
}

.move-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.move-card.draft {
    border-left-color: #6c757d;
}

.move-card.posted {
    border-left-color: var(--odoo-success);
}

.move-card.cancelled {
    border-left-color: var(--odoo-danger);
}

.move-card .card-header {
    background: #f8f9fa;
    border-bottom: 1px solid var(--odoo-border);
    padding: 12px 16px;
    font-size: 13px;
}

.move-card .card-body {
    padding: 16px;
    font-size: 13px;
}

.move-card .card-footer {
    background: #f8f9fa;
    border-top: 1px solid var(--odoo-border);
    padding: 12px 16px;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .o_control_panel {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }
    
    .o_cp_left,
    .o_cp_right {
        justify-content: center;
    }
    
    .o_search_panel .d-flex {
        flex-direction: column;
        gap: 8px;
    }
    
    .o_list_table {
        font-size: 12px;
    }
    
    .o_list_table th,
    .o_list_table td {
        padding: 6px 8px;
    }
}

/* تأثيرات التحميل */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--odoo-primary);
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* تحسينات إضافية */
.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.cursor-pointer {
    cursor: pointer;
}

.user-select-none {
    user-select: none;
}
