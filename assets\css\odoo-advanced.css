/**
 * ملف CSS متقدم لوحدة المحاسبة بأسلوب Odoo
 * Advanced CSS for Odoo-Style Accounting Module
 */

:root {
    --odoo-primary: #714B67;
    --odoo-secondary: #8B5A8C;
    --odoo-accent: #f8f9fa;
    --odoo-success: #28a745;
    --odoo-warning: #ffc107;
    --odoo-danger: #dc3545;
    --odoo-info: #17a2b8;
    --odoo-light: #f8f9fa;
    --odoo-dark: #343a40;
    --odoo-border: #dee2e6;
    --odoo-text: #495057;
    --odoo-text-muted: #6c757d;
    --border-radius: 8px;
    --box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    --transition: all 0.3s ease;
}

/* الخطوط والتصميم العام */
body {
    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    font-size: 13px;
    line-height: 1.5;
    color: var(--odoo-text);
    background: #f5f5f5;
}

/* الجداول المتقدمة */
.odoo-table-container {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.odoo-table-header {
    background: linear-gradient(135deg, var(--odoo-primary), var(--odoo-secondary));
    color: white;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.odoo-table-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
}

.odoo-table-controls {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.table-control-btn {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    cursor: pointer;
    transition: var(--transition);
}

.table-control-btn:hover {
    background: rgba(255,255,255,0.3);
    border-color: rgba(255,255,255,0.5);
}

.odoo-table {
    width: 100%;
    margin: 0;
    border-collapse: collapse;
    font-size: 12px;
}

.odoo-table th {
    background: #f8f9fa;
    border-bottom: 2px solid var(--odoo-border);
    padding: 0.75rem 0.5rem;
    text-align: right;
    font-weight: 600;
    color: var(--odoo-text);
    position: relative;
    cursor: pointer;
    user-select: none;
    white-space: nowrap;
    min-width: 80px;
    resize: horizontal;
    overflow: hidden;
}

.odoo-table th:hover {
    background: #e9ecef;
}

.odoo-table th.sortable::after {
    content: '⇅';
    position: absolute;
    left: 5px;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0.5;
    font-size: 0.8rem;
}

.odoo-table th.sort-asc::after {
    content: '↑';
    opacity: 1;
    color: var(--odoo-primary);
}

.odoo-table th.sort-desc::after {
    content: '↓';
    opacity: 1;
    color: var(--odoo-primary);
}

.odoo-table td {
    padding: 0.6rem 0.5rem;
    border-bottom: 1px solid #f0f0f0;
    vertical-align: middle;
    word-wrap: break-word;
}

.odoo-table tbody tr {
    transition: var(--transition);
}

.odoo-table tbody tr:hover {
    background: #f8f9fa;
}

.odoo-table tbody tr:nth-child(even) {
    background: #fafafa;
}

.odoo-table tbody tr:nth-child(even):hover {
    background: #f0f0f0;
}

/* أحجام الخطوط */
.font-size-xs { font-size: 10px !important; }
.font-size-sm { font-size: 11px !important; }
.font-size-md { font-size: 12px !important; }
.font-size-lg { font-size: 14px !important; }
.font-size-xl { font-size: 16px !important; }

/* عرض الأعمدة */
.col-width-xs { width: 60px !important; min-width: 60px !important; }
.col-width-sm { width: 100px !important; min-width: 100px !important; }
.col-width-md { width: 150px !important; min-width: 150px !important; }
.col-width-lg { width: 200px !important; min-width: 200px !important; }
.col-width-xl { width: 300px !important; min-width: 300px !important; }
.col-width-auto { width: auto !important; min-width: 80px !important; }

/* إخفاء الأعمدة */
.column-hidden {
    display: none !important;
}

/* أدوات التحكم في الجدول */
.table-toolbar {
    background: white;
    border-bottom: 1px solid var(--odoo-border);
    padding: 0.75rem 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.table-toolbar-left,
.table-toolbar-right {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.search-box {
    border: 1px solid var(--odoo-border);
    border-radius: 4px;
    padding: 0.4rem 0.6rem;
    font-size: 0.8rem;
    width: 200px;
    transition: var(--transition);
}

.search-box:focus {
    border-color: var(--odoo-primary);
    box-shadow: 0 0 0 2px rgba(113, 75, 103, 0.2);
    outline: none;
}

.filter-dropdown {
    position: relative;
    display: inline-block;
}

.filter-btn {
    background: white;
    border: 1px solid var(--odoo-border);
    padding: 0.4rem 0.8rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    gap: 0.3rem;
    transition: var(--transition);
}

.filter-btn:hover {
    background: var(--odoo-accent);
    border-color: var(--odoo-primary);
}

.filter-dropdown-content {
    display: none;
    position: absolute;
    background: white;
    min-width: 200px;
    box-shadow: var(--box-shadow);
    border-radius: var(--border-radius);
    z-index: 1000;
    top: 100%;
    right: 0;
    border: 1px solid var(--odoo-border);
    max-height: 300px;
    overflow-y: auto;
}

.filter-dropdown.active .filter-dropdown-content {
    display: block;
}

.filter-option {
    padding: 0.5rem 0.75rem;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-option:hover {
    background: var(--odoo-accent);
}

.filter-option:last-child {
    border-bottom: none;
}

.filter-option input[type="checkbox"] {
    margin: 0;
}

/* أدوات التحكم في الأعمدة */
.column-controls {
    background: white;
    border: 1px solid var(--odoo-border);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 1rem;
    box-shadow: var(--box-shadow);
}

.column-controls h6 {
    margin: 0 0 0.75rem 0;
    color: var(--odoo-primary);
    font-weight: 600;
}

.column-control-group {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.column-control-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.4rem;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    background: #fafafa;
}

.column-control-item label {
    flex: 1;
    margin: 0;
    font-size: 0.8rem;
    cursor: pointer;
}

.column-width-control {
    width: 80px;
    font-size: 0.75rem;
    padding: 0.2rem 0.4rem;
    border: 1px solid var(--odoo-border);
    border-radius: 3px;
}

/* أدوات التحكم في الخط */
.font-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.font-size-control {
    width: 70px;
    font-size: 0.75rem;
    padding: 0.2rem 0.4rem;
    border: 1px solid var(--odoo-border);
    border-radius: 3px;
}

/* أزرار الإجراءات */
.action-buttons {
    display: flex;
    gap: 0.3rem;
}

.btn-action {
    padding: 0.3rem 0.5rem;
    border: 1px solid var(--odoo-border);
    background: white;
    border-radius: 3px;
    cursor: pointer;
    font-size: 0.75rem;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.2rem;
}

.btn-action:hover {
    background: var(--odoo-accent);
    border-color: var(--odoo-primary);
}

.btn-action.btn-primary {
    background: var(--odoo-primary);
    color: white;
    border-color: var(--odoo-primary);
}

.btn-action.btn-primary:hover {
    background: var(--odoo-secondary);
}

.btn-action.btn-success {
    background: var(--odoo-success);
    color: white;
    border-color: var(--odoo-success);
}

.btn-action.btn-danger {
    background: var(--odoo-danger);
    color: white;
    border-color: var(--odoo-danger);
}

.btn-action.btn-warning {
    background: var(--odoo-warning);
    color: white;
    border-color: var(--odoo-warning);
}

.btn-action.btn-info {
    background: var(--odoo-info);
    color: white;
    border-color: var(--odoo-info);
}

/* تحديد الصفوف */
.row-selector {
    width: 16px;
    height: 16px;
    margin: 0;
    cursor: pointer;
}

.selected-row {
    background: rgba(113, 75, 103, 0.1) !important;
}

/* شريط الحالة */
.table-status-bar {
    background: white;
    border-top: 1px solid var(--odoo-border);
    padding: 0.5rem 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.75rem;
    color: var(--odoo-text-muted);
}

.status-info {
    display: flex;
    gap: 1rem;
}

.status-actions {
    display: flex;
    gap: 0.5rem;
}

/* التحميل والتحديث */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
}

.loading-spinner {
    width: 30px;
    height: 30px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid var(--odoo-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* التصدير والطباعة */
.export-controls {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.export-btn {
    background: var(--odoo-success);
    color: white;
    border: none;
    padding: 0.4rem 0.8rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.export-btn:hover {
    background: #218838;
}

.print-btn {
    background: var(--odoo-info);
    color: white;
    border: none;
    padding: 0.4rem 0.8rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.print-btn:hover {
    background: #138496;
}

/* أنماط الطباعة */
@media print {
    body {
        font-size: 10px;
        color: black;
        background: white;
    }
    
    .no-print {
        display: none !important;
    }
    
    .odoo-table-container {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .odoo-table {
        font-size: 9px;
    }
    
    .odoo-table th,
    .odoo-table td {
        border: 1px solid #000;
        padding: 0.3rem;
    }
    
    .odoo-table th {
        background: #f0f0f0 !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }
    
    .page-break {
        page-break-before: always;
    }
    
    .print-header {
        text-align: center;
        margin-bottom: 1rem;
        border-bottom: 2px solid #000;
        padding-bottom: 0.5rem;
    }
    
    .print-footer {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        text-align: center;
        font-size: 8px;
        border-top: 1px solid #000;
        padding-top: 0.25rem;
    }
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .table-toolbar {
        flex-direction: column;
        align-items: stretch;
    }
    
    .table-toolbar-left,
    .table-toolbar-right {
        justify-content: center;
    }
    
    .column-control-group {
        grid-template-columns: 1fr;
    }
    
    .odoo-table {
        font-size: 11px;
    }
    
    .search-box {
        width: 100%;
    }
}

/* تحسينات إضافية */
.highlight-row {
    background: rgba(255, 193, 7, 0.2) !important;
}

.error-row {
    background: rgba(220, 53, 69, 0.1) !important;
}

.success-row {
    background: rgba(40, 167, 69, 0.1) !important;
}

.info-row {
    background: rgba(23, 162, 184, 0.1) !important;
}

/* تأثيرات الحركة */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}
