/**
 * تصميم بأسلوب Odoo
 * نظام ERP المحاسبي
 */

/* الخطوط العربية */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

/* متغيرات Odoo المحدثة */
:root {
    --odoo-primary: #714B67;
    --odoo-secondary: #875A7B;
    --odoo-accent: #F0EEEE;
    --odoo-success: #00A09D;
    --odoo-danger: #D73502;
    --odoo-warning: #F0AD4E;
    --odoo-info: #00A7E1;
    --odoo-light: #F9F9F9;
    --odoo-dark: #2F3349;
    --odoo-border: #DEE2E6;
    --odoo-text: #495057;
    --odoo-text-muted: #6C757D;
    --font-family: 'Cairo', 'Lucida Grande', Helvetica, Verdana, Arial, sans-serif;
    --border-radius: 4px;
    --box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
    --box-shadow-hover: 0 4px 8px rgba(0,0,0,0.15);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --header-height: 56px;
    --sidebar-width: 220px;
}

/* الإعدادات العامة */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background-color: #f5f5f5;
    direction: rtl;
    text-align: right;
    color: var(--odoo-text);
    line-height: 1.5;
    font-size: 13px;
    margin: 0;
    padding: 0;
}

/* تخطيط Odoo الأساسي */
.o_main_content {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.o_content {
    flex: 1;
    display: flex;
    background: #f5f5f5;
}

.o_action_manager {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: white;
    margin: 8px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

/* شريط التحكم العلوي */
.o_control_panel {
    background: white;
    border-bottom: 1px solid var(--odoo-border);
    padding: 8px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 48px;
}

.o_cp_left {
    display: flex;
    align-items: center;
    gap: 8px;
}

.o_cp_right {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* مسار التنقل */
.o_breadcrumb {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 13px;
    color: var(--odoo-text-muted);
}

.o_breadcrumb_item {
    color: var(--odoo-text-muted);
    text-decoration: none;
}

.o_breadcrumb_item:hover {
    color: var(--odoo-primary);
}

.o_breadcrumb_item.active {
    color: var(--odoo-text);
    font-weight: 500;
}

/* شريط التنقل العلوي */
.navbar {
    background: linear-gradient(135deg, var(--odoo-primary) 0%, var(--odoo-secondary) 100%);
    border-bottom: 1px solid var(--odoo-border);
    box-shadow: var(--box-shadow);
}

.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: white !important;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
}

/* الشريط الجانبي */
.sidebar {
    background: white;
    border-left: 1px solid var(--odoo-border);
    box-shadow: var(--box-shadow);
    min-height: calc(100vh - 56px);
}

.sidebar .nav-link {
    color: var(--odoo-text);
    font-weight: 500;
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    margin: 0.125rem 0.5rem;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.sidebar .nav-link:hover {
    color: var(--odoo-primary);
    background-color: var(--odoo-accent);
    border-color: var(--odoo-border);
}

.sidebar .nav-link.active {
    color: white;
    background: linear-gradient(135deg, var(--odoo-primary) 0%, var(--odoo-secondary) 100%);
    font-weight: 600;
    box-shadow: var(--box-shadow);
}

/* البطاقات */
.card {
    border: 1px solid var(--odoo-border);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.card-header {
    background: var(--odoo-accent);
    border-bottom: 1px solid var(--odoo-border);
    font-weight: 600;
    color: var(--odoo-text);
}

/* أزرار Odoo المحسنة */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    padding: 6px 12px;
    font-size: 13px;
    line-height: 1.4;
    transition: var(--transition);
    border: 1px solid transparent;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    text-decoration: none;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(113, 75, 103, 0.25);
}

.btn-primary {
    background: var(--odoo-primary);
    border-color: var(--odoo-primary);
    color: white;
}

.btn-primary:hover {
    background: #5d3e56;
    border-color: #5d3e56;
    color: white;
}

.btn-secondary {
    background: #6c757d;
    border-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    border-color: #545b62;
    color: white;
}

.btn-outline-primary {
    background: transparent;
    border-color: var(--odoo-primary);
    color: var(--odoo-primary);
}

.btn-outline-primary:hover {
    background: var(--odoo-primary);
    border-color: var(--odoo-primary);
    color: white;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
}

.btn-lg {
    padding: 8px 16px;
    font-size: 14px;
}

/* مجموعات الأزرار */
.btn-group {
    display: inline-flex;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.btn-group .btn {
    border-radius: 0;
    border-left-width: 0;
}

.btn-group .btn:first-child {
    border-radius: var(--border-radius) 0 0 var(--border-radius);
    border-left-width: 1px;
}

.btn-group .btn:last-child {
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

.btn-group .btn:only-child {
    border-radius: var(--border-radius);
    border-left-width: 1px;
}

.btn-primary:hover {
    background: var(--odoo-secondary);
    border-color: var(--odoo-secondary);
    transform: translateY(-1px);
    box-shadow: var(--box-shadow);
}

.btn-success {
    background-color: var(--odoo-success);
    border-color: var(--odoo-success);
}

.btn-danger {
    background-color: var(--odoo-danger);
    border-color: var(--odoo-danger);
}

.btn-warning {
    background-color: var(--odoo-warning);
    border-color: var(--odoo-warning);
    color: var(--odoo-dark);
}

.btn-info {
    background-color: var(--odoo-info);
    border-color: var(--odoo-info);
}

/* النماذج */
.form-control {
    border: 1px solid var(--odoo-border);
    border-radius: var(--border-radius);
    padding: 0.5rem 0.75rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--odoo-primary);
    box-shadow: 0 0 0 0.2rem rgba(113, 75, 103, 0.25);
}

.form-label {
    font-weight: 500;
    color: var(--odoo-text);
    margin-bottom: 0.5rem;
}

/* جداول Odoo المحسنة */
.o_list_view {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.o_list_table, .table {
    width: 100%;
    border-collapse: collapse;
    font-size: 13px;
    background: white;
}

.o_list_table thead, .table thead {
    background: #f8f9fa;
    border-bottom: 1px solid var(--odoo-border);
}

.o_list_table th, .table th {
    padding: 8px 12px;
    font-weight: 600;
    color: var(--odoo-text);
    border-right: 1px solid #f0f0f0;
    text-align: right;
    white-space: nowrap;
    position: relative;
    cursor: pointer;
    user-select: none;
    background: #f8f9fa;
    font-size: 12px;
}

.o_list_table th:hover, .table th:hover {
    background: #e9ecef;
}

.o_list_table th.o_column_sortable::after {
    content: '';
    position: absolute;
    left: 8px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-bottom: 4px solid #ccc;
    opacity: 0.5;
}

.o_list_table tbody tr, .table tbody tr {
    border-bottom: 1px solid #f0f0f0;
    transition: var(--transition);
}

.o_list_table tbody tr:hover, .table tbody tr:hover {
    background: #f8f9fa;
}

.o_list_table tbody tr.o_selected_row {
    background: #e3f2fd;
}

.o_list_table td, .table td {
    padding: 8px 12px;
    border-right: 1px solid #f0f0f0;
    vertical-align: middle;
    color: var(--odoo-text);
    font-size: 13px;
}

.o_list_table td:last-child, .table td:last-child {
    border-right: none;
}

/* خانة الاختيار */
.o_list_record_selector {
    width: 40px;
    text-align: center;
}

.o_list_record_selector input[type="checkbox"] {
    margin: 0;
    cursor: pointer;
}

/* أعمدة خاصة */
.o_list_table .o_list_number, .table .o_list_number {
    text-align: left;
    font-family: monospace;
}

.o_list_table .o_list_monetary, .table .o_list_monetary {
    text-align: left;
    font-family: monospace;
    font-weight: 500;
}

/* شارات الحالة */
.o_status_badge, .status-badge {
    display: inline-flex;
    align-items: center;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.o_status_badge.draft, .status-badge.draft {
    background: #f8f9fa;
    color: #6c757d;
    border: 1px solid #dee2e6;
}

.o_status_badge.posted, .status-badge.posted {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.o_status_badge.cancelled, .status-badge.cancelled {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* التنبيهات */
.alert {
    border-radius: var(--border-radius);
    border: 1px solid transparent;
    padding: 1rem;
    margin-bottom: 1rem;
}

.alert-success {
    background-color: rgba(0, 160, 157, 0.1);
    border-color: var(--odoo-success);
    color: var(--odoo-success);
}

.alert-danger {
    background-color: rgba(215, 53, 2, 0.1);
    border-color: var(--odoo-danger);
    color: var(--odoo-danger);
}

.alert-warning {
    background-color: rgba(240, 173, 78, 0.1);
    border-color: var(--odoo-warning);
    color: #856404;
}

.alert-info {
    background-color: rgba(0, 167, 225, 0.1);
    border-color: var(--odoo-info);
    color: var(--odoo-info);
}

/* الإحصائيات */
.stat-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    text-align: center;
    box-shadow: var(--box-shadow);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--odoo-text-muted);
    font-weight: 500;
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.5px;
}

/* الشارات */
.badge {
    border-radius: var(--border-radius);
    font-weight: 500;
    padding: 0.375rem 0.75rem;
}

.badge.bg-success {
    background-color: var(--odoo-success) !important;
}

.badge.bg-danger {
    background-color: var(--odoo-danger) !important;
}

.badge.bg-warning {
    background-color: var(--odoo-warning) !important;
    color: var(--odoo-dark) !important;
}

.badge.bg-info {
    background-color: var(--odoo-info) !important;
}

/* القوائم المنسدلة */
.dropdown-menu {
    border: 1px solid var(--odoo-border);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 0.5rem 0;
}

.dropdown-item {
    padding: 0.5rem 1rem;
    color: var(--odoo-text);
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background-color: var(--odoo-accent);
    color: var(--odoo-primary);
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .sidebar {
        position: relative;
        width: 100%;
        min-height: auto;
    }
    
    .stat-card {
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        border-radius: var(--border-radius);
    }
}

/* تحسينات إضافية */
.text-primary {
    color: var(--odoo-primary) !important;
}

.text-success {
    color: var(--odoo-success) !important;
}

.text-danger {
    color: var(--odoo-danger) !important;
}

.text-warning {
    color: var(--odoo-warning) !important;
}

.text-info {
    color: var(--odoo-info) !important;
}

.bg-primary {
    background: linear-gradient(135deg, var(--odoo-primary) 0%, var(--odoo-secondary) 100%) !important;
}

/* تأثيرات التحميل */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border {
    color: var(--odoo-primary);
}

/* تحسينات الطباعة */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .no-print {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}
