/**
 * تصميم بأسلوب Odoo
 * نظام ERP المحاسبي
 */

/* الخطوط العربية */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

/* متغيرات Odoo */
:root {
    --odoo-primary: #714B67;
    --odoo-secondary: #875A7B;
    --odoo-accent: #F0EEEE;
    --odoo-success: #00A09D;
    --odoo-danger: #D73502;
    --odoo-warning: #F0AD4E;
    --odoo-info: #00A7E1;
    --odoo-light: #F9F9F9;
    --odoo-dark: #2F3349;
    --odoo-border: #DEE2E6;
    --odoo-text: #495057;
    --odoo-text-muted: #6C757D;
    --font-family: 'Cairo', 'Lucida Grande', Helvetica, Verdana, Arial, sans-serif;
    --border-radius: 3px;
    --box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
}

/* الإعدادات العامة */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background-color: var(--odoo-light);
    direction: rtl;
    text-align: right;
    color: var(--odoo-text);
    line-height: 1.6;
}

/* شريط التنقل العلوي */
.navbar {
    background: linear-gradient(135deg, var(--odoo-primary) 0%, var(--odoo-secondary) 100%);
    border-bottom: 1px solid var(--odoo-border);
    box-shadow: var(--box-shadow);
}

.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: white !important;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
}

/* الشريط الجانبي */
.sidebar {
    background: white;
    border-left: 1px solid var(--odoo-border);
    box-shadow: var(--box-shadow);
    min-height: calc(100vh - 56px);
}

.sidebar .nav-link {
    color: var(--odoo-text);
    font-weight: 500;
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    margin: 0.125rem 0.5rem;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.sidebar .nav-link:hover {
    color: var(--odoo-primary);
    background-color: var(--odoo-accent);
    border-color: var(--odoo-border);
}

.sidebar .nav-link.active {
    color: white;
    background: linear-gradient(135deg, var(--odoo-primary) 0%, var(--odoo-secondary) 100%);
    font-weight: 600;
    box-shadow: var(--box-shadow);
}

/* البطاقات */
.card {
    border: 1px solid var(--odoo-border);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.card-header {
    background: var(--odoo-accent);
    border-bottom: 1px solid var(--odoo-border);
    font-weight: 600;
    color: var(--odoo-text);
}

/* الأزرار */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.btn-primary {
    background: linear-gradient(135deg, var(--odoo-primary) 0%, var(--odoo-secondary) 100%);
    border-color: var(--odoo-primary);
    color: white;
}

.btn-primary:hover {
    background: var(--odoo-secondary);
    border-color: var(--odoo-secondary);
    transform: translateY(-1px);
    box-shadow: var(--box-shadow);
}

.btn-success {
    background-color: var(--odoo-success);
    border-color: var(--odoo-success);
}

.btn-danger {
    background-color: var(--odoo-danger);
    border-color: var(--odoo-danger);
}

.btn-warning {
    background-color: var(--odoo-warning);
    border-color: var(--odoo-warning);
    color: var(--odoo-dark);
}

.btn-info {
    background-color: var(--odoo-info);
    border-color: var(--odoo-info);
}

/* النماذج */
.form-control {
    border: 1px solid var(--odoo-border);
    border-radius: var(--border-radius);
    padding: 0.5rem 0.75rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--odoo-primary);
    box-shadow: 0 0 0 0.2rem rgba(113, 75, 103, 0.25);
}

.form-label {
    font-weight: 500;
    color: var(--odoo-text);
    margin-bottom: 0.5rem;
}

/* الجداول */
.table {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table th {
    background: var(--odoo-accent);
    border-bottom: 1px solid var(--odoo-border);
    font-weight: 600;
    color: var(--odoo-text);
    padding: 1rem 0.75rem;
}

.table td {
    padding: 0.75rem;
    border-bottom: 1px solid var(--odoo-border);
    vertical-align: middle;
}

.table tbody tr:hover {
    background-color: var(--odoo-accent);
}

/* التنبيهات */
.alert {
    border-radius: var(--border-radius);
    border: 1px solid transparent;
    padding: 1rem;
    margin-bottom: 1rem;
}

.alert-success {
    background-color: rgba(0, 160, 157, 0.1);
    border-color: var(--odoo-success);
    color: var(--odoo-success);
}

.alert-danger {
    background-color: rgba(215, 53, 2, 0.1);
    border-color: var(--odoo-danger);
    color: var(--odoo-danger);
}

.alert-warning {
    background-color: rgba(240, 173, 78, 0.1);
    border-color: var(--odoo-warning);
    color: #856404;
}

.alert-info {
    background-color: rgba(0, 167, 225, 0.1);
    border-color: var(--odoo-info);
    color: var(--odoo-info);
}

/* الإحصائيات */
.stat-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    text-align: center;
    box-shadow: var(--box-shadow);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--odoo-text-muted);
    font-weight: 500;
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.5px;
}

/* الشارات */
.badge {
    border-radius: var(--border-radius);
    font-weight: 500;
    padding: 0.375rem 0.75rem;
}

.badge.bg-success {
    background-color: var(--odoo-success) !important;
}

.badge.bg-danger {
    background-color: var(--odoo-danger) !important;
}

.badge.bg-warning {
    background-color: var(--odoo-warning) !important;
    color: var(--odoo-dark) !important;
}

.badge.bg-info {
    background-color: var(--odoo-info) !important;
}

/* القوائم المنسدلة */
.dropdown-menu {
    border: 1px solid var(--odoo-border);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 0.5rem 0;
}

.dropdown-item {
    padding: 0.5rem 1rem;
    color: var(--odoo-text);
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background-color: var(--odoo-accent);
    color: var(--odoo-primary);
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .sidebar {
        position: relative;
        width: 100%;
        min-height: auto;
    }
    
    .stat-card {
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        border-radius: var(--border-radius);
    }
}

/* تحسينات إضافية */
.text-primary {
    color: var(--odoo-primary) !important;
}

.text-success {
    color: var(--odoo-success) !important;
}

.text-danger {
    color: var(--odoo-danger) !important;
}

.text-warning {
    color: var(--odoo-warning) !important;
}

.text-info {
    color: var(--odoo-info) !important;
}

.bg-primary {
    background: linear-gradient(135deg, var(--odoo-primary) 0%, var(--odoo-secondary) 100%) !important;
}

/* تأثيرات التحميل */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border {
    color: var(--odoo-primary);
}

/* تحسينات الطباعة */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .no-print {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}
