/**
 * مدير الأزرار المتقدم لوحدة المحاسبة بأسلوب Odoo
 * Advanced Buttons Manager for Odoo-Style Accounting Module
 */

class OdooButtonsManager {
    constructor(options = {}) {
        this.options = {
            enablePrint: true,
            enableExport: true,
            enableAdd: true,
            enableEdit: true,
            enableDelete: true,
            enableRefresh: true,
            ...options
        };
        
        this.init();
    }
    
    init() {
        this.addGlobalStyles();
        this.setupEventListeners();
    }
    
    /**
     * إضافة أزرار شاملة لأي جدول
     */
    addTableButtons(tableId, options = {}) {
        const table = document.getElementById(tableId);
        if (!table) return;
        
        const container = table.closest('.odoo-table-container') || table.parentElement;
        
        // إنشاء شريط الأدوات إذا لم يكن موجوداً
        let toolbar = container.querySelector('.table-toolbar');
        if (!toolbar) {
            toolbar = document.createElement('div');
            toolbar.className = 'table-toolbar';
            container.insertBefore(toolbar, table);
        }
        
        // إضافة الأزرار
        this.addToolbarButtons(toolbar, tableId, options);
    }
    
    /**
     * إضافة أزرار شريط الأدوات
     */
    addToolbarButtons(toolbar, tableId, options) {
        const buttonsConfig = {
            add: { icon: 'fas fa-plus', text: 'إضافة', class: 'btn-success', action: 'add' },
            edit: { icon: 'fas fa-edit', text: 'تعديل', class: 'btn-primary', action: 'edit' },
            delete: { icon: 'fas fa-trash', text: 'حذف', class: 'btn-danger', action: 'delete' },
            print: { icon: 'fas fa-print', text: 'طباعة', class: 'btn-info', action: 'print' },
            export: { icon: 'fas fa-download', text: 'تصدير', class: 'btn-success', action: 'export' },
            refresh: { icon: 'fas fa-sync-alt', text: 'تحديث', class: 'btn-secondary', action: 'refresh' }
        };
        
        // إنشاء مجموعة الأزرار
        const buttonGroup = document.createElement('div');
        buttonGroup.className = 'btn-group me-2';
        buttonGroup.setAttribute('role', 'group');
        
        // إضافة الأزرار المطلوبة
        Object.keys(buttonsConfig).forEach(key => {
            if (this.options[`enable${key.charAt(0).toUpperCase() + key.slice(1)}`] !== false) {
                const config = buttonsConfig[key];
                const button = this.createButton(config, tableId, options);
                buttonGroup.appendChild(button);
            }
        });
        
        // إضافة مجموعة الأزرار إلى الشريط
        let leftSection = toolbar.querySelector('.table-toolbar-left');
        if (!leftSection) {
            leftSection = document.createElement('div');
            leftSection.className = 'table-toolbar-left';
            toolbar.appendChild(leftSection);
        }
        
        leftSection.appendChild(buttonGroup);
        
        // إضافة أزرار التصدير المتقدمة
        if (this.options.enableExport) {
            this.addExportDropdown(leftSection, tableId, options);
        }
    }
    
    /**
     * إنشاء زر
     */
    createButton(config, tableId, options) {
        const button = document.createElement('button');
        button.className = `btn ${config.class} btn-sm odoo-btn`;
        button.innerHTML = `<i class="${config.icon} me-1"></i>${config.text}`;
        button.setAttribute('data-action', config.action);
        button.setAttribute('data-table', tableId);
        
        // إضافة معالج الحدث
        button.addEventListener('click', (e) => {
            this.handleButtonClick(e, config.action, tableId, options);
        });
        
        return button;
    }
    
    /**
     * إضافة قائمة التصدير المنسدلة
     */
    addExportDropdown(container, tableId, options) {
        const dropdown = document.createElement('div');
        dropdown.className = 'dropdown';
        dropdown.innerHTML = `
            <button class="btn btn-outline-success btn-sm dropdown-toggle" type="button" 
                    data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fas fa-file-export me-1"></i>تصدير متقدم
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#" data-action="export-excel" data-table="${tableId}">
                    <i class="fas fa-file-excel text-success me-2"></i>Excel (.xlsx)
                </a></li>
                <li><a class="dropdown-item" href="#" data-action="export-xls" data-table="${tableId}">
                    <i class="fas fa-file-excel text-success me-2"></i>Excel (.xls)
                </a></li>
                <li><a class="dropdown-item" href="#" data-action="export-pdf" data-table="${tableId}">
                    <i class="fas fa-file-pdf text-danger me-2"></i>PDF
                </a></li>
                <li><a class="dropdown-item" href="#" data-action="export-csv" data-table="${tableId}">
                    <i class="fas fa-file-csv text-info me-2"></i>CSV
                </a></li>
                <li><a class="dropdown-item" href="#" data-action="export-word" data-table="${tableId}">
                    <i class="fas fa-file-word text-primary me-2"></i>Word
                </a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="#" data-action="print-advanced" data-table="${tableId}">
                    <i class="fas fa-print text-secondary me-2"></i>طباعة متقدمة
                </a></li>
            </ul>
        `;
        
        container.appendChild(dropdown);
        
        // إضافة معالجات الأحداث لعناصر القائمة
        dropdown.querySelectorAll('.dropdown-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const action = e.currentTarget.getAttribute('data-action');
                const table = e.currentTarget.getAttribute('data-table');
                this.handleButtonClick(e, action, table, options);
            });
        });
    }
    
    /**
     * معالجة نقرات الأزرار
     */
    handleButtonClick(event, action, tableId, options) {
        event.preventDefault();
        
        switch (action) {
            case 'add':
                this.handleAdd(tableId, options);
                break;
            case 'edit':
                this.handleEdit(tableId, options);
                break;
            case 'delete':
                this.handleDelete(tableId, options);
                break;
            case 'print':
                this.handlePrint(tableId, options);
                break;
            case 'export':
                this.handleExport(tableId, options);
                break;
            case 'export-excel':
                this.handleExportExcel(tableId, options);
                break;
            case 'export-xls':
                this.handleExportXLS(tableId, options);
                break;
            case 'export-pdf':
                this.handleExportPDF(tableId, options);
                break;
            case 'export-csv':
                this.handleExportCSV(tableId, options);
                break;
            case 'export-word':
                this.handleExportWord(tableId, options);
                break;
            case 'print-advanced':
                this.handlePrintAdvanced(tableId, options);
                break;
            case 'refresh':
                this.handleRefresh(tableId, options);
                break;
        }
    }
    
    /**
     * معالجة إضافة عنصر جديد
     */
    handleAdd(tableId, options) {
        const modalId = `addModal_${tableId}`;
        let modal = document.getElementById(modalId);
        
        if (!modal) {
            modal = this.createAddModal(tableId, options);
            document.body.appendChild(modal);
        }
        
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();
    }
    
    /**
     * إنشاء نافذة الإضافة
     */
    createAddModal(tableId, options) {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = `addModal_${tableId}`;
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-success text-white">
                        <h5 class="modal-title">
                            <i class="fas fa-plus me-2"></i>إضافة عنصر جديد
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="addForm_${tableId}">
                            ${this.generateFormFields(tableId, options)}
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-1"></i>إلغاء
                        </button>
                        <button type="button" class="btn btn-success" onclick="odooButtons.saveNew('${tableId}')">
                            <i class="fas fa-save me-1"></i>حفظ
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        return modal;
    }
    
    /**
     * إنشاء حقول النموذج
     */
    generateFormFields(tableId, options) {
        // حقول افتراضية حسب نوع الجدول
        const fieldTemplates = {
            'accounts-table': `
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">كود الحساب</label>
                            <input type="text" class="form-control" name="code" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">اسم الحساب</label>
                            <input type="text" class="form-control" name="name" required>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">نوع الحساب</label>
                            <select class="form-select" name="account_type" required>
                                <option value="">اختر النوع</option>
                                <option value="asset">أصول</option>
                                <option value="liability">خصوم</option>
                                <option value="equity">حقوق ملكية</option>
                                <option value="income">إيرادات</option>
                                <option value="expense">مصروفات</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">الحساب الأب</label>
                            <select class="form-select" name="parent_id">
                                <option value="">بدون حساب أب</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="reconcile" id="reconcile">
                        <label class="form-check-label" for="reconcile">
                            قابل للتسوية
                        </label>
                    </div>
                </div>
            `,
            'journal-entries-table': `
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">اليومية</label>
                            <select class="form-select" name="journal_id" required>
                                <option value="">اختر اليومية</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">التاريخ</label>
                            <input type="date" class="form-control" name="date" value="${new Date().toISOString().split('T')[0]}" required>
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label">المرجع</label>
                    <input type="text" class="form-control" name="ref">
                </div>
                <div class="mb-3">
                    <label class="form-label">البيان</label>
                    <textarea class="form-control" name="narration" rows="3"></textarea>
                </div>
            `,
            'partners-table': `
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">اسم الشريك</label>
                            <input type="text" class="form-control" name="name" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" name="email">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">الهاتف</label>
                            <input type="tel" class="form-control" name="phone">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">الجوال</label>
                            <input type="tel" class="form-control" name="mobile">
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label">العنوان</label>
                    <textarea class="form-control" name="street" rows="2"></textarea>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="is_customer" id="is_customer">
                            <label class="form-check-label" for="is_customer">عميل</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="is_supplier" id="is_supplier">
                            <label class="form-check-label" for="is_supplier">مورد</label>
                        </div>
                    </div>
                </div>
            `
        };
        
        return fieldTemplates[tableId] || `
            <div class="mb-3">
                <label class="form-label">الاسم</label>
                <input type="text" class="form-control" name="name" required>
            </div>
            <div class="mb-3">
                <label class="form-label">الوصف</label>
                <textarea class="form-control" name="description" rows="3"></textarea>
            </div>
        `;
    }
    
    /**
     * حفظ عنصر جديد
     */
    saveNew(tableId) {
        const form = document.getElementById(`addForm_${tableId}`);
        const formData = new FormData(form);
        
        // تحويل FormData إلى كائن
        const data = {};
        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }
        
        // إرسال البيانات
        this.submitData(tableId, data, 'create')
            .then(response => {
                this.showMessage('تم الحفظ بنجاح', 'success');
                const modal = bootstrap.Modal.getInstance(document.getElementById(`addModal_${tableId}`));
                modal.hide();
                this.handleRefresh(tableId);
            })
            .catch(error => {
                this.showMessage('خطأ في الحفظ: ' + error.message, 'error');
            });
    }
    
    /**
     * معالجة التصدير إلى Excel
     */
    handleExportExcel(tableId, options) {
        this.showMessage('جاري تصدير البيانات إلى Excel...', 'info');
        
        const table = document.getElementById(tableId);
        const data = this.extractTableData(table);
        
        // استخدام مكتبة SheetJS لتصدير Excel
        this.exportToExcelFile(data, `${tableId}_export.xlsx`);
    }
    
    /**
     * معالجة التصدير إلى XLS
     */
    handleExportXLS(tableId, options) {
        this.showMessage('جاري تصدير البيانات إلى XLS...', 'info');
        
        const table = document.getElementById(tableId);
        const data = this.extractTableData(table);
        
        // تصدير بصيغة XLS القديمة
        this.exportToExcelFile(data, `${tableId}_export.xls`, 'xls');
    }
    
    /**
     * معالجة التصدير إلى PDF
     */
    handleExportPDF(tableId, options) {
        this.showMessage('جاري تصدير البيانات إلى PDF...', 'info');
        
        const table = document.getElementById(tableId);
        const data = this.extractTableData(table);
        
        // استخدام jsPDF لتصدير PDF
        this.exportToPDFFile(data, `${tableId}_export.pdf`);
    }
    
    /**
     * معالجة التصدير إلى CSV
     */
    handleExportCSV(tableId, options) {
        const table = document.getElementById(tableId);
        const data = this.extractTableData(table);
        
        let csvContent = '';
        
        // إضافة العناوين
        csvContent += data.headers.join(',') + '\n';
        
        // إضافة البيانات
        data.rows.forEach(row => {
            csvContent += row.map(cell => `"${cell}"`).join(',') + '\n';
        });
        
        // تحميل الملف
        this.downloadFile(csvContent, `${tableId}_export.csv`, 'text/csv');
        this.showMessage('تم تصدير البيانات بنجاح', 'success');
    }
    
    /**
     * استخراج بيانات الجدول
     */
    extractTableData(table) {
        const headers = Array.from(table.querySelectorAll('thead th')).map(th => 
            th.textContent.trim()
        );
        
        const rows = Array.from(table.querySelectorAll('tbody tr')).map(row => 
            Array.from(row.querySelectorAll('td')).map(td => 
                td.textContent.trim()
            )
        );
        
        return { headers, rows };
    }
    
    /**
     * تصدير إلى ملف Excel
     */
    exportToExcelFile(data, filename, format = 'xlsx') {
        // هذه دالة مبسطة - في التطبيق الحقيقي نحتاج مكتبة SheetJS
        const csvContent = this.convertToCSV(data);
        this.downloadFile(csvContent, filename.replace('.xlsx', '.csv').replace('.xls', '.csv'), 'text/csv');
        this.showMessage('تم تصدير البيانات بنجاح (CSV)', 'success');
    }
    
    /**
     * تصدير إلى ملف PDF
     */
    exportToPDFFile(data, filename) {
        // هذه دالة مبسطة - في التطبيق الحقيقي نحتاج مكتبة jsPDF
        const htmlContent = this.convertToHTML(data);
        const printWindow = window.open('', '_blank');
        printWindow.document.write(htmlContent);
        printWindow.document.close();
        printWindow.print();
        this.showMessage('تم فتح نافذة الطباعة', 'success');
    }
    
    /**
     * تحويل البيانات إلى CSV
     */
    convertToCSV(data) {
        let csv = data.headers.join(',') + '\n';
        data.rows.forEach(row => {
            csv += row.map(cell => `"${cell}"`).join(',') + '\n';
        });
        return csv;
    }
    
    /**
     * تحويل البيانات إلى HTML
     */
    convertToHTML(data) {
        let html = `
            <!DOCTYPE html>
            <html dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>تقرير البيانات</title>
                <style>
                    body { font-family: Arial, sans-serif; }
                    table { width: 100%; border-collapse: collapse; }
                    th, td { border: 1px solid #000; padding: 8px; text-align: right; }
                    th { background-color: #f0f0f0; }
                </style>
            </head>
            <body>
                <h2>تقرير البيانات</h2>
                <table>
                    <thead><tr>
        `;
        
        data.headers.forEach(header => {
            html += `<th>${header}</th>`;
        });
        
        html += '</tr></thead><tbody>';
        
        data.rows.forEach(row => {
            html += '<tr>';
            row.forEach(cell => {
                html += `<td>${cell}</td>`;
            });
            html += '</tr>';
        });
        
        html += '</tbody></table></body></html>';
        return html;
    }
    
    /**
     * تحميل ملف
     */
    downloadFile(content, filename, mimeType) {
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.click();
        URL.revokeObjectURL(url);
    }
    
    /**
     * معالجة الطباعة
     */
    handlePrint(tableId, options) {
        window.print();
    }
    
    /**
     * معالجة التحديث
     */
    handleRefresh(tableId, options) {
        this.showMessage('جاري تحديث البيانات...', 'info');
        setTimeout(() => {
            location.reload();
        }, 1000);
    }
    
    /**
     * إرسال البيانات
     */
    async submitData(tableId, data, action) {
        // محاكاة إرسال البيانات
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                if (Math.random() > 0.1) { // 90% نجاح
                    resolve({ success: true, id: Date.now() });
                } else {
                    reject(new Error('خطأ في الشبكة'));
                }
            }, 1000);
        });
    }
    
    /**
     * عرض رسالة
     */
    showMessage(message, type = 'info') {
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alert.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alert);
        
        setTimeout(() => {
            alert.remove();
        }, 4000);
    }
    
    /**
     * إضافة الأنماط العامة
     */
    addGlobalStyles() {
        if (document.getElementById('odoo-buttons-styles')) return;
        
        const style = document.createElement('style');
        style.id = 'odoo-buttons-styles';
        style.textContent = `
            .odoo-btn {
                transition: all 0.3s ease;
                border-radius: 6px;
                font-weight: 500;
            }
            
            .odoo-btn:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 8px rgba(0,0,0,0.15);
            }
            
            .table-toolbar {
                background: white;
                padding: 1rem;
                border-radius: 8px 8px 0 0;
                border-bottom: 1px solid #dee2e6;
                display: flex;
                justify-content: space-between;
                align-items: center;
                flex-wrap: wrap;
                gap: 0.5rem;
            }
            
            .table-toolbar-left {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                flex-wrap: wrap;
            }
            
            .table-toolbar-right {
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }
        `;
        
        document.head.appendChild(style);
    }
    
    /**
     * إعداد معالجات الأحداث العامة
     */
    setupEventListeners() {
        // معالج عام للأزرار
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-action]') || e.target.closest('[data-action]')) {
                const button = e.target.matches('[data-action]') ? e.target : e.target.closest('[data-action]');
                const action = button.getAttribute('data-action');
                const table = button.getAttribute('data-table');
                
                if (action && table) {
                    this.handleButtonClick(e, action, table, {});
                }
            }
        });
    }
}

// إنشاء مثيل عام
window.odooButtons = new OdooButtonsManager();

// تهيئة تلقائية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // البحث عن الجداول وإضافة الأزرار تلقائياً
    const tables = document.querySelectorAll('table[id]');
    tables.forEach(table => {
        if (table.id && !table.closest('.no-auto-buttons')) {
            odooButtons.addTableButtons(table.id);
        }
    });
});
