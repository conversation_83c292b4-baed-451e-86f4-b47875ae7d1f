/**
 * مدير التصدير المتقدم لوحدة المحاسبة بأسلوب Odoo
 * Advanced Export Manager for Odoo-Style Accounting Module
 */

class OdooExportManager {
    constructor(options = {}) {
        this.options = {
            companyName: 'شركة النظام المتقدم',
            companyLogo: '',
            dateFormat: 'YYYY-MM-DD',
            currency: 'SAR',
            locale: 'ar-SA',
            ...options
        };
        
        this.loadExternalLibraries();
    }
    
    /**
     * تحميل المكتبات الخارجية
     */
    loadExternalLibraries() {
        // تحميل SheetJS للتعامل مع Excel
        if (!window.XLSX) {
            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js';
            document.head.appendChild(script);
        }
        
        // تحميل jsPDF للتعامل مع PDF
        if (!window.jsPDF) {
            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
            document.head.appendChild(script);
        }
        
        // تحميل html2canvas للتقاط الجداول
        if (!window.html2canvas) {
            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js';
            document.head.appendChild(script);
        }
    }
    
    /**
     * تصدير إلى Excel (.xlsx)
     */
    exportToExcel(tableId, filename = null) {
        return new Promise((resolve, reject) => {
            this.waitForLibrary('XLSX').then(() => {
                try {
                    const table = document.getElementById(tableId);
                    const data = this.extractTableData(table);
                    
                    // إنشاء workbook جديد
                    const wb = XLSX.utils.book_new();
                    
                    // تحويل البيانات إلى worksheet
                    const ws = XLSX.utils.aoa_to_sheet([data.headers, ...data.rows]);
                    
                    // تطبيق التنسيق
                    this.applyExcelFormatting(ws, data);
                    
                    // إضافة الورقة إلى الكتاب
                    XLSX.utils.book_append_sheet(wb, ws, 'البيانات');
                    
                    // إضافة ورقة معلومات الشركة
                    const companyWs = this.createCompanyInfoSheet();
                    XLSX.utils.book_append_sheet(wb, companyWs, 'معلومات الشركة');
                    
                    // حفظ الملف
                    const fileName = filename || `${tableId}_${this.getCurrentDate()}.xlsx`;
                    XLSX.writeFile(wb, fileName);
                    
                    resolve(fileName);
                } catch (error) {
                    reject(error);
                }
            });
        });
    }
    
    /**
     * تصدير إلى Excel (.xls) - النسخة القديمة
     */
    exportToXLS(tableId, filename = null) {
        return new Promise((resolve, reject) => {
            this.waitForLibrary('XLSX').then(() => {
                try {
                    const table = document.getElementById(tableId);
                    const data = this.extractTableData(table);
                    
                    // إنشاء workbook
                    const wb = XLSX.utils.book_new();
                    const ws = XLSX.utils.aoa_to_sheet([data.headers, ...data.rows]);
                    
                    XLSX.utils.book_append_sheet(wb, ws, 'البيانات');
                    
                    // حفظ بصيغة XLS
                    const fileName = filename || `${tableId}_${this.getCurrentDate()}.xls`;
                    XLSX.writeFile(wb, fileName, { bookType: 'xls' });
                    
                    resolve(fileName);
                } catch (error) {
                    reject(error);
                }
            });
        });
    }
    
    /**
     * تصدير إلى PDF
     */
    exportToPDF(tableId, filename = null, options = {}) {
        return new Promise((resolve, reject) => {
            Promise.all([
                this.waitForLibrary('jsPDF'),
                this.waitForLibrary('html2canvas')
            ]).then(() => {
                try {
                    const table = document.getElementById(tableId);
                    const data = this.extractTableData(table);
                    
                    // إنشاء مستند PDF جديد
                    const { jsPDF } = window.jspdf;
                    const doc = new jsPDF({
                        orientation: options.orientation || 'portrait',
                        unit: 'mm',
                        format: 'a4'
                    });
                    
                    // إضافة الخط العربي
                    this.setupArabicFont(doc);
                    
                    // إضافة رأس الصفحة
                    this.addPDFHeader(doc, options.title || 'تقرير البيانات');
                    
                    // إضافة الجدول
                    this.addTableToPDF(doc, data, options);
                    
                    // إضافة تذييل الصفحة
                    this.addPDFFooter(doc);
                    
                    // حفظ الملف
                    const fileName = filename || `${tableId}_${this.getCurrentDate()}.pdf`;
                    doc.save(fileName);
                    
                    resolve(fileName);
                } catch (error) {
                    reject(error);
                }
            });
        });
    }
    
    /**
     * تصدير إلى Word
     */
    exportToWord(tableId, filename = null) {
        return new Promise((resolve, reject) => {
            try {
                const table = document.getElementById(tableId);
                const data = this.extractTableData(table);
                
                // إنشاء محتوى HTML للوورد
                const htmlContent = this.generateWordHTML(data);
                
                // تحويل إلى Blob
                const blob = new Blob([htmlContent], {
                    type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                });
                
                // تحميل الملف
                const fileName = filename || `${tableId}_${this.getCurrentDate()}.doc`;
                this.downloadBlob(blob, fileName);
                
                resolve(fileName);
            } catch (error) {
                reject(error);
            }
        });
    }
    
    /**
     * تصدير إلى CSV
     */
    exportToCSV(tableId, filename = null) {
        return new Promise((resolve, reject) => {
            try {
                const table = document.getElementById(tableId);
                const data = this.extractTableData(table);
                
                // تحويل إلى CSV
                let csvContent = '\uFEFF'; // BOM للدعم العربي
                csvContent += data.headers.join(',') + '\n';
                
                data.rows.forEach(row => {
                    csvContent += row.map(cell => `"${cell.replace(/"/g, '""')}"`).join(',') + '\n';
                });
                
                // تحميل الملف
                const fileName = filename || `${tableId}_${this.getCurrentDate()}.csv`;
                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                this.downloadBlob(blob, fileName);
                
                resolve(fileName);
            } catch (error) {
                reject(error);
            }
        });
    }
    
    /**
     * استخراج بيانات الجدول
     */
    extractTableData(table) {
        // استخراج العناوين
        const headers = Array.from(table.querySelectorAll('thead th:not(.column-hidden)'))
            .map(th => th.textContent.trim());
        
        // استخراج الصفوف
        const rows = Array.from(table.querySelectorAll('tbody tr:not([style*="display: none"])'))
            .map(row => {
                return Array.from(row.querySelectorAll('td:not(.column-hidden)'))
                    .map(td => {
                        // تنظيف النص من HTML
                        let text = td.textContent || td.innerText || '';
                        return text.trim();
                    });
            });
        
        return { headers, rows };
    }
    
    /**
     * تطبيق تنسيق Excel
     */
    applyExcelFormatting(ws, data) {
        const range = XLSX.utils.decode_range(ws['!ref']);
        
        // تنسيق العناوين
        for (let col = range.s.c; col <= range.e.c; col++) {
            const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col });
            if (!ws[cellAddress]) continue;
            
            ws[cellAddress].s = {
                font: { bold: true, color: { rgb: "FFFFFF" } },
                fill: { fgColor: { rgb: "366092" } },
                alignment: { horizontal: "center", vertical: "center" }
            };
        }
        
        // تنسيق البيانات
        for (let row = range.s.r + 1; row <= range.e.r; row++) {
            for (let col = range.s.c; col <= range.e.c; col++) {
                const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
                if (!ws[cellAddress]) continue;
                
                ws[cellAddress].s = {
                    alignment: { horizontal: "right", vertical: "center" },
                    border: {
                        top: { style: "thin" },
                        bottom: { style: "thin" },
                        left: { style: "thin" },
                        right: { style: "thin" }
                    }
                };
            }
        }
        
        // تعيين عرض الأعمدة
        const colWidths = data.headers.map(() => ({ wch: 20 }));
        ws['!cols'] = colWidths;
    }
    
    /**
     * إنشاء ورقة معلومات الشركة
     */
    createCompanyInfoSheet() {
        const companyData = [
            ['معلومات الشركة'],
            ['اسم الشركة', this.options.companyName],
            ['تاريخ التقرير', this.getCurrentDate()],
            ['العملة', this.options.currency],
            ['المنطقة الزمنية', Intl.DateTimeFormat().resolvedOptions().timeZone]
        ];
        
        return XLSX.utils.aoa_to_sheet(companyData);
    }
    
    /**
     * إعداد الخط العربي في PDF
     */
    setupArabicFont(doc) {
        // في التطبيق الحقيقي، نحتاج تحميل خط عربي
        // هنا نستخدم الخط الافتراضي
        doc.setFont('helvetica');
        doc.setFontSize(12);
    }
    
    /**
     * إضافة رأس الصفحة للـ PDF
     */
    addPDFHeader(doc, title) {
        const pageWidth = doc.internal.pageSize.width;
        
        // عنوان الشركة
        doc.setFontSize(16);
        doc.text(this.options.companyName, pageWidth / 2, 20, { align: 'center' });
        
        // عنوان التقرير
        doc.setFontSize(14);
        doc.text(title, pageWidth / 2, 30, { align: 'center' });
        
        // التاريخ
        doc.setFontSize(10);
        doc.text(`تاريخ التقرير: ${this.getCurrentDate()}`, pageWidth - 20, 40, { align: 'right' });
        
        // خط فاصل
        doc.line(20, 45, pageWidth - 20, 45);
    }
    
    /**
     * إضافة الجدول إلى PDF
     */
    addTableToPDF(doc, data, options) {
        let yPosition = 55;
        const pageWidth = doc.internal.pageSize.width;
        const colWidth = (pageWidth - 40) / data.headers.length;
        
        // رسم العناوين
        doc.setFillColor(54, 96, 146);
        doc.rect(20, yPosition, pageWidth - 40, 10, 'F');
        
        doc.setTextColor(255, 255, 255);
        doc.setFontSize(10);
        
        data.headers.forEach((header, index) => {
            doc.text(header, 20 + (index * colWidth) + (colWidth / 2), yPosition + 7, { align: 'center' });
        });
        
        yPosition += 10;
        doc.setTextColor(0, 0, 0);
        
        // رسم البيانات
        data.rows.forEach((row, rowIndex) => {
            if (yPosition > 270) { // صفحة جديدة
                doc.addPage();
                yPosition = 20;
            }
            
            // خلفية متناوبة
            if (rowIndex % 2 === 0) {
                doc.setFillColor(248, 249, 250);
                doc.rect(20, yPosition, pageWidth - 40, 8, 'F');
            }
            
            row.forEach((cell, cellIndex) => {
                doc.text(String(cell), 20 + (cellIndex * colWidth) + (colWidth / 2), yPosition + 5, { align: 'center' });
            });
            
            yPosition += 8;
        });
    }
    
    /**
     * إضافة تذييل الصفحة للـ PDF
     */
    addPDFFooter(doc) {
        const pageCount = doc.internal.getNumberOfPages();
        const pageWidth = doc.internal.pageSize.width;
        
        for (let i = 1; i <= pageCount; i++) {
            doc.setPage(i);
            doc.setFontSize(8);
            doc.text(`صفحة ${i} من ${pageCount}`, pageWidth / 2, 285, { align: 'center' });
            doc.text('تم إنشاؤه بواسطة نظام ERP', pageWidth - 20, 285, { align: 'right' });
        }
    }
    
    /**
     * إنشاء محتوى HTML للوورد
     */
    generateWordHTML(data) {
        let html = `
            <html xmlns:o="urn:schemas-microsoft-com:office:office" 
                  xmlns:w="urn:schemas-microsoft-com:office:word" 
                  xmlns="http://www.w3.org/TR/REC-html40">
            <head>
                <meta charset="utf-8">
                <title>تقرير البيانات</title>
                <style>
                    body { font-family: Arial, sans-serif; direction: rtl; }
                    table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                    th, td { border: 1px solid #000; padding: 8px; text-align: right; }
                    th { background-color: #366092; color: white; font-weight: bold; }
                    .header { text-align: center; margin-bottom: 20px; }
                    .company-name { font-size: 18px; font-weight: bold; }
                    .report-date { font-size: 12px; color: #666; }
                </style>
            </head>
            <body>
                <div class="header">
                    <div class="company-name">${this.options.companyName}</div>
                    <div class="report-date">تاريخ التقرير: ${this.getCurrentDate()}</div>
                </div>
                <table>
                    <thead>
                        <tr>
        `;
        
        // إضافة العناوين
        data.headers.forEach(header => {
            html += `<th>${header}</th>`;
        });
        
        html += '</tr></thead><tbody>';
        
        // إضافة البيانات
        data.rows.forEach(row => {
            html += '<tr>';
            row.forEach(cell => {
                html += `<td>${cell}</td>`;
            });
            html += '</tr>';
        });
        
        html += '</tbody></table></body></html>';
        return html;
    }
    
    /**
     * تحميل Blob كملف
     */
    downloadBlob(blob, filename) {
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    }
    
    /**
     * انتظار تحميل مكتبة
     */
    waitForLibrary(libraryName) {
        return new Promise((resolve) => {
            const checkLibrary = () => {
                if (window[libraryName]) {
                    resolve();
                } else {
                    setTimeout(checkLibrary, 100);
                }
            };
            checkLibrary();
        });
    }
    
    /**
     * الحصول على التاريخ الحالي
     */
    getCurrentDate() {
        return new Date().toLocaleDateString(this.options.locale);
    }
    
    /**
     * تصدير متعدد الصيغ
     */
    exportMultiple(tableId, formats = ['excel', 'pdf', 'csv']) {
        const promises = [];
        
        formats.forEach(format => {
            switch (format) {
                case 'excel':
                    promises.push(this.exportToExcel(tableId));
                    break;
                case 'xls':
                    promises.push(this.exportToXLS(tableId));
                    break;
                case 'pdf':
                    promises.push(this.exportToPDF(tableId));
                    break;
                case 'csv':
                    promises.push(this.exportToCSV(tableId));
                    break;
                case 'word':
                    promises.push(this.exportToWord(tableId));
                    break;
            }
        });
        
        return Promise.all(promises);
    }
    
    /**
     * تصدير مخصص مع خيارات متقدمة
     */
    exportCustom(tableId, options = {}) {
        const {
            format = 'excel',
            filename = null,
            includeHidden = false,
            filterRows = null,
            customHeaders = null,
            styling = true
        } = options;
        
        // تطبيق المرشحات المخصصة
        if (filterRows || customHeaders || includeHidden) {
            // تعديل الجدول مؤقتاً
            const table = document.getElementById(tableId);
            const originalTable = table.cloneNode(true);
            
            // تطبيق التعديلات
            this.applyCustomOptions(table, options);
            
            // التصدير
            let exportPromise;
            switch (format) {
                case 'excel':
                    exportPromise = this.exportToExcel(tableId, filename);
                    break;
                case 'pdf':
                    exportPromise = this.exportToPDF(tableId, filename, options);
                    break;
                case 'csv':
                    exportPromise = this.exportToCSV(tableId, filename);
                    break;
                default:
                    exportPromise = this.exportToExcel(tableId, filename);
            }
            
            // استعادة الجدول الأصلي
            exportPromise.finally(() => {
                table.parentNode.replaceChild(originalTable, table);
            });
            
            return exportPromise;
        } else {
            // تصدير عادي
            switch (format) {
                case 'excel':
                    return this.exportToExcel(tableId, filename);
                case 'pdf':
                    return this.exportToPDF(tableId, filename, options);
                case 'csv':
                    return this.exportToCSV(tableId, filename);
                default:
                    return this.exportToExcel(tableId, filename);
            }
        }
    }
    
    /**
     * تطبيق الخيارات المخصصة
     */
    applyCustomOptions(table, options) {
        // إظهار الأعمدة المخفية إذا طُلب ذلك
        if (options.includeHidden) {
            table.querySelectorAll('.column-hidden').forEach(el => {
                el.classList.remove('column-hidden');
            });
        }
        
        // تطبيق عناوين مخصصة
        if (options.customHeaders) {
            const headers = table.querySelectorAll('thead th');
            options.customHeaders.forEach((header, index) => {
                if (headers[index]) {
                    headers[index].textContent = header;
                }
            });
        }
        
        // تطبيق مرشح الصفوف
        if (options.filterRows) {
            const rows = table.querySelectorAll('tbody tr');
            rows.forEach((row, index) => {
                if (!options.filterRows(row, index)) {
                    row.style.display = 'none';
                }
            });
        }
    }
}

// إنشاء مثيل عام
window.odooExport = new OdooExportManager();
