/**
 * مدير الطباعة المتقدم لوحدة المحاسبة بأسلوب Odoo
 * Advanced Print Manager for Odoo-Style Accounting Module
 */

class OdooPrintManager {
    constructor(options = {}) {
        this.options = {
            companyName: 'شركة النظام المتقدم',
            companyLogo: '',
            companyAddress: 'المملكة العربية السعودية',
            companyPhone: '+966 11 234 5678',
            companyEmail: '<EMAIL>',
            currency: 'SAR',
            locale: 'ar-SA',
            ...options
        };
        
        this.printStyles = this.generatePrintStyles();
    }
    
    /**
     * طباعة جدول مع تنسيق متقدم
     */
    printTable(tableId, title = '', options = {}) {
        const table = document.getElementById(tableId);
        if (!table) {
            console.error(`Table with ID ${tableId} not found`);
            return;
        }
        
        const printOptions = {
            orientation: 'portrait',
            paperSize: 'A4',
            margins: '20mm',
            fontSize: '10px',
            showHeader: true,
            showFooter: true,
            showPageNumbers: true,
            showDate: true,
            showLogo: false,
            ...options
        };
        
        const printContent = this.generateTablePrintContent(table, title, printOptions);
        this.openPrintWindow(printContent, printOptions);
    }
    
    /**
     * طباعة تقرير مالي
     */
    printFinancialReport(reportData, reportType, options = {}) {
        const printOptions = {
            orientation: 'portrait',
            paperSize: 'A4',
            showComparison: false,
            showSummary: true,
            groupByCategory: true,
            ...options
        };
        
        const printContent = this.generateFinancialReportContent(reportData, reportType, printOptions);
        this.openPrintWindow(printContent, printOptions);
    }
    
    /**
     * طباعة فاتورة
     */
    printInvoice(invoiceData, options = {}) {
        const printOptions = {
            template: 'modern',
            showQR: true,
            showTerms: true,
            ...options
        };
        
        const printContent = this.generateInvoiceContent(invoiceData, printOptions);
        this.openPrintWindow(printContent, printOptions);
    }
    
    /**
     * إنشاء محتوى طباعة الجدول
     */
    generateTablePrintContent(table, title, options) {
        const tableClone = table.cloneNode(true);
        
        // إزالة الأعمدة المخفية
        this.removeHiddenColumns(tableClone);
        
        // تطبيق تنسيق الطباعة
        this.applyPrintFormatting(tableClone, options);
        
        const header = options.showHeader ? this.generatePrintHeader(title, options) : '';
        const footer = options.showFooter ? this.generatePrintFooter(options) : '';
        
        return `
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>${title || 'طباعة الجدول'}</title>
                <style>${this.printStyles}</style>
            </head>
            <body class="print-body ${options.orientation}">
                ${header}
                <div class="print-content">
                    ${tableClone.outerHTML}
                </div>
                ${footer}
            </body>
            </html>
        `;
    }
    
    /**
     * إنشاء محتوى التقرير المالي
     */
    generateFinancialReportContent(reportData, reportType, options) {
        const reportTitles = {
            'balance_sheet': 'الميزانية العمومية',
            'income_statement': 'قائمة الدخل',
            'trial_balance': 'ميزان المراجعة',
            'cash_flow': 'قائمة التدفقات النقدية'
        };
        
        const title = reportTitles[reportType] || 'التقرير المالي';
        const header = this.generatePrintHeader(title, options);
        const footer = this.generatePrintFooter(options);
        
        let content = '<div class="financial-report">';
        
        // ملخص التقرير
        if (options.showSummary) {
            content += this.generateReportSummary(reportData, options);
        }
        
        // جدول البيانات
        content += this.generateFinancialTable(reportData, options);
        
        content += '</div>';
        
        return `
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>${title}</title>
                <style>${this.printStyles}</style>
            </head>
            <body class="print-body ${options.orientation}">
                ${header}
                <div class="print-content">
                    ${content}
                </div>
                ${footer}
            </body>
            </html>
        `;
    }
    
    /**
     * إنشاء محتوى الفاتورة
     */
    generateInvoiceContent(invoiceData, options) {
        const header = this.generateInvoiceHeader(invoiceData, options);
        const body = this.generateInvoiceBody(invoiceData, options);
        const footer = this.generateInvoiceFooter(invoiceData, options);
        
        return `
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>فاتورة رقم ${invoiceData.number}</title>
                <style>${this.printStyles}</style>
            </head>
            <body class="print-body invoice-template-${options.template}">
                ${header}
                ${body}
                ${footer}
            </body>
            </html>
        `;
    }
    
    /**
     * إنشاء رأس الطباعة
     */
    generatePrintHeader(title, options) {
        const currentDate = new Date().toLocaleDateString(this.options.locale);
        const currentTime = new Date().toLocaleTimeString(this.options.locale);
        
        return `
            <div class="print-header">
                <div class="header-top">
                    <div class="company-info">
                        ${options.showLogo && this.options.companyLogo ? 
                            `<img src="${this.options.companyLogo}" alt="شعار الشركة" class="company-logo">` : ''}
                        <h1 class="company-name">${this.options.companyName}</h1>
                        <p class="company-details">
                            ${this.options.companyAddress}<br>
                            هاتف: ${this.options.companyPhone} | بريد إلكتروني: ${this.options.companyEmail}
                        </p>
                    </div>
                    <div class="report-info">
                        <h2 class="report-title">${title}</h2>
                        ${options.showDate ? `<p class="print-date">تاريخ الطباعة: ${currentDate} ${currentTime}</p>` : ''}
                    </div>
                </div>
                <hr class="header-separator">
            </div>
        `;
    }
    
    /**
     * إنشاء تذييل الطباعة
     */
    generatePrintFooter(options) {
        const pageInfo = options.showPageNumbers ? 
            '<span class="page-info">صفحة <span class="page-number"></span> من <span class="total-pages"></span></span>' : '';
        
        return `
            <div class="print-footer">
                <hr class="footer-separator">
                <div class="footer-content">
                    <div class="footer-left">
                        <p>تم إنشاء هذا التقرير بواسطة نظام ERP</p>
                        <p>جميع الحقوق محفوظة © ${new Date().getFullYear()}</p>
                    </div>
                    <div class="footer-right">
                        ${pageInfo}
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * إنشاء ملخص التقرير
     */
    generateReportSummary(reportData, options) {
        const totalBalance = reportData.reduce((sum, item) => sum + (item.balance || 0), 0);
        const itemCount = reportData.length;
        
        return `
            <div class="report-summary">
                <h3>ملخص التقرير</h3>
                <div class="summary-grid">
                    <div class="summary-item">
                        <span class="summary-label">عدد البنود:</span>
                        <span class="summary-value">${itemCount}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">إجمالي الأرصدة:</span>
                        <span class="summary-value">${this.formatCurrency(totalBalance)}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">العملة:</span>
                        <span class="summary-value">${this.options.currency}</span>
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * إنشاء جدول مالي
     */
    generateFinancialTable(reportData, options) {
        let table = `
            <table class="financial-table">
                <thead>
                    <tr>
                        <th>اسم الحساب</th>
                        <th>الكود</th>
                        <th>الرصيد</th>
                        ${options.showComparison ? '<th>رصيد المقارنة</th><th>الفرق</th>' : ''}
                    </tr>
                </thead>
                <tbody>
        `;
        
        reportData.forEach(item => {
            const levelClass = `level-${item.level || 0}`;
            const balanceClass = item.balance > 0 ? 'positive' : item.balance < 0 ? 'negative' : 'zero';
            
            table += `
                <tr class="${levelClass}">
                    <td>${'&nbsp;'.repeat((item.level || 0) * 4)}${item.name}</td>
                    <td>${item.code || ''}</td>
                    <td class="amount ${balanceClass}">${this.formatCurrency(item.balance || 0)}</td>
                    ${options.showComparison ? `
                        <td class="amount">${this.formatCurrency(item.comparison_balance || 0)}</td>
                        <td class="amount">${this.formatCurrency((item.balance || 0) - (item.comparison_balance || 0))}</td>
                    ` : ''}
                </tr>
            `;
        });
        
        table += '</tbody></table>';
        return table;
    }
    
    /**
     * إزالة الأعمدة المخفية
     */
    removeHiddenColumns(table) {
        const hiddenColumns = table.querySelectorAll('.column-hidden');
        hiddenColumns.forEach(col => col.remove());
    }
    
    /**
     * تطبيق تنسيق الطباعة
     */
    applyPrintFormatting(table, options) {
        table.classList.add('print-table');
        
        // تطبيق حجم الخط
        if (options.fontSize) {
            table.style.fontSize = options.fontSize;
        }
        
        // إضافة حدود للجدول
        const cells = table.querySelectorAll('th, td');
        cells.forEach(cell => {
            cell.style.border = '1px solid #000';
            cell.style.padding = '0.4rem';
        });
        
        // تنسيق العناوين
        const headers = table.querySelectorAll('th');
        headers.forEach(header => {
            header.style.backgroundColor = '#f0f0f0';
            header.style.fontWeight = 'bold';
        });
    }
    
    /**
     * فتح نافذة الطباعة
     */
    openPrintWindow(content, options) {
        const printWindow = window.open('', '_blank', 'width=800,height=600');
        
        if (!printWindow) {
            alert('تم حظر النافذة المنبثقة. يرجى السماح بالنوافذ المنبثقة وإعادة المحاولة.');
            return;
        }
        
        printWindow.document.write(content);
        printWindow.document.close();
        
        // انتظار تحميل المحتوى ثم الطباعة
        printWindow.onload = function() {
            setTimeout(() => {
                printWindow.focus();
                printWindow.print();
                
                // إغلاق النافذة بعد الطباعة (اختياري)
                setTimeout(() => {
                    printWindow.close();
                }, 1000);
            }, 500);
        };
    }
    
    /**
     * تنسيق العملة
     */
    formatCurrency(amount) {
        return new Intl.NumberFormat(this.options.locale, {
            style: 'currency',
            currency: this.options.currency,
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(amount);
    }
    
    /**
     * إنشاء أنماط الطباعة
     */
    generatePrintStyles() {
        return `
            @page {
                margin: 20mm;
                size: A4;
            }
            
            .print-body {
                font-family: 'Arial', sans-serif;
                font-size: 11px;
                line-height: 1.4;
                color: #000;
                background: #fff;
                margin: 0;
                padding: 0;
            }
            
            .print-body.landscape {
                /* أنماط الطباعة الأفقية */
            }
            
            .print-header {
                margin-bottom: 20px;
                page-break-inside: avoid;
            }
            
            .header-top {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: 10px;
            }
            
            .company-info {
                flex: 1;
            }
            
            .company-name {
                font-size: 18px;
                font-weight: bold;
                margin: 0 0 5px 0;
                color: #2c3e50;
            }
            
            .company-logo {
                max-height: 60px;
                margin-bottom: 10px;
            }
            
            .company-details {
                font-size: 10px;
                color: #666;
                margin: 0;
            }
            
            .report-info {
                text-align: left;
                flex: 1;
            }
            
            .report-title {
                font-size: 16px;
                font-weight: bold;
                margin: 0 0 5px 0;
                color: #2c3e50;
            }
            
            .print-date {
                font-size: 10px;
                color: #666;
                margin: 0;
            }
            
            .header-separator,
            .footer-separator {
                border: none;
                border-top: 2px solid #2c3e50;
                margin: 10px 0;
            }
            
            .print-content {
                min-height: calc(100vh - 200px);
            }
            
            .print-table,
            .financial-table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 20px;
                font-size: 10px;
            }
            
            .print-table th,
            .print-table td,
            .financial-table th,
            .financial-table td {
                border: 1px solid #000;
                padding: 0.4rem;
                text-align: right;
            }
            
            .print-table th,
            .financial-table th {
                background-color: #f0f0f0 !important;
                font-weight: bold;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            
            .level-0 {
                font-weight: bold;
                background-color: #f8f9fa !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            
            .level-1 { padding-right: 1rem; }
            .level-2 { padding-right: 2rem; }
            .level-3 { padding-right: 3rem; }
            
            .amount {
                text-align: left;
                font-family: 'Courier New', monospace;
            }
            
            .amount.positive { color: #28a745; }
            .amount.negative { color: #dc3545; }
            .amount.zero { color: #6c757d; }
            
            .report-summary {
                background-color: #e9ecef !important;
                padding: 15px;
                margin-bottom: 20px;
                border-radius: 5px;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            
            .summary-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 10px;
            }
            
            .summary-item {
                display: flex;
                justify-content: space-between;
                padding: 5px 0;
                border-bottom: 1px solid #ccc;
            }
            
            .summary-label {
                font-weight: bold;
            }
            
            .summary-value {
                font-family: 'Courier New', monospace;
            }
            
            .print-footer {
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                background: #fff;
                padding-top: 10px;
            }
            
            .footer-content {
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: 9px;
                color: #666;
            }
            
            .page-break {
                page-break-before: always;
            }
            
            .no-break {
                page-break-inside: avoid;
            }
            
            /* أنماط الفاتورة */
            .invoice-template-modern {
                /* أنماط قالب الفاتورة الحديث */
            }
            
            .invoice-header {
                display: flex;
                justify-content: space-between;
                margin-bottom: 30px;
            }
            
            .invoice-details {
                background-color: #f8f9fa !important;
                padding: 15px;
                margin-bottom: 20px;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            
            .invoice-items {
                margin-bottom: 30px;
            }
            
            .invoice-totals {
                margin-top: 20px;
                text-align: left;
            }
            
            .total-row {
                font-weight: bold;
                font-size: 12px;
                background-color: #e9ecef !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
        `;
    }
}

// إنشاء مثيل عام
window.OdooPrintManager = OdooPrintManager;
