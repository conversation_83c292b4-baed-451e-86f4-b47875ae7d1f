/**
 * مدير الجداول المتقدم لوحدة المحاسبة بأسلوب Odoo
 * Advanced Table Manager for Odoo-Style Accounting Module
 */

class OdooTableManager {
    constructor(tableId, options = {}) {
        this.tableId = tableId;
        this.table = document.getElementById(tableId);
        this.options = {
            sortable: true,
            filterable: true,
            searchable: true,
            exportable: true,
            printable: true,
            columnControl: true,
            fontControl: true,
            selectable: false,
            ...options
        };
        
        this.currentSort = { column: null, direction: null };
        this.filters = {};
        this.hiddenColumns = new Set();
        this.selectedRows = new Set();
        this.originalData = [];
        this.filteredData = [];
        
        this.init();
    }
    
    init() {
        if (!this.table) {
            console.error(`Table with ID ${this.tableId} not found`);
            return;
        }
        
        this.setupTableStructure();
        this.setupEventListeners();
        this.setupColumnControls();
        this.setupToolbar();
        this.storeOriginalData();
        
        // تطبيق الإعدادات المحفوظة
        this.loadSettings();
    }
    
    setupTableStructure() {
        // إضافة container للجدول
        if (!this.table.closest('.odoo-table-container')) {
            const container = document.createElement('div');
            container.className = 'odoo-table-container';
            this.table.parentNode.insertBefore(container, this.table);
            container.appendChild(this.table);
        }
        
        // إضافة classes للجدول
        this.table.classList.add('odoo-table');
        
        // إعداد headers للترتيب
        if (this.options.sortable) {
            const headers = this.table.querySelectorAll('th');
            headers.forEach((header, index) => {
                if (!header.classList.contains('no-sort')) {
                    header.classList.add('sortable');
                    header.dataset.column = index;
                }
            });
        }
        
        // إضافة checkboxes للتحديد
        if (this.options.selectable) {
            this.addSelectionColumn();
        }
    }
    
    setupEventListeners() {
        // ترتيب الأعمدة
        if (this.options.sortable) {
            this.table.addEventListener('click', (e) => {
                if (e.target.tagName === 'TH' && e.target.classList.contains('sortable')) {
                    this.sortColumn(parseInt(e.target.dataset.column));
                }
            });
        }
        
        // تحديد الصفوف
        if (this.options.selectable) {
            this.table.addEventListener('change', (e) => {
                if (e.target.type === 'checkbox') {
                    this.handleRowSelection(e.target);
                }
            });
        }
        
        // البحث المباشر
        const searchBox = document.querySelector(`#${this.tableId}-search`);
        if (searchBox) {
            searchBox.addEventListener('input', (e) => {
                this.search(e.target.value);
            });
        }
    }
    
    setupToolbar() {
        const container = this.table.closest('.odoo-table-container');
        const toolbar = document.createElement('div');
        toolbar.className = 'table-toolbar no-print';
        toolbar.innerHTML = this.generateToolbarHTML();
        
        container.insertBefore(toolbar, this.table);
        
        // إعداد أحداث الشريط
        this.setupToolbarEvents(toolbar);
    }
    
    generateToolbarHTML() {
        return `
            <div class="table-toolbar-left">
                ${this.options.searchable ? `
                    <input type="text" id="${this.tableId}-search" class="search-box" placeholder="البحث...">
                ` : ''}
                
                ${this.options.filterable ? `
                    <div class="filter-dropdown">
                        <button class="filter-btn" id="${this.tableId}-filter-btn">
                            <i class="fas fa-filter"></i>
                            فلترة
                        </button>
                        <div class="filter-dropdown-content" id="${this.tableId}-filter-content">
                            ${this.generateFilterOptions()}
                        </div>
                    </div>
                ` : ''}
                
                ${this.options.columnControl ? `
                    <button class="filter-btn" id="${this.tableId}-columns-btn">
                        <i class="fas fa-columns"></i>
                        الأعمدة
                    </button>
                ` : ''}
                
                ${this.options.fontControl ? `
                    <div class="font-controls">
                        <select id="${this.tableId}-font-size" class="font-size-control">
                            <option value="xs">صغير جداً</option>
                            <option value="sm">صغير</option>
                            <option value="md" selected>متوسط</option>
                            <option value="lg">كبير</option>
                            <option value="xl">كبير جداً</option>
                        </select>
                    </div>
                ` : ''}
            </div>
            
            <div class="table-toolbar-right">
                <div class="export-controls">
                    ${this.options.exportable ? `
                        <button class="export-btn" id="${this.tableId}-export-excel">
                            <i class="fas fa-file-excel"></i>
                            Excel
                        </button>
                        <button class="export-btn" id="${this.tableId}-export-pdf">
                            <i class="fas fa-file-pdf"></i>
                            PDF
                        </button>
                    ` : ''}
                    
                    ${this.options.printable ? `
                        <button class="print-btn" id="${this.tableId}-print">
                            <i class="fas fa-print"></i>
                            طباعة
                        </button>
                    ` : ''}
                </div>
                
                <button class="filter-btn" id="${this.tableId}-refresh">
                    <i class="fas fa-sync-alt"></i>
                    تحديث
                </button>
            </div>
        `;
    }
    
    setupToolbarEvents(toolbar) {
        // البحث
        const searchBox = toolbar.querySelector(`#${this.tableId}-search`);
        if (searchBox) {
            searchBox.addEventListener('input', (e) => {
                this.search(e.target.value);
            });
        }
        
        // الفلترة
        const filterBtn = toolbar.querySelector(`#${this.tableId}-filter-btn`);
        if (filterBtn) {
            filterBtn.addEventListener('click', () => {
                this.toggleFilterDropdown();
            });
        }
        
        // التحكم في الأعمدة
        const columnsBtn = toolbar.querySelector(`#${this.tableId}-columns-btn`);
        if (columnsBtn) {
            columnsBtn.addEventListener('click', () => {
                this.showColumnControls();
            });
        }
        
        // حجم الخط
        const fontSizeSelect = toolbar.querySelector(`#${this.tableId}-font-size`);
        if (fontSizeSelect) {
            fontSizeSelect.addEventListener('change', (e) => {
                this.changeFontSize(e.target.value);
            });
        }
        
        // التصدير
        const exportExcelBtn = toolbar.querySelector(`#${this.tableId}-export-excel`);
        if (exportExcelBtn) {
            exportExcelBtn.addEventListener('click', () => {
                this.exportToExcel();
            });
        }
        
        const exportPdfBtn = toolbar.querySelector(`#${this.tableId}-export-pdf`);
        if (exportPdfBtn) {
            exportPdfBtn.addEventListener('click', () => {
                this.exportToPDF();
            });
        }
        
        // الطباعة
        const printBtn = toolbar.querySelector(`#${this.tableId}-print`);
        if (printBtn) {
            printBtn.addEventListener('click', () => {
                this.print();
            });
        }
        
        // التحديث
        const refreshBtn = toolbar.querySelector(`#${this.tableId}-refresh`);
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.refresh();
            });
        }
    }
    
    sortColumn(columnIndex) {
        const headers = this.table.querySelectorAll('th');
        const header = headers[columnIndex];
        
        // تحديد اتجاه الترتيب
        let direction = 'asc';
        if (this.currentSort.column === columnIndex) {
            direction = this.currentSort.direction === 'asc' ? 'desc' : 'asc';
        }
        
        // إزالة classes السابقة
        headers.forEach(h => h.classList.remove('sort-asc', 'sort-desc'));
        
        // إضافة class الجديد
        header.classList.add(`sort-${direction}`);
        
        // حفظ حالة الترتيب
        this.currentSort = { column: columnIndex, direction };
        
        // ترتيب البيانات
        this.sortTableData(columnIndex, direction);
        
        // حفظ الإعدادات
        this.saveSettings();
    }
    
    sortTableData(columnIndex, direction) {
        const tbody = this.table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        
        rows.sort((a, b) => {
            const aValue = this.getCellValue(a, columnIndex);
            const bValue = this.getCellValue(b, columnIndex);
            
            // تحديد نوع البيانات
            const aNum = parseFloat(aValue);
            const bNum = parseFloat(bValue);
            
            let comparison = 0;
            
            if (!isNaN(aNum) && !isNaN(bNum)) {
                // مقارنة رقمية
                comparison = aNum - bNum;
            } else {
                // مقارنة نصية
                comparison = aValue.localeCompare(bValue, 'ar');
            }
            
            return direction === 'asc' ? comparison : -comparison;
        });
        
        // إعادة ترتيب الصفوف
        rows.forEach(row => tbody.appendChild(row));
        
        // تطبيق تأثير بصري
        this.animateRows();
    }
    
    getCellValue(row, columnIndex) {
        const cell = row.cells[columnIndex];
        if (!cell) return '';
        
        // استخراج النص من الخلية
        let value = cell.textContent || cell.innerText || '';
        
        // تنظيف القيمة
        value = value.trim();
        
        // إزالة الفواصل من الأرقام
        value = value.replace(/,/g, '');
        
        return value;
    }
    
    search(searchTerm) {
        const tbody = this.table.querySelector('tbody');
        const rows = tbody.querySelectorAll('tr');
        
        searchTerm = searchTerm.toLowerCase().trim();
        
        rows.forEach(row => {
            if (searchTerm === '') {
                row.style.display = '';
                return;
            }
            
            const cells = row.querySelectorAll('td');
            let found = false;
            
            cells.forEach(cell => {
                const text = (cell.textContent || cell.innerText || '').toLowerCase();
                if (text.includes(searchTerm)) {
                    found = true;
                }
            });
            
            row.style.display = found ? '' : 'none';
        });
        
        this.updateStatusBar();
    }
    
    toggleFilterDropdown() {
        const dropdown = document.querySelector(`#${this.tableId}-filter-content`).parentElement;
        dropdown.classList.toggle('active');
        
        // إغلاق عند النقر خارج القائمة
        document.addEventListener('click', (e) => {
            if (!dropdown.contains(e.target)) {
                dropdown.classList.remove('active');
            }
        }, { once: true });
    }
    
    generateFilterOptions() {
        // سيتم تخصيص هذا حسب نوع البيانات
        return `
            <div class="filter-option">
                <input type="checkbox" id="filter-all" checked>
                <label for="filter-all">عرض الكل</label>
            </div>
            <div class="filter-option">
                <input type="checkbox" id="filter-active">
                <label for="filter-active">النشط فقط</label>
            </div>
            <div class="filter-option">
                <input type="checkbox" id="filter-inactive">
                <label for="filter-inactive">غير النشط فقط</label>
            </div>
        `;
    }
    
    showColumnControls() {
        // إنشاء نافذة التحكم في الأعمدة
        const modal = this.createColumnControlModal();
        document.body.appendChild(modal);
        
        // عرض النافذة
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);
    }
    
    createColumnControlModal() {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">التحكم في الأعمدة</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        ${this.generateColumnControlsHTML()}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-primary" id="apply-column-settings">تطبيق</button>
                    </div>
                </div>
            </div>
        `;
        
        // إعداد الأحداث
        this.setupColumnControlEvents(modal);
        
        return modal;
    }
    
    generateColumnControlsHTML() {
        const headers = this.table.querySelectorAll('th');
        let html = '<div class="column-control-group">';
        
        headers.forEach((header, index) => {
            const columnName = header.textContent.trim();
            const isHidden = this.hiddenColumns.has(index);
            
            html += `
                <div class="column-control-item">
                    <input type="checkbox" id="col-${index}" ${!isHidden ? 'checked' : ''}>
                    <label for="col-${index}">${columnName}</label>
                    <select class="column-width-control" data-column="${index}">
                        <option value="xs">ضيق جداً</option>
                        <option value="sm">ضيق</option>
                        <option value="md" selected>متوسط</option>
                        <option value="lg">عريض</option>
                        <option value="xl">عريض جداً</option>
                        <option value="auto">تلقائي</option>
                    </select>
                </div>
            `;
        });
        
        html += '</div>';
        return html;
    }
    
    setupColumnControlEvents(modal) {
        const applyBtn = modal.querySelector('#apply-column-settings');
        applyBtn.addEventListener('click', () => {
            this.applyColumnSettings(modal);
            modal.remove();
        });
        
        const closeBtn = modal.querySelector('.btn-close');
        closeBtn.addEventListener('click', () => {
            modal.remove();
        });
    }
    
    applyColumnSettings(modal) {
        const checkboxes = modal.querySelectorAll('input[type="checkbox"]');
        const widthSelects = modal.querySelectorAll('.column-width-control');
        
        // تطبيق إظهار/إخفاء الأعمدة
        checkboxes.forEach((checkbox, index) => {
            if (checkbox.checked) {
                this.showColumn(index);
            } else {
                this.hideColumn(index);
            }
        });
        
        // تطبيق عرض الأعمدة
        widthSelects.forEach(select => {
            const columnIndex = parseInt(select.dataset.column);
            const width = select.value;
            this.setColumnWidth(columnIndex, width);
        });
        
        this.saveSettings();
    }
    
    hideColumn(columnIndex) {
        this.hiddenColumns.add(columnIndex);
        const headers = this.table.querySelectorAll('th');
        const rows = this.table.querySelectorAll('tbody tr');
        
        // إخفاء العنوان
        if (headers[columnIndex]) {
            headers[columnIndex].classList.add('column-hidden');
        }
        
        // إخفاء الخلايا
        rows.forEach(row => {
            if (row.cells[columnIndex]) {
                row.cells[columnIndex].classList.add('column-hidden');
            }
        });
    }
    
    showColumn(columnIndex) {
        this.hiddenColumns.delete(columnIndex);
        const headers = this.table.querySelectorAll('th');
        const rows = this.table.querySelectorAll('tbody tr');
        
        // إظهار العنوان
        if (headers[columnIndex]) {
            headers[columnIndex].classList.remove('column-hidden');
        }
        
        // إظهار الخلايا
        rows.forEach(row => {
            if (row.cells[columnIndex]) {
                row.cells[columnIndex].classList.remove('column-hidden');
            }
        });
    }
    
    setColumnWidth(columnIndex, width) {
        const headers = this.table.querySelectorAll('th');
        const rows = this.table.querySelectorAll('tbody tr');
        
        // إزالة classes العرض السابقة
        const widthClasses = ['col-width-xs', 'col-width-sm', 'col-width-md', 'col-width-lg', 'col-width-xl', 'col-width-auto'];
        
        // تطبيق على العنوان
        if (headers[columnIndex]) {
            widthClasses.forEach(cls => headers[columnIndex].classList.remove(cls));
            headers[columnIndex].classList.add(`col-width-${width}`);
        }
        
        // تطبيق على الخلايا
        rows.forEach(row => {
            if (row.cells[columnIndex]) {
                widthClasses.forEach(cls => row.cells[columnIndex].classList.remove(cls));
                row.cells[columnIndex].classList.add(`col-width-${width}`);
            }
        });
    }
    
    changeFontSize(size) {
        const fontClasses = ['font-size-xs', 'font-size-sm', 'font-size-md', 'font-size-lg', 'font-size-xl'];
        
        // إزالة classes السابقة
        fontClasses.forEach(cls => this.table.classList.remove(cls));
        
        // إضافة class الجديد
        this.table.classList.add(`font-size-${size}`);
        
        this.saveSettings();
    }
    
    exportToExcel() {
        // تحضير البيانات للتصدير
        const data = this.getTableData();
        
        // إنشاء ملف Excel (يتطلب مكتبة خارجية)
        this.showMessage('جاري تصدير البيانات إلى Excel...', 'info');
        
        // هنا يمكن إضافة كود التصدير الفعلي
        setTimeout(() => {
            this.showMessage('تم تصدير البيانات بنجاح', 'success');
        }, 1000);
    }
    
    exportToPDF() {
        this.showMessage('جاري تصدير البيانات إلى PDF...', 'info');
        
        // هنا يمكن إضافة كود التصدير الفعلي
        setTimeout(() => {
            this.showMessage('تم تصدير البيانات بنجاح', 'success');
        }, 1000);
    }
    
    print() {
        // إنشاء نافذة طباعة مخصصة
        const printWindow = window.open('', '_blank');
        const printContent = this.generatePrintContent();
        
        printWindow.document.write(printContent);
        printWindow.document.close();
        printWindow.focus();
        printWindow.print();
        printWindow.close();
    }
    
    generatePrintContent() {
        const tableClone = this.table.cloneNode(true);
        
        // إزالة الأعمدة المخفية
        this.hiddenColumns.forEach(columnIndex => {
            const headers = tableClone.querySelectorAll('th');
            const rows = tableClone.querySelectorAll('tbody tr');
            
            if (headers[columnIndex]) {
                headers[columnIndex].remove();
            }
            
            rows.forEach(row => {
                if (row.cells[columnIndex]) {
                    row.cells[columnIndex].remove();
                }
            });
        });
        
        return `
            <!DOCTYPE html>
            <html dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>طباعة الجدول</title>
                <link href="../assets/css/odoo-advanced.css" rel="stylesheet">
                <style>
                    body { font-family: Arial, sans-serif; }
                    .print-header { text-align: center; margin-bottom: 20px; }
                    .print-date { text-align: left; margin-bottom: 10px; }
                </style>
            </head>
            <body>
                <div class="print-header">
                    <h2>تقرير البيانات</h2>
                    <div class="print-date">تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}</div>
                </div>
                ${tableClone.outerHTML}
                <div class="print-footer">
                    <p>تم إنشاء هذا التقرير بواسطة نظام ERP</p>
                </div>
            </body>
            </html>
        `;
    }
    
    refresh() {
        this.showMessage('جاري تحديث البيانات...', 'info');
        
        // إعادة تحميل البيانات
        setTimeout(() => {
            location.reload();
        }, 500);
    }
    
    getTableData() {
        const headers = Array.from(this.table.querySelectorAll('th')).map(th => th.textContent.trim());
        const rows = Array.from(this.table.querySelectorAll('tbody tr')).map(row => {
            return Array.from(row.cells).map(cell => cell.textContent.trim());
        });
        
        return { headers, rows };
    }
    
    updateStatusBar() {
        const container = this.table.closest('.odoo-table-container');
        let statusBar = container.querySelector('.table-status-bar');
        
        if (!statusBar) {
            statusBar = document.createElement('div');
            statusBar.className = 'table-status-bar no-print';
            container.appendChild(statusBar);
        }
        
        const totalRows = this.table.querySelectorAll('tbody tr').length;
        const visibleRows = this.table.querySelectorAll('tbody tr:not([style*="display: none"])').length;
        const selectedRows = this.selectedRows.size;
        
        statusBar.innerHTML = `
            <div class="status-info">
                <span>إجمالي الصفوف: ${totalRows}</span>
                <span>المعروض: ${visibleRows}</span>
                ${this.options.selectable ? `<span>المحدد: ${selectedRows}</span>` : ''}
            </div>
            <div class="status-actions">
                <span>آخر تحديث: ${new Date().toLocaleTimeString('ar-SA')}</span>
            </div>
        `;
    }
    
    animateRows() {
        const rows = this.table.querySelectorAll('tbody tr');
        rows.forEach((row, index) => {
            row.style.animation = 'none';
            setTimeout(() => {
                row.style.animation = `fadeIn 0.3s ease-in ${index * 0.02}s both`;
            }, 10);
        });
    }
    
    showMessage(message, type = 'info') {
        // إنشاء رسالة تنبيه
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alert);
        
        // إزالة الرسالة تلقائياً
        setTimeout(() => {
            alert.remove();
        }, 3000);
    }
    
    saveSettings() {
        const settings = {
            sort: this.currentSort,
            hiddenColumns: Array.from(this.hiddenColumns),
            fontSize: this.getCurrentFontSize(),
            filters: this.filters
        };
        
        localStorage.setItem(`table-settings-${this.tableId}`, JSON.stringify(settings));
    }
    
    loadSettings() {
        const saved = localStorage.getItem(`table-settings-${this.tableId}`);
        if (!saved) return;
        
        try {
            const settings = JSON.parse(saved);
            
            // تطبيق الإعدادات المحفوظة
            if (settings.hiddenColumns) {
                settings.hiddenColumns.forEach(index => this.hideColumn(index));
            }
            
            if (settings.fontSize) {
                this.changeFontSize(settings.fontSize);
                const fontSelect = document.querySelector(`#${this.tableId}-font-size`);
                if (fontSelect) fontSelect.value = settings.fontSize;
            }
            
            if (settings.sort && settings.sort.column !== null) {
                this.sortColumn(settings.sort.column);
            }
            
        } catch (e) {
            console.error('Error loading table settings:', e);
        }
    }
    
    getCurrentFontSize() {
        const fontClasses = ['font-size-xs', 'font-size-sm', 'font-size-md', 'font-size-lg', 'font-size-xl'];
        for (let cls of fontClasses) {
            if (this.table.classList.contains(cls)) {
                return cls.replace('font-size-', '');
            }
        }
        return 'md';
    }
    
    storeOriginalData() {
        this.originalData = this.getTableData();
    }
    
    addSelectionColumn() {
        // إضافة عمود التحديد في البداية
        const headers = this.table.querySelector('thead tr');
        const selectAllHeader = document.createElement('th');
        selectAllHeader.innerHTML = '<input type="checkbox" class="row-selector" id="select-all">';
        selectAllHeader.style.width = '40px';
        headers.insertBefore(selectAllHeader, headers.firstChild);
        
        // إضافة checkboxes للصفوف
        const rows = this.table.querySelectorAll('tbody tr');
        rows.forEach((row, index) => {
            const selectCell = document.createElement('td');
            selectCell.innerHTML = `<input type="checkbox" class="row-selector" data-row="${index}">`;
            row.insertBefore(selectCell, row.firstChild);
        });
        
        // إعداد تحديد الكل
        const selectAllCheckbox = document.getElementById('select-all');
        selectAllCheckbox.addEventListener('change', (e) => {
            const checkboxes = this.table.querySelectorAll('tbody .row-selector');
            checkboxes.forEach(cb => {
                cb.checked = e.target.checked;
                this.handleRowSelection(cb);
            });
        });
    }
    
    handleRowSelection(checkbox) {
        const row = checkbox.closest('tr');
        const rowIndex = parseInt(checkbox.dataset.row);
        
        if (checkbox.checked) {
            this.selectedRows.add(rowIndex);
            row.classList.add('selected-row');
        } else {
            this.selectedRows.delete(rowIndex);
            row.classList.remove('selected-row');
        }
        
        this.updateStatusBar();
    }
}

// تهيئة تلقائية للجداول
document.addEventListener('DOMContentLoaded', function() {
    // البحث عن الجداول التي تحتوي على class odoo-table-auto
    const autoTables = document.querySelectorAll('.odoo-table-auto');
    autoTables.forEach(table => {
        new OdooTableManager(table.id, {
            sortable: true,
            filterable: true,
            searchable: true,
            exportable: true,
            printable: true,
            columnControl: true,
            fontControl: true,
            selectable: table.dataset.selectable === 'true'
        });
    });
});
