<?php
/**
 * نظام التثبيت والتشغيل التلقائي الشامل
 * Automatic Setup and Launch System
 */

session_start();

// إعدادات التثبيت التلقائي
$auto_config = array(
    'db_host' => 'localhost',
    'db_name' => 'erp_accounting',
    'db_user' => 'root',
    'db_pass' => '',
    'admin_name' => 'مدير النظام',
    'admin_email' => '<EMAIL>',
    'admin_password' => 'admin123',
    'company_name' => 'شركتي',
    'company_code' => 'MYCO'
);

$setup_steps = array();
$errors = array();
$success = false;

try {
    // الخطوة 1: التحقق من المتطلبات
    $setup_steps[] = array('step' => 'التحقق من المتطلبات', 'status' => 'running');
    
    if (!extension_loaded('pdo') && !extension_loaded('mysql')) {
        throw new Exception('PDO أو MySQL غير متوفر');
    }
    
    if (!is_writable('.')) {
        throw new Exception('المجلد غير قابل للكتابة');
    }
    
    $setup_steps[count($setup_steps)-1]['status'] = 'success';
    $setup_steps[count($setup_steps)-1]['message'] = 'تم التحقق من المتطلبات بنجاح';
    
    // الخطوة 2: إنشاء المجلدات المطلوبة
    $setup_steps[] = array('step' => 'إنشاء المجلدات', 'status' => 'running');
    
    $directories = array('logs', 'cache', 'uploads', 'backups', 'tmp', 'sessions');
    foreach ($directories as $dir) {
        if (!file_exists($dir)) {
            mkdir($dir, 0755, true);
        }
    }
    
    $setup_steps[count($setup_steps)-1]['status'] = 'success';
    $setup_steps[count($setup_steps)-1]['message'] = 'تم إنشاء جميع المجلدات';
    
    // الخطوة 3: إنشاء ملف التكوين
    $setup_steps[] = array('step' => 'إنشاء ملف التكوين', 'status' => 'running');
    
    $config_content = "<?php
// إعدادات قاعدة البيانات - تم إنشاؤها تلقائياً
define('DB_HOST', '{$auto_config['db_host']}');
define('DB_NAME', '{$auto_config['db_name']}');
define('DB_USER', '{$auto_config['db_user']}');
define('DB_PASS', '{$auto_config['db_pass']}');
define('DB_CHARSET', 'utf8mb4');

// إعدادات النظام
define('SITE_URL', 'http://localhost/acc/');
define('SITE_NAME', 'نظام ERP المحاسبي');
define('CURRENCY', 'ر.س');
define('DATE_FORMAT', 'Y-m-d');
define('DATETIME_FORMAT', 'Y-m-d H:i:s');

// إعدادات الأمان
define('SESSION_TIMEOUT', 3600);
define('PASSWORD_MIN_LENGTH', 6);
define('MAX_LOGIN_ATTEMPTS', 5);

// تم التثبيت
define('SYSTEM_INSTALLED', true);
define('INSTALL_DATE', '" . date('Y-m-d H:i:s') . "');
?>";
    
    file_put_contents('config/database_config.php', $config_content);
    
    $setup_steps[count($setup_steps)-1]['status'] = 'success';
    $setup_steps[count($setup_steps)-1]['message'] = 'تم إنشاء ملف التكوين';
    
    // الخطوة 4: اختبار الاتصال بقاعدة البيانات
    $setup_steps[] = array('step' => 'اختبار قاعدة البيانات', 'status' => 'running');
    
    try {
        if (class_exists('PDO')) {
            $dsn = "mysql:host={$auto_config['db_host']};charset=utf8mb4";
            $pdo = new PDO($dsn, $auto_config['db_user'], $auto_config['db_pass']);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // إنشاء قاعدة البيانات
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$auto_config['db_name']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            $pdo->exec("USE `{$auto_config['db_name']}`");
            
            $setup_steps[count($setup_steps)-1]['status'] = 'success';
            $setup_steps[count($setup_steps)-1]['message'] = 'تم إنشاء قاعدة البيانات بنجاح';
            
        } else {
            // استخدام MySQL التقليدي
            $connection = mysql_connect($auto_config['db_host'], $auto_config['db_user'], $auto_config['db_pass']);
            if (!$connection) {
                throw new Exception('فشل الاتصال بـ MySQL');
            }
            
            mysql_query("CREATE DATABASE IF NOT EXISTS `{$auto_config['db_name']}`", $connection);
            mysql_select_db($auto_config['db_name'], $connection);
            mysql_query("SET NAMES utf8", $connection);
            
            $setup_steps[count($setup_steps)-1]['status'] = 'success';
            $setup_steps[count($setup_steps)-1]['message'] = 'تم إنشاء قاعدة البيانات (MySQL)';
        }
        
    } catch (Exception $e) {
        // إذا فشل الاتصال، نستخدم الوضع التجريبي
        $setup_steps[count($setup_steps)-1]['status'] = 'warning';
        $setup_steps[count($setup_steps)-1]['message'] = 'سيتم استخدام الوضع التجريبي (بدون قاعدة بيانات)';
        
        // إنشاء ملف تكوين للوضع التجريبي
        $demo_config = str_replace("define('SYSTEM_INSTALLED', true);", "define('SYSTEM_INSTALLED', true);\ndefine('DEMO_MODE', true);", $config_content);
        file_put_contents('config/database_config.php', $demo_config);
    }
    
    // الخطوة 5: إنشاء الجداول (إذا كانت قاعدة البيانات متوفرة)
    if (!isset($e)) {
        $setup_steps[] = array('step' => 'إنشاء الجداول', 'status' => 'running');
        
        require_once 'config/database_config.php';
        require_once 'config/database_odoo.php';
        
        // تنفيذ ملف إنشاء الجداول
        if (file_exists('sql/odoo_database.sql')) {
            $sql_content = file_get_contents('sql/odoo_database.sql');
            $statements = explode(';', $sql_content);
            
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement) && !preg_match('/^(CREATE DATABASE|USE)/i', $statement)) {
                    try {
                        $database->query($statement);
                    } catch (Exception $ex) {
                        // تجاهل الأخطاء البسيطة مثل الجداول الموجودة
                        if (!strpos($ex->getMessage(), 'already exists')) {
                            error_log("SQL Error: " . $ex->getMessage());
                        }
                    }
                }
            }
        }
        
        $setup_steps[count($setup_steps)-1]['status'] = 'success';
        $setup_steps[count($setup_steps)-1]['message'] = 'تم إنشاء الجداول';
        
        // الخطوة 6: إدراج البيانات الأساسية
        $setup_steps[] = array('step' => 'إدراج البيانات الأساسية', 'status' => 'running');
        
        insertBasicData($database, $auto_config);
        
        $setup_steps[count($setup_steps)-1]['status'] = 'success';
        $setup_steps[count($setup_steps)-1]['message'] = 'تم إدراج البيانات الأساسية';
        
        // الخطوة 7: إنشاء المستخدم الإداري
        $setup_steps[] = array('step' => 'إنشاء المستخدم الإداري', 'status' => 'running');
        
        require_once 'models/BaseModel.php';
        require_once 'models/ResUsers.php';
        
        $user_model = new ResUsers();
        $admin_id = $user_model->create(array(
            'name' => $auto_config['admin_name'],
            'login' => $auto_config['admin_email'],
            'email' => $auto_config['admin_email'],
            'password' => $auto_config['admin_password'],
            'active' => true,
            'company_id' => 1,
            'groups_id' => json_encode(array(1))
        ));
        
        $setup_steps[count($setup_steps)-1]['status'] = 'success';
        $setup_steps[count($setup_steps)-1]['message'] = 'تم إنشاء المستخدم الإداري (ID: ' . $admin_id . ')';
    }
    
    // الخطوة الأخيرة: تسجيل دخول تلقائي
    $setup_steps[] = array('step' => 'تسجيل الدخول التلقائي', 'status' => 'running');
    
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = $auto_config['admin_name'];
    $_SESSION['email'] = $auto_config['admin_email'];
    $_SESSION['role'] = 'admin';
    $_SESSION['company_id'] = 1;
    $_SESSION['auto_setup'] = true;
    
    $setup_steps[count($setup_steps)-1]['status'] = 'success';
    $setup_steps[count($setup_steps)-1]['message'] = 'تم تسجيل الدخول تلقائياً';
    
    $success = true;
    
} catch (Exception $e) {
    $errors[] = $e->getMessage();
    if (!empty($setup_steps)) {
        $setup_steps[count($setup_steps)-1]['status'] = 'error';
        $setup_steps[count($setup_steps)-1]['message'] = $e->getMessage();
    }
}

/**
 * إدراج البيانات الأساسية
 */
function insertBasicData($database, $config) {
    $database->begin_transaction();
    
    try {
        // العملات
        $currencies = array(
            array('name' => 'SAR', 'symbol' => 'ر.س', 'rate' => 1.000000, 'active' => 1),
            array('name' => 'USD', 'symbol' => '$', 'rate' => 0.266667, 'active' => 1),
            array('name' => 'EUR', 'symbol' => '€', 'rate' => 0.240000, 'active' => 1)
        );
        
        foreach ($currencies as $currency) {
            try {
                $database->insert('res_currency', $currency);
            } catch (Exception $e) {
                // تجاهل إذا كانت موجودة
            }
        }
        
        // الشركة الافتراضية
        try {
            $database->insert('res_company', array(
                'name' => $config['company_name'],
                'display_name' => $config['company_name'],
                'code' => $config['company_code'],
                'country' => 'السعودية',
                'currency_id' => 1,
                'active' => 1,
                'sequence' => 1
            ));
        } catch (Exception $e) {
            // تجاهل إذا كانت موجودة
        }
        
        // فئات وحدات القياس
        $uom_categories = array(
            array('name' => 'الوحدة'),
            array('name' => 'الوزن'),
            array('name' => 'الطول'),
            array('name' => 'الحجم')
        );
        
        foreach ($uom_categories as $category) {
            try {
                $database->insert('uom_category', $category);
            } catch (Exception $e) {
                // تجاهل إذا كانت موجودة
            }
        }
        
        // وحدات القياس
        $uoms = array(
            array('name' => 'قطعة', 'category_id' => 1, 'factor' => 1.0, 'uom_type' => 'reference', 'active' => 1),
            array('name' => 'كيلوجرام', 'category_id' => 2, 'factor' => 1.0, 'uom_type' => 'reference', 'active' => 1),
            array('name' => 'متر', 'category_id' => 3, 'factor' => 1.0, 'uom_type' => 'reference', 'active' => 1),
            array('name' => 'لتر', 'category_id' => 4, 'factor' => 1.0, 'uom_type' => 'reference', 'active' => 1)
        );
        
        foreach ($uoms as $uom) {
            try {
                $database->insert('uom_uom', $uom);
            } catch (Exception $e) {
                // تجاهل إذا كانت موجودة
            }
        }
        
        // فئة المنتجات الافتراضية
        try {
            $database->insert('product_category', array(
                'name' => 'جميع المنتجات',
                'complete_name' => 'جميع المنتجات',
                'active' => 1,
                'company_id' => 1
            ));
        } catch (Exception $e) {
            // تجاهل إذا كانت موجودة
        }
        
        $database->commit();
        
    } catch (Exception $e) {
        $database->rollback();
        throw $e;
    }
}

// إعادة توجيه تلقائي بعد 3 ثوان إذا نجح التثبيت
if ($success && !isset($_GET['no_redirect'])) {
    header("Refresh: 3; url=demo.php");
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التثبيت التلقائي - نظام ERP المحاسبي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/odoo-style.css" rel="stylesheet">
    <style>
        .setup-step {
            transition: all 0.3s ease;
            margin-bottom: 1rem;
        }
        .step-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 1rem;
        }
        .step-running { background: #ffc107; color: white; }
        .step-success { background: #28a745; color: white; }
        .step-error { background: #dc3545; color: white; }
        .step-warning { background: #fd7e14; color: white; }
        
        .progress-bar-animated {
            animation: progress-bar-stripes 1s linear infinite;
        }
        
        @keyframes progress-bar-stripes {
            0% { background-position: 1rem 0; }
            100% { background-position: 0 0; }
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white text-center">
                        <h3 class="mb-0">
                            <i class="fas fa-magic me-2"></i>
                            التثبيت التلقائي الشامل
                        </h3>
                        <p class="mb-0 mt-2">نظام ERP المحاسبي - بأسلوب Odoo</p>
                    </div>
                    
                    <div class="card-body">
                        <?php if ($success): ?>
                            <div class="alert alert-success text-center">
                                <i class="fas fa-check-circle fa-3x mb-3"></i>
                                <h4>تم التثبيت بنجاح! 🎉</h4>
                                <p>سيتم توجيهك للنظام خلال 3 ثوان...</p>
                                <div class="progress mb-3">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 100%"></div>
                                </div>
                                <a href="demo.php" class="btn btn-success btn-lg">
                                    <i class="fas fa-arrow-right me-2"></i>
                                    دخول النظام الآن
                                </a>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($errors)): ?>
                            <div class="alert alert-danger">
                                <h5><i class="fas fa-exclamation-triangle me-2"></i>حدثت أخطاء:</h5>
                                <ul class="mb-0">
                                    <?php foreach ($errors as $error): ?>
                                        <li><?php echo $error; ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                        
                        <!-- خطوات التثبيت -->
                        <h5 class="mb-3">خطوات التثبيت:</h5>
                        <div class="setup-steps">
                            <?php foreach ($setup_steps as $index => $step): ?>
                                <div class="setup-step d-flex align-items-center">
                                    <div class="step-icon step-<?php echo $step['status']; ?>">
                                        <?php if ($step['status'] == 'running'): ?>
                                            <i class="fas fa-spinner fa-spin"></i>
                                        <?php elseif ($step['status'] == 'success'): ?>
                                            <i class="fas fa-check"></i>
                                        <?php elseif ($step['status'] == 'error'): ?>
                                            <i class="fas fa-times"></i>
                                        <?php else: ?>
                                            <i class="fas fa-exclamation"></i>
                                        <?php endif; ?>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1"><?php echo $step['step']; ?></h6>
                                        <?php if (isset($step['message'])): ?>
                                            <small class="text-muted"><?php echo $step['message']; ?></small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <?php if ($success): ?>
                            <!-- معلومات النظام -->
                            <div class="mt-4">
                                <h5>معلومات النظام:</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card bg-light">
                                            <div class="card-body">
                                                <h6><i class="fas fa-user me-2"></i>بيانات تسجيل الدخول</h6>
                                                <p class="mb-1"><strong>البريد:</strong> <?php echo $auto_config['admin_email']; ?></p>
                                                <p class="mb-0"><strong>كلمة المرور:</strong> <?php echo $auto_config['admin_password']; ?></p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card bg-light">
                                            <div class="card-body">
                                                <h6><i class="fas fa-building me-2"></i>معلومات الشركة</h6>
                                                <p class="mb-1"><strong>الاسم:</strong> <?php echo $auto_config['company_name']; ?></p>
                                                <p class="mb-0"><strong>الكود:</strong> <?php echo $auto_config['company_code']; ?></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- روابط سريعة -->
                            <div class="mt-4">
                                <h5>روابط سريعة:</h5>
                                <div class="btn-group-vertical w-100" role="group">
                                    <a href="demo.php" class="btn btn-primary">
                                        <i class="fas fa-home me-2"></i>الصفحة الرئيسية
                                    </a>
                                    <a href="pages/companies.php" class="btn btn-success">
                                        <i class="fas fa-building me-2"></i>إدارة الشركات
                                    </a>
                                    <a href="pages/customers.php" class="btn btn-info">
                                        <i class="fas fa-users me-2"></i>إدارة العملاء
                                    </a>
                                    <a href="test_database.php" class="btn btn-secondary">
                                        <i class="fas fa-vial me-2"></i>اختبار النظام
                                    </a>
                                </div>
                            </div>
                        <?php else: ?>
                            <!-- أزرار إعادة المحاولة -->
                            <div class="mt-4 text-center">
                                <a href="auto_setup.php" class="btn btn-primary">
                                    <i class="fas fa-redo me-2"></i>إعادة المحاولة
                                </a>
                                <a href="start.php" class="btn btn-secondary">
                                    <i class="fas fa-home me-2"></i>الصفحة الرئيسية
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة تأثير للخطوات
            const steps = document.querySelectorAll('.setup-step');
            steps.forEach((step, index) => {
                setTimeout(() => {
                    step.style.opacity = '1';
                    step.style.transform = 'translateX(0)';
                }, index * 200);
            });
            
            <?php if ($success): ?>
            // عداد تنازلي للتوجيه
            let countdown = 3;
            const updateCountdown = () => {
                const elements = document.querySelectorAll('.countdown');
                elements.forEach(el => el.textContent = countdown);
                
                if (countdown <= 0) {
                    window.location.href = 'demo.php';
                } else {
                    countdown--;
                    setTimeout(updateCountdown, 1000);
                }
            };
            
            // بدء العداد بعد ثانية واحدة
            setTimeout(updateCountdown, 1000);
            <?php endif; ?>
        });
    </script>
</body>
</html>
