<?php
/**
 * فحص حالة قاعدة البيانات
 * Database Status Check
 */

header('Content-Type: application/json; charset=utf-8');

$response = array(
    'status' => 'error',
    'message' => '',
    'database_exists' => false,
    'tables_count' => 0,
    'users_count' => 0,
    'can_connect' => false
);

try {
    // محاولة الاتصال بـ MySQL
    $pdo = new PDO("mysql:host=localhost", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $response['can_connect'] = true;
    
    // التحقق من وجود قاعدة البيانات
    $stmt = $pdo->query("SHOW DATABASES LIKE 'erp_accounting'");
    if ($stmt->rowCount() > 0) {
        $response['database_exists'] = true;
        
        // الاتصال بقاعدة البيانات
        $pdo = new PDO("mysql:host=localhost;dbname=erp_accounting", "root", "");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // عد الجداول
        $stmt = $pdo->query("SHOW TABLES");
        $response['tables_count'] = $stmt->rowCount();
        
        // عد المستخدمين
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $response['users_count'] = $result['count'];
            
            $response['status'] = 'success';
            $response['message'] = 'قاعدة البيانات جاهزة ومتصلة';
        } catch (PDOException $e) {
            $response['message'] = 'قاعدة البيانات موجودة لكن الجداول غير مكتملة';
        }
    } else {
        $response['message'] = 'قاعدة البيانات غير موجودة - تحتاج إعداد';
    }
    
} catch (PDOException $e) {
    $response['message'] = 'خطأ في الاتصال: ' . $e->getMessage();
}

echo json_encode($response, JSON_UNESCAPED_UNICODE);
?>
