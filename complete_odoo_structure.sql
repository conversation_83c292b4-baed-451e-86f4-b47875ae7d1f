-- Enable foreign key checks
SET FOREIGN_KEY_CHECKS = 0;

-- =============================================
-- Module: CRM
-- =============================================
CREATE TABLE IF NOT EXISTS `crm_lead` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(256) NOT NULL,
    `partner_id` INT,
    `email_from` VARCHAR(256),
    `phone` VARCHAR(32),
    `user_id` INT,
    `team_id` INT,
    `stage_id` INT,
    `probability` FLOAT,
    `expected_revenue` DECIMAL(16,2),
    `priority` VARCHAR(16),
    `description` TEXT,
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME,
    FOREIGN KEY (`partner_id`) REFERENCES `res_partner`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- =============================================
-- Module: Project
-- =============================================
CREATE TABLE IF NOT EXISTS `project_project` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(256) NOT NULL,
    `partner_id` INT,
    `user_id` INT,
    `date_start` DATE,
    `date` DATE,
    `description` TEXT,
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `project_task` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(256) NOT NULL,
    `project_id` INT,
    `user_id` INT,
    `partner_id` INT,
    `date_deadline` DATE,
    `description` TEXT,
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME,
    FOREIGN KEY (`project_id`) REFERENCES `project_project`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`partner_id`) REFERENCES `res_partner`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- =============================================
-- Module: Manufacturing
-- =============================================
CREATE TABLE IF NOT EXISTS `mrp_bom` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `code` VARCHAR(64),
    `product_tmpl_id` INT,
    `product_qty` DECIMAL(16,2) DEFAULT 1.0,
    `product_uom_id` INT,
    `type` VARCHAR(16) DEFAULT 'normal',
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME,
    FOREIGN KEY (`product_tmpl_id`) REFERENCES `product_template`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `mrp_production` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(64) NOT NULL,
    `product_id` INT,
    `product_qty` DECIMAL(16,2) NOT NULL,
    `product_uom_id` INT,
    `bom_id` INT,
    `state` VARCHAR(32) DEFAULT 'draft',
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME,
    FOREIGN KEY (`product_id`) REFERENCES `product_template`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`bom_id`) REFERENCES `mrp_bom`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- =============================================
-- Module: Point of Sale
-- =============================================
CREATE TABLE IF NOT EXISTS `pos_session` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(64) NOT NULL,
    `user_id` INT,
    `config_id` INT,
    `start_at` DATETIME,
    `stop_at` DATETIME,
    `state` VARCHAR(32) DEFAULT 'opened',
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- =============================================
-- Module: Website
-- =============================================
CREATE TABLE IF NOT EXISTS `website` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(256) NOT NULL,
    `domain` VARCHAR(256),
    `company_id` INT,
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- =============================================
-- Module: eCommerce
-- =============================================
CREATE TABLE IF NOT EXISTS `product_public_category` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(256) NOT NULL,
    `parent_id` INT,
    `sequence` INT,
    `website_id` INT,
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME,
    FOREIGN KEY (`parent_id`) REFERENCES `product_public_category`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- =============================================
-- Module: Helpdesk
-- =============================================
CREATE TABLE IF NOT EXISTS `helpdesk_ticket` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(256) NOT NULL,
    `description` TEXT,
    `partner_id` INT,
    `user_id` INT,
    `team_id` INT,
    `stage_id` INT,
    `priority` VARCHAR(16),
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME,
    FOREIGN KEY (`partner_id`) REFERENCES `res_partner`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- =============================================
-- Module: Timesheet
-- =============================================
CREATE TABLE IF NOT EXISTS `account_analytic_line` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(256) NOT NULL,
    `date` DATE,
    `unit_amount` DECIMAL(16,2),
    `employee_id` INT,
    `project_id` INT,
    `task_id` INT,
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME,
    FOREIGN KEY (`project_id`) REFERENCES `project_project`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`task_id`) REFERENCES `project_task`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- =============================================
-- Module: Fleet
-- =============================================
CREATE TABLE IF NOT EXISTS `fleet_vehicle` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(64) NOT NULL,
    `license_plate` VARCHAR(32),
    `model_id` INT,
    `driver_id` INT,
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- =============================================
-- Module: Events
-- =============================================
CREATE TABLE IF NOT EXISTS `event_event` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(256) NOT NULL,
    `date_begin` DATETIME,
    `date_end` DATETIME,
    `seats_max` INT,
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- =============================================
-- Module: Surveys
-- =============================================
CREATE TABLE IF NOT EXISTS `survey_survey` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `title` VARCHAR(256) NOT NULL,
    `description` TEXT,
    `active` BOOLEAN DEFAULT TRUE,
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- Create default website
INSERT INTO `website` (`name`, `domain`, `company_id`) VALUES
('My Website', 'localhost', 1);

-- Create default helpdesk team
INSERT INTO `crm_team` (`name`, `team_type`, `create_uid`, `create_date`) VALUES
('Helpdesk Team', 'helpdesk', 1, NOW());
