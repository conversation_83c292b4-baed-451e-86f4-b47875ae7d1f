<?php
/**
 * إعدادات المحاسبة المتقدمة بأسلوب Odoo
 * Advanced Accounting Configuration - Odoo Style
 */

class OdooAccountingConfig {
    private static $instance = null;
    private $settings = array();
    private $config_file = 'accounting_settings.json';
    
    private function __construct() {
        $this->loadSettings();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * تحميل الإعدادات
     */
    private function loadSettings() {
        $default_settings = array(
            // إعدادات الشركة
            'company' => array(
                'name' => 'شركة النظام المتقدم',
                'currency' => 'SAR',
                'country' => 'SA',
                'timezone' => 'Asia/Riyadh',
                'language' => 'ar',
                'fiscal_year_start' => '01-01',
                'fiscal_year_end' => '12-31'
            ),
            
            // إعدادات المحاسبة
            'accounting' => array(
                'decimal_precision' => 2,
                'currency_precision' => 2,
                'auto_reconcile' => true,
                'multi_currency' => true,
                'analytic_accounting' => false,
                'budget_management' => false,
                'asset_management' => false,
                'cost_center' => false,
                'lock_date' => null,
                'tax_lock_date' => null
            ),
            
            // إعدادات الفواتير
            'invoicing' => array(
                'invoice_policy' => 'order',
                'payment_terms' => 'immediate',
                'tax_calculation' => 'exclusive',
                'auto_send_invoice' => false,
                'invoice_sequence' => 'yearly',
                'credit_limit_check' => true,
                'discount_policy' => 'percentage'
            ),
            
            // إعدادات اليوميات
            'journals' => array(
                'sequence_type' => 'monthly',
                'auto_post' => false,
                'entry_sequence' => 'journal',
                'allow_cancel' => true,
                'check_chronology' => true
            ),
            
            // إعدادات التقارير
            'reports' => array(
                'default_currency' => 'SAR',
                'comparison_periods' => 3,
                'auto_refresh' => true,
                'export_format' => 'pdf',
                'email_reports' => false,
                'schedule_reports' => false
            ),
            
            // إعدادات الواجهة
            'interface' => array(
                'theme' => 'odoo',
                'sidebar_collapsed' => false,
                'table_page_size' => 50,
                'default_view' => 'list',
                'show_help_tips' => true,
                'auto_save' => true
            ),
            
            // إعدادات الأمان
            'security' => array(
                'session_timeout' => 3600,
                'password_policy' => 'medium',
                'two_factor_auth' => false,
                'audit_trail' => true,
                'backup_frequency' => 'daily'
            ),
            
            // إعدادات التكامل
            'integration' => array(
                'api_enabled' => false,
                'webhook_enabled' => false,
                'external_systems' => array(),
                'data_sync' => false
            )
        );
        
        // تحميل الإعدادات المحفوظة
        if (file_exists($this->config_file)) {
            $saved_settings = json_decode(file_get_contents($this->config_file), true);
            if ($saved_settings) {
                $this->settings = array_merge_recursive($default_settings, $saved_settings);
            } else {
                $this->settings = $default_settings;
            }
        } else {
            $this->settings = $default_settings;
        }
    }
    
    /**
     * حفظ الإعدادات
     */
    public function saveSettings() {
        $json = json_encode($this->settings, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        return file_put_contents($this->config_file, $json) !== false;
    }
    
    /**
     * الحصول على إعداد
     */
    public function get($key, $default = null) {
        $keys = explode('.', $key);
        $value = $this->settings;
        
        foreach ($keys as $k) {
            if (isset($value[$k])) {
                $value = $value[$k];
            } else {
                return $default;
            }
        }
        
        return $value;
    }
    
    /**
     * تعيين إعداد
     */
    public function set($key, $value) {
        $keys = explode('.', $key);
        $current = &$this->settings;
        
        foreach ($keys as $k) {
            if (!isset($current[$k])) {
                $current[$k] = array();
            }
            $current = &$current[$k];
        }
        
        $current = $value;
        return $this->saveSettings();
    }
    
    /**
     * الحصول على جميع الإعدادات
     */
    public function getAll() {
        return $this->settings;
    }
    
    /**
     * تحديث إعدادات متعددة
     */
    public function updateSettings($new_settings) {
        $this->settings = array_merge_recursive($this->settings, $new_settings);
        return $this->saveSettings();
    }
    
    /**
     * إعادة تعيين الإعدادات للافتراضية
     */
    public function resetToDefaults() {
        if (file_exists($this->config_file)) {
            unlink($this->config_file);
        }
        $this->loadSettings();
        return true;
    }
    
    /**
     * التحقق من صحة الإعدادات
     */
    public function validateSettings() {
        $errors = array();
        
        // التحقق من إعدادات الشركة
        if (empty($this->get('company.name'))) {
            $errors[] = 'اسم الشركة مطلوب';
        }
        
        if (empty($this->get('company.currency'))) {
            $errors[] = 'العملة الأساسية مطلوبة';
        }
        
        // التحقق من دقة الأرقام العشرية
        $decimal_precision = $this->get('accounting.decimal_precision');
        if (!is_numeric($decimal_precision) || $decimal_precision < 0 || $decimal_precision > 6) {
            $errors[] = 'دقة الأرقام العشرية يجب أن تكون بين 0 و 6';
        }
        
        // التحقق من تواريخ السنة المالية
        $fiscal_start = $this->get('company.fiscal_year_start');
        $fiscal_end = $this->get('company.fiscal_year_end');
        
        if (!$this->isValidDate($fiscal_start, 'm-d')) {
            $errors[] = 'تاريخ بداية السنة المالية غير صحيح';
        }
        
        if (!$this->isValidDate($fiscal_end, 'm-d')) {
            $errors[] = 'تاريخ نهاية السنة المالية غير صحيح';
        }
        
        return $errors;
    }
    
    /**
     * التحقق من صحة التاريخ
     */
    private function isValidDate($date, $format = 'Y-m-d') {
        $d = DateTime::createFromFormat($format, $date);
        return $d && $d->format($format) === $date;
    }
    
    /**
     * الحصول على إعدادات الجدول
     */
    public function getTableSettings() {
        return array(
            'page_size' => $this->get('interface.table_page_size', 50),
            'sortable' => true,
            'filterable' => true,
            'searchable' => true,
            'exportable' => true,
            'printable' => true,
            'column_control' => true,
            'font_control' => true,
            'auto_save' => $this->get('interface.auto_save', true)
        );
    }
    
    /**
     * الحصول على إعدادات التقارير
     */
    public function getReportSettings() {
        return array(
            'default_currency' => $this->get('reports.default_currency', 'SAR'),
            'comparison_periods' => $this->get('reports.comparison_periods', 3),
            'auto_refresh' => $this->get('reports.auto_refresh', true),
            'export_format' => $this->get('reports.export_format', 'pdf'),
            'decimal_precision' => $this->get('accounting.decimal_precision', 2)
        );
    }
    
    /**
     * الحصول على إعدادات الأمان
     */
    public function getSecuritySettings() {
        return array(
            'session_timeout' => $this->get('security.session_timeout', 3600),
            'password_policy' => $this->get('security.password_policy', 'medium'),
            'two_factor_auth' => $this->get('security.two_factor_auth', false),
            'audit_trail' => $this->get('security.audit_trail', true)
        );
    }
    
    /**
     * تصدير الإعدادات
     */
    public function exportSettings() {
        return json_encode($this->settings, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }
    
    /**
     * استيراد الإعدادات
     */
    public function importSettings($json_data) {
        $imported_settings = json_decode($json_data, true);
        
        if ($imported_settings === null) {
            throw new Exception('بيانات JSON غير صحيحة');
        }
        
        // التحقق من صحة الإعدادات المستوردة
        $backup = $this->settings;
        $this->settings = array_merge_recursive($this->settings, $imported_settings);
        
        $errors = $this->validateSettings();
        if (!empty($errors)) {
            $this->settings = $backup;
            throw new Exception('الإعدادات المستوردة غير صحيحة: ' . implode(', ', $errors));
        }
        
        return $this->saveSettings();
    }
    
    /**
     * الحصول على معلومات النظام
     */
    public function getSystemInfo() {
        return array(
            'version' => '2.0.0',
            'php_version' => PHP_VERSION,
            'mysql_version' => $this->getMySQLVersion(),
            'server_info' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'timezone' => date_default_timezone_get(),
            'locale' => setlocale(LC_ALL, 0)
        );
    }
    
    /**
     * الحصول على إصدار MySQL
     */
    private function getMySQLVersion() {
        try {
            require_once 'odoo_database.php';
            $db = OdooDatabase::getInstance();
            $result = $db->query("SELECT VERSION() as version");
            return $result[0]['version'] ?? 'Unknown';
        } catch (Exception $e) {
            return 'Unknown';
        }
    }
    
    /**
     * تحسين الأداء
     */
    public function optimizePerformance() {
        $optimizations = array();
        
        // تحسين إعدادات قاعدة البيانات
        $optimizations['database'] = array(
            'query_cache' => true,
            'index_optimization' => true,
            'connection_pooling' => true
        );
        
        // تحسين إعدادات الواجهة
        $optimizations['interface'] = array(
            'lazy_loading' => true,
            'compression' => true,
            'caching' => true
        );
        
        return $optimizations;
    }
    
    /**
     * إنشاء نسخة احتياطية من الإعدادات
     */
    public function createBackup() {
        $backup_file = 'accounting_settings_backup_' . date('Y-m-d_H-i-s') . '.json';
        $backup_data = array(
            'timestamp' => date('Y-m-d H:i:s'),
            'version' => '2.0.0',
            'settings' => $this->settings
        );
        
        $json = json_encode($backup_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        return file_put_contents($backup_file, $json) !== false ? $backup_file : false;
    }
    
    /**
     * استعادة من نسخة احتياطية
     */
    public function restoreFromBackup($backup_file) {
        if (!file_exists($backup_file)) {
            throw new Exception('ملف النسخة الاحتياطية غير موجود');
        }
        
        $backup_data = json_decode(file_get_contents($backup_file), true);
        
        if (!$backup_data || !isset($backup_data['settings'])) {
            throw new Exception('ملف النسخة الاحتياطية تالف');
        }
        
        $this->settings = $backup_data['settings'];
        return $this->saveSettings();
    }
}

// إنشاء مثيل عام
$odoo_accounting_config = OdooAccountingConfig::getInstance();
?>
