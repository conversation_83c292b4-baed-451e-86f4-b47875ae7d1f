<?php
/**
 * إعدادات قاعدة البيانات - بأسلوب Odoo
 * Database Configuration - Odoo Style
 */

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'erp_accounting');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// إعدادات النظام
define('SITE_URL', 'http://localhost/acc/');
define('SITE_NAME', 'نظام ERP المحاسبي');
define('CURRENCY', 'ر.س');
define('DATE_FORMAT', 'Y-m-d');
define('DATETIME_FORMAT', 'Y-m-d H:i:s');

// إعدادات الأمان
define('SESSION_TIMEOUT', 3600);
define('PASSWORD_MIN_LENGTH', 6);
define('MAX_LOGIN_ATTEMPTS', 5);

/**
 * كلاس قاعدة البيانات المحسن - بأسلوب Odoo
 */
class OdooDatabase {
    private $host = DB_HOST;
    private $db_name = DB_NAME;
    private $username = DB_USER;
    private $password = DB_PASS;
    public $conn;
    private $transaction_level = 0;
    private $in_transaction = false;
    
    /**
     * الاتصال بقاعدة البيانات
     */
    public function connect() {
        $this->conn = null;
        
        try {
            if (class_exists('PDO')) {
                $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . DB_CHARSET;
                $options = array(
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
                );
                
                $this->conn = new PDO($dsn, $this->username, $this->password, $options);
                
                // تعيين المنطقة الزمنية
                $this->conn->exec("SET time_zone = '+03:00'");
                
            } else {
                // استخدام MySQL التقليدي كبديل
                $this->conn = mysql_connect($this->host, $this->username, $this->password);
                if ($this->conn) {
                    mysql_select_db($this->db_name, $this->conn);
                    mysql_query("SET NAMES utf8mb4", $this->conn);
                }
            }
        } catch(Exception $e) {
            error_log("Database Connection Error: " . $e->getMessage());
            throw new Exception("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
        }
        
        return $this->conn;
    }
    
    /**
     * تنفيذ استعلام مع معاملات
     */
    public function query($sql, $params = array()) {
        if (!$this->conn) {
            $this->connect();
        }
        
        if (class_exists('PDO') && $this->conn instanceof PDO) {
            try {
                $stmt = $this->conn->prepare($sql);
                $stmt->execute($params);
                return $stmt;
            } catch(PDOException $e) {
                error_log("Database Query Error: " . $e->getMessage() . " SQL: " . $sql);
                throw new Exception("خطأ في تنفيذ الاستعلام: " . $e->getMessage());
            }
        } else {
            // MySQL التقليدي
            $result = mysql_query($sql, $this->conn);
            if (!$result) {
                error_log("MySQL Error: " . mysql_error($this->conn) . " SQL: " . $sql);
                throw new Exception("خطأ في تنفيذ الاستعلام: " . mysql_error($this->conn));
            }
            return $result;
        }
    }
    
    /**
     * جلب سجل واحد
     */
    public function fetch($sql, $params = array()) {
        $stmt = $this->query($sql, $params);
        
        if (class_exists('PDO') && $this->conn instanceof PDO) {
            return $stmt ? $stmt->fetch(PDO::FETCH_ASSOC) : false;
        } else {
            return $stmt ? mysql_fetch_assoc($stmt) : false;
        }
    }
    
    /**
     * جلب جميع السجلات
     */
    public function fetchAll($sql, $params = array()) {
        $stmt = $this->query($sql, $params);
        
        if (class_exists('PDO') && $this->conn instanceof PDO) {
            return $stmt ? $stmt->fetchAll(PDO::FETCH_ASSOC) : array();
        } else {
            $rows = array();
            if ($stmt) {
                while ($row = mysql_fetch_assoc($stmt)) {
                    $rows[] = $row;
                }
            }
            return $rows;
        }
    }
    
    /**
     * إدراج سجل جديد - بأسلوب Odoo
     */
    public function insert($table, $data) {
        if (empty($data)) {
            throw new Exception("لا يمكن إدراج بيانات فارغة");
        }
        
        // تنظيف أسماء الأعمدة
        $columns = array_keys($data);
        $placeholders = array_fill(0, count($columns), '?');
        
        $sql = "INSERT INTO `{$table}` (`" . implode('`, `', $columns) . "`) VALUES (" . implode(', ', $placeholders) . ")";
        
        $stmt = $this->query($sql, array_values($data));
        
        if (class_exists('PDO') && $this->conn instanceof PDO) {
            return $this->conn->lastInsertId();
        } else {
            return mysql_insert_id($this->conn);
        }
    }
    
    /**
     * تحديث سجل - بأسلوب Odoo
     */
    public function update($table, $data, $where, $where_params = array()) {
        if (empty($data)) {
            throw new Exception("لا يمكن تحديث بيانات فارغة");
        }
        
        $set_clauses = array();
        $params = array();
        
        foreach ($data as $column => $value) {
            $set_clauses[] = "`{$column}` = ?";
            $params[] = $value;
        }
        
        // إضافة معاملات WHERE
        $params = array_merge($params, $where_params);
        
        $sql = "UPDATE `{$table}` SET " . implode(', ', $set_clauses) . " WHERE {$where}";
        
        $stmt = $this->query($sql, $params);
        
        if (class_exists('PDO') && $this->conn instanceof PDO) {
            return $stmt->rowCount();
        } else {
            return mysql_affected_rows($this->conn);
        }
    }
    
    /**
     * حذف سجل - بأسلوب Odoo
     */
    public function delete($table, $where, $where_params = array()) {
        $sql = "DELETE FROM `{$table}` WHERE {$where}";
        
        $stmt = $this->query($sql, $where_params);
        
        if (class_exists('PDO') && $this->conn instanceof PDO) {
            return $stmt->rowCount();
        } else {
            return mysql_affected_rows($this->conn);
        }
    }
    
    /**
     * عد السجلات
     */
    public function count($table, $where = '1=1', $where_params = array()) {
        $sql = "SELECT COUNT(*) as count FROM `{$table}` WHERE {$where}";
        $result = $this->fetch($sql, $where_params);
        return $result ? intval($result['count']) : 0;
    }
    
    /**
     * بدء معاملة
     */
    public function begin_transaction() {
        if (!$this->in_transaction) {
            if (class_exists('PDO') && $this->conn instanceof PDO) {
                $this->conn->beginTransaction();
            } else {
                $this->query("START TRANSACTION");
            }
            $this->in_transaction = true;
        }
        $this->transaction_level++;
    }
    
    /**
     * تأكيد المعاملة
     */
    public function commit() {
        if ($this->transaction_level > 0) {
            $this->transaction_level--;
            
            if ($this->transaction_level === 0 && $this->in_transaction) {
                if (class_exists('PDO') && $this->conn instanceof PDO) {
                    $this->conn->commit();
                } else {
                    $this->query("COMMIT");
                }
                $this->in_transaction = false;
            }
        }
    }
    
    /**
     * إلغاء المعاملة
     */
    public function rollback() {
        if ($this->in_transaction) {
            if (class_exists('PDO') && $this->conn instanceof PDO) {
                $this->conn->rollBack();
            } else {
                $this->query("ROLLBACK");
            }
            $this->in_transaction = false;
            $this->transaction_level = 0;
        }
    }
    
    /**
     * اقتباس القيمة
     */
    public function quote($value) {
        if (class_exists('PDO') && $this->conn instanceof PDO) {
            return $this->conn->quote($value);
        } else {
            return "'" . mysql_real_escape_string($value, $this->conn) . "'";
        }
    }
    
    /**
     * الحصول على آخر خطأ
     */
    public function getLastError() {
        if (class_exists('PDO') && $this->conn instanceof PDO) {
            $error = $this->conn->errorInfo();
            return $error[2];
        } else {
            return mysql_error($this->conn);
        }
    }
    
    /**
     * إغلاق الاتصال
     */
    public function close() {
        if ($this->in_transaction) {
            $this->rollback();
        }
        
        if (class_exists('PDO') && $this->conn instanceof PDO) {
            $this->conn = null;
        } else {
            if ($this->conn) {
                mysql_close($this->conn);
            }
        }
    }
    
    /**
     * تدمير الكائن
     */
    public function __destruct() {
        $this->close();
    }
}

// إنشاء اتصال عام
$database = new OdooDatabase();
$db = $database->connect();

/**
 * دوال مساعدة محسنة
 */
function getDB() {
    global $database;
    return $database;
}

function sanitize($data) {
    return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
}

function hashPassword($password) {
    if (function_exists('password_hash')) {
        return password_hash($password, PASSWORD_DEFAULT);
    } else {
        return md5($password . 'erp_salt_key_2024');
    }
}

function verifyPassword($password, $hash) {
    if (function_exists('password_verify')) {
        return password_verify($password, $hash);
    } else {
        return md5($password . 'erp_salt_key_2024') === $hash;
    }
}

function formatDate($date, $format = DATE_FORMAT) {
    if (empty($date)) return '';
    return date($format, strtotime($date));
}

function formatDateTime($datetime, $format = DATETIME_FORMAT) {
    if (empty($datetime)) return '';
    return date($format, strtotime($datetime));
}

function formatCurrency($amount, $currency = CURRENCY) {
    return number_format(floatval($amount), 2) . ' ' . $currency;
}

function generateToken($length = 32) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $token = '';
    for ($i = 0; $i < $length; $i++) {
        $token .= $characters[rand(0, strlen($characters) - 1)];
    }
    return $token;
}

/**
 * دالة تنفيذ SQL من ملف
 */
function executeSQLFile($filename) {
    global $database;
    
    if (!file_exists($filename)) {
        throw new Exception("ملف SQL غير موجود: " . $filename);
    }
    
    $sql = file_get_contents($filename);
    $statements = explode(';', $sql);
    
    $database->begin_transaction();
    
    try {
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement)) {
                $database->query($statement);
            }
        }
        $database->commit();
        return true;
    } catch (Exception $e) {
        $database->rollback();
        throw $e;
    }
}
?>
