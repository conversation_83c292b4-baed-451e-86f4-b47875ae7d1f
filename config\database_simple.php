<?php
/**
 * إعدادات قاعدة البيانات المبسطة
 * متوافقة مع PHP 5.2
 */

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'erp_accounting');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8');

// إعدادات النظام
define('SITE_URL', 'http://localhost:8000/');
define('SITE_NAME', 'نظام ERP المحاسبي');
define('CURRENCY', 'ر.س');
define('DATE_FORMAT', 'Y-m-d');
define('DATETIME_FORMAT', 'Y-m-d H:i:s');

// إعدادات الأمان
define('SESSION_TIMEOUT', 3600);
define('PASSWORD_MIN_LENGTH', 6);
define('MAX_LOGIN_ATTEMPTS', 5);

// متغير الاتصال العام
$db_connection = null;

/**
 * الاتصال بقاعدة البيانات
 */
function getConnection() {
    global $db_connection;
    
    if ($db_connection === null) {
        $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
        
        if (class_exists('PDO')) {
            $db_connection = new PDO($dsn, DB_USER, DB_PASS);
            $db_connection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $db_connection->exec("SET NAMES utf8");
        } else {
            // استخدام mysql_connect للإصدارات القديمة
            $db_connection = mysql_connect(DB_HOST, DB_USER, DB_PASS);
            mysql_select_db(DB_NAME, $db_connection);
            mysql_query("SET NAMES utf8", $db_connection);
        }
    }
    
    return $db_connection;
}

/**
 * تنفيذ استعلام
 */
function executeQuery($sql, $params = array()) {
    $conn = getConnection();
    
    if (class_exists('PDO')) {
        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    } else {
        // للإصدارات القديمة
        return mysql_query($sql, $conn);
    }
}

/**
 * جلب سجل واحد
 */
function fetchRow($sql, $params = array()) {
    $result = executeQuery($sql, $params);
    
    if (class_exists('PDO')) {
        return $result ? $result->fetch(PDO::FETCH_ASSOC) : false;
    } else {
        return mysql_fetch_assoc($result);
    }
}

/**
 * جلب جميع السجلات
 */
function fetchAll($sql, $params = array()) {
    $result = executeQuery($sql, $params);
    $rows = array();
    
    if (class_exists('PDO')) {
        if ($result) {
            while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
                $rows[] = $row;
            }
        }
    } else {
        while ($row = mysql_fetch_assoc($result)) {
            $rows[] = $row;
        }
    }
    
    return $rows;
}

/**
 * تنظيف البيانات
 */
function sanitize($data) {
    return htmlspecialchars(strip_tags(trim($data)));
}

/**
 * تشفير كلمة المرور
 */
function hashPassword($password) {
    if (function_exists('password_hash')) {
        return password_hash($password, PASSWORD_DEFAULT);
    } else {
        return md5($password . 'salt_key_here');
    }
}

/**
 * التحقق من كلمة المرور
 */
function verifyPassword($password, $hash) {
    if (function_exists('password_verify')) {
        return password_verify($password, $hash);
    } else {
        return md5($password . 'salt_key_here') === $hash;
    }
}

/**
 * توليد رمز عشوائي
 */
function generateToken($length = 32) {
    if (function_exists('random_bytes')) {
        return bin2hex(random_bytes($length));
    } else {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $token = '';
        for ($i = 0; $i < $length; $i++) {
            $token .= $characters[rand(0, strlen($characters) - 1)];
        }
        return $token;
    }
}

/**
 * تنسيق التاريخ
 */
function formatDate($date, $format = DATE_FORMAT) {
    return date($format, strtotime($date));
}

/**
 * تنسيق المبلغ
 */
function formatCurrency($amount) {
    return number_format($amount, 2) . ' ' . CURRENCY;
}

/**
 * إنشاء قاعدة البيانات إذا لم تكن موجودة
 */
function createDatabaseIfNotExists() {
    // محاولة الاتصال بدون تحديد قاعدة البيانات
    $dsn = "mysql:host=" . DB_HOST . ";charset=" . DB_CHARSET;
    
    if (class_exists('PDO')) {
        $conn = new PDO($dsn, DB_USER, DB_PASS);
        $conn->exec("CREATE DATABASE IF NOT EXISTS " . DB_NAME . " CHARACTER SET utf8 COLLATE utf8_general_ci");
        $conn = null;
    } else {
        $conn = mysql_connect(DB_HOST, DB_USER, DB_PASS);
        mysql_query("CREATE DATABASE IF NOT EXISTS " . DB_NAME, $conn);
        mysql_close($conn);
    }
}

/**
 * التحقق من وجود الجداول
 */
function tablesExist() {
    $sql = "SHOW TABLES LIKE 'users'";
    $result = fetchRow($sql);
    return $result !== false;
}

// إنشاء قاعدة البيانات إذا لم تكن موجودة
if (!tablesExist()) {
    createDatabaseIfNotExists();
}
?>
