<?php
/**
 * إعدادات النظام بأسلوب Odoo
 * Odoo-Style System Configuration
 */

// منع التحميل المتكرر
if (defined('ODOO_CONFIG_LOADED')) {
    return;
}
define('ODOO_CONFIG_LOADED', true);

// إعدادات قاعدة البيانات
if (!defined('ODOO_DB_HOST')) {
    define('ODOO_DB_HOST', 'localhost');
    define('ODOO_DB_NAME', 'odoo_erp_accounting');
    define('ODOO_DB_USER', 'root');
    define('ODOO_DB_PASS', '');
    define('ODOO_DB_CHARSET', 'utf8mb4');
    define('ODOO_DB_PORT', 3306);
}

// إعدادات النظام الأساسية
if (!defined('ODOO_SITE_NAME')) {
    define('ODOO_SITE_NAME', 'نظام ERP المحاسبي - بأسلوب Odoo');
    define('ODOO_SITE_URL', 'http://localhost/acc/');
    define('ODOO_VERSION', '17.0');
    define('ODOO_SYSTEM_VERSION', '1.0.0');
    define('ODOO_CURRENCY', 'SAR');
    define('ODOO_CURRENCY_SYMBOL', 'ر.س');
    define('ODOO_LANGUAGE', 'ar_SA');
    define('ODOO_TIMEZONE', 'Asia/Riyadh');
}

// إعدادات التاريخ والوقت
if (!defined('ODOO_DATE_FORMAT')) {
    define('ODOO_DATE_FORMAT', 'Y-m-d');
    define('ODOO_DATETIME_FORMAT', 'Y-m-d H:i:s');
    define('ODOO_TIME_FORMAT', 'H:i:s');
}

// إعدادات الأمان
if (!defined('ODOO_SESSION_TIMEOUT')) {
    define('ODOO_SESSION_TIMEOUT', 3600);
    define('ODOO_PASSWORD_MIN_LENGTH', 6);
    define('ODOO_MAX_LOGIN_ATTEMPTS', 5);
    define('ODOO_SECRET_KEY', 'odoo_erp_secret_key_2024');
    define('ODOO_CSRF_PROTECTION', true);
}

// إعدادات الملفات
if (!defined('ODOO_UPLOAD_MAX_SIZE')) {
    define('ODOO_UPLOAD_MAX_SIZE', 32 * 1024 * 1024); // 32MB
    define('ODOO_UPLOAD_PATH', 'filestore/');
    define('ODOO_TEMP_PATH', 'tmp/');
    define('ODOO_LOG_PATH', 'logs/');
    define('ODOO_CACHE_PATH', 'cache/');
    define('ODOO_SESSION_PATH', 'sessions/');
}

// إعدادات الوحدات
if (!defined('ODOO_ADDONS_PATH')) {
    define('ODOO_ADDONS_PATH', 'addons/');
    define('ODOO_MODULES_AUTO_INSTALL', true);
    define('ODOO_MODULES_UPDATE_ON_START', false);
}

// إعدادات التقارير
if (!defined('ODOO_REPORT_FORMAT')) {
    define('ODOO_REPORT_FORMAT', 'pdf');
    define('ODOO_REPORT_PAGE_SIZE', 'A4');
    define('ODOO_REPORT_FONT', 'Arial');
    define('ODOO_REPORT_FONT_SIZE', 12);
}

// إعدادات البريد الإلكتروني
if (!defined('ODOO_SMTP_SERVER')) {
    define('ODOO_SMTP_SERVER', 'localhost');
    define('ODOO_SMTP_PORT', 587);
    define('ODOO_SMTP_USER', '');
    define('ODOO_SMTP_PASS', '');
    define('ODOO_SMTP_ENCRYPTION', 'tls');
    define('ODOO_DEFAULT_FROM_EMAIL', 'noreply@localhost');
}

// إعدادات التخزين المؤقت
if (!defined('ODOO_CACHE_ENABLED')) {
    define('ODOO_CACHE_ENABLED', true);
    define('ODOO_CACHE_TYPE', 'file');
    define('ODOO_CACHE_TIMEOUT', 3600);
    define('ODOO_CACHE_PREFIX', 'odoo_erp_');
}

// إعدادات السجلات
if (!defined('ODOO_LOG_ENABLED')) {
    define('ODOO_LOG_ENABLED', true);
    define('ODOO_LOG_LEVEL', 'INFO');
    define('ODOO_LOG_FILE', 'logs/odoo_system.log');
    define('ODOO_LOG_MAX_SIZE', 10 * 1024 * 1024); // 10MB
    define('ODOO_LOG_BACKUP_COUNT', 5);
}

// إعدادات النسخ الاحتياطي
if (!defined('ODOO_BACKUP_ENABLED')) {
    define('ODOO_BACKUP_ENABLED', true);
    define('ODOO_BACKUP_AUTO', true);
    define('ODOO_BACKUP_INTERVAL', 24); // ساعات
    define('ODOO_BACKUP_KEEP', 7); // عدد النسخ
    define('ODOO_BACKUP_PATH', 'backups/');
    define('ODOO_BACKUP_COMPRESS', true);
}

// إعدادات الوضع التجريبي
if (!defined('ODOO_DEMO_MODE')) {
    define('ODOO_DEMO_MODE', true);
    define('ODOO_DEMO_DATA_ENABLED', true);
    define('ODOO_DEMO_USERS_ENABLED', true);
}

// إعدادات الأداء
if (!defined('ODOO_MEMORY_LIMIT')) {
    define('ODOO_MEMORY_LIMIT', '256M');
    define('ODOO_MAX_EXECUTION_TIME', 300);
    define('ODOO_MAX_INPUT_VARS', 3000);
}

// مسارات النظام
if (!defined('ODOO_ROOT_PATH')) {
    define('ODOO_ROOT_PATH', dirname(dirname(__FILE__)) . '/');
    define('ODOO_CONFIG_PATH', ODOO_ROOT_PATH . 'config/');
    define('ODOO_INCLUDES_PATH', ODOO_ROOT_PATH . 'includes/');
    define('ODOO_MODELS_PATH', ODOO_ROOT_PATH . 'models/');
    define('ODOO_PAGES_PATH', ODOO_ROOT_PATH . 'pages/');
    define('ODOO_ASSETS_PATH', ODOO_ROOT_PATH . 'assets/');
}

// الوحدات الأساسية
$ODOO_CORE_MODULES = array(
    'base' => array(
        'name' => 'الوحدة الأساسية',
        'version' => '1.0.0',
        'depends' => array(),
        'auto_install' => true,
        'models' => array('res.company', 'res.users', 'res.partner', 'res.currency')
    ),
    'account' => array(
        'name' => 'المحاسبة',
        'version' => '1.0.0',
        'depends' => array('base'),
        'auto_install' => true,
        'models' => array('account.account', 'account.move', 'account.move.line')
    ),
    'sale' => array(
        'name' => 'المبيعات',
        'version' => '1.0.0',
        'depends' => array('base', 'account'),
        'auto_install' => true,
        'models' => array('sale.order', 'sale.order.line')
    ),
    'purchase' => array(
        'name' => 'المشتريات',
        'version' => '1.0.0',
        'depends' => array('base', 'account'),
        'auto_install' => true,
        'models' => array('purchase.order', 'purchase.order.line')
    ),
    'stock' => array(
        'name' => 'المخزون',
        'version' => '1.0.0',
        'depends' => array('base'),
        'auto_install' => true,
        'models' => array('stock.location', 'stock.move', 'stock.picking')
    )
);

// مجموعات المستخدمين
$ODOO_USER_GROUPS = array(
    'admin' => array(
        'name' => 'مدير النظام',
        'permissions' => array('read', 'write', 'create', 'unlink', 'admin'),
        'modules' => array('base', 'account', 'sale', 'purchase', 'stock')
    ),
    'manager' => array(
        'name' => 'مدير',
        'permissions' => array('read', 'write', 'create', 'unlink'),
        'modules' => array('base', 'account', 'sale', 'purchase', 'stock')
    ),
    'user' => array(
        'name' => 'مستخدم',
        'permissions' => array('read', 'write', 'create'),
        'modules' => array('base', 'sale', 'purchase')
    ),
    'readonly' => array(
        'name' => 'قراءة فقط',
        'permissions' => array('read'),
        'modules' => array('base')
    )
);

// أنواع الحقول
$ODOO_FIELD_TYPES = array(
    'char' => 'نص',
    'text' => 'نص طويل',
    'integer' => 'رقم صحيح',
    'float' => 'رقم عشري',
    'boolean' => 'صح/خطأ',
    'date' => 'تاريخ',
    'datetime' => 'تاريخ ووقت',
    'selection' => 'اختيار',
    'many2one' => 'علاقة واحد لكثير',
    'one2many' => 'علاقة كثير لواحد',
    'many2many' => 'علاقة كثير لكثير',
    'binary' => 'ملف',
    'html' => 'HTML',
    'monetary' => 'مبلغ مالي'
);

// حالات السجلات
$ODOO_RECORD_STATES = array(
    'draft' => 'مسودة',
    'confirmed' => 'مؤكد',
    'done' => 'منجز',
    'cancelled' => 'ملغي'
);

// دالة للحصول على إعداد
function odoo_get_config($key, $default = null) {
    $constant_name = 'ODOO_' . strtoupper($key);
    return defined($constant_name) ? constant($constant_name) : $default;
}

// دالة لتعيين إعداد
function odoo_set_config($key, $value) {
    $constant_name = 'ODOO_' . strtoupper($key);
    if (!defined($constant_name)) {
        define($constant_name, $value);
        return true;
    }
    return false;
}

// دالة للحصول على مسار
function odoo_get_path($type) {
    switch ($type) {
        case 'root': return ODOO_ROOT_PATH;
        case 'config': return ODOO_CONFIG_PATH;
        case 'includes': return ODOO_INCLUDES_PATH;
        case 'models': return ODOO_MODELS_PATH;
        case 'pages': return ODOO_PAGES_PATH;
        case 'assets': return ODOO_ASSETS_PATH;
        case 'filestore': return ODOO_ROOT_PATH . ODOO_UPLOAD_PATH;
        case 'logs': return ODOO_ROOT_PATH . ODOO_LOG_PATH;
        case 'cache': return ODOO_ROOT_PATH . ODOO_CACHE_PATH;
        case 'tmp': return ODOO_ROOT_PATH . ODOO_TEMP_PATH;
        default: return ODOO_ROOT_PATH;
    }
}

// دالة لإنشاء المجلدات المطلوبة
function odoo_create_directories() {
    $directories = array(
        odoo_get_path('filestore'),
        odoo_get_path('logs'),
        odoo_get_path('cache'),
        odoo_get_path('tmp'),
        ODOO_ROOT_PATH . ODOO_SESSION_PATH,
        ODOO_ROOT_PATH . ODOO_BACKUP_PATH,
        ODOO_ROOT_PATH . ODOO_ADDONS_PATH
    );
    
    foreach ($directories as $dir) {
        if (!file_exists($dir)) {
            @mkdir($dir, 0755, true);
        }
    }
}

// إنشاء المجلدات عند التحميل
odoo_create_directories();

// تسجيل تحميل التكوين
if (ODOO_LOG_ENABLED) {
    $log_message = date(ODOO_DATETIME_FORMAT) . " - Odoo Configuration Loaded\n";
    @file_put_contents(ODOO_ROOT_PATH . ODOO_LOG_FILE, $log_message, FILE_APPEND | LOCK_EX);
}
?>
