<?php
/**
 * كلاس قاعدة البيانات بأسلوب Odoo
 * Odoo-Style Database Class
 */

// تحميل التكوين
require_once dirname(__FILE__) . '/odoo_config.php';

class OdooDatabase {
    private static $instance = null;
    private $connection = null;
    private $in_transaction = false;
    private $demo_mode = false;
    
    private function __construct() {
        $this->connect();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * الاتصال بقاعدة البيانات
     */
    private function connect() {
        try {
            // محاولة الاتصال بـ PDO أولاً
            if (class_exists('PDO')) {
                $this->connectPDO();
            } else {
                // استخدام MySQL التقليدي
                $this->connectMySQL();
            }
        } catch (Exception $e) {
            // في حالة فشل الاتصال، تفعيل الوضع التجريبي
            $this->demo_mode = true;
            $this->logError("Database connection failed, switching to demo mode: " . $e->getMessage());
        }
    }
    
    /**
     * الاتصال باستخدام PDO
     */
    private function connectPDO() {
        try {
            // محاولة الاتصال بالخادم أولاً
            $dsn = "mysql:host=" . ODOO_DB_HOST . ";port=" . ODOO_DB_PORT . ";charset=" . ODOO_DB_CHARSET;
            $this->connection = new PDO($dsn, ODOO_DB_USER, ODOO_DB_PASS, array(
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . ODOO_DB_CHARSET
            ));
            
            // إنشاء قاعدة البيانات إذا لم تكن موجودة
            $this->connection->exec("CREATE DATABASE IF NOT EXISTS `" . ODOO_DB_NAME . "` CHARACTER SET " . ODOO_DB_CHARSET . " COLLATE " . ODOO_DB_CHARSET . "_unicode_ci");
            
            // الاتصال بقاعدة البيانات
            $this->connection->exec("USE `" . ODOO_DB_NAME . "`");
            
            // إنشاء الجداول الأساسية
            $this->createBasicTables();
            
            $this->logInfo("Connected to database successfully using PDO");
            
        } catch (PDOException $e) {
            throw new Exception("PDO Connection Error: " . $e->getMessage());
        }
    }
    
    /**
     * الاتصال باستخدام MySQL التقليدي
     */
    private function connectMySQL() {
        if (!function_exists('mysql_connect') && !function_exists('mysqli_connect')) {
            throw new Exception("No MySQL extension available");
        }
        
        // استخدام MySQLi إذا كان متوفراً
        if (function_exists('mysqli_connect')) {
            $this->connection = mysqli_connect(ODOO_DB_HOST, ODOO_DB_USER, ODOO_DB_PASS, '', ODOO_DB_PORT);
            if (!$this->connection) {
                throw new Exception("MySQLi Connection Error: " . mysqli_connect_error());
            }
            
            // إنشاء قاعدة البيانات
            mysqli_query($this->connection, "CREATE DATABASE IF NOT EXISTS `" . ODOO_DB_NAME . "` CHARACTER SET " . ODOO_DB_CHARSET);
            mysqli_select_db($this->connection, ODOO_DB_NAME);
            mysqli_set_charset($this->connection, ODOO_DB_CHARSET);
            
        } else {
            // استخدام MySQL القديم
            $this->connection = mysql_connect(ODOO_DB_HOST . ':' . ODOO_DB_PORT, ODOO_DB_USER, ODOO_DB_PASS);
            if (!$this->connection) {
                throw new Exception("MySQL Connection Error: " . mysql_error());
            }
            
            mysql_query("CREATE DATABASE IF NOT EXISTS `" . ODOO_DB_NAME . "`", $this->connection);
            mysql_select_db(ODOO_DB_NAME, $this->connection);
            mysql_query("SET NAMES " . ODOO_DB_CHARSET, $this->connection);
        }
        
        $this->createBasicTables();
        $this->logInfo("Connected to database successfully using MySQL");
    }
    
    /**
     * إنشاء الجداول الأساسية
     */
    private function createBasicTables() {
        $tables = array(
            // جدول الشركات
            "CREATE TABLE IF NOT EXISTS res_company (
                id INT PRIMARY KEY AUTO_INCREMENT,
                name VARCHAR(255) NOT NULL,
                display_name VARCHAR(255),
                code VARCHAR(10),
                email VARCHAR(100),
                phone VARCHAR(20),
                website VARCHAR(255),
                country VARCHAR(100) DEFAULT 'السعودية',
                city VARCHAR(100),
                street VARCHAR(255),
                currency_id INT DEFAULT 1,
                active BOOLEAN DEFAULT TRUE,
                sequence INT DEFAULT 10,
                create_uid INT DEFAULT 1,
                write_uid INT DEFAULT 1,
                create_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                write_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_company_active (active),
                INDEX idx_company_code (code)
            ) ENGINE=InnoDB DEFAULT CHARSET=" . ODOO_DB_CHARSET,
            
            // جدول المستخدمين
            "CREATE TABLE IF NOT EXISTS res_users (
                id INT PRIMARY KEY AUTO_INCREMENT,
                name VARCHAR(255) NOT NULL,
                login VARCHAR(100) UNIQUE NOT NULL,
                email VARCHAR(100),
                password VARCHAR(255) NOT NULL,
                active BOOLEAN DEFAULT TRUE,
                company_id INT DEFAULT 1,
                groups_id TEXT,
                last_login TIMESTAMP NULL,
                login_attempts INT DEFAULT 0,
                create_uid INT DEFAULT 1,
                write_uid INT DEFAULT 1,
                create_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                write_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_users_login (login),
                INDEX idx_users_active (active),
                FOREIGN KEY (company_id) REFERENCES res_company(id)
            ) ENGINE=InnoDB DEFAULT CHARSET=" . ODOO_DB_CHARSET,
            
            // جدول الشركاء
            "CREATE TABLE IF NOT EXISTS res_partner (
                id INT PRIMARY KEY AUTO_INCREMENT,
                name VARCHAR(255) NOT NULL,
                display_name VARCHAR(255),
                email VARCHAR(100),
                phone VARCHAR(20),
                mobile VARCHAR(20),
                website VARCHAR(255),
                customer_rank INT DEFAULT 0,
                supplier_rank INT DEFAULT 0,
                is_company BOOLEAN DEFAULT FALSE,
                parent_id INT NULL,
                country VARCHAR(100) DEFAULT 'السعودية',
                city VARCHAR(100),
                street VARCHAR(255),
                zip VARCHAR(20),
                active BOOLEAN DEFAULT TRUE,
                company_id INT DEFAULT 1,
                create_uid INT DEFAULT 1,
                write_uid INT DEFAULT 1,
                create_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                write_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_partner_customer (customer_rank),
                INDEX idx_partner_supplier (supplier_rank),
                INDEX idx_partner_active (active),
                FOREIGN KEY (company_id) REFERENCES res_company(id),
                FOREIGN KEY (parent_id) REFERENCES res_partner(id)
            ) ENGINE=InnoDB DEFAULT CHARSET=" . ODOO_DB_CHARSET,
            
            // جدول العملات
            "CREATE TABLE IF NOT EXISTS res_currency (
                id INT PRIMARY KEY AUTO_INCREMENT,
                name VARCHAR(10) NOT NULL,
                symbol VARCHAR(10) NOT NULL,
                rate DECIMAL(12,6) DEFAULT 1.000000,
                active BOOLEAN DEFAULT TRUE,
                create_uid INT DEFAULT 1,
                write_uid INT DEFAULT 1,
                create_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                write_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_currency_active (active)
            ) ENGINE=InnoDB DEFAULT CHARSET=" . ODOO_DB_CHARSET,
            
            // جدول فئات وحدات القياس
            "CREATE TABLE IF NOT EXISTS uom_category (
                id INT PRIMARY KEY AUTO_INCREMENT,
                name VARCHAR(255) NOT NULL,
                create_uid INT DEFAULT 1,
                write_uid INT DEFAULT 1,
                create_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                write_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=" . ODOO_DB_CHARSET,
            
            // جدول وحدات القياس
            "CREATE TABLE IF NOT EXISTS uom_uom (
                id INT PRIMARY KEY AUTO_INCREMENT,
                name VARCHAR(255) NOT NULL,
                category_id INT NOT NULL,
                factor DECIMAL(12,6) DEFAULT 1.000000,
                uom_type ENUM('reference', 'bigger', 'smaller') DEFAULT 'reference',
                active BOOLEAN DEFAULT TRUE,
                create_uid INT DEFAULT 1,
                write_uid INT DEFAULT 1,
                create_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                write_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (category_id) REFERENCES uom_category(id)
            ) ENGINE=InnoDB DEFAULT CHARSET=" . ODOO_DB_CHARSET,
            
            // جدول فئات المنتجات
            "CREATE TABLE IF NOT EXISTS product_category (
                id INT PRIMARY KEY AUTO_INCREMENT,
                name VARCHAR(255) NOT NULL,
                complete_name VARCHAR(500),
                parent_id INT NULL,
                active BOOLEAN DEFAULT TRUE,
                company_id INT DEFAULT 1,
                create_uid INT DEFAULT 1,
                write_uid INT DEFAULT 1,
                create_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                write_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (parent_id) REFERENCES product_category(id),
                FOREIGN KEY (company_id) REFERENCES res_company(id)
            ) ENGINE=InnoDB DEFAULT CHARSET=" . ODOO_DB_CHARSET,
            
            // جدول قوالب المنتجات
            "CREATE TABLE IF NOT EXISTS product_template (
                id INT PRIMARY KEY AUTO_INCREMENT,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                type ENUM('product', 'service', 'consu') DEFAULT 'product',
                categ_id INT DEFAULT 1,
                list_price DECIMAL(12,2) DEFAULT 0.00,
                standard_price DECIMAL(12,2) DEFAULT 0.00,
                uom_id INT DEFAULT 1,
                uom_po_id INT DEFAULT 1,
                sale_ok BOOLEAN DEFAULT TRUE,
                purchase_ok BOOLEAN DEFAULT TRUE,
                active BOOLEAN DEFAULT TRUE,
                company_id INT DEFAULT 1,
                create_uid INT DEFAULT 1,
                write_uid INT DEFAULT 1,
                create_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                write_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (categ_id) REFERENCES product_category(id),
                FOREIGN KEY (uom_id) REFERENCES uom_uom(id),
                FOREIGN KEY (uom_po_id) REFERENCES uom_uom(id),
                FOREIGN KEY (company_id) REFERENCES res_company(id)
            ) ENGINE=InnoDB DEFAULT CHARSET=" . ODOO_DB_CHARSET
        );
        
        foreach ($tables as $sql) {
            try {
                $this->query($sql);
            } catch (Exception $e) {
                $this->logError("Error creating table: " . $e->getMessage());
            }
        }
        
        // إدراج البيانات الأساسية
        $this->insertBasicData();
    }
    
    /**
     * إدراج البيانات الأساسية
     */
    private function insertBasicData() {
        try {
            // إدراج العملات
            $this->query("INSERT IGNORE INTO res_currency (id, name, symbol, rate, active) VALUES 
                (1, 'SAR', 'ر.س', 1.000000, 1),
                (2, 'USD', '$', 0.266667, 1),
                (3, 'EUR', '€', 0.240000, 1)");
            
            // إدراج الشركة الافتراضية
            $this->query("INSERT IGNORE INTO res_company (id, name, display_name, code, country, currency_id, active) VALUES 
                (1, 'شركتي', 'شركتي', 'MYCO', 'السعودية', 1, 1)");
            
            // إدراج فئات وحدات القياس
            $this->query("INSERT IGNORE INTO uom_category (id, name) VALUES 
                (1, 'الوحدة'),
                (2, 'الوزن'),
                (3, 'الطول'),
                (4, 'الحجم')");
            
            // إدراج وحدات القياس
            $this->query("INSERT IGNORE INTO uom_uom (id, name, category_id, factor, uom_type, active) VALUES 
                (1, 'قطعة', 1, 1.0, 'reference', 1),
                (2, 'كيلوجرام', 2, 1.0, 'reference', 1),
                (3, 'متر', 3, 1.0, 'reference', 1),
                (4, 'لتر', 4, 1.0, 'reference', 1)");
            
            // إدراج فئة المنتجات الافتراضية
            $this->query("INSERT IGNORE INTO product_category (id, name, complete_name, active, company_id) VALUES 
                (1, 'جميع المنتجات', 'جميع المنتجات', 1, 1)");
            
            // إدراج المستخدم الإداري
            $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
            $this->query("INSERT IGNORE INTO res_users (id, name, login, email, password, active, company_id, groups_id) VALUES 
                (1, 'مدير النظام', '<EMAIL>', '<EMAIL>', '$admin_password', 1, 1, '[\"admin\",\"manager\",\"user\"]')");
            
            $this->logInfo("Basic data inserted successfully");
            
        } catch (Exception $e) {
            $this->logError("Error inserting basic data: " . $e->getMessage());
        }
    }
    
    /**
     * تنفيذ استعلام
     */
    public function query($sql, $params = array()) {
        if ($this->demo_mode) {
            return $this->demoQuery($sql, $params);
        }
        
        try {
            if ($this->connection instanceof PDO) {
                if (empty($params)) {
                    return $this->connection->query($sql);
                } else {
                    $stmt = $this->connection->prepare($sql);
                    $stmt->execute($params);
                    return $stmt;
                }
            } else {
                // MySQL التقليدي
                if (function_exists('mysqli_query')) {
                    return mysqli_query($this->connection, $sql);
                } else {
                    return mysql_query($sql, $this->connection);
                }
            }
        } catch (Exception $e) {
            $this->logError("Query error: " . $e->getMessage() . " SQL: " . $sql);
            throw $e;
        }
    }
    
    /**
     * استعلام تجريبي
     */
    private function demoQuery($sql, $params = array()) {
        // في الوضع التجريبي، نرجع بيانات وهمية
        $this->logInfo("Demo query executed: " . $sql);
        return true;
    }

    /**
     * تنظيف وحماية النصوص
     */
    public function quote($value) {
        if ($this->demo_mode) {
            return "'" . addslashes($value) . "'";
        }

        if ($this->connection instanceof PDO) {
            return $this->connection->quote($value);
        } else {
            // MySQL التقليدي
            if (function_exists('mysqli_real_escape_string')) {
                return "'" . mysqli_real_escape_string($this->connection, $value) . "'";
            } else {
                return "'" . mysql_real_escape_string($value, $this->connection) . "'";
            }
        }
    }

    /**
     * الحصول على آخر ID مدرج
     */
    public function lastInsertId() {
        if ($this->demo_mode) {
            return rand(1, 1000);
        }

        if ($this->connection instanceof PDO) {
            return $this->connection->lastInsertId();
        } else {
            if (function_exists('mysqli_insert_id')) {
                return mysqli_insert_id($this->connection);
            } else {
                return mysql_insert_id($this->connection);
            }
        }
    }

    /**
     * جلب صف واحد
     */
    public function fetchRow($result) {
        if ($this->demo_mode) {
            return array();
        }

        if ($this->connection instanceof PDO) {
            return $result->fetch(PDO::FETCH_ASSOC);
        } else {
            if (function_exists('mysqli_fetch_assoc')) {
                return mysqli_fetch_assoc($result);
            } else {
                return mysql_fetch_assoc($result);
            }
        }
    }

    /**
     * جلب جميع الصفوف
     */
    public function fetchAll($result) {
        if ($this->demo_mode) {
            return array();
        }

        if ($this->connection instanceof PDO) {
            return $result->fetchAll(PDO::FETCH_ASSOC);
        } else {
            $rows = array();
            if (function_exists('mysqli_fetch_assoc')) {
                while ($row = mysqli_fetch_assoc($result)) {
                    $rows[] = $row;
                }
            } else {
                while ($row = mysql_fetch_assoc($result)) {
                    $rows[] = $row;
                }
            }
            return $rows;
        }
    }

    /**
     * عدد الصفوف المتأثرة
     */
    public function affectedRows() {
        if ($this->demo_mode) {
            return 1;
        }

        if ($this->connection instanceof PDO) {
            return $this->connection->rowCount();
        } else {
            if (function_exists('mysqli_affected_rows')) {
                return mysqli_affected_rows($this->connection);
            } else {
                return mysql_affected_rows($this->connection);
            }
        }
    }
    
    /**
     * بدء معاملة
     */
    public function beginTransaction() {
        if ($this->demo_mode) return true;
        
        if (!$this->in_transaction) {
            if ($this->connection instanceof PDO) {
                $this->connection->beginTransaction();
            } else {
                if (function_exists('mysqli_autocommit')) {
                    mysqli_autocommit($this->connection, false);
                } else {
                    mysql_query("START TRANSACTION", $this->connection);
                }
            }
            $this->in_transaction = true;
        }
    }
    
    /**
     * تأكيد المعاملة
     */
    public function commit() {
        if ($this->demo_mode) return true;
        
        if ($this->in_transaction) {
            if ($this->connection instanceof PDO) {
                $this->connection->commit();
            } else {
                if (function_exists('mysqli_commit')) {
                    mysqli_commit($this->connection);
                    mysqli_autocommit($this->connection, true);
                } else {
                    mysql_query("COMMIT", $this->connection);
                }
            }
            $this->in_transaction = false;
        }
    }
    
    /**
     * إلغاء المعاملة
     */
    public function rollback() {
        if ($this->demo_mode) return true;
        
        if ($this->in_transaction) {
            if ($this->connection instanceof PDO) {
                $this->connection->rollback();
            } else {
                if (function_exists('mysqli_rollback')) {
                    mysqli_rollback($this->connection);
                    mysqli_autocommit($this->connection, true);
                } else {
                    mysql_query("ROLLBACK", $this->connection);
                }
            }
            $this->in_transaction = false;
        }
    }
    
    /**
     * التحقق من الوضع التجريبي
     */
    public function isDemoMode() {
        return $this->demo_mode;
    }
    
    /**
     * تسجيل خطأ
     */
    private function logError($message) {
        if (ODOO_LOG_ENABLED) {
            $log_message = date(ODOO_DATETIME_FORMAT) . " - ERROR: " . $message . "\n";
            @file_put_contents(odoo_get_path('root') . ODOO_LOG_FILE, $log_message, FILE_APPEND | LOCK_EX);
        }
    }
    
    /**
     * تسجيل معلومة
     */
    private function logInfo($message) {
        if (ODOO_LOG_ENABLED) {
            $log_message = date(ODOO_DATETIME_FORMAT) . " - INFO: " . $message . "\n";
            @file_put_contents(odoo_get_path('root') . ODOO_LOG_FILE, $log_message, FILE_APPEND | LOCK_EX);
        }
    }
}

// إنشاء مثيل عام
$odoo_db = OdooDatabase::getInstance();
?>
