<?php
/**
 * إعدادات النظام المتقدمة
 * Advanced System Settings
 */

// إعدادات الوحدات
$MODULES_CONFIG = array(
    'accounting' => array(
        'enabled' => true,
        'version' => '1.0.0',
        'dependencies' => array(),
        'settings' => array(
            'chart_of_accounts' => 'standard',
            'journal_entries' => true,
            'financial_reports' => true,
            'budget_management' => false,
            'asset_management' => false
        )
    ),
    'inventory' => array(
        'enabled' => false,
        'version' => '1.0.0',
        'dependencies' => array('accounting'),
        'settings' => array(
            'stock_management' => true,
            'warehouse_management' => false,
            'barcode_scanning' => false,
            'lot_tracking' => false
        )
    ),
    'sales' => array(
        'enabled' => false,
        'version' => '1.0.0',
        'dependencies' => array('accounting', 'inventory'),
        'settings' => array(
            'quotations' => true,
            'sales_orders' => true,
            'invoicing' => true,
            'delivery_management' => false
        )
    ),
    'purchases' => array(
        'enabled' => false,
        'version' => '1.0.0',
        'dependencies' => array('accounting', 'inventory'),
        'settings' => array(
            'purchase_requests' => true,
            'purchase_orders' => true,
            'vendor_bills' => true,
            'receipt_management' => false
        )
    ),
    'hr' => array(
        'enabled' => false,
        'version' => '1.0.0',
        'dependencies' => array('accounting'),
        'settings' => array(
            'employee_management' => true,
            'payroll' => false,
            'attendance' => false,
            'leave_management' => false
        )
    ),
    'project' => array(
        'enabled' => false,
        'version' => '1.0.0',
        'dependencies' => array(),
        'settings' => array(
            'project_management' => true,
            'task_management' => true,
            'time_tracking' => false,
            'gantt_charts' => false
        )
    )
);

// إعدادات الأذونات والأدوار
$PERMISSIONS_CONFIG = array(
    'admin' => array(
        'name' => 'مدير النظام',
        'permissions' => array('*'), // جميع الصلاحيات
        'modules' => array('*')
    ),
    'accountant' => array(
        'name' => 'محاسب',
        'permissions' => array(
            'accounting.read',
            'accounting.write',
            'reports.financial.read',
            'partners.read',
            'partners.write'
        ),
        'modules' => array('accounting')
    ),
    'sales_manager' => array(
        'name' => 'مدير المبيعات',
        'permissions' => array(
            'sales.read',
            'sales.write',
            'inventory.read',
            'partners.read',
            'partners.write',
            'reports.sales.read'
        ),
        'modules' => array('sales', 'inventory')
    ),
    'user' => array(
        'name' => 'مستخدم',
        'permissions' => array(
            'dashboard.read',
            'partners.read',
            'products.read'
        ),
        'modules' => array()
    )
);

// إعدادات التكامل مع الأنظمة الخارجية
$INTEGRATIONS_CONFIG = array(
    'payment_gateways' => array(
        'paypal' => array(
            'enabled' => false,
            'sandbox' => true,
            'client_id' => '',
            'client_secret' => ''
        ),
        'stripe' => array(
            'enabled' => false,
            'test_mode' => true,
            'public_key' => '',
            'secret_key' => ''
        ),
        'mada' => array(
            'enabled' => false,
            'merchant_id' => '',
            'terminal_id' => '',
            'secret_key' => ''
        )
    ),
    'shipping' => array(
        'aramex' => array(
            'enabled' => false,
            'username' => '',
            'password' => '',
            'account_number' => ''
        ),
        'dhl' => array(
            'enabled' => false,
            'site_id' => '',
            'password' => ''
        )
    ),
    'sms' => array(
        'taqnyat' => array(
            'enabled' => false,
            'bearer_token' => '',
            'sender' => ''
        ),
        'twilio' => array(
            'enabled' => false,
            'account_sid' => '',
            'auth_token' => '',
            'phone_number' => ''
        )
    ),
    'social_media' => array(
        'whatsapp_business' => array(
            'enabled' => false,
            'phone_number_id' => '',
            'access_token' => ''
        ),
        'telegram' => array(
            'enabled' => false,
            'bot_token' => '',
            'chat_id' => ''
        )
    )
);

// دالة تحميل الإعدادات من ملف JSON
function loadSettingsFromFile($category) {
    $config_file = dirname(__FILE__) . '/settings_' . $category . '.json';
    
    if (file_exists($config_file)) {
        $settings = json_decode(file_get_contents($config_file), true);
        return $settings ? $settings : array();
    }
    
    return array();
}

// دالة حفظ الإعدادات في ملف JSON
function saveSettingsToFile($category, $settings) {
    $config_file = dirname(__FILE__) . '/settings_' . $category . '.json';
    
    $json = json_encode($settings, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    return file_put_contents($config_file, $json) !== false;
}

// دالة التحقق من صلاحية المستخدم
function checkUserPermission($user_role, $permission) {
    global $PERMISSIONS_CONFIG;
    
    if (!isset($PERMISSIONS_CONFIG[$user_role])) {
        return false;
    }
    
    $user_permissions = $PERMISSIONS_CONFIG[$user_role]['permissions'];
    
    // إذا كان لديه صلاحية شاملة
    if (in_array('*', $user_permissions)) {
        return true;
    }
    
    // التحقق من الصلاحية المحددة
    return in_array($permission, $user_permissions);
}

// دالة التحقق من تفعيل الوحدة
function isModuleEnabled($module_name) {
    global $MODULES_CONFIG;
    
    return isset($MODULES_CONFIG[$module_name]) && $MODULES_CONFIG[$module_name]['enabled'];
}

// دالة الحصول على إعدادات الوحدة
function getModuleSettings($module_name) {
    global $MODULES_CONFIG;
    
    if (isset($MODULES_CONFIG[$module_name])) {
        return $MODULES_CONFIG[$module_name]['settings'];
    }
    
    return array();
}

// دالة تسجيل الأحداث
function logEvent($level, $message, $context = array()) {
    $log_entry = array(
        'timestamp' => date('Y-m-d H:i:s'),
        'level' => $level,
        'message' => $message,
        'context' => $context,
        'user_id' => $_SESSION['user_id'] ?? null,
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
    );
    
    $log_file = dirname(__FILE__) . '/../logs/system_' . date('Y-m-d') . '.log';
    
    // إنشاء مجلد السجلات إذا لم يكن موجوداً
    $log_dir = dirname($log_file);
    if (!file_exists($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    $log_line = json_encode($log_entry, JSON_UNESCAPED_UNICODE) . "\n";
    file_put_contents($log_file, $log_line, FILE_APPEND | LOCK_EX);
}

// دالة تنظيف السجلات القديمة
function cleanOldLogs($retention_days = 30) {
    $log_dir = dirname(__FILE__) . '/../logs/';
    
    if (!is_dir($log_dir)) {
        return;
    }
    
    $files = glob($log_dir . 'system_*.log');
    $cutoff_time = time() - ($retention_days * 24 * 60 * 60);
    
    foreach ($files as $file) {
        if (filemtime($file) < $cutoff_time) {
            unlink($file);
        }
    }
}

// دالة إنشاء نسخة احتياطية
function createBackup() {
    $backup_dir = dirname(__FILE__) . '/../backups/';
    
    if (!is_dir($backup_dir)) {
        mkdir($backup_dir, 0755, true);
    }
    
    $backup_file = $backup_dir . 'backup_' . date('Y-m-d_H-i-s') . '.sql';
    
    // هنا يمكن إضافة كود إنشاء النسخة الاحتياطية الفعلية
    // mysqldump أو أي طريقة أخرى
    
    return $backup_file;
}

// دالة تحسين قاعدة البيانات
function optimizeDatabase() {
    // هنا يمكن إضافة كود تحسين قاعدة البيانات
    // OPTIMIZE TABLE, ANALYZE TABLE, etc.
    
    logEvent('INFO', 'Database optimization completed');
    return true;
}

// دالة مسح التخزين المؤقت
function clearCache() {
    $cache_dir = dirname(__FILE__) . '/../cache/';
    
    if (!is_dir($cache_dir)) {
        return true;
    }
    
    $files = glob($cache_dir . '*');
    
    foreach ($files as $file) {
        if (is_file($file)) {
            unlink($file);
        }
    }
    
    logEvent('INFO', 'Cache cleared successfully');
    return true;
}

// دالة فحص التحديثات
function checkForUpdates() {
    // هنا يمكن إضافة كود فحص التحديثات من خادم بعيد
    
    return array(
        'current_version' => '1.0.0',
        'latest_version' => '1.0.0',
        'update_available' => false,
        'update_url' => '',
        'changelog' => array()
    );
}

// دالة إرسال إشعار
function sendNotification($user_id, $title, $message, $type = 'info') {
    $notification = array(
        'user_id' => $user_id,
        'title' => $title,
        'message' => $message,
        'type' => $type,
        'created_at' => date('Y-m-d H:i:s'),
        'read' => false
    );
    
    // هنا يمكن حفظ الإشعار في قاعدة البيانات
    // أو إرساله عبر البريد الإلكتروني أو SMS
    
    logEvent('INFO', 'Notification sent', $notification);
    return true;
}

?>
