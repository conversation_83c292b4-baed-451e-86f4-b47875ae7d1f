-- Create a simplified mail_template table
CREATE TABLE IF NOT EXISTS `mail_template` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(128) NOT NULL,
    `model` VA<PERSON>HAR(64) NOT NULL,
    `subject` VA<PERSON><PERSON><PERSON>(256),
    `body_html` LONGTEXT,
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME,
    `model_id` INT,
    `lang` VARCHAR(16),
    `use_default_to` BOOLEAN DEFAULT TRUE,
    `email_to` <PERSON><PERSON><PERSON><PERSON>(256),
    `email_cc` VARCHAR(256),
    `reply_to` <PERSON><PERSON><PERSON><PERSON>(256),
    `auto_delete` <PERSON><PERSON><PERSON><PERSON><PERSON> DEFAULT TRUE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
