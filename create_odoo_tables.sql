-- Enable foreign key checks
SET FOREIGN_KEY_CHECKS = 0;

-- Drop existing tables if they exist
DROP TABLE IF EXISTS `account_account`;
DROP TABLE IF EXISTS `account_move`;
DROP TABLE IF EXISTS `account_move_line`;
DROP TABLE IF EXISTS `account_journal`;
DROP TABLE IF EXISTS `account_payment`;
DROP TABLE IF EXISTS `account_tax`;
DROP TABLE IF EXISTS `sale_order`;
DROP TABLE IF EXISTS `sale_order_line`;
DROP TABLE IF EXISTS `purchase_order`;
DROP TABLE IF EXISTS `purchase_order_line`;
DROP TABLE IF EXISTS `stock_picking`;
DROP TABLE IF EXISTS `stock_move`;
DROP TABLE IF EXISTS `stock_quant`;
DROP TABLE IF EXISTS `stock_location`;
DROP TABLE IF EXISTS `hr_employee`;
DROP TABLE IF EXISTS `hr_department`;

-- Accounting Tables
CREATE TABLE `account_account` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `code` VARCHAR(64) NOT NULL,
    `name` VARCHAR(256) NOT NULL,
    `user_type_id` INT,
    `company_id` INT,
    `reconcile` BOOLEAN DEFAULT FALSE,
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME,
    UNIQUE KEY `account_code_company_uniq` (`code`, `company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `account_journal` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(64) NOT NULL,
    `code` VARCHAR(8) NOT NULL,
    `type` VARCHAR(32) NOT NULL,
    `company_id` INT,
    `default_account_id` INT,
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME,
    UNIQUE KEY `name_company_uniq` (`name`, `company_id`),
    UNIQUE KEY `code_company_uniq` (`code`, `company_id`),
    FOREIGN KEY (`default_account_id`) REFERENCES `account_account`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `account_move` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(64) NOT NULL,
    `ref` VARCHAR(255),
    `date` DATE NOT NULL,
    `journal_id` INT NOT NULL,
    `company_id` INT,
    `state` VARCHAR(32) DEFAULT 'draft',
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME,
    FOREIGN KEY (`journal_id`) REFERENCES `account_journal`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `account_move_line` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `move_id` INT NOT NULL,
    `account_id` INT NOT NULL,
    `partner_id` INT,
    `name` TEXT,
    `debit` DECIMAL(16,2) DEFAULT 0.0,
    `credit` DECIMAL(16,2) DEFAULT 0.0,
    `balance` DECIMAL(16,2) GENERATED ALWAYS AS (debit - credit) STORED,
    `date` DATE,
    `date_maturity` DATE,
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME,
    FOREIGN KEY (`move_id`) REFERENCES `account_move`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`account_id`) REFERENCES `account_account`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Sales Tables
CREATE TABLE `sale_order` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(64) NOT NULL,
    `partner_id` INT NOT NULL,
    `date_order` DATETIME NOT NULL,
    `amount_total` DECIMAL(16,2) DEFAULT 0.0,
    `state` VARCHAR(32) DEFAULT 'draft',
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME,
    FOREIGN KEY (`partner_id`) REFERENCES `res_partner`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Purchase Tables
CREATE TABLE `purchase_order` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(64) NOT NULL,
    `partner_id` INT NOT NULL,
    `date_order` DATETIME NOT NULL,
    `amount_total` DECIMAL(16,2) DEFAULT 0.0,
    `state` VARCHAR(32) DEFAULT 'draft',
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME,
    FOREIGN KEY (`partner_id`) REFERENCES `res_partner`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Stock Tables
CREATE TABLE `stock_location` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(64) NOT NULL,
    `complete_name` VARCHAR(256),
    `location_id` INT,
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME,
    FOREIGN KEY (`location_id`) REFERENCES `stock_location`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `stock_move` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(256) NOT NULL,
    `product_id` INT,
    `product_uom_qty` DECIMAL(16,2) NOT NULL,
    `product_uom` INT,
    `location_id` INT,
    `location_dest_id` INT,
    `state` VARCHAR(32) DEFAULT 'draft',
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME,
    FOREIGN KEY (`product_id`) REFERENCES `product_template`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`location_id`) REFERENCES `stock_location`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`location_dest_id`) REFERENCES `stock_location`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- HR Tables
CREATE TABLE `hr_department` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(64) NOT NULL,
    `company_id` INT,
    `manager_id` INT,
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `hr_employee` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(256) NOT NULL,
    `department_id` INT,
    `job_title` VARCHAR(128),
    `work_email` VARCHAR(256),
    `work_phone` VARCHAR(32),
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME,
    FOREIGN KEY (`department_id`) REFERENCES `hr_department`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- Create basic chart of accounts
INSERT INTO `account_account` (`code`, `name`, `user_type_id`, `reconcile`) VALUES
('1000', 'Current Assets', 1, FALSE),
('1010', 'Bank Accounts', 1, FALSE),
('101001', 'Cash', 1, TRUE),
('101002', 'Bank Current Account', 1, TRUE),
('2000', 'Current Liabilities', 2, FALSE),
('2010', 'Payable', 2, TRUE),
('3000', 'Equity', 3, FALSE),
('4000', 'Income', 4, FALSE),
('5000', 'Cost of Goods Sold', 5, FALSE),
('6000', 'Expenses', 6, FALSE);

-- Create default journals
INSERT INTO `account_journal` (`name`, `code`, `type`, `default_account_id`) VALUES
('Miscellaneous Operations', 'MISC', 'general', 1),
('Customer Invoices', 'INV', 'sale', 4),
('Vendor Bills', 'BILL', 'purchase', 6),
('Bank', 'BNK1', 'bank', 3);
