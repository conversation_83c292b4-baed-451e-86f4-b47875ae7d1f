-- =============================================
-- إنشاء جداول المنتجات المفقودة
-- =============================================

-- بدء المعاملة
START TRANSACTION;

-- جدول قوالب المنتجات
CREATE TABLE IF NOT EXISTS `product_template` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(256) NOT NULL,
    `categ_id` INT,
    `list_price` DECIMAL(10,2) DEFAULT 0.00,
    `standard_price` DECIMAL(10,2) DEFAULT 0.00,
    `type` ENUM('consu', 'service', 'product') DEFAULT 'product',
    `uom_id` INT NOT NULL,
    `uom_po_id` INT NOT NULL,
    `sale_ok` BOOLEAN DEFAULT TRUE,
    `purchase_ok` BOOLEAN DEFAULT TRUE,
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME,
    FOREIGN KEY (`categ_id`) REFERENCES `product_category`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`uom_id`) REFERENCES `uom_uom`(`id`) ON DELETE RESTRICT,
    FOREIGN KEY (`uom_po_id`) REFERENCES `uom_uom`(`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول المنتجات
CREATE TABLE IF NOT EXISTS `product_product` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `product_tmpl_id` INT NOT NULL,
    `default_code` VARCHAR(64),
    `active` BOOLEAN DEFAULT TRUE,
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME,
    FOREIGN KEY (`product_tmpl_id`) REFERENCES `product_template`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول كميات المخزون
CREATE TABLE IF NOT EXISTS `stock_quant` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `product_id` INT NOT NULL,
    `location_id` INT NOT NULL,
    `quantity` DECIMAL(18,3) NOT NULL DEFAULT 0.000,
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME,
    FOREIGN KEY (`product_id`) REFERENCES `product_product`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`location_id`) REFERENCES `stock_location`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- تأكيد التغييرات
COMMIT;

-- رسالة نجاح
SELECT 'تم إنشاء جداول المنتجات بنجاح!' AS message;
