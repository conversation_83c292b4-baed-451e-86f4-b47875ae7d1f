-- =============================================
-- إنشاء جدول res_lang للغات
-- =============================================
CREATE TABLE IF NOT EXISTS `res_lang` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(64) NOT NULL,
    `code` VARCHAR(16) NOT NULL,
    `iso_code` VARCHAR(16) NOT NULL,
    `url_code` VARCHAR(16) NOT NULL,
    `active` BOOLEAN DEFAULT TRUE,
    `translatable` BOOLEAN DEFAULT TRUE,
    `direction` ENUM('ltr', 'rtl') DEFAULT 'ltr',
    `date_format` VARCHAR(32) NOT NULL,
    `time_format` VARCHAR(32) NOT NULL,
    `week_start` ENUM('1','2','3','4','5','6','7') DEFAULT '7',
    `grouping` VARCHAR(32) NOT NULL,
    `decimal_point` VARCHAR(8) NOT NULL,
    `thousands_sep` VARCHAR(8) NOT NULL,
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME,
    UNIQUE KEY `res_lang_code_uniq` (`code`),
    UNIQUE KEY `res_lang_iso_code_uniq` (`iso_code`),
    UNIQUE KEY `res_lang_url_code_uniq` (`url_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إضافة رسالة نجاح
SELECT 'تم إنشاء جدول اللغات بنجاح!' AS message;
