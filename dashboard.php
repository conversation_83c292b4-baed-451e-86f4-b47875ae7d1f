<?php
/**
 * لوحة التحكم الرئيسية بأسلوب Odoo
 * Odoo-Style Main Dashboard
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

// تحميل نظام Odoo
if (file_exists('includes/odoo_registry.php')) {
    require_once 'includes/odoo_registry.php';
    require_once 'includes/odoo_menu.php';
    require_once 'includes/odoo_actions.php';
}

// تحميل نظام الوحدات
require_once 'includes/odoo_modules.php';
$module_manager = OdooModuleManager::getInstance();
$installed_menus = $module_manager->getInstalledMenus();

// دالة للحصول على علامة التفعيل
function getMenuBadge($menu_name) {
    $activated_menus = array(
        'دليل الحسابات' => 'مفعل',
        'القيود اليومية' => 'مفعل',
        'إنشاء قيد جديد' => 'مفعل',
        'الشركاء' => 'مفعل',
        'التقارير المالية' => 'مفعل',
        'إعدادات المحاسبة' => 'مفعل'
    );

    if (isset($activated_menus[$menu_name])) {
        return '<span class="badge bg-success ms-auto">مفعل</span>';
    }

    return '';
}

// دالة للحصول على رابط القائمة
function getMenuUrl($menu_name, $module) {
    $menu_urls = array(
        // وحدة base
        'الشركات' => 'pages/companies_new.php',
        'المستخدمين' => 'pages/users_new.php',
        'الشركاء' => 'pages/partners.php',

        // وحدة account
        'دليل الحسابات' => 'pages/chart_of_accounts.php',
        'القيود اليومية' => 'pages/journal_entries.php',
        'إنشاء قيد جديد' => 'pages/create_entry.php',
        'الشركاء' => 'pages/partners.php',
        'التقارير المالية' => 'pages/financial_reports_advanced.php',
        'اليوميات' => 'pages/journals.php',
        'دفتر الأستاذ' => 'pages/account_ledger.php',
        'ميزان المراجعة' => 'pages/trial_balance.php',
        'الفترات المحاسبية' => 'pages/accounting_periods.php',
        'إعدادات المحاسبة' => 'pages/accounting_settings.php',
        'إعداد المحاسبة' => 'setup_accounting.php',

        // وحدة sale
        'عروض الأسعار' => 'pages/quotations.php',
        'أوامر البيع' => 'pages/sale_orders.php',
        'العملاء' => 'pages/customers.php',
        'تقارير المبيعات' => 'pages/sale_reports.php',

        // وحدة purchase
        'طلبات الأسعار' => 'pages/rfq.php',
        'أوامر الشراء' => 'pages/purchase_orders.php',
        'الموردين' => 'pages/suppliers.php',
        'تقارير المشتريات' => 'pages/purchase_reports.php',

        // وحدة stock
        'المنتجات' => 'pages/products_new.php',
        'المواقع' => 'pages/locations.php',
        'حركات المخزون' => 'pages/stock_moves.php',
        'تقارير المخزون' => 'pages/stock_reports.php',

        // وحدة hr
        'الموظفين' => 'pages/employees.php',
        'الأقسام' => 'pages/departments.php',
        'الوظائف' => 'pages/jobs.php',

        // وحدة project
        'جميع المشاريع' => 'pages/projects.php',
        'المهام' => 'pages/tasks.php',
        'التقارير' => 'pages/project_reports.php',

        // وحدة crm
        'العملاء المحتملين' => 'pages/leads.php',
        'الفرص' => 'pages/opportunities.php'
    );

    return isset($menu_urls[$menu_name]) ? $menu_urls[$menu_name] : '#';
}

// الحصول على معلومات المستخدم
$user_name = isset($_SESSION['username']) ? $_SESSION['username'] : 'مستخدم';
$user_email = isset($_SESSION['email']) ? $_SESSION['email'] : '';
$user_role = isset($_SESSION['role']) ? $_SESSION['role'] : 'user';
$user_groups = isset($_SESSION['groups']) ? $_SESSION['groups'] : array('user');
$company_name = isset($_SESSION['company_name']) ? $_SESSION['company_name'] : 'شركتي';

// إحصائيات تجريبية
$stats = array(
    'companies' => array('count' => 2, 'icon' => 'fas fa-building', 'color' => 'primary'),
    'customers' => array('count' => 15, 'icon' => 'fas fa-users', 'color' => 'success'),
    'products' => array('count' => 48, 'icon' => 'fas fa-box', 'color' => 'info'),
    'invoices' => array('count' => 23, 'icon' => 'fas fa-file-invoice', 'color' => 'warning'),
    'revenue' => array('count' => '125,450', 'icon' => 'fas fa-chart-line', 'color' => 'success'),
    'orders' => array('count' => 67, 'icon' => 'fas fa-shopping-cart', 'color' => 'danger')
);

// الأنشطة الأخيرة
$recent_activities = array(
    array('action' => 'إنشاء فاتورة جديدة', 'user' => 'أحمد محمد', 'time' => '5 دقائق', 'icon' => 'fas fa-file-invoice', 'color' => 'success'),
    array('action' => 'تحديث بيانات عميل', 'user' => 'فاطمة علي', 'time' => '15 دقيقة', 'icon' => 'fas fa-user-edit', 'color' => 'info'),
    array('action' => 'إضافة منتج جديد', 'user' => 'محمد أحمد', 'time' => '30 دقيقة', 'icon' => 'fas fa-plus-circle', 'color' => 'primary'),
    array('action' => 'تأكيد طلب شراء', 'user' => 'سارة محمود', 'time' => '1 ساعة', 'icon' => 'fas fa-check-circle', 'color' => 'warning'),
    array('action' => 'إنشاء تقرير مالي', 'user' => 'عبدالله سالم', 'time' => '2 ساعة', 'icon' => 'fas fa-chart-bar', 'color' => 'danger')
);

// المهام المعلقة
$pending_tasks = array(
    array('task' => 'مراجعة الفواتير المعلقة', 'priority' => 'عالية', 'due' => 'اليوم', 'color' => 'danger'),
    array('task' => 'تحديث أسعار المنتجات', 'priority' => 'متوسطة', 'due' => 'غداً', 'color' => 'warning'),
    array('task' => 'إعداد تقرير شهري', 'priority' => 'منخفضة', 'due' => 'الأسبوع القادم', 'color' => 'info')
);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام ERP بأسلوب Odoo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/odoo-style.css" rel="stylesheet">
    <style>
        :root {
            --odoo-primary: #714B67;
            --odoo-secondary: #8B5A8C;
            --odoo-accent: #A0729C;
        }
        
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar-odoo {
            background: linear-gradient(45deg, var(--odoo-primary), var(--odoo-secondary));
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .sidebar-odoo {
            background: white;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            min-height: calc(100vh - 56px);
        }
        
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            border-left: 4px solid;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }
        
        .activity-item {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 0.5rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        
        .activity-item:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .nav-link-odoo {
            color: #6c757d;
            padding: 0.75rem 1rem;
            border-radius: 8px;
            margin: 0.25rem 0;
            transition: all 0.3s ease;
        }
        
        .nav-link-odoo:hover, .nav-link-odoo.active {
            background: linear-gradient(45deg, var(--odoo-primary), var(--odoo-secondary));
            color: white;
            transform: translateX(5px);
        }
        
        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .welcome-banner {
            background: linear-gradient(135deg, var(--odoo-primary), var(--odoo-secondary));
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .quick-action {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 1rem;
            text-align: center;
            transition: all 0.3s ease;
            text-decoration: none;
            color: #6c757d;
        }
        
        .quick-action:hover {
            border-color: var(--odoo-primary);
            color: var(--odoo-primary);
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-odoo">
        <div class="container-fluid">
            <a class="navbar-brand text-white" href="#">
                <i class="fas fa-cube me-2"></i>
                نظام ERP - بأسلوب Odoo
                <span class="badge bg-light text-dark ms-2">v1.0</span>
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-2"></i>
                        <?php echo $user_name; ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>الملف الشخصي</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar-odoo">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-3">
                        <img src="https://via.placeholder.com/80x80/714B67/FFFFFF?text=<?php echo substr($user_name, 0, 2); ?>" 
                             class="rounded-circle" alt="صورة المستخدم">
                        <h6 class="mt-2"><?php echo $user_name; ?></h6>
                        <small class="text-muted"><?php echo $company_name; ?></small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link nav-link-odoo active" href="#dashboard">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة التحكم
                            </a>
                        </li>

                        <?php
                        // تجميع القوائم حسب الوحدة الرئيسية وإزالة التكرار
                        $main_menus = array();
                        $sub_menus = array();
                        $processed_modules = array();

                        foreach ($installed_menus as $menu) {
                            if ($menu['parent'] === null) {
                                // تجنب تكرار الوحدات الرئيسية
                                if (!in_array($menu['module'], $processed_modules)) {
                                    $main_menus[] = $menu;
                                    $processed_modules[] = $menu['module'];
                                }
                            } else {
                                if (!isset($sub_menus[$menu['parent']])) {
                                    $sub_menus[$menu['parent']] = array();
                                }
                                $sub_menus[$menu['parent']][] = $menu;
                            }
                        }

                        // عرض القوائم الرئيسية بدون تكرار
                        foreach ($main_menus as $main_menu):
                            $module_info = $module_manager->getModuleInfo($main_menu['module']);
                            $icon = $module_info ? $module_info['icon'] : 'fas fa-folder';
                        ?>
                            <li class="nav-item">
                                <a class="nav-link nav-link-odoo" href="#" data-bs-toggle="collapse" data-bs-target="#menu-<?php echo $main_menu['module']; ?>">
                                    <i class="<?php echo $icon; ?> me-2"></i>
                                    <?php echo $main_menu['name']; ?>
                                    <i class="fas fa-chevron-down ms-auto"></i>
                                </a>

                                <?php if (isset($sub_menus[$main_menu['name']])): ?>
                                <div class="collapse" id="menu-<?php echo $main_menu['module']; ?>">
                                    <ul class="nav flex-column ms-3">
                                        <?php
                                        // إزالة التكرار من القوائم الفرعية
                                        $unique_sub_menus = array();
                                        foreach ($sub_menus[$main_menu['name']] as $sub_menu) {
                                            $key = $sub_menu['name'] . '_' . $sub_menu['module'];
                                            if (!isset($unique_sub_menus[$key])) {
                                                $unique_sub_menus[$key] = $sub_menu;
                                            }
                                        }
                                        foreach ($unique_sub_menus as $sub_menu):
                                        ?>
                                            <li class="nav-item">
                                                <a class="nav-link nav-link-odoo" href="<?php echo getMenuUrl($sub_menu['name'], $main_menu['module']); ?>">
                                                    <i class="fas fa-circle me-2" style="font-size: 0.5rem;"></i>
                                                    <?php echo $sub_menu['name']; ?>
                                                    <?php echo getMenuBadge($sub_menu['name']); ?>
                                                </a>
                                            </li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                                <?php endif; ?>
                            </li>
                        <?php endforeach; ?>

                        <?php if (in_array('admin', $user_groups)): ?>
                        <li class="nav-item mt-3">
                            <hr class="text-muted">
                            <a class="nav-link nav-link-odoo" href="pages/modules.php">
                                <i class="fas fa-puzzle-piece me-2"></i>
                                إدارة الوحدات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link nav-link-odoo" href="pages/settings.php">
                                <i class="fas fa-cog me-2"></i>
                                إعدادات النظام
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </nav>

            <!-- المحتوى الرئيسي -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="pt-3">
                    <!-- بانر الترحيب -->
                    <div class="welcome-banner">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h2>مرحباً، <?php echo $user_name; ?>! 👋</h2>
                                <p class="mb-0">إليك نظرة سريعة على أداء شركتك اليوم</p>
                            </div>
                            <div class="col-md-4 text-end">
                                <i class="fas fa-chart-line fa-3x opacity-50"></i>
                            </div>
                        </div>
                    </div>

                    <!-- الإحصائيات -->
                    <div class="row mb-4">
                        <div class="col-md-2 mb-3">
                            <div class="stat-card border-start border-primary">
                                <div class="d-flex align-items-center">
                                    <div class="stat-icon bg-primary me-3">
                                        <i class="fas fa-building"></i>
                                    </div>
                                    <div>
                                        <h4 class="mb-0"><?php echo $stats['companies']['count']; ?></h4>
                                        <small class="text-muted">الشركات</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-2 mb-3">
                            <div class="stat-card border-start border-success">
                                <div class="d-flex align-items-center">
                                    <div class="stat-icon bg-success me-3">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <div>
                                        <h4 class="mb-0"><?php echo $stats['customers']['count']; ?></h4>
                                        <small class="text-muted">العملاء</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-2 mb-3">
                            <div class="stat-card border-start border-info">
                                <div class="d-flex align-items-center">
                                    <div class="stat-icon bg-info me-3">
                                        <i class="fas fa-box"></i>
                                    </div>
                                    <div>
                                        <h4 class="mb-0"><?php echo $stats['products']['count']; ?></h4>
                                        <small class="text-muted">المنتجات</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-2 mb-3">
                            <div class="stat-card border-start border-warning">
                                <div class="d-flex align-items-center">
                                    <div class="stat-icon bg-warning me-3">
                                        <i class="fas fa-file-invoice"></i>
                                    </div>
                                    <div>
                                        <h4 class="mb-0"><?php echo $stats['invoices']['count']; ?></h4>
                                        <small class="text-muted">الفواتير</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-2 mb-3">
                            <div class="stat-card border-start border-success">
                                <div class="d-flex align-items-center">
                                    <div class="stat-icon bg-success me-3">
                                        <i class="fas fa-chart-line"></i>
                                    </div>
                                    <div>
                                        <h4 class="mb-0"><?php echo $stats['revenue']['count']; ?></h4>
                                        <small class="text-muted">الإيرادات</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-2 mb-3">
                            <div class="stat-card border-start border-danger">
                                <div class="d-flex align-items-center">
                                    <div class="stat-icon bg-danger me-3">
                                        <i class="fas fa-shopping-cart"></i>
                                    </div>
                                    <div>
                                        <h4 class="mb-0"><?php echo $stats['orders']['count']; ?></h4>
                                        <small class="text-muted">الطلبات</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الإجراءات السريعة -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="mb-3">الإجراءات السريعة</h5>
                            <div class="row">
                                <div class="col-md-2 mb-3">
                                    <a href="pages/company_form.php" class="quick-action d-block">
                                        <i class="fas fa-building fa-2x mb-2"></i>
                                        <div>إضافة شركة</div>
                                    </a>
                                </div>
                                <div class="col-md-2 mb-3">
                                    <a href="pages/partner_form.php?type=customer" class="quick-action d-block">
                                        <i class="fas fa-user-plus fa-2x mb-2"></i>
                                        <div>إضافة عميل</div>
                                    </a>
                                </div>
                                <div class="col-md-2 mb-3">
                                    <a href="pages/product_form.php" class="quick-action d-block">
                                        <i class="fas fa-box fa-2x mb-2"></i>
                                        <div>إضافة منتج</div>
                                    </a>
                                </div>
                                <div class="col-md-2 mb-3">
                                    <a href="pages/partner_form.php?type=supplier" class="quick-action d-block">
                                        <i class="fas fa-truck fa-2x mb-2"></i>
                                        <div>إضافة مورد</div>
                                    </a>
                                </div>
                                <div class="col-md-2 mb-3">
                                    <a href="pages/user_form.php" class="quick-action d-block">
                                        <i class="fas fa-user-plus fa-2x mb-2"></i>
                                        <div>إضافة مستخدم</div>
                                    </a>
                                </div>
                                <div class="col-md-2 mb-3">
                                    <a href="pages/invoices.php?action=create" class="quick-action d-block">
                                        <i class="fas fa-file-invoice fa-2x mb-2"></i>
                                        <div>إنشاء فاتورة</div>
                                    </a>
                                </div>
                                <div class="col-md-2 mb-3">
                                    <a href="pages/reports.php" class="quick-action d-block">
                                        <i class="fas fa-chart-bar fa-2x mb-2"></i>
                                        <div>عرض التقارير</div>
                                    </a>
                                </div>
                                <div class="col-md-2 mb-3">
                                    <a href="pages/settings.php" class="quick-action d-block">
                                        <i class="fas fa-cog fa-2x mb-2"></i>
                                        <div>الإعدادات</div>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الأنشطة الأخيرة والمهام -->
                    <div class="row">
                        <div class="col-md-8">
                            <div class="chart-container">
                                <h5 class="mb-3">الأنشطة الأخيرة</h5>
                                <?php foreach ($recent_activities as $activity): ?>
                                    <div class="activity-item">
                                        <div class="d-flex align-items-center">
                                            <div class="me-3">
                                                <i class="<?php echo $activity['icon']; ?> text-<?php echo $activity['color']; ?>"></i>
                                            </div>
                                            <div class="flex-grow-1">
                                                <div class="fw-bold"><?php echo $activity['action']; ?></div>
                                                <small class="text-muted">بواسطة <?php echo $activity['user']; ?> - منذ <?php echo $activity['time']; ?></small>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="chart-container">
                                <h5 class="mb-3">المهام المعلقة</h5>
                                <?php foreach ($pending_tasks as $task): ?>
                                    <div class="activity-item">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <div class="fw-bold"><?php echo $task['task']; ?></div>
                                                <small class="text-muted">موعد الانتهاء: <?php echo $task['due']; ?></small>
                                            </div>
                                            <span class="badge bg-<?php echo $task['color']; ?>"><?php echo $task['priority']; ?></span>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير الظهور التدريجي للبطاقات
            const cards = document.querySelectorAll('.stat-card, .activity-item, .chart-container');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
        
        // تحديث الوقت
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('ar-SA');
            document.title = 'لوحة التحكم - ' + timeString;
        }
        
        setInterval(updateTime, 1000);
    </script>
</body>
</html>
