-- إن<PERSON><PERSON><PERSON> قاعدة البيانات
CREATE DATABASE IF NOT EXISTS erp_accounting CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE erp_accounting;

-- جدول المستخدمين
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'manager', 'accountant', 'user') NOT NULL DEFAULT 'user',
    active TINYINT(1) NOT NULL DEFAULT 1,
    last_login DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج مستخدم افتراضي (admin/admin123)
INSERT INTO users (name, username, email, password, role) VALUES 
('مدير النظام', 'admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin');

-- جدول الشركات
CREATE TABLE IF NOT EXISTS companies (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    legal_name VARCHAR(150),
    tax_number VARCHAR(50),
    email VARCHAR(100),
    phone VARCHAR(50),
    website VARCHAR(100),
    address TEXT,
    logo VARCHAR(255),
    currency VARCHAR(10) DEFAULT 'SAR',
    fiscal_year_start DATE,
    active TINYINT(1) NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج شركة افتراضية
INSERT INTO companies (name, legal_name, email, phone, currency) VALUES 
('الشركة الافتراضية', 'الشركة الافتراضية للحلول التقنية', '<EMAIL>', '+966 11 123 4567', 'SAR');

-- جدول الشركاء (العملاء والموردين)
CREATE TABLE IF NOT EXISTS partners (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    is_company TINYINT(1) NOT NULL DEFAULT 0,
    parent_id INT NULL,
    email VARCHAR(100),
    phone VARCHAR(50),
    mobile VARCHAR(50),
    website VARCHAR(100),
    vat VARCHAR(50),
    street VARCHAR(255),
    street2 VARCHAR(255),
    city VARCHAR(100),
    zip VARCHAR(20),
    country VARCHAR(100),
    customer_rank INT NOT NULL DEFAULT 0,
    supplier_rank INT NOT NULL DEFAULT 0,
    category VARCHAR(100),
    active TINYINT(1) NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES partners(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج شركاء افتراضيين
INSERT INTO partners (name, is_company, email, phone, customer_rank, supplier_rank) VALUES 
('شركة التقنية المتقدمة', 1, '<EMAIL>', '+966 11 234 5678', 1, 0),
('أحمد محمد العلي', 0, '<EMAIL>', '+966 50 123 4567', 1, 0),
('فاطمة أحمد السالم', 0, '<EMAIL>', '+966 55 987 6543', 1, 0),
('شركة الإنشاءات الحديثة', 1, '<EMAIL>', '+966 11 876 5432', 1, 1),
('مؤسسة الخدمات التجارية', 1, '<EMAIL>', '+966 12 345 6789', 0, 1);

-- جدول فئات المنتجات
CREATE TABLE IF NOT EXISTS product_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    parent_id INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES product_categories(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج فئات افتراضية
INSERT INTO product_categories (id, name) VALUES 
(1, 'الإلكترونيات'),
(2, 'الخدمات'),
(3, 'المكتبية'),
(4, 'المواد الاستهلاكية'),
(5, 'الأثاث'),
(6, 'المعدات'),
(7, 'البرمجيات'),
(8, 'الكتب والمراجع'),
(9, 'الملابس'),
(10, 'أخرى');

-- جدول المنتجات
CREATE TABLE IF NOT EXISTS products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    default_code VARCHAR(50),
    barcode VARCHAR(50),
    list_price DECIMAL(15,2) DEFAULT 0.00,
    standard_price DECIMAL(15,2) DEFAULT 0.00,
    categ_id INT NOT NULL,
    type ENUM('product', 'consu', 'service') NOT NULL DEFAULT 'product',
    uom VARCHAR(50) DEFAULT 'قطعة',
    sale_ok TINYINT(1) NOT NULL DEFAULT 1,
    purchase_ok TINYINT(1) NOT NULL DEFAULT 1,
    description TEXT,
    active TINYINT(1) NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (categ_id) REFERENCES product_categories(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج منتجات افتراضية
INSERT INTO products (name, default_code, list_price, standard_price, categ_id, type, sale_ok, purchase_ok, description) VALUES 
('لابتوب Dell Inspiron 15', 'DELL-INS-15', 2500.00, 2000.00, 1, 'product', 1, 1, 'لابتوب Dell Inspiron 15 مع معالج Core i5'),
('خدمة الصيانة الشهرية', 'SERV-MAINT', 150.00, 100.00, 2, 'service', 1, 0, 'خدمة صيانة شهرية للأجهزة'),
('طابعة HP LaserJet Pro', 'HP-LJ-PRO', 800.00, 650.00, 1, 'product', 1, 1, 'طابعة ليزر احترافية من HP'),
('ورق A4 - علبة 500 ورقة', 'PAPER-A4-500', 25.00, 18.00, 3, 'consu', 1, 1, 'ورق طباعة A4 عالي الجودة'),
('استشارة تقنية', 'CONSULT-TECH', 200.00, 150.00, 2, 'service', 1, 0, 'استشارة تقنية متخصصة');

-- جدول دليل الحسابات
CREATE TABLE IF NOT EXISTS chart_of_accounts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(20) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    type ENUM('asset', 'liability', 'equity', 'revenue', 'expense') NOT NULL,
    parent_id INT NULL,
    is_group TINYINT(1) NOT NULL DEFAULT 0,
    active TINYINT(1) NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES chart_of_accounts(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج حسابات افتراضية شاملة
INSERT INTO chart_of_accounts (code, name, type, parent_id, is_group) VALUES
-- الأصول
('1000', 'الأصول', 'asset', NULL, 1),
('1100', 'الأصول المتداولة', 'asset', 1, 1),
('1110', 'النقد وما يعادله', 'asset', 2, 1),
('1111', 'الصندوق', 'asset', 3, 0),
('1112', 'البنك الأهلي', 'asset', 3, 0),
('1113', 'بنك الراجحي', 'asset', 3, 0),
('1114', 'بنك سامبا', 'asset', 3, 0),
('1120', 'الذمم المدينة', 'asset', 2, 1),
('1121', 'ذمم العملاء', 'asset', 8, 0),
('1122', 'أوراق القبض', 'asset', 8, 0),
('1123', 'مخصص الديون المشكوك فيها', 'asset', 8, 0),
('1130', 'المخزون', 'asset', 2, 1),
('1131', 'مخزون المواد الخام', 'asset', 12, 0),
('1132', 'مخزون البضاعة الجاهزة', 'asset', 12, 0),
('1133', 'مخزون قطع الغيار', 'asset', 12, 0),
('1140', 'المصروفات المدفوعة مقدماً', 'asset', 2, 1),
('1141', 'إيجار مدفوع مقدماً', 'asset', 16, 0),
('1142', 'تأمين مدفوع مقدماً', 'asset', 16, 0),
('1200', 'الأصول الثابتة', 'asset', 1, 1),
('1210', 'الأراضي والمباني', 'asset', 19, 1),
('1211', 'الأراضي', 'asset', 20, 0),
('1212', 'المباني', 'asset', 20, 0),
('1213', 'مجمع إهلاك المباني', 'asset', 20, 0),
('1220', 'الآلات والمعدات', 'asset', 19, 1),
('1221', 'الآلات', 'asset', 24, 0),
('1222', 'المعدات', 'asset', 24, 0),
('1223', 'مجمع إهلاك الآلات والمعدات', 'asset', 24, 0),
('1230', 'الأثاث والتجهيزات', 'asset', 19, 1),
('1231', 'الأثاث', 'asset', 28, 0),
('1232', 'أجهزة الكمبيوتر', 'asset', 28, 0),
('1233', 'مجمع إهلاك الأثاث والتجهيزات', 'asset', 28, 0),
('1240', 'وسائل النقل', 'asset', 19, 1),
('1241', 'السيارات', 'asset', 32, 0),
('1242', 'مجمع إهلاك وسائل النقل', 'asset', 32, 0),

-- الخصوم
('2000', 'الخصوم', 'liability', NULL, 1),
('2100', 'الخصوم المتداولة', 'liability', 35, 1),
('2110', 'الذمم الدائنة', 'liability', 36, 1),
('2111', 'ذمم الموردين', 'liability', 37, 0),
('2112', 'أوراق الدفع', 'liability', 37, 0),
('2120', 'المصروفات المستحقة', 'liability', 36, 1),
('2121', 'رواتب مستحقة', 'liability', 40, 0),
('2122', 'إيجار مستحق', 'liability', 40, 0),
('2123', 'فوائد مستحقة', 'liability', 40, 0),
('2130', 'الضرائب المستحقة', 'liability', 36, 1),
('2131', 'ضريبة القيمة المضافة', 'liability', 44, 0),
('2132', 'ضريبة الدخل', 'liability', 44, 0),
('2200', 'الخصوم طويلة الأجل', 'liability', 35, 1),
('2210', 'القروض طويلة الأجل', 'liability', 47, 1),
('2211', 'قرض بنكي', 'liability', 48, 0),
('2212', 'سندات مستحقة الدفع', 'liability', 48, 0),

-- حقوق الملكية
('3000', 'حقوق الملكية', 'equity', NULL, 1),
('3100', 'رأس المال', 'equity', 51, 1),
('3101', 'رأس المال المدفوع', 'equity', 52, 0),
('3102', 'علاوة الإصدار', 'equity', 52, 0),
('3200', 'الأرباح المحتجزة', 'equity', 51, 1),
('3201', 'أرباح محتجزة', 'equity', 55, 0),
('3202', 'أرباح السنة الحالية', 'equity', 55, 0),

-- الإيرادات
('4000', 'الإيرادات', 'revenue', NULL, 1),
('4100', 'إيرادات التشغيل', 'revenue', 58, 1),
('4110', 'إيرادات المبيعات', 'revenue', 59, 1),
('4111', 'مبيعات البضائع', 'revenue', 60, 0),
('4112', 'مبيعات الخدمات', 'revenue', 60, 0),
('4113', 'مردودات ومسموحات المبيعات', 'revenue', 60, 0),
('4120', 'إيرادات أخرى', 'revenue', 59, 1),
('4121', 'إيرادات الاستثمار', 'revenue', 64, 0),
('4122', 'إيرادات الفوائد', 'revenue', 64, 0),
('4123', 'أرباح بيع الأصول', 'revenue', 64, 0),

-- المصروفات
('5000', 'المصروفات', 'expense', NULL, 1),
('5100', 'تكلفة البضاعة المباعة', 'expense', 68, 1),
('5101', 'تكلفة المواد', 'expense', 69, 0),
('5102', 'تكلفة العمالة', 'expense', 69, 0),
('5103', 'تكاليف صناعية', 'expense', 69, 0),
('5200', 'مصروفات التشغيل', 'expense', 68, 1),
('5210', 'مصروفات البيع والتسويق', 'expense', 73, 1),
('5211', 'رواتب قسم المبيعات', 'expense', 74, 0),
('5212', 'عمولات المبيعات', 'expense', 74, 0),
('5213', 'مصروفات الإعلان', 'expense', 74, 0),
('5214', 'مصروفات الشحن', 'expense', 74, 0),
('5220', 'مصروفات إدارية', 'expense', 73, 1),
('5221', 'رواتب الإدارة', 'expense', 79, 0),
('5222', 'إيجار المكتب', 'expense', 79, 0),
('5223', 'مصروفات الكهرباء', 'expense', 79, 0),
('5224', 'مصروفات الهاتف والإنترنت', 'expense', 79, 0),
('5225', 'مصروفات القرطاسية', 'expense', 79, 0),
('5226', 'مصروفات الصيانة', 'expense', 79, 0),
('5227', 'مصروفات التأمين', 'expense', 79, 0),
('5228', 'مصروفات قانونية ومهنية', 'expense', 79, 0),
('5230', 'مصروفات الإهلاك', 'expense', 73, 1),
('5231', 'إهلاك المباني', 'expense', 88, 0),
('5232', 'إهلاك الآلات والمعدات', 'expense', 88, 0),
('5233', 'إهلاك الأثاث والتجهيزات', 'expense', 88, 0),
('5234', 'إهلاك وسائل النقل', 'expense', 88, 0),
('5300', 'مصروفات أخرى', 'expense', 68, 1),
('5301', 'مصروفات الفوائد', 'expense', 93, 0),
('5302', 'خسائر بيع الأصول', 'expense', 93, 0),
('5303', 'مصروفات متنوعة', 'expense', 93, 0);

-- جدول دفاتر اليومية
CREATE TABLE IF NOT EXISTS journals (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(20) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    type ENUM('sale', 'purchase', 'cash', 'bank', 'general', 'misc') NOT NULL,
    active TINYINT(1) NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج دفاتر افتراضية
INSERT INTO journals (code, name, type) VALUES 
('SALE', 'دفتر المبيعات', 'sale'),
('PURCH', 'دفتر المشتريات', 'purchase'),
('CASH', 'دفتر الصندوق', 'cash'),
('BANK', 'دفتر البنك', 'bank'),
('GEN', 'دفتر اليومية العام', 'general'),
('MISC', 'دفتر متنوع', 'misc');

-- جدول قيود اليومية
CREATE TABLE IF NOT EXISTS journal_entries (
    id INT AUTO_INCREMENT PRIMARY KEY,
    journal_id INT NOT NULL,
    reference VARCHAR(50),
    date DATE NOT NULL,
    state ENUM('draft', 'posted', 'cancelled') NOT NULL DEFAULT 'draft',
    narration TEXT,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (journal_id) REFERENCES journals(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول بنود قيود اليومية
CREATE TABLE IF NOT EXISTS journal_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    entry_id INT NOT NULL,
    account_id INT NOT NULL,
    partner_id INT,
    name VARCHAR(255) NOT NULL,
    debit DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    credit DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (entry_id) REFERENCES journal_entries(id) ON DELETE CASCADE,
    FOREIGN KEY (account_id) REFERENCES chart_of_accounts(id),
    FOREIGN KEY (partner_id) REFERENCES partners(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الفترات المحاسبية
CREATE TABLE IF NOT EXISTS accounting_periods (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    state ENUM('open', 'closed') NOT NULL DEFAULT 'open',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج فترة محاسبية افتراضية
INSERT INTO accounting_periods (name, start_date, end_date) VALUES 
('الربع الأول 2023', '2023-01-01', '2023-03-31'),
('الربع الثاني 2023', '2023-04-01', '2023-06-30'),
('الربع الثالث 2023', '2023-07-01', '2023-09-30'),
('الربع الرابع 2023', '2023-10-01', '2023-12-31');

-- جدول إعدادات النظام
CREATE TABLE IF NOT EXISTS settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    description VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج إعدادات افتراضية شاملة
INSERT INTO settings (setting_key, setting_value, description) VALUES
-- إعدادات الشركة
('company_name', 'نظام ERP المحاسبي', 'اسم الشركة'),
('company_legal_name', 'شركة نظام ERP المحاسبي المحدودة', 'الاسم القانوني للشركة'),
('company_tax_number', '123456789012345', 'الرقم الضريبي للشركة'),
('company_address', 'الرياض، المملكة العربية السعودية', 'عنوان الشركة'),
('company_phone', '+966 11 123 4567', 'هاتف الشركة'),
('company_email', '<EMAIL>', 'بريد الشركة الإلكتروني'),
('company_website', 'www.erp-system.com', 'موقع الشركة الإلكتروني'),

-- إعدادات العملة والتنسيق
('currency', 'SAR', 'العملة الافتراضية'),
('currency_symbol', 'ر.س', 'رمز العملة'),
('currency_position', 'after', 'موضع رمز العملة (before/after)'),
('date_format', 'Y-m-d', 'تنسيق التاريخ'),
('time_format', 'H:i:s', 'تنسيق الوقت'),
('datetime_format', 'Y-m-d H:i:s', 'تنسيق التاريخ والوقت'),
('decimal_places', '2', 'عدد الخانات العشرية'),
('thousands_separator', ',', 'فاصل الآلاف'),
('decimal_separator', '.', 'فاصل العشرية'),

-- إعدادات السنة المالية
('fiscal_year_start', '01-01', 'بداية السنة المالية'),
('fiscal_year_end', '12-31', 'نهاية السنة المالية'),
('fiscal_year_current', '2024', 'السنة المالية الحالية'),

-- إعدادات النظام
('default_language', 'ar', 'اللغة الافتراضية'),
('system_theme', 'light', 'سمة النظام'),
('timezone', 'Asia/Riyadh', 'المنطقة الزمنية'),
('week_start', '6', 'بداية الأسبوع (0=الأحد، 6=السبت)'),

-- إعدادات المبيعات
('default_sale_journal', '1', 'دفتر المبيعات الافتراضي'),
('default_customer_payment_term', '30', 'مدة الدفع الافتراضية للعملاء (بالأيام)'),
('auto_invoice_sale_order', '1', 'إنشاء فاتورة تلقائياً عند تأكيد طلب البيع'),
('sale_order_validity_days', '30', 'مدة صلاحية عرض السعر (بالأيام)'),

-- إعدادات المشتريات
('default_purchase_journal', '2', 'دفتر المشتريات الافتراضي'),
('default_supplier_payment_term', '30', 'مدة الدفع الافتراضية للموردين (بالأيام)'),
('auto_invoice_purchase_order', '0', 'إنشاء فاتورة تلقائياً عند تأكيد طلب الشراء'),
('purchase_order_approval_required', '1', 'يتطلب موافقة على طلبات الشراء'),

-- إعدادات المخزون
('default_warehouse', '1', 'المستودع الافتراضي'),
('auto_validate_stock_moves', '0', 'تأكيد حركات المخزون تلقائياً'),
('negative_stock_allowed', '0', 'السماح بالمخزون السالب'),
('stock_valuation_method', 'fifo', 'طريقة تقييم المخزون (fifo/lifo/average)'),

-- إعدادات المحاسبة
('default_cash_journal', '3', 'دفتر الصندوق الافتراضي'),
('default_bank_journal', '4', 'دفتر البنك الافتراضي'),
('auto_reconcile_payments', '1', 'تسوية المدفوعات تلقائياً'),
('lock_posted_entries', '1', 'منع تعديل القيود المرحلة'),

-- إعدادات الضرائب
('default_sale_tax', '1', 'الضريبة الافتراضية للمبيعات'),
('default_purchase_tax', '2', 'الضريبة الافتراضية للمشتريات'),
('tax_calculation_rounding', 'round_per_line', 'طريقة تقريب الضريبة'),

-- إعدادات التقارير
('report_logo', '', 'شعار التقارير'),
('report_header', '1', 'عرض رأس الصفحة في التقارير'),
('report_footer', '1', 'عرض تذييل الصفحة في التقارير'),
('report_page_numbers', '1', 'عرض أرقام الصفحات في التقارير'),

-- إعدادات الأمان
('session_timeout', '3600', 'مهلة انتهاء الجلسة (بالثواني)'),
('password_min_length', '6', 'الحد الأدنى لطول كلمة المرور'),
('max_login_attempts', '5', 'الحد الأقصى لمحاولات تسجيل الدخول'),
('auto_logout_inactive', '1800', 'تسجيل خروج تلقائي عند عدم النشاط (بالثواني)'),

-- إعدادات البريد الإلكتروني
('email_smtp_server', '', 'خادم SMTP'),
('email_smtp_port', '587', 'منفذ SMTP'),
('email_smtp_user', '', 'مستخدم SMTP'),
('email_smtp_password', '', 'كلمة مرور SMTP'),
('email_from_name', 'نظام ERP المحاسبي', 'اسم المرسل'),
('email_from_address', '<EMAIL>', 'عنوان المرسل'),

-- إعدادات النسخ الاحتياطي
('backup_auto_enabled', '1', 'تفعيل النسخ الاحتياطي التلقائي'),
('backup_frequency', 'daily', 'تكرار النسخ الاحتياطي'),
('backup_retention_days', '30', 'مدة الاحتفاظ بالنسخ الاحتياطية'),
('backup_location', 'backups/', 'مجلد النسخ الاحتياطية'),

-- إعدادات الإشعارات
('notifications_enabled', '1', 'تفعيل الإشعارات'),
('email_notifications', '1', 'إشعارات البريد الإلكتروني'),
('sms_notifications', '0', 'إشعارات الرسائل النصية'),
('system_notifications', '1', 'إشعارات النظام'),

-- إعدادات الأداء
('cache_enabled', '1', 'تفعيل التخزين المؤقت'),
('cache_duration', '3600', 'مدة التخزين المؤقت (بالثواني)'),
('pagination_limit', '50', 'عدد السجلات في الصفحة الواحدة'),
('search_limit', '100', 'الحد الأقصى لنتائج البحث'),

-- إعدادات التكامل
('api_enabled', '1', 'تفعيل واجهة برمجة التطبيقات'),
('api_rate_limit', '1000', 'حد معدل استخدام API (طلب/ساعة)'),
('webhook_enabled', '0', 'تفعيل Webhooks'),
('external_integrations', '0', 'تفعيل التكاملات الخارجية');

-- ===============================================
-- وحدة المبيعات (Sales Module)
-- ===============================================

-- جدول عروض الأسعار والطلبات
CREATE TABLE IF NOT EXISTS sale_orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    partner_id INT NOT NULL,
    date_order DATE NOT NULL,
    validity_date DATE,
    state ENUM('draft', 'sent', 'sale', 'done', 'cancel') NOT NULL DEFAULT 'draft',
    amount_untaxed DECIMAL(15,2) DEFAULT 0.00,
    amount_tax DECIMAL(15,2) DEFAULT 0.00,
    amount_total DECIMAL(15,2) DEFAULT 0.00,
    currency_id VARCHAR(10) DEFAULT 'SAR',
    payment_term VARCHAR(100),
    note TEXT,
    user_id INT NOT NULL,
    team_id INT,
    company_id INT DEFAULT 1,
    invoice_status ENUM('no', 'to_invoice', 'invoiced') DEFAULT 'no',
    delivery_status ENUM('no', 'partial', 'full') DEFAULT 'no',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (partner_id) REFERENCES partners(id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (company_id) REFERENCES companies(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول بنود طلبات المبيعات
CREATE TABLE IF NOT EXISTS sale_order_lines (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    product_id INT NOT NULL,
    name TEXT NOT NULL,
    product_qty DECIMAL(12,3) DEFAULT 1.000,
    product_uom VARCHAR(50) DEFAULT 'قطعة',
    price_unit DECIMAL(15,2) NOT NULL,
    discount DECIMAL(5,2) DEFAULT 0.00,
    price_subtotal DECIMAL(15,2) NOT NULL,
    price_total DECIMAL(15,2) NOT NULL,
    tax_id VARCHAR(100),
    sequence INT DEFAULT 10,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES sale_orders(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول فرق المبيعات
CREATE TABLE IF NOT EXISTS crm_teams (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    user_id INT,
    member_ids TEXT,
    active TINYINT(1) NOT NULL DEFAULT 1,
    company_id INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (company_id) REFERENCES companies(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===============================================
-- وحدة المشتريات (Purchase Module)
-- ===============================================

-- جدول طلبات الشراء
CREATE TABLE IF NOT EXISTS purchase_orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    partner_id INT NOT NULL,
    date_order DATE NOT NULL,
    date_planned DATE,
    state ENUM('draft', 'sent', 'to_approve', 'purchase', 'done', 'cancel') NOT NULL DEFAULT 'draft',
    amount_untaxed DECIMAL(15,2) DEFAULT 0.00,
    amount_tax DECIMAL(15,2) DEFAULT 0.00,
    amount_total DECIMAL(15,2) DEFAULT 0.00,
    currency_id VARCHAR(10) DEFAULT 'SAR',
    payment_term VARCHAR(100),
    notes TEXT,
    user_id INT NOT NULL,
    company_id INT DEFAULT 1,
    invoice_status ENUM('no', 'to_invoice', 'invoiced') DEFAULT 'no',
    receipt_status ENUM('no', 'partial', 'full') DEFAULT 'no',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (partner_id) REFERENCES partners(id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (company_id) REFERENCES companies(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول بنود طلبات الشراء
CREATE TABLE IF NOT EXISTS purchase_order_lines (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    product_id INT NOT NULL,
    name TEXT NOT NULL,
    product_qty DECIMAL(12,3) DEFAULT 1.000,
    product_uom VARCHAR(50) DEFAULT 'قطعة',
    price_unit DECIMAL(15,2) NOT NULL,
    discount DECIMAL(5,2) DEFAULT 0.00,
    price_subtotal DECIMAL(15,2) NOT NULL,
    price_total DECIMAL(15,2) NOT NULL,
    tax_id VARCHAR(100),
    date_planned DATE,
    sequence INT DEFAULT 10,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES purchase_orders(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===============================================
-- وحدة المخزون (Inventory Module)
-- ===============================================

-- جدول المستودعات
CREATE TABLE IF NOT EXISTS stock_warehouses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(20) NOT NULL UNIQUE,
    partner_id INT,
    company_id INT DEFAULT 1,
    active TINYINT(1) NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (partner_id) REFERENCES partners(id),
    FOREIGN KEY (company_id) REFERENCES companies(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول المواقع
CREATE TABLE IF NOT EXISTS stock_locations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    complete_name VARCHAR(255),
    location_id INT,
    usage ENUM('supplier', 'view', 'internal', 'customer', 'inventory', 'procurement', 'production', 'transit') NOT NULL DEFAULT 'internal',
    company_id INT DEFAULT 1,
    active TINYINT(1) NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (location_id) REFERENCES stock_locations(id),
    FOREIGN KEY (company_id) REFERENCES companies(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول حركات المخزون
CREATE TABLE IF NOT EXISTS stock_moves (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    product_id INT NOT NULL,
    product_qty DECIMAL(12,3) NOT NULL,
    product_uom VARCHAR(50) DEFAULT 'قطعة',
    location_id INT NOT NULL,
    location_dest_id INT NOT NULL,
    partner_id INT,
    picking_id INT,
    origin VARCHAR(100),
    state ENUM('draft', 'waiting', 'confirmed', 'assigned', 'done', 'cancel') NOT NULL DEFAULT 'draft',
    date DATE NOT NULL,
    date_expected DATE,
    company_id INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id),
    FOREIGN KEY (location_id) REFERENCES stock_locations(id),
    FOREIGN KEY (location_dest_id) REFERENCES stock_locations(id),
    FOREIGN KEY (partner_id) REFERENCES partners(id),
    FOREIGN KEY (company_id) REFERENCES companies(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الكميات المتاحة
CREATE TABLE IF NOT EXISTS stock_quants (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_id INT NOT NULL,
    location_id INT NOT NULL,
    quantity DECIMAL(12,3) NOT NULL DEFAULT 0.000,
    reserved_quantity DECIMAL(12,3) NOT NULL DEFAULT 0.000,
    lot_id INT,
    package_id INT,
    owner_id INT,
    company_id INT DEFAULT 1,
    in_date DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id),
    FOREIGN KEY (location_id) REFERENCES stock_locations(id),
    FOREIGN KEY (owner_id) REFERENCES partners(id),
    FOREIGN KEY (company_id) REFERENCES companies(id),
    UNIQUE KEY unique_product_location (product_id, location_id, lot_id, package_id, owner_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===============================================
-- وحدة الفواتير (Invoicing Module)
-- ===============================================

-- جدول الفواتير
CREATE TABLE IF NOT EXISTS account_moves (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100),
    ref VARCHAR(100),
    move_type ENUM('entry', 'out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt') NOT NULL DEFAULT 'entry',
    partner_id INT,
    date DATE NOT NULL,
    invoice_date DATE,
    invoice_date_due DATE,
    state ENUM('draft', 'posted', 'cancel') NOT NULL DEFAULT 'draft',
    amount_untaxed DECIMAL(15,2) DEFAULT 0.00,
    amount_tax DECIMAL(15,2) DEFAULT 0.00,
    amount_total DECIMAL(15,2) DEFAULT 0.00,
    amount_residual DECIMAL(15,2) DEFAULT 0.00,
    currency_id VARCHAR(10) DEFAULT 'SAR',
    journal_id INT NOT NULL,
    company_id INT DEFAULT 1,
    user_id INT NOT NULL,
    payment_state ENUM('not_paid', 'in_payment', 'paid', 'partial', 'reversed', 'invoicing_legacy') DEFAULT 'not_paid',
    payment_reference VARCHAR(100),
    narration TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (partner_id) REFERENCES partners(id),
    FOREIGN KEY (journal_id) REFERENCES journals(id),
    FOREIGN KEY (company_id) REFERENCES companies(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول بنود الفواتير
CREATE TABLE IF NOT EXISTS account_move_lines (
    id INT AUTO_INCREMENT PRIMARY KEY,
    move_id INT NOT NULL,
    account_id INT NOT NULL,
    partner_id INT,
    product_id INT,
    name TEXT NOT NULL,
    quantity DECIMAL(12,3) DEFAULT 1.000,
    product_uom_id VARCHAR(50),
    price_unit DECIMAL(15,2) DEFAULT 0.00,
    discount DECIMAL(5,2) DEFAULT 0.00,
    debit DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    credit DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    balance DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    amount_currency DECIMAL(15,2) DEFAULT 0.00,
    currency_id VARCHAR(10),
    tax_ids TEXT,
    tax_line_id INT,
    analytic_account_id INT,
    date DATE NOT NULL,
    date_maturity DATE,
    ref VARCHAR(100),
    sequence INT DEFAULT 10,
    company_id INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (move_id) REFERENCES account_moves(id) ON DELETE CASCADE,
    FOREIGN KEY (account_id) REFERENCES chart_of_accounts(id),
    FOREIGN KEY (partner_id) REFERENCES partners(id),
    FOREIGN KEY (product_id) REFERENCES products(id),
    FOREIGN KEY (company_id) REFERENCES companies(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===============================================
-- وحدة المدفوعات (Payments Module)
-- ===============================================

-- جدول المدفوعات
CREATE TABLE IF NOT EXISTS account_payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100),
    payment_type ENUM('outbound', 'inbound', 'transfer') NOT NULL,
    partner_type ENUM('customer', 'supplier') NOT NULL,
    partner_id INT,
    amount DECIMAL(15,2) NOT NULL,
    currency_id VARCHAR(10) DEFAULT 'SAR',
    payment_date DATE NOT NULL,
    communication VARCHAR(255),
    journal_id INT NOT NULL,
    payment_method_id INT,
    destination_journal_id INT,
    state ENUM('draft', 'posted', 'sent', 'reconciled', 'cancelled') NOT NULL DEFAULT 'draft',
    move_id INT,
    company_id INT DEFAULT 1,
    user_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (partner_id) REFERENCES partners(id),
    FOREIGN KEY (journal_id) REFERENCES journals(id),
    FOREIGN KEY (destination_journal_id) REFERENCES journals(id),
    FOREIGN KEY (move_id) REFERENCES account_moves(id),
    FOREIGN KEY (company_id) REFERENCES companies(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول طرق الدفع
CREATE TABLE IF NOT EXISTS account_payment_methods (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) NOT NULL UNIQUE,
    payment_type ENUM('inbound', 'outbound') NOT NULL,
    active TINYINT(1) NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===============================================
-- وحدة الضرائب (Tax Module)
-- ===============================================

-- جدول الضرائب
CREATE TABLE IF NOT EXISTS account_taxes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    type_tax_use ENUM('sale', 'purchase', 'none') NOT NULL DEFAULT 'sale',
    amount_type ENUM('group', 'fixed', 'percent', 'division') NOT NULL DEFAULT 'percent',
    amount DECIMAL(10,4) NOT NULL DEFAULT 0.0000,
    active TINYINT(1) NOT NULL DEFAULT 1,
    company_id INT DEFAULT 1,
    sequence INT DEFAULT 1,
    description VARCHAR(255),
    price_include TINYINT(1) NOT NULL DEFAULT 0,
    include_base_amount TINYINT(1) NOT NULL DEFAULT 0,
    analytic TINYINT(1) NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===============================================
-- وحدة إدارة علاقات العملاء (CRM Module)
-- ===============================================

-- جدول العملاء المحتملين
CREATE TABLE IF NOT EXISTS crm_leads (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    partner_name VARCHAR(100),
    email_from VARCHAR(100),
    phone VARCHAR(50),
    mobile VARCHAR(50),
    website VARCHAR(100),
    street VARCHAR(255),
    street2 VARCHAR(255),
    city VARCHAR(100),
    zip VARCHAR(20),
    country VARCHAR(100),
    function VARCHAR(100),
    title VARCHAR(100),
    description TEXT,
    user_id INT,
    team_id INT,
    stage_id INT,
    source_id INT,
    medium_id INT,
    campaign_id INT,
    referred VARCHAR(100),
    date_open DATETIME,
    date_closed DATETIME,
    date_last_stage_update DATETIME,
    priority ENUM('0', '1', '2', '3') DEFAULT '1',
    color INT DEFAULT 0,
    kanban_state ENUM('normal', 'blocked', 'done') DEFAULT 'normal',
    email_cc TEXT,
    partner_id INT,
    active TINYINT(1) NOT NULL DEFAULT 1,
    type ENUM('lead', 'opportunity') NOT NULL DEFAULT 'lead',
    probability DECIMAL(5,2) DEFAULT 0.00,
    planned_revenue DECIMAL(15,2) DEFAULT 0.00,
    expected_revenue DECIMAL(15,2) DEFAULT 0.00,
    date_deadline DATE,
    company_id INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (team_id) REFERENCES crm_teams(id),
    FOREIGN KEY (partner_id) REFERENCES partners(id),
    FOREIGN KEY (company_id) REFERENCES companies(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول مراحل CRM
CREATE TABLE IF NOT EXISTS crm_stages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    sequence INT DEFAULT 1,
    probability DECIMAL(5,2) DEFAULT 0.00,
    on_change TINYINT(1) NOT NULL DEFAULT 1,
    requirements TEXT,
    team_id INT,
    fold TINYINT(1) NOT NULL DEFAULT 0,
    active TINYINT(1) NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (team_id) REFERENCES crm_teams(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===============================================
-- وحدة الموارد البشرية (HR Module)
-- ===============================================

-- جدول الموظفين
CREATE TABLE IF NOT EXISTS hr_employees (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    user_id INT,
    active TINYINT(1) NOT NULL DEFAULT 1,
    company_id INT DEFAULT 1,
    address_home_id INT,
    country_id VARCHAR(100),
    gender ENUM('male', 'female', 'other'),
    marital ENUM('single', 'married', 'cohabitant', 'widower', 'divorced'),
    spouse_complete_name VARCHAR(100),
    spouse_birthdate DATE,
    children INT DEFAULT 0,
    place_of_birth VARCHAR(100),
    country_of_birth VARCHAR(100),
    birthday DATE,
    ssnid VARCHAR(50),
    sinid VARCHAR(50),
    identification_id VARCHAR(50),
    passport_id VARCHAR(50),
    bank_account_id VARCHAR(100),
    permit_no VARCHAR(50),
    visa_no VARCHAR(50),
    visa_expire DATE,
    work_phone VARCHAR(50),
    mobile_phone VARCHAR(50),
    work_email VARCHAR(100),
    work_location VARCHAR(100),
    job_title VARCHAR(100),
    department_id INT,
    parent_id INT,
    coach_id INT,
    job_id INT,
    resource_id INT,
    resource_calendar_id INT,
    tz VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (company_id) REFERENCES companies(id),
    FOREIGN KEY (address_home_id) REFERENCES partners(id),
    FOREIGN KEY (parent_id) REFERENCES hr_employees(id),
    FOREIGN KEY (coach_id) REFERENCES hr_employees(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الأقسام
CREATE TABLE IF NOT EXISTS hr_departments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    complete_name VARCHAR(255),
    active TINYINT(1) NOT NULL DEFAULT 1,
    company_id INT DEFAULT 1,
    parent_id INT,
    manager_id INT,
    note TEXT,
    color INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id),
    FOREIGN KEY (parent_id) REFERENCES hr_departments(id),
    FOREIGN KEY (manager_id) REFERENCES hr_employees(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الوظائف
CREATE TABLE IF NOT EXISTS hr_jobs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    expected_employees INT DEFAULT 1,
    no_of_employee INT DEFAULT 0,
    no_of_recruitment INT DEFAULT 0,
    no_of_hired_employee INT DEFAULT 0,
    description TEXT,
    requirements TEXT,
    department_id INT,
    company_id INT DEFAULT 1,
    state ENUM('recruit', 'open') DEFAULT 'recruit',
    active TINYINT(1) NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (department_id) REFERENCES hr_departments(id),
    FOREIGN KEY (company_id) REFERENCES companies(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===============================================
-- وحدة إدارة المشاريع (Project Module)
-- ===============================================

-- جدول المشاريع
CREATE TABLE IF NOT EXISTS projects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    active TINYINT(1) NOT NULL DEFAULT 1,
    sequence INT DEFAULT 10,
    partner_id INT,
    user_id INT,
    date_start DATE,
    date DATE,
    description TEXT,
    privacy_visibility ENUM('portal', 'employees', 'followers') DEFAULT 'employees',
    alias_name VARCHAR(100),
    color INT DEFAULT 0,
    company_id INT DEFAULT 1,
    analytic_account_id INT,
    favorite_user_ids TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (partner_id) REFERENCES partners(id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (company_id) REFERENCES companies(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول المهام
CREATE TABLE IF NOT EXISTS project_tasks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    active TINYINT(1) NOT NULL DEFAULT 1,
    description TEXT,
    priority ENUM('0', '1') DEFAULT '0',
    sequence INT DEFAULT 10,
    stage_id INT,
    tag_ids TEXT,
    kanban_state ENUM('normal', 'blocked', 'done') DEFAULT 'normal',
    kanban_state_label VARCHAR(100),
    color INT DEFAULT 0,
    user_id INT,
    partner_id INT,
    manager_id INT,
    company_id INT DEFAULT 1,
    project_id INT,
    planned_hours DECIMAL(8,2) DEFAULT 0.00,
    effective_hours DECIMAL(8,2) DEFAULT 0.00,
    subtask_effective_hours DECIMAL(8,2) DEFAULT 0.00,
    remaining_hours DECIMAL(8,2) DEFAULT 0.00,
    progress DECIMAL(5,2) DEFAULT 0.00,
    delay_hours DECIMAL(8,2) DEFAULT 0.00,
    date_start DATETIME,
    date_end DATETIME,
    date_assign DATETIME,
    date_deadline DATE,
    date_last_stage_update DATETIME,
    email_from VARCHAR(100),
    email_cc TEXT,
    parent_id INT,
    subtask_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (partner_id) REFERENCES partners(id),
    FOREIGN KEY (manager_id) REFERENCES users(id),
    FOREIGN KEY (company_id) REFERENCES companies(id),
    FOREIGN KEY (project_id) REFERENCES projects(id),
    FOREIGN KEY (parent_id) REFERENCES project_tasks(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===============================================
-- إدراج البيانات الافتراضية للوحدات الجديدة
-- ===============================================

-- إدراج المستودعات الافتراضية
INSERT INTO stock_warehouses (name, code, company_id) VALUES
('المستودع الرئيسي', 'WH01', 1),
('مستودع فرع الرياض', 'WH02', 1),
('مستودع فرع جدة', 'WH03', 1);

-- إدراج المواقع الافتراضية
INSERT INTO stock_locations (id, name, complete_name, location_id, usage, company_id) VALUES
(1, 'المواقع الفعلية', 'المواقع الفعلية', NULL, 'view', 1),
(2, 'المستودع الرئيسي', 'المواقع الفعلية/المستودع الرئيسي', 1, 'internal', 1),
(3, 'العملاء', 'العملاء', NULL, 'customer', 1),
(4, 'الموردين', 'الموردين', NULL, 'supplier', 1),
(5, 'الجرد', 'الجرد', NULL, 'inventory', 1),
(6, 'الإنتاج', 'الإنتاج', NULL, 'production', 1),
(7, 'العبور', 'العبور', NULL, 'transit', 1);

-- إدراج طرق الدفع الافتراضية
INSERT INTO account_payment_methods (name, code, payment_type) VALUES
('نقدي', 'manual', 'inbound'),
('نقدي', 'manual', 'outbound'),
('تحويل بنكي', 'electronic', 'inbound'),
('تحويل بنكي', 'electronic', 'outbound'),
('شيك', 'check_printing', 'outbound'),
('بطاقة ائتمان', 'credit_card', 'inbound');

-- إدراج الضرائب الافتراضية
INSERT INTO account_taxes (name, type_tax_use, amount_type, amount, company_id, description) VALUES
('ضريبة القيمة المضافة 15%', 'sale', 'percent', 15.0000, 1, 'ضريبة القيمة المضافة للمبيعات'),
('ضريبة القيمة المضافة 15% - مشتريات', 'purchase', 'percent', 15.0000, 1, 'ضريبة القيمة المضافة للمشتريات'),
('معفى من الضريبة', 'sale', 'percent', 0.0000, 1, 'معفى من ضريبة القيمة المضافة'),
('ضريبة صفر', 'sale', 'percent', 0.0000, 1, 'ضريبة بمعدل صفر');

-- إدراج فرق المبيعات الافتراضية
INSERT INTO crm_teams (name, user_id, company_id) VALUES
('فريق المبيعات الرئيسي', 1, 1),
('فريق مبيعات الشركات', 1, 1),
('فريق مبيعات التجزئة', 1, 1);

-- إدراج مراحل CRM الافتراضية
INSERT INTO crm_stages (name, sequence, probability, team_id) VALUES
('عميل محتمل جديد', 1, 10.00, 1),
('مؤهل', 2, 20.00, 1),
('اقتراح', 3, 40.00, 1),
('تفاوض', 4, 60.00, 1),
('فاز', 5, 100.00, 1),
('خسر', 6, 0.00, 1);

-- إدراج الأقسام الافتراضية
INSERT INTO hr_departments (name, complete_name, company_id) VALUES
('الإدارة العامة', 'الإدارة العامة', 1),
('المبيعات والتسويق', 'المبيعات والتسويق', 1),
('المحاسبة والمالية', 'المحاسبة والمالية', 1),
('تقنية المعلومات', 'تقنية المعلومات', 1),
('الموارد البشرية', 'الموارد البشرية', 1),
('العمليات والإنتاج', 'العمليات والإنتاج', 1);

-- إدراج الوظائف الافتراضية
INSERT INTO hr_jobs (name, expected_employees, department_id, company_id, description) VALUES
('مدير عام', 1, 1, 1, 'مسؤول عن الإدارة العامة للشركة'),
('مدير مبيعات', 1, 2, 1, 'مسؤول عن إدارة فريق المبيعات'),
('محاسب', 2, 3, 1, 'مسؤول عن العمليات المحاسبية'),
('مطور برمجيات', 3, 4, 1, 'مسؤول عن تطوير وصيانة الأنظمة'),
('أخصائي موارد بشرية', 1, 5, 1, 'مسؤول عن شؤون الموظفين'),
('مشرف إنتاج', 2, 6, 1, 'مسؤول عن الإشراف على العمليات');

-- إدراج الموظفين الافتراضيين
INSERT INTO hr_employees (name, user_id, company_id, work_email, job_title, department_id, work_phone) VALUES
('مدير النظام', 1, 1, '<EMAIL>', 'مدير عام', 1, '+966 11 123 4567');

-- إدراج المشاريع الافتراضية
INSERT INTO projects (name, user_id, company_id, description, date_start) VALUES
('تطوير نظام ERP', 1, 1, 'مشروع تطوير وتحسين نظام إدارة موارد المؤسسة', '2024-01-01'),
('تحسين العمليات التشغيلية', 1, 1, 'مشروع تحسين وتطوير العمليات التشغيلية', '2024-02-01'),
('تدريب الموظفين', 1, 1, 'مشروع تدريب الموظفين على النظام الجديد', '2024-03-01');

-- إدراج المهام الافتراضية
INSERT INTO project_tasks (name, project_id, user_id, company_id, description, planned_hours, date_deadline) VALUES
('تحليل المتطلبات', 1, 1, 1, 'تحليل وتوثيق متطلبات النظام', 40.00, '2024-01-15'),
('تصميم قاعدة البيانات', 1, 1, 1, 'تصميم وإنشاء قاعدة البيانات', 60.00, '2024-01-30'),
('تطوير الواجهات', 1, 1, 1, 'تطوير واجهات المستخدم', 120.00, '2024-02-28'),
('اختبار النظام', 1, 1, 1, 'اختبار وتجربة جميع وظائف النظام', 80.00, '2024-03-15'),
('تدريب المستخدمين', 3, 1, 1, 'تدريب المستخدمين على استخدام النظام', 40.00, '2024-03-30');

-- إدراج عملاء محتملين افتراضيين
INSERT INTO crm_leads (name, partner_name, email_from, phone, user_id, team_id, stage_id, type, probability, planned_revenue, company_id) VALUES
('فرصة بيع نظام محاسبي', 'شركة الأعمال المتقدمة', '<EMAIL>', '+966 11 555 0001', 1, 1, 1, 'opportunity', 25.00, 50000.00, 1),
('عميل محتمل - خدمات استشارية', 'مؤسسة التطوير الحديث', '<EMAIL>', '+966 12 555 0002', 1, 1, 2, 'opportunity', 40.00, 25000.00, 1),
('طلب عرض سعر - أجهزة كمبيوتر', 'شركة التقنيات الذكية', '<EMAIL>', '+966 13 555 0003', 1, 1, 3, 'opportunity', 60.00, 75000.00, 1);

-- تحديث تسلسل الجداول
ALTER TABLE sale_orders AUTO_INCREMENT = 1001;
ALTER TABLE purchase_orders AUTO_INCREMENT = 2001;
ALTER TABLE account_moves AUTO_INCREMENT = 3001;
ALTER TABLE account_payments AUTO_INCREMENT = 4001;
ALTER TABLE crm_leads AUTO_INCREMENT = 5001;
ALTER TABLE projects AUTO_INCREMENT = 6001;
ALTER TABLE project_tasks AUTO_INCREMENT = 7001;
