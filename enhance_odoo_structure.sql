-- Enable foreign key checks
SET FOREIGN_KEY_CHECKS = 0;

-- =============================================
-- Module: Accounting Enhancements
-- =============================================
CREATE TABLE IF NOT EXISTS `account_tax` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(64) NOT NULL,
    `amount` DECIMAL(16,4) DEFAULT 0.0,
    `amount_type` VARCHAR(32) DEFAULT 'percent',
    `type_tax_use` VARCHAR(32) NOT NULL, -- sale, purchase, none, all
    `account_id` INT,
    `refund_account_id` INT,
    `company_id` INT,
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME,
    FOR<PERSON><PERSON>N KEY (`account_id`) REFERENCES `account_account`(`id`) ON DELETE SET NULL,
    F<PERSON>EIG<PERSON> KEY (`refund_account_id`) REFERENCES `account_account`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `account_payment` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(64) NOT NULL,
    `payment_type` VARCHAR(32) NOT NULL, -- in, out
    `partner_type` VARCHAR(32), -- customer, supplier
    `partner_id` INT,
    `amount` DECIMAL(16,2) NOT NULL,
    `currency_id` INT,
    `payment_date` DATE,
    `journal_id` INT,
    `state` VARCHAR(32) DEFAULT 'draft',
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME,
    FOREIGN KEY (`partner_id`) REFERENCES `res_partner`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`journal_id`) REFERENCES `account_journal`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- =============================================
-- Module: Inventory Enhancements
-- =============================================
CREATE TABLE IF NOT EXISTS `stock_quant` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `product_id` INT,
    `location_id` INT,
    `quantity` DECIMAL(16,2) NOT NULL,
    `reserved_quantity` DECIMAL(16,2) DEFAULT 0.0,
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME,
    FOREIGN KEY (`product_id`) REFERENCES `product_template`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`location_id`) REFERENCES `stock_location`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `stock_picking` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(64) NOT NULL,
    `origin` VARCHAR(64),
    `partner_id` INT,
    `picking_type_id` INT,
    `location_id` INT,
    `location_dest_id` INT,
    `state` VARCHAR(32) DEFAULT 'draft',
    `scheduled_date` DATETIME,
    `date_done` DATETIME,
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME,
    FOREIGN KEY (`partner_id`) REFERENCES `res_partner`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`location_id`) REFERENCES `stock_location`(`id`),
    FOREIGN KEY (`location_dest_id`) REFERENCES `stock_location`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- =============================================
-- Module: Sales & Purchase Enhancements
-- =============================================
ALTER TABLE `sale_order` 
ADD COLUMN IF NOT EXISTS `order_line` TEXT AFTER `partner_id`,
ADD COLUMN IF NOT EXISTS `amount_untaxed` DECIMAL(16,2) DEFAULT 0.0 AFTER `amount_total`,
ADD COLUMN IF NOT EXISTS `amount_tax` DECIMAL(16,2) DEFAULT 0.0 AFTER `amount_untaxed`,
ADD COLUMN IF NOT EXISTS `invoice_status` VARCHAR(32) DEFAULT 'no' AFTER `state`;

ALTER TABLE `purchase_order` 
ADD COLUMN IF NOT EXISTS `order_line` TEXT AFTER `partner_id`,
ADD COLUMN IF NOT EXISTS `amount_untaxed` DECIMAL(16,2) DEFAULT 0.0 AFTER `amount_total`,
ADD COLUMN IF NOT EXISTS `amount_tax` DECIMAL(16,2) DEFAULT 0.0 AFTER `amount_untaxed`,
ADD COLUMN IF NOT EXISTS `invoice_status` VARCHAR(32) DEFAULT 'no' AFTER `state`;

-- =============================================
-- Module: Human Resources Enhancements
-- =============================================
CREATE TABLE IF NOT EXISTS `hr_contract` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(64) NOT NULL,
    `employee_id` INT,
    `job_id` INT,
    `department_id` INT,
    `wage` DECIMAL(10,2),
    `date_start` DATE,
    `date_end` DATE,
    `state` VARCHAR(32) DEFAULT 'draft',
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME,
    FOREIGN KEY (`employee_id`) REFERENCES `hr_employee`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`department_id`) REFERENCES `hr_department`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- =============================================
-- Module: System Configuration
-- =============================================
CREATE TABLE IF NOT EXISTS `ir_config_parameter` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `key` VARCHAR(256) NOT NULL,
    `value` TEXT,
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME,
    UNIQUE KEY `ir_config_parameter_key_unique` (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `ir_module_module` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(128) NOT NULL,
    `state` VARCHAR(16) DEFAULT 'uninstalled',
    `latest_version` VARCHAR(64),
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME,
    UNIQUE KEY `ir_module_module_name_unique` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- =============================================
-- Module: Reporting & Dashboard
-- =============================================
CREATE TABLE IF NOT EXISTS `board_board` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(128) NOT NULL,
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert default system parameters
INSERT IGNORE INTO `ir_config_parameter` (`key`, `value`, `create_date`) VALUES
('database.create_date', NOW(), NOW()),
('database.uuid', UUID(), NOW()),
('web.base.url', 'http://localhost', NOW());

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;
