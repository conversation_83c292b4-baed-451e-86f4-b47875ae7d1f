-- =============================================
-- إضافة أعمدة مفقودة إلى جدول res_partner
-- =============================================
ALTER TABLE `res_partner` 
ADD COLUMN IF NOT EXISTS `type` VARCHAR(32) NULL DEFAULT 'contact' AFTER `supplier_rank`,
ADD COLUMN IF NOT EXISTS `customer_rank` INT DEFAULT 0 AFTER `type`,
ADD COLUMN IF NOT EXISTS `supplier_rank` INT DEFAULT 0 AFTER `customer_rank`,
ADD COLUMN IF NOT EXISTS `lang` VARCHAR(16) NULL AFTER `email`;

-- تحديث البيانات الموجودة
UPDATE `res_partner` SET 
    `type` = 'contact',
    `customer_rank` = COALESCE(`customer_rank`, 0),
    `supplier_rank` = COALESCE(`supplier_rank`, 0),
    `lang` = 'ar_001'
WHERE `type` IS NULL;

-- إضافة رسالة نجاح
SELECT 'تم تحديث جدول الشركاء بنجاح!' AS message;
