<?php
/**
 * تشغيل فوري للنظام بأسلوب Odoo - بدون أي تعقيدات
 * Instant Odoo-Style System Launch - Zero Complexity
 */

// إيقاف عرض الأخطاء
error_reporting(0);
ini_set('display_errors', 0);

session_start();

// إنشاء المجلدات الأساسية بأسلوب Odoo
$dirs = array('config', 'logs', 'cache', 'tmp', 'addons', 'filestore', 'sessions');
foreach ($dirs as $dir) {
    if (!file_exists($dir)) {
        @mkdir($dir, 0755, true);
    }
}

// إنشاء ملف تكوين Odoo إذا لم يكن موجود
if (!file_exists('config/database_config.php')) {
    $odoo_config = "<?php
// إعدادات النظام بأسلوب Odoo
define('SITE_NAME', 'نظام ERP المحاسبي - بأسلوب Odoo');
define('CURRENCY', 'ر.س');
define('DEMO_MODE', true);
define('SYSTEM_INSTALLED', true);
define('ODOO_STYLE', true);
define('VERSION', '1.0.0');
define('AUTHOR', 'Odoo Style ERP');

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'erp_accounting');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// إعدادات Odoo
define('ODOO_MODULES_PATH', 'addons/');
define('ODOO_FILESTORE_PATH', 'filestore/');
define('ODOO_SESSION_PATH', 'sessions/');
define('ODOO_LOG_PATH', 'logs/');
?>";
    @file_put_contents('config/database_config.php', $odoo_config);
}

// تحميل نظام Odoo
require_once 'includes/odoo_registry.php';
require_once 'includes/odoo_menu.php';
require_once 'includes/odoo_actions.php';

// تسجيل دخول فوري بأسلوب Odoo
$_SESSION['user_id'] = 1;
$_SESSION['username'] = 'مدير النظام';
$_SESSION['email'] = '<EMAIL>';
$_SESSION['role'] = 'admin';
$_SESSION['groups'] = array('admin', 'manager', 'user');
$_SESSION['company_id'] = 1;
$_SESSION['company_name'] = 'شركتي';
$_SESSION['demo_mode'] = true;
$_SESSION['odoo_style'] = true;
$_SESSION['instant_launch'] = true;
$_SESSION['modules'] = array('base', 'account', 'sale', 'purchase', 'stock');

// إنشاء بيانات تجريبية بأسلوب Odoo
$demo_data = array(
    'companies' => array(
        array('id' => 1, 'name' => 'شركتي', 'code' => 'MYCO', 'active' => true),
        array('id' => 2, 'name' => 'شركة تجريبية', 'code' => 'DEMO', 'active' => true)
    ),
    'partners' => array(
        array('id' => 1, 'name' => 'عميل تجريبي 1', 'customer_rank' => 1, 'supplier_rank' => 0),
        array('id' => 2, 'name' => 'عميل تجريبي 2', 'customer_rank' => 1, 'supplier_rank' => 0),
        array('id' => 3, 'name' => 'مورد تجريبي 1', 'customer_rank' => 0, 'supplier_rank' => 1)
    ),
    'products' => array(
        array('id' => 1, 'name' => 'منتج تجريبي 1', 'list_price' => 100.00, 'type' => 'product'),
        array('id' => 2, 'name' => 'منتج تجريبي 2', 'list_price' => 200.00, 'type' => 'product'),
        array('id' => 3, 'name' => 'خدمة تجريبية', 'list_price' => 50.00, 'type' => 'service')
    )
);

$_SESSION['demo_data'] = $demo_data;

// تسجيل في سجل النظام
$log_entry = array(
    'timestamp' => date('Y-m-d H:i:s'),
    'action' => 'instant_launch',
    'user_id' => 1,
    'ip' => isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : 'unknown',
    'user_agent' => isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : 'unknown'
);

@file_put_contents('logs/system.log', json_encode($log_entry) . "\n", FILE_APPEND | LOCK_EX);

// توجيه فوري للنظام
header('Location: demo.php');
exit();
?>
