<?php
/**
 * شريط التنقل العلوي بأسلوب Odoo
 * Odoo-Style Navigation Bar
 */

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    $current_user = array('name' => 'مستخدم', 'email' => '<EMAIL>');
} else {
    $current_user = array('name' => 'المدير', 'email' => '<EMAIL>');
}
?>

<nav class="navbar navbar-expand-lg" style="background: linear-gradient(135deg, #714B67 0%, #875A7B 100%); box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
    <div class="container-fluid">
        <!-- شعار النظام -->
        <a class="navbar-brand text-white d-flex align-items-center" href="../dashboard.php">
            <i class="fas fa-cube me-2" style="font-size: 1.5rem;"></i>
            <div>
                <div style="font-weight: 600; font-size: 1.1rem;">نظام ERP</div>
                <div style="font-size: 0.75rem; opacity: 0.9;">إدارة الموارد المؤسسية</div>
            </div>
        </a>

        <!-- زر القائمة للموبايل -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>

        <!-- عناصر التنقل -->
        <div class="collapse navbar-collapse" id="navbarNav">
            <!-- القائمة الرئيسية -->
            <ul class="navbar-nav me-auto">
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-calculator me-1"></i>المحاسبة
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="../pages/journal_entries.php">
                            <i class="fas fa-book me-2"></i>القيود اليومية
                        </a></li>
                        <li><a class="dropdown-item" href="../pages/chart_of_accounts.php">
                            <i class="fas fa-sitemap me-2"></i>دليل الحسابات
                        </a></li>
                        <li><a class="dropdown-item" href="../pages/financial_reports.php">
                            <i class="fas fa-chart-line me-2"></i>التقارير المالية
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="../pages/accounting_settings.php">
                            <i class="fas fa-cog me-2"></i>إعدادات المحاسبة
                        </a></li>
                    </ul>
                </li>

                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-users me-1"></i>الشركاء
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="../pages/customers.php">
                            <i class="fas fa-user-tie me-2"></i>العملاء
                        </a></li>
                        <li><a class="dropdown-item" href="../pages/partners.php">
                            <i class="fas fa-handshake me-2"></i>الموردين
                        </a></li>
                    </ul>
                </li>

                <li class="nav-item">
                    <a class="nav-link text-white" href="../pages/reports.php">
                        <i class="fas fa-chart-bar me-1"></i>التقارير
                    </a>
                </li>
            </ul>

            <!-- أدوات المستخدم -->
            <ul class="navbar-nav">
                <!-- البحث السريع -->
                <li class="nav-item me-3">
                    <div class="input-group" style="width: 250px;">
                        <input type="text" class="form-control form-control-sm" placeholder="البحث السريع..." id="globalSearch">
                        <button class="btn btn-outline-light btn-sm" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </li>

                <!-- الإشعارات -->
                <li class="nav-item dropdown me-2">
                    <a class="nav-link text-white position-relative" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-bell"></i>
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" style="font-size: 0.6rem;">
                            3
                        </span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end" style="width: 300px;">
                        <li class="dropdown-header">الإشعارات الجديدة</li>
                        <li><a class="dropdown-item" href="#">
                            <div class="d-flex">
                                <i class="fas fa-exclamation-triangle text-warning me-2 mt-1"></i>
                                <div>
                                    <div class="fw-bold">قيد غير متوازن</div>
                                    <small class="text-muted">القيد رقم JE001 يحتاج مراجعة</small>
                                </div>
                            </div>
                        </a></li>
                        <li><a class="dropdown-item" href="#">
                            <div class="d-flex">
                                <i class="fas fa-info-circle text-info me-2 mt-1"></i>
                                <div>
                                    <div class="fw-bold">تقرير جديد</div>
                                    <small class="text-muted">تم إنشاء تقرير الميزانية العمومية</small>
                                </div>
                            </div>
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-center" href="#">عرض جميع الإشعارات</a></li>
                    </ul>
                </li>

                <!-- ملف المستخدم -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle text-white d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown">
                        <div class="rounded-circle bg-light text-dark d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px; font-size: 0.9rem; font-weight: 600;">
                            <?php echo substr($current_user['name'], 0, 1); ?>
                        </div>
                        <div class="d-none d-md-block">
                            <div style="font-size: 0.9rem; line-height: 1.2;"><?php echo $current_user['name']; ?></div>
                            <div style="font-size: 0.7rem; opacity: 0.8;"><?php echo $current_user['email']; ?></div>
                        </div>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="../pages/settings.php">
                            <i class="fas fa-user-cog me-2"></i>الملف الشخصي
                        </a></li>
                        <li><a class="dropdown-item" href="../pages/settings.php">
                            <i class="fas fa-cog me-2"></i>الإعدادات
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="../logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>

<style>
/* تحسينات إضافية للشريط العلوي */
.navbar-nav .nav-link {
    transition: all 0.3s ease;
    border-radius: 4px;
    margin: 0 2px;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.dropdown-menu {
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    padding: 8px 0;
}

.dropdown-item {
    padding: 8px 16px;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
    transform: translateX(4px);
}

.dropdown-item i {
    width: 20px;
    text-align: center;
}

#globalSearch {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

#globalSearch:focus {
    background: white;
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
}

@media (max-width: 768px) {
    .navbar-nav {
        padding-top: 1rem;
    }
    
    .navbar-nav .nav-link {
        padding: 0.75rem 1rem;
    }
}
</style>

<script>
// البحث السريع
document.getElementById('globalSearch')?.addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    if (searchTerm.length > 2) {
        // تنفيذ البحث السريع
        console.log('البحث عن:', searchTerm);
        // يمكن إضافة AJAX هنا للبحث في قاعدة البيانات
    }
});

// تأثيرات بصرية للقائمة
document.addEventListener('DOMContentLoaded', function() {
    // تأثير تدريجي للقوائم المنسدلة
    const dropdowns = document.querySelectorAll('.dropdown-menu');
    dropdowns.forEach(dropdown => {
        dropdown.addEventListener('show.bs.dropdown', function() {
            this.style.opacity = '0';
            this.style.transform = 'translateY(-10px)';
            setTimeout(() => {
                this.style.transition = 'all 0.3s ease';
                this.style.opacity = '1';
                this.style.transform = 'translateY(0)';
            }, 10);
        });
    });
});
</script>
