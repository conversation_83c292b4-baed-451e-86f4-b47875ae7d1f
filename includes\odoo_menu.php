<?php
/**
 * نظام القوائم بأسلوب Odoo
 * Odoo-Style Menu System
 */

class OdooMenu {
    private static $instance = null;
    private $menus = array();
    private $user_groups = array();
    
    private function __construct() {
        $this->loadMenus();
        $this->loadUserGroups();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * تحميل القوائم
     */
    private function loadMenus() {
        // القائمة الرئيسية
        $this->menus = array(
            array(
                'id' => 'dashboard',
                'name' => 'لوحة التحكم',
                'icon' => 'fas fa-tachometer-alt',
                'url' => 'demo.php',
                'sequence' => 1,
                'parent_id' => null,
                'groups' => array('admin', 'manager', 'user'),
                'module' => 'base',
                'active' => true
            ),
            array(
                'id' => 'companies',
                'name' => 'الشركات',
                'icon' => 'fas fa-building',
                'url' => 'pages/companies.php',
                'sequence' => 10,
                'parent_id' => null,
                'groups' => array('admin', 'manager'),
                'module' => 'base',
                'active' => true
            ),
            array(
                'id' => 'partners',
                'name' => 'الشركاء',
                'icon' => 'fas fa-handshake',
                'url' => '#',
                'sequence' => 20,
                'parent_id' => null,
                'groups' => array('admin', 'manager', 'user'),
                'module' => 'base',
                'active' => true
            ),
            array(
                'id' => 'customers',
                'name' => 'العملاء',
                'icon' => 'fas fa-users',
                'url' => 'pages/customers.php',
                'sequence' => 21,
                'parent_id' => 'partners',
                'groups' => array('admin', 'manager', 'user'),
                'module' => 'base',
                'active' => true
            ),
            array(
                'id' => 'suppliers',
                'name' => 'الموردون',
                'icon' => 'fas fa-truck',
                'url' => 'pages/suppliers.php',
                'sequence' => 22,
                'parent_id' => 'partners',
                'groups' => array('admin', 'manager', 'user'),
                'module' => 'base',
                'active' => true
            ),
            array(
                'id' => 'products',
                'name' => 'المنتجات',
                'icon' => 'fas fa-box',
                'url' => 'pages/products.php',
                'sequence' => 30,
                'parent_id' => null,
                'groups' => array('admin', 'manager', 'user'),
                'module' => 'stock',
                'active' => true
            ),
            array(
                'id' => 'sales',
                'name' => 'المبيعات',
                'icon' => 'fas fa-shopping-cart',
                'url' => '#',
                'sequence' => 40,
                'parent_id' => null,
                'groups' => array('admin', 'manager', 'user'),
                'module' => 'sale',
                'active' => true
            ),
            array(
                'id' => 'sale_orders',
                'name' => 'أوامر البيع',
                'icon' => 'fas fa-file-alt',
                'url' => 'pages/sale_orders.php',
                'sequence' => 41,
                'parent_id' => 'sales',
                'groups' => array('admin', 'manager', 'user'),
                'module' => 'sale',
                'active' => true
            ),
            array(
                'id' => 'invoices',
                'name' => 'الفواتير',
                'icon' => 'fas fa-file-invoice',
                'url' => 'pages/invoices.php',
                'sequence' => 42,
                'parent_id' => 'sales',
                'groups' => array('admin', 'manager', 'user'),
                'module' => 'account',
                'active' => true
            ),
            array(
                'id' => 'purchases',
                'name' => 'المشتريات',
                'icon' => 'fas fa-shopping-bag',
                'url' => '#',
                'sequence' => 50,
                'parent_id' => null,
                'groups' => array('admin', 'manager', 'user'),
                'module' => 'purchase',
                'active' => true
            ),
            array(
                'id' => 'purchase_orders',
                'name' => 'أوامر الشراء',
                'icon' => 'fas fa-file-contract',
                'url' => 'pages/purchase_orders.php',
                'sequence' => 51,
                'parent_id' => 'purchases',
                'groups' => array('admin', 'manager', 'user'),
                'module' => 'purchase',
                'active' => true
            ),
            array(
                'id' => 'accounting',
                'name' => 'المحاسبة',
                'icon' => 'fas fa-calculator',
                'url' => '#',
                'sequence' => 60,
                'parent_id' => null,
                'groups' => array('admin', 'manager'),
                'module' => 'account',
                'active' => true
            ),
            array(
                'id' => 'chart_of_accounts',
                'name' => 'دليل الحسابات',
                'icon' => 'fas fa-list',
                'url' => 'pages/chart_of_accounts.php',
                'sequence' => 61,
                'parent_id' => 'accounting',
                'groups' => array('admin', 'manager'),
                'module' => 'account',
                'active' => true
            ),
            array(
                'id' => 'journal_entries',
                'name' => 'القيود المحاسبية',
                'icon' => 'fas fa-book',
                'url' => 'pages/journal_entries.php',
                'sequence' => 62,
                'parent_id' => 'accounting',
                'groups' => array('admin', 'manager'),
                'module' => 'account',
                'active' => true
            ),
            array(
                'id' => 'reports',
                'name' => 'التقارير',
                'icon' => 'fas fa-chart-bar',
                'url' => '#',
                'sequence' => 70,
                'parent_id' => null,
                'groups' => array('admin', 'manager'),
                'module' => 'base',
                'active' => true
            ),
            array(
                'id' => 'financial_reports',
                'name' => 'التقارير المالية',
                'icon' => 'fas fa-chart-line',
                'url' => 'pages/financial_reports.php',
                'sequence' => 71,
                'parent_id' => 'reports',
                'groups' => array('admin', 'manager'),
                'module' => 'account',
                'active' => true
            ),
            array(
                'id' => 'sales_reports',
                'name' => 'تقارير المبيعات',
                'icon' => 'fas fa-chart-pie',
                'url' => 'pages/sales_reports.php',
                'sequence' => 72,
                'parent_id' => 'reports',
                'groups' => array('admin', 'manager'),
                'module' => 'sale',
                'active' => true
            ),
            array(
                'id' => 'settings',
                'name' => 'الإعدادات',
                'icon' => 'fas fa-cog',
                'url' => '#',
                'sequence' => 80,
                'parent_id' => null,
                'groups' => array('admin'),
                'module' => 'base',
                'active' => true
            ),
            array(
                'id' => 'general_settings',
                'name' => 'الإعدادات العامة',
                'icon' => 'fas fa-sliders-h',
                'url' => 'pages/settings.php',
                'sequence' => 81,
                'parent_id' => 'settings',
                'groups' => array('admin'),
                'module' => 'base',
                'active' => true
            ),
            array(
                'id' => 'users',
                'name' => 'المستخدمون',
                'icon' => 'fas fa-user-cog',
                'url' => 'pages/users.php',
                'sequence' => 82,
                'parent_id' => 'settings',
                'groups' => array('admin'),
                'module' => 'base',
                'active' => true
            )
        );
    }
    
    /**
     * تحميل مجموعات المستخدمين
     */
    private function loadUserGroups() {
        $this->user_groups = array(
            'admin' => 'مدير النظام',
            'manager' => 'مدير',
            'user' => 'مستخدم',
            'readonly' => 'قراءة فقط'
        );
    }
    
    /**
     * الحصول على القوائم المتاحة للمستخدم
     */
    public function getMenusForUser($user_groups = array('user')) {
        $available_menus = array();
        
        foreach ($this->menus as $menu) {
            if ($this->hasAccess($menu, $user_groups)) {
                $available_menus[] = $menu;
            }
        }
        
        // ترتيب القوائم حسب التسلسل
        usort($available_menus, function($a, $b) {
            return $a['sequence'] - $b['sequence'];
        });
        
        return $available_menus;
    }
    
    /**
     * التحقق من صلاحية الوصول للقائمة
     */
    private function hasAccess($menu, $user_groups) {
        if (!$menu['active']) {
            return false;
        }
        
        // التحقق من المجموعات
        $menu_groups = $menu['groups'];
        foreach ($user_groups as $group) {
            if (in_array($group, $menu_groups)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * بناء هيكل القوائم الهرمي
     */
    public function buildMenuTree($user_groups = array('user')) {
        $menus = $this->getMenusForUser($user_groups);
        $tree = array();
        $children = array();
        
        // تجميع القوائم الفرعية
        foreach ($menus as $menu) {
            if ($menu['parent_id'] !== null) {
                $children[$menu['parent_id']][] = $menu;
            } else {
                $tree[] = $menu;
            }
        }
        
        // إضافة القوائم الفرعية للقوائم الرئيسية
        foreach ($tree as &$menu) {
            if (isset($children[$menu['id']])) {
                $menu['children'] = $children[$menu['id']];
            }
        }
        
        return $tree;
    }
    
    /**
     * عرض القوائم كـ HTML
     */
    public function renderMenu($user_groups = array('user'), $template = 'sidebar') {
        $menu_tree = $this->buildMenuTree($user_groups);
        
        if ($template === 'sidebar') {
            return $this->renderSidebarMenu($menu_tree);
        } elseif ($template === 'navbar') {
            return $this->renderNavbarMenu($menu_tree);
        } elseif ($template === 'breadcrumb') {
            return $this->renderBreadcrumbMenu($menu_tree);
        }
        
        return '';
    }
    
    /**
     * عرض قائمة جانبية
     */
    private function renderSidebarMenu($menu_tree) {
        $html = '<ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu">';
        
        foreach ($menu_tree as $menu) {
            $html .= $this->renderMenuItem($menu, 'sidebar');
        }
        
        $html .= '</ul>';
        return $html;
    }
    
    /**
     * عرض عنصر قائمة
     */
    private function renderMenuItem($menu, $template = 'sidebar') {
        $has_children = isset($menu['children']) && !empty($menu['children']);
        $is_active = $this->isActiveMenu($menu);
        
        $html = '<li class="nav-item' . ($has_children ? ' has-treeview' : '') . ($is_active ? ' menu-open' : '') . '">';
        
        if ($has_children) {
            $html .= '<a href="#" class="nav-link' . ($is_active ? ' active' : '') . '">';
            $html .= '<i class="nav-icon ' . $menu['icon'] . '"></i>';
            $html .= '<p>' . $menu['name'] . '<i class="right fas fa-angle-left"></i></p>';
            $html .= '</a>';
            
            $html .= '<ul class="nav nav-treeview">';
            foreach ($menu['children'] as $child) {
                $html .= $this->renderMenuItem($child, $template);
            }
            $html .= '</ul>';
        } else {
            $html .= '<a href="' . $menu['url'] . '" class="nav-link' . ($is_active ? ' active' : '') . '">';
            $html .= '<i class="nav-icon ' . $menu['icon'] . '"></i>';
            $html .= '<p>' . $menu['name'] . '</p>';
            $html .= '</a>';
        }
        
        $html .= '</li>';
        return $html;
    }
    
    /**
     * التحقق من القائمة النشطة
     */
    private function isActiveMenu($menu) {
        $current_page = basename($_SERVER['PHP_SELF']);
        $menu_page = basename($menu['url']);
        
        return $current_page === $menu_page;
    }
    
    /**
     * عرض شريط التنقل العلوي
     */
    private function renderNavbarMenu($menu_tree) {
        $html = '<ul class="navbar-nav">';
        
        foreach ($menu_tree as $menu) {
            $has_children = isset($menu['children']) && !empty($menu['children']);
            
            if ($has_children) {
                $html .= '<li class="nav-item dropdown">';
                $html .= '<a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">';
                $html .= '<i class="' . $menu['icon'] . ' me-1"></i>' . $menu['name'];
                $html .= '</a>';
                $html .= '<ul class="dropdown-menu">';
                
                foreach ($menu['children'] as $child) {
                    $html .= '<li><a class="dropdown-item" href="' . $child['url'] . '">';
                    $html .= '<i class="' . $child['icon'] . ' me-2"></i>' . $child['name'];
                    $html .= '</a></li>';
                }
                
                $html .= '</ul></li>';
            } else {
                $html .= '<li class="nav-item">';
                $html .= '<a class="nav-link" href="' . $menu['url'] . '">';
                $html .= '<i class="' . $menu['icon'] . ' me-1"></i>' . $menu['name'];
                $html .= '</a></li>';
            }
        }
        
        $html .= '</ul>';
        return $html;
    }
    
    /**
     * عرض مسار التنقل
     */
    private function renderBreadcrumbMenu($menu_tree) {
        $current_page = basename($_SERVER['PHP_SELF']);
        $breadcrumb = $this->findMenuPath($menu_tree, $current_page);
        
        if (empty($breadcrumb)) {
            return '';
        }
        
        $html = '<nav aria-label="breadcrumb">';
        $html .= '<ol class="breadcrumb">';
        
        foreach ($breadcrumb as $index => $item) {
            if ($index === count($breadcrumb) - 1) {
                $html .= '<li class="breadcrumb-item active" aria-current="page">' . $item['name'] . '</li>';
            } else {
                $html .= '<li class="breadcrumb-item"><a href="' . $item['url'] . '">' . $item['name'] . '</a></li>';
            }
        }
        
        $html .= '</ol></nav>';
        return $html;
    }
    
    /**
     * البحث عن مسار القائمة
     */
    private function findMenuPath($menu_tree, $current_page, $path = array()) {
        foreach ($menu_tree as $menu) {
            $current_path = array_merge($path, array($menu));
            
            if (basename($menu['url']) === $current_page) {
                return $current_path;
            }
            
            if (isset($menu['children'])) {
                $result = $this->findMenuPath($menu['children'], $current_page, $current_path);
                if ($result) {
                    return $result;
                }
            }
        }
        
        return null;
    }
    
    /**
     * إضافة قائمة جديدة
     */
    public function addMenu($menu) {
        $this->menus[] = $menu;
    }
    
    /**
     * حذف قائمة
     */
    public function removeMenu($menu_id) {
        foreach ($this->menus as $index => $menu) {
            if ($menu['id'] === $menu_id) {
                unset($this->menus[$index]);
                break;
            }
        }
        
        // إعادة ترقيم المصفوفة
        $this->menus = array_values($this->menus);
    }
    
    /**
     * تحديث قائمة
     */
    public function updateMenu($menu_id, $updates) {
        foreach ($this->menus as &$menu) {
            if ($menu['id'] === $menu_id) {
                $menu = array_merge($menu, $updates);
                break;
            }
        }
    }
    
    /**
     * البحث في القوائم
     */
    public function searchMenus($query, $user_groups = array('user')) {
        $menus = $this->getMenusForUser($user_groups);
        $results = array();
        
        foreach ($menus as $menu) {
            if (stripos($menu['name'], $query) !== false) {
                $results[] = $menu;
            }
        }
        
        return $results;
    }
}

// إنشاء مثيل عام
$odoo_menu = OdooMenu::getInstance();
?>
