<?php
/**
 * نظام إدارة الوحدات بأسلوب Odoo
 * Odoo-Style Module Management System
 */

class OdooModuleManager {
    private static $instance = null;
    private $modules = array();
    private $installed_modules = array();
    private $module_dependencies = array();
    
    private function __construct() {
        $this->loadModules();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * تحميل الوحدات المتاحة
     */
    private function loadModules() {
        $this->modules = array(
            // الوحدة الأساسية
            'base' => array(
                'name' => 'الوحدة الأساسية',
                'technical_name' => 'base',
                'description' => 'الوحدة الأساسية لنظام Odoo - إدارة الشركات والمستخدمين والشركاء',
                'version' => '1.0.0',
                'author' => 'Odoo ERP System',
                'category' => 'Administration',
                'depends' => array(),
                'auto_install' => true,
                'installable' => true,
                'application' => true,
                'icon' => 'fas fa-cogs',
                'color' => '#875A7B',
                'models' => array('res.company', 'res.users', 'res.partner', 'res.currency', 'res.country'),
                'views' => array('companies', 'users', 'partners'),
                'menus' => array(
                    array('name' => 'إدارة النظام', 'sequence' => 100, 'parent' => null),
                    array('name' => 'الشركات', 'sequence' => 10, 'parent' => 'إدارة النظام'),
                    array('name' => 'المستخدمين', 'sequence' => 20, 'parent' => 'إدارة النظام'),
                    array('name' => 'الشركاء', 'sequence' => 30, 'parent' => 'إدارة النظام')
                )
            ),
            
            // وحدة المحاسبة
            'account' => array(
                'name' => 'المحاسبة',
                'technical_name' => 'account',
                'description' => 'إدارة المحاسبة والمالية المتكاملة - دليل الحسابات والقيود والتقارير المالية',
                'version' => '2.0.0',
                'author' => 'Odoo ERP System',
                'category' => 'Accounting',
                'depends' => array('base'),
                'auto_install' => false,
                'installable' => true,
                'application' => true,
                'icon' => 'fas fa-calculator',
                'color' => '#00A09D',
                'models' => array(
                    'account.account', 'account.move', 'account.move.line',
                    'account.journal', 'account.financial.report', 'res.currency'
                ),
                'views' => array(
                    'chart_of_accounts', 'journal_entries', 'journals',
                    'financial_reports', 'account_ledger', 'trial_balance', 'create_entry'
                ),
                'menus' => array(
                    array('name' => 'المحاسبة', 'sequence' => 200, 'parent' => null),
                    array('name' => 'دليل الحسابات', 'sequence' => 10, 'parent' => 'المحاسبة'),
                    array('name' => 'القيود اليومية', 'sequence' => 20, 'parent' => 'المحاسبة'),
                    array('name' => 'إنشاء قيد جديد', 'sequence' => 25, 'parent' => 'المحاسبة'),
                    array('name' => 'اليوميات', 'sequence' => 30, 'parent' => 'المحاسبة'),
                    array('name' => 'دفتر الأستاذ', 'sequence' => 35, 'parent' => 'المحاسبة'),
                    array('name' => 'التقارير المالية', 'sequence' => 40, 'parent' => 'المحاسبة'),
                    array('name' => 'ميزان المراجعة', 'sequence' => 45, 'parent' => 'المحاسبة'),
                    array('name' => 'إعداد المحاسبة', 'sequence' => 50, 'parent' => 'المحاسبة'),
                    array('name' => 'الشركاء', 'sequence' => 55, 'parent' => 'المحاسبة'),
                    array('name' => 'الفترات المحاسبية', 'sequence' => 60, 'parent' => 'المحاسبة'),
                    array('name' => 'إعدادات المحاسبة', 'sequence' => 65, 'parent' => 'المحاسبة')
                )
            ),
            
            // وحدة المبيعات
            'sale' => array(
                'name' => 'المبيعات',
                'technical_name' => 'sale',
                'description' => 'إدارة المبيعات - عروض الأسعار وأوامر البيع والفواتير',
                'version' => '1.0.0',
                'author' => 'Odoo ERP System',
                'category' => 'Sales',
                'depends' => array('base'),
                'auto_install' => false,
                'installable' => true,
                'application' => true,
                'icon' => 'fas fa-shopping-cart',
                'color' => '#875A7B',
                'models' => array('sale.order', 'sale.order.line'),
                'views' => array('sale_orders', 'quotations'),
                'menus' => array(
                    array('name' => 'المبيعات', 'sequence' => 300, 'parent' => null),
                    array('name' => 'عروض الأسعار', 'sequence' => 10, 'parent' => 'المبيعات'),
                    array('name' => 'أوامر البيع', 'sequence' => 20, 'parent' => 'المبيعات'),
                    array('name' => 'العملاء', 'sequence' => 30, 'parent' => 'المبيعات'),
                    array('name' => 'تقارير المبيعات', 'sequence' => 40, 'parent' => 'المبيعات')
                )
            ),
            
            // وحدة المشتريات
            'purchase' => array(
                'name' => 'المشتريات',
                'technical_name' => 'purchase',
                'description' => 'إدارة المشتريات - طلبات الأسعار وأوامر الشراء',
                'version' => '1.0.0',
                'author' => 'Odoo ERP System',
                'category' => 'Purchase',
                'depends' => array('base'),
                'auto_install' => false,
                'installable' => true,
                'application' => true,
                'icon' => 'fas fa-shopping-bag',
                'color' => '#00A09D',
                'models' => array('purchase.order', 'purchase.order.line'),
                'views' => array('purchase_orders', 'rfq'),
                'menus' => array(
                    array('name' => 'المشتريات', 'sequence' => 400, 'parent' => null),
                    array('name' => 'طلبات الأسعار', 'sequence' => 10, 'parent' => 'المشتريات'),
                    array('name' => 'أوامر الشراء', 'sequence' => 20, 'parent' => 'المشتريات'),
                    array('name' => 'الموردين', 'sequence' => 30, 'parent' => 'المشتريات'),
                    array('name' => 'تقارير المشتريات', 'sequence' => 40, 'parent' => 'المشتريات')
                )
            ),
            
            // وحدة المخزون
            'stock' => array(
                'name' => 'إدارة المخزون',
                'technical_name' => 'stock',
                'description' => 'إدارة المخزون والمستودعات - المنتجات وحركات المخزون',
                'version' => '1.0.0',
                'author' => 'Odoo ERP System',
                'category' => 'Inventory',
                'depends' => array('base'),
                'auto_install' => false,
                'installable' => true,
                'application' => true,
                'icon' => 'fas fa-boxes',
                'color' => '#F56C6C',
                'models' => array('product.template', 'product.product', 'stock.location', 'stock.move'),
                'views' => array('products', 'locations', 'moves'),
                'menus' => array(
                    array('name' => 'المخزون', 'sequence' => 500, 'parent' => null),
                    array('name' => 'المنتجات', 'sequence' => 10, 'parent' => 'المخزون'),
                    array('name' => 'المواقع', 'sequence' => 20, 'parent' => 'المخزون'),
                    array('name' => 'حركات المخزون', 'sequence' => 30, 'parent' => 'المخزون'),
                    array('name' => 'تقارير المخزون', 'sequence' => 40, 'parent' => 'المخزون')
                )
            ),
            
            // وحدة الموارد البشرية
            'hr' => array(
                'name' => 'الموارد البشرية',
                'technical_name' => 'hr',
                'description' => 'إدارة الموارد البشرية - الموظفين والرواتب والإجازات',
                'version' => '1.0.0',
                'author' => 'Odoo ERP System',
                'category' => 'Human Resources',
                'depends' => array('base'),
                'auto_install' => false,
                'installable' => true,
                'application' => true,
                'icon' => 'fas fa-users',
                'color' => '#E67E22',
                'models' => array('hr.employee', 'hr.department', 'hr.job'),
                'views' => array('employees', 'departments', 'jobs'),
                'menus' => array(
                    array('name' => 'الموارد البشرية', 'sequence' => 600, 'parent' => null),
                    array('name' => 'الموظفين', 'sequence' => 10, 'parent' => 'الموارد البشرية'),
                    array('name' => 'الأقسام', 'sequence' => 20, 'parent' => 'الموارد البشرية'),
                    array('name' => 'الوظائف', 'sequence' => 30, 'parent' => 'الموارد البشرية')
                )
            ),
            
            // وحدة إدارة المشاريع
            'project' => array(
                'name' => 'إدارة المشاريع',
                'technical_name' => 'project',
                'description' => 'إدارة المشاريع والمهام - تخطيط وتنفيذ ومتابعة المشاريع',
                'version' => '1.0.0',
                'author' => 'Odoo ERP System',
                'category' => 'Project Management',
                'depends' => array('base'),
                'auto_install' => false,
                'installable' => true,
                'application' => true,
                'icon' => 'fas fa-project-diagram',
                'color' => '#9B59B6',
                'models' => array('project.project', 'project.task'),
                'views' => array('projects', 'tasks'),
                'menus' => array(
                    array('name' => 'المشاريع', 'sequence' => 700, 'parent' => null),
                    array('name' => 'جميع المشاريع', 'sequence' => 10, 'parent' => 'المشاريع'),
                    array('name' => 'المهام', 'sequence' => 20, 'parent' => 'المشاريع'),
                    array('name' => 'التقارير', 'sequence' => 30, 'parent' => 'المشاريع')
                )
            ),
            
            // وحدة إدارة علاقات العملاء
            'crm' => array(
                'name' => 'إدارة علاقات العملاء',
                'technical_name' => 'crm',
                'description' => 'إدارة علاقات العملاء - الفرص والعملاء المحتملين',
                'version' => '1.0.0',
                'author' => 'Odoo ERP System',
                'category' => 'Sales',
                'depends' => array('base'),
                'auto_install' => false,
                'installable' => true,
                'application' => true,
                'icon' => 'fas fa-handshake',
                'color' => '#3498DB',
                'models' => array('crm.lead', 'crm.stage'),
                'views' => array('leads', 'opportunities'),
                'menus' => array(
                    array('name' => 'إدارة العملاء', 'sequence' => 800, 'parent' => null),
                    array('name' => 'العملاء المحتملين', 'sequence' => 10, 'parent' => 'إدارة العملاء'),
                    array('name' => 'الفرص', 'sequence' => 20, 'parent' => 'إدارة العملاء'),
                    array('name' => 'التقارير', 'sequence' => 30, 'parent' => 'إدارة العملاء')
                )
            )
        );
        
        // تحديد الوحدات المثبتة افتراضياً
        $this->installed_modules = array('base');
        
        // إضافة وحدات أخرى حسب الجلسة
        if (isset($_SESSION['installed_modules'])) {
            $this->installed_modules = array_merge($this->installed_modules, $_SESSION['installed_modules']);
        } else {
            // تثبيت الوحدات الأساسية افتراضياً
            $this->installed_modules = array('base', 'account', 'sale', 'purchase', 'stock');
            $_SESSION['installed_modules'] = $this->installed_modules;
        }
    }
    
    /**
     * الحصول على جميع الوحدات
     */
    public function getAllModules() {
        return $this->modules;
    }
    
    /**
     * الحصول على الوحدات المثبتة
     */
    public function getInstalledModules() {
        $installed = array();
        foreach ($this->installed_modules as $module_name) {
            if (isset($this->modules[$module_name])) {
                $installed[$module_name] = $this->modules[$module_name];
            }
        }
        return $installed;
    }
    
    /**
     * الحصول على الوحدات المتاحة للتثبيت
     */
    public function getAvailableModules() {
        $available = array();
        foreach ($this->modules as $name => $module) {
            if (!in_array($name, $this->installed_modules) && $module['installable']) {
                $available[$name] = $module;
            }
        }
        return $available;
    }
    
    /**
     * تثبيت وحدة
     */
    public function installModule($module_name) {
        if (!isset($this->modules[$module_name])) {
            throw new Exception("الوحدة '{$module_name}' غير موجودة");
        }
        
        if (in_array($module_name, $this->installed_modules)) {
            return true; // مثبتة بالفعل
        }
        
        $module = $this->modules[$module_name];
        
        // تثبيت التبعيات أولاً
        foreach ($module['depends'] as $dependency) {
            if (!in_array($dependency, $this->installed_modules)) {
                $this->installModule($dependency);
            }
        }
        
        // تثبيت الوحدة
        $this->installed_modules[] = $module_name;
        $_SESSION['installed_modules'] = $this->installed_modules;
        
        return true;
    }
    
    /**
     * إلغاء تثبيت وحدة
     */
    public function uninstallModule($module_name) {
        if ($module_name === 'base') {
            throw new Exception("لا يمكن إلغاء تثبيت الوحدة الأساسية");
        }
        
        $key = array_search($module_name, $this->installed_modules);
        if ($key !== false) {
            unset($this->installed_modules[$key]);
            $this->installed_modules = array_values($this->installed_modules);
            $_SESSION['installed_modules'] = $this->installed_modules;
        }
        
        return true;
    }
    
    /**
     * الحصول على قوائم الوحدات المثبتة
     */
    public function getInstalledMenus() {
        $menus = array();
        foreach ($this->installed_modules as $module_name) {
            if (isset($this->modules[$module_name]['menus'])) {
                foreach ($this->modules[$module_name]['menus'] as $menu) {
                    $menu['module'] = $module_name;
                    $menus[] = $menu;
                }
            }
        }
        
        // ترتيب القوائم حسب التسلسل
        usort($menus, array($this, 'sortMenusBySequence'));
        
        return $menus;
    }
    
    /**
     * التحقق من تثبيت وحدة
     */
    public function isModuleInstalled($module_name) {
        return in_array($module_name, $this->installed_modules);
    }
    
    /**
     * الحصول على معلومات وحدة
     */
    public function getModuleInfo($module_name) {
        return isset($this->modules[$module_name]) ? $this->modules[$module_name] : null;
    }

    /**
     * دالة مساعدة لترتيب القوائم
     */
    private function sortMenusBySequence($a, $b) {
        return $a['sequence'] - $b['sequence'];
    }
}

// إنشاء مثيل عام
$odoo_modules = OdooModuleManager::getInstance();
?>
