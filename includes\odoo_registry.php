<?php
/**
 * سجل الوحدات بأسلوب Odoo
 * Odoo-Style Module Registry
 */

class OdooRegistry {
    private static $instance = null;
    private $models = array();
    private $modules = array();
    private $menus = array();
    private $actions = array();
    private $views = array();
    
    private function __construct() {
        $this->loadModules();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * تحميل الوحدات
     */
    private function loadModules() {
        // الوحدة الأساسية
        $this->registerModule('base', array(
            'name' => 'الوحدة الأساسية',
            'description' => 'الوحدة الأساسية للنظام',
            'version' => '1.0.0',
            'depends' => array(),
            'models' => array('res.company', 'res.users', 'res.partner', 'res.currency'),
            'views' => array('base_views.xml'),
            'menus' => array('base_menu.xml'),
            'auto_install' => true
        ));
        
        // وحدة المحاسبة
        $this->registerModule('account', array(
            'name' => 'المحاسبة',
            'description' => 'وحدة المحاسبة والحسابات',
            'version' => '1.0.0',
            'depends' => array('base'),
            'models' => array('account.account', 'account.move', 'account.move.line'),
            'views' => array('account_views.xml'),
            'menus' => array('account_menu.xml'),
            'auto_install' => true
        ));
        
        // وحدة المبيعات
        $this->registerModule('sale', array(
            'name' => 'المبيعات',
            'description' => 'وحدة إدارة المبيعات',
            'version' => '1.0.0',
            'depends' => array('base', 'account'),
            'models' => array('sale.order', 'sale.order.line'),
            'views' => array('sale_views.xml'),
            'menus' => array('sale_menu.xml'),
            'auto_install' => true
        ));
        
        // وحدة المشتريات
        $this->registerModule('purchase', array(
            'name' => 'المشتريات',
            'description' => 'وحدة إدارة المشتريات',
            'version' => '1.0.0',
            'depends' => array('base', 'account'),
            'models' => array('purchase.order', 'purchase.order.line'),
            'views' => array('purchase_views.xml'),
            'menus' => array('purchase_menu.xml'),
            'auto_install' => true
        ));
        
        // وحدة المخزون
        $this->registerModule('stock', array(
            'name' => 'المخزون',
            'description' => 'وحدة إدارة المخزون',
            'version' => '1.0.0',
            'depends' => array('base'),
            'models' => array('stock.location', 'stock.move', 'stock.picking'),
            'views' => array('stock_views.xml'),
            'menus' => array('stock_menu.xml'),
            'auto_install' => true
        ));
    }
    
    /**
     * تسجيل وحدة جديدة
     */
    public function registerModule($name, $config) {
        $this->modules[$name] = $config;
        
        // تسجيل النماذج
        if (isset($config['models'])) {
            foreach ($config['models'] as $model) {
                $this->registerModel($model, $name);
            }
        }
        
        // تسجيل القوائم
        if (isset($config['menus'])) {
            foreach ($config['menus'] as $menu) {
                $this->registerMenu($menu, $name);
            }
        }
    }
    
    /**
     * تسجيل نموذج
     */
    public function registerModel($model_name, $module) {
        $this->models[$model_name] = array(
            'module' => $module,
            'class' => $this->getModelClass($model_name),
            'table' => $this->getModelTable($model_name)
        );
    }
    
    /**
     * تسجيل قائمة
     */
    public function registerMenu($menu_name, $module) {
        $this->menus[$menu_name] = array(
            'module' => $module,
            'config' => $this->loadMenuConfig($menu_name)
        );
    }
    
    /**
     * الحصول على كلاس النموذج
     */
    private function getModelClass($model_name) {
        // تحويل اسم النموذج إلى اسم الكلاس
        // مثال: res.company -> ResCompany
        $parts = explode('.', $model_name);
        $class_name = '';
        foreach ($parts as $part) {
            $class_name .= ucfirst($part);
        }
        return $class_name;
    }
    
    /**
     * الحصول على جدول النموذج
     */
    private function getModelTable($model_name) {
        // تحويل اسم النموذج إلى اسم الجدول
        // مثال: res.company -> res_company
        return str_replace('.', '_', $model_name);
    }
    
    /**
     * تحميل إعدادات القائمة
     */
    private function loadMenuConfig($menu_name) {
        // في النسخة الكاملة، سيتم تحميل الإعدادات من ملف XML
        return array();
    }
    
    /**
     * الحصول على جميع الوحدات
     */
    public function getModules() {
        return $this->modules;
    }
    
    /**
     * الحصول على وحدة محددة
     */
    public function getModule($name) {
        return isset($this->modules[$name]) ? $this->modules[$name] : null;
    }
    
    /**
     * الحصول على جميع النماذج
     */
    public function getModels() {
        return $this->models;
    }
    
    /**
     * الحصول على نموذج محدد
     */
    public function getModel($name) {
        return isset($this->models[$name]) ? $this->models[$name] : null;
    }
    
    /**
     * التحقق من وجود وحدة
     */
    public function hasModule($name) {
        return isset($this->modules[$name]);
    }
    
    /**
     * التحقق من وجود نموذج
     */
    public function hasModel($name) {
        return isset($this->models[$name]);
    }
    
    /**
     * الحصول على الوحدات المثبتة
     */
    public function getInstalledModules() {
        $installed = array();
        foreach ($this->modules as $name => $config) {
            if ($config['auto_install'] || $this->isModuleInstalled($name)) {
                $installed[$name] = $config;
            }
        }
        return $installed;
    }
    
    /**
     * التحقق من تثبيت الوحدة
     */
    private function isModuleInstalled($name) {
        // في النسخة الكاملة، سيتم التحقق من قاعدة البيانات
        return true;
    }
    
    /**
     * تثبيت وحدة
     */
    public function installModule($name) {
        if (!$this->hasModule($name)) {
            throw new Exception("الوحدة '{$name}' غير موجودة");
        }
        
        $module = $this->modules[$name];
        
        // تثبيت الوحدات المطلوبة أولاً
        if (isset($module['depends'])) {
            foreach ($module['depends'] as $dependency) {
                if (!$this->isModuleInstalled($dependency)) {
                    $this->installModule($dependency);
                }
            }
        }
        
        // تثبيت الوحدة
        $this->createModuleTables($name);
        $this->loadModuleData($name);
        
        return true;
    }
    
    /**
     * إنشاء جداول الوحدة
     */
    private function createModuleTables($name) {
        $module = $this->modules[$name];
        
        if (isset($module['models'])) {
            foreach ($module['models'] as $model_name) {
                $this->createModelTable($model_name);
            }
        }
    }
    
    /**
     * إنشاء جدول النموذج
     */
    private function createModelTable($model_name) {
        // في النسخة الكاملة، سيتم إنشاء الجداول تلقائياً
        // بناءً على تعريف النموذج
    }
    
    /**
     * تحميل بيانات الوحدة
     */
    private function loadModuleData($name) {
        // في النسخة الكاملة، سيتم تحميل البيانات الأولية
        // من ملفات XML أو CSV
    }
    
    /**
     * إلغاء تثبيت وحدة
     */
    public function uninstallModule($name) {
        if (!$this->hasModule($name)) {
            throw new Exception("الوحدة '{$name}' غير موجودة");
        }
        
        // التحقق من عدم وجود وحدات تعتمد على هذه الوحدة
        foreach ($this->modules as $module_name => $config) {
            if (isset($config['depends']) && in_array($name, $config['depends'])) {
                if ($this->isModuleInstalled($module_name)) {
                    throw new Exception("لا يمكن إلغاء تثبيت الوحدة '{$name}' لأن الوحدة '{$module_name}' تعتمد عليها");
                }
            }
        }
        
        // إلغاء تثبيت الوحدة
        $this->dropModuleTables($name);
        
        return true;
    }
    
    /**
     * حذف جداول الوحدة
     */
    private function dropModuleTables($name) {
        $module = $this->modules[$name];
        
        if (isset($module['models'])) {
            foreach ($module['models'] as $model_name) {
                $this->dropModelTable($model_name);
            }
        }
    }
    
    /**
     * حذف جدول النموذج
     */
    private function dropModelTable($model_name) {
        // في النسخة الكاملة، سيتم حذف الجداول
    }
    
    /**
     * تحديث وحدة
     */
    public function updateModule($name) {
        if (!$this->hasModule($name)) {
            throw new Exception("الوحدة '{$name}' غير موجودة");
        }
        
        // تحديث الوحدة
        $this->updateModuleTables($name);
        $this->updateModuleData($name);
        
        return true;
    }
    
    /**
     * تحديث جداول الوحدة
     */
    private function updateModuleTables($name) {
        // في النسخة الكاملة، سيتم تحديث الجداول
    }
    
    /**
     * تحديث بيانات الوحدة
     */
    private function updateModuleData($name) {
        // في النسخة الكاملة، سيتم تحديث البيانات
    }
    
    /**
     * الحصول على معلومات الوحدة
     */
    public function getModuleInfo($name) {
        if (!$this->hasModule($name)) {
            return null;
        }
        
        $module = $this->modules[$name];
        $module['installed'] = $this->isModuleInstalled($name);
        $module['models_count'] = isset($module['models']) ? count($module['models']) : 0;
        
        return $module;
    }
    
    /**
     * البحث في الوحدات
     */
    public function searchModules($query) {
        $results = array();
        
        foreach ($this->modules as $name => $config) {
            if (stripos($config['name'], $query) !== false || 
                stripos($config['description'], $query) !== false ||
                stripos($name, $query) !== false) {
                $results[$name] = $config;
            }
        }
        
        return $results;
    }
}

// إنشاء مثيل عام
$odoo_registry = OdooRegistry::getInstance();
?>
