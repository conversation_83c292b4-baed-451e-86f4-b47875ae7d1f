-- Start transaction
START TRANSACTION;

-- =============================================
-- 1. Setup Default Company
-- =============================================
INSERT INTO `res_company` (
    `name`, 
    `currency_id`,
    `create_date`, 
    `write_date`
) VALUES (
    'شركتك', -- Replace with your company name
    (SELECT `id` FROM `res_currency` WHERE `name` = 'SAR' LIMIT 1),
    NOW(),
    NOW()
);

SET @company_id = LAST_INSERT_ID();

-- Update system parameter with company ID
INSERT INTO `ir_config_parameter` (`key`, `value`, `create_date`)
VALUES ('base_setup.default_company_id', @company_id, NOW())
ON DUPLICATE KEY UPDATE `value` = @company_id;

-- =============================================
-- 2. Setup Default Users and Groups
-- =============================================
-- Create Admin User (if not exists)
INSERT IGNORE INTO `res_users` (
    `login`, 
    `password`, 
    `name`, 
    `company_id`,
    `active`,
    `create_date`,
    `write_date`
) VALUES (
    'admin',
    MD5('admin'), -- Default password (should be changed after setup)
    'مدير النظام',
    @company_id,
    TRUE,
    NOW(),
    NOW()
);

SET @admin_user_id = (SELECT `id` FROM `res_users` WHERE `login` = 'admin' LIMIT 1);

-- =============================================
-- 3. Setup Accounting Configuration
-- =============================================
-- Update company currency if needed
UPDATE `res_company` 
SET `currency_id` = (SELECT `id` FROM `res_currency` WHERE `name` = 'SAR' LIMIT 1)
WHERE `id` = @company_id;

-- =============================================
-- 4. Setup Default Chart of Accounts
-- =============================================
-- This is a simplified chart of accounts
INSERT INTO `account_account` (`code`, `name`, `user_type_id`, `company_id`, `create_date`, `write_date`)
VALUES 
-- Assets
('1010', 'النقدية بالصندوق', 1, @company_id, NOW(), NOW()),
('1011', 'البنك الأهلي', 1, @company_id, NOW(), NOW()),
('1012', 'البنك الرياض', 1, @company_id, NOW(), NOW()),
('12', 'الذمم المدينة', 1, @company_id, NOW(), NOW()),
('13', 'المخزون', 1, @company_id, NOW(), NOW()),
('14', 'الأصول الثابتة', 1, @company_id, NOW(), NOW()),

-- Liabilities
('20', 'الذمم الدائنة', 2, @company_id, NOW(), NOW()),
('21', 'الضرائب المستحقة', 2, @company_id, NOW(), NOW()),

-- Equity
('30', 'رأس المال', 3, @company_id, NOW(), NOW()),
('31', 'الأرباح المحتجزة', 3, @company_id, NOW(), NOW()),

-- Income
('40', 'المبيعات', 4, @company_id, NOW(), NOW()),
('41', 'إيرادات أخرى', 4, @company_id, NOW(), NOW()),

-- Expenses
('50', 'تكلفة المبيعات', 5, @company_id, NOW(), NOW()),
('51', 'المصروفات الإدارية', 5, @company_id, NOW(), NOW()),
('52', 'المصروفات التسويقية', 5, @company_id, NOW(), NOW()),
('53', 'مصروفات أخرى', 5, @company_id, NOW(), NOW());

-- =============================================
-- 5. Setup Tax Configuration
-- =============================================
-- VAT 15%
INSERT INTO `account_tax` (
    `name`, 
    `amount`, 
    `amount_type`, 
    `type_tax_use`, 
    `company_id`,
    `create_date`,
    `write_date`
) VALUES (
    'ضريبة القيمة المضافة 15%',
    15.0,
    'percent',
    'sale',
    @company_id,
    NOW(),
    NOW()
);

-- =============================================
-- 6. Setup Product Categories
-- =============================================
-- Default product category
INSERT INTO `product_category` (
    `name`,
    `create_date`,
    `write_date`
) VALUES (
    'عام',
    NOW(),
    NOW()
);

-- =============================================
-- 7. Setup UoM (Units of Measure)
-- =============================================
-- Ensure we have default UoM categories
INSERT IGNORE INTO `uom_category` (`name`, `create_date`) VALUES 
('الوحدة', NOW()),
('الوزن', NOW()),
('الطول', NOW()),
('الحجم', NOW()),
('الوقت', NOW());

-- Add default UoM
INSERT IGNORE INTO `uom_uom` (
    `name`, 
    `category_id`,
    `uom_type`,
    `factor`,
    `create_date`
) VALUES 
('وحدة', (SELECT `id` FROM `uom_category` WHERE `name` = 'الوحدة' LIMIT 1), 'reference', 1.0, NOW()),
('كيلوجرام', (SELECT `id` FROM `uom_category` WHERE `name` = 'الوزن' LIMIT 1), 'reference', 1.0, NOW()),
('جرام', (SELECT `id` FROM `uom_category` WHERE `name` = 'الوزن' LIMIT 1), 'smaller', 0.001, NOW()),
('متر', (SELECT `id` FROM `uom_category` WHERE `name` = 'الطول' LIMIT 1), 'reference', 1.0, NOW()),
('ساعة', (SELECT `id` FROM `uom_category` WHERE `name` = 'الوقت' LIMIT 1), 'reference', 1.0, NOW());

-- =============================================
-- 8. Setup Default Warehouse
-- =============================================
-- Main warehouse location
INSERT INTO `stock_location` (
    `name`,
    `complete_name`,
    `create_date`,
    `write_date`
) VALUES (
    'المستودع الرئيسي',
    'الشركة/المستودع الرئيسي',
    NOW(),
    NOW()
);

-- Customer location
INSERT INTO `stock_location` (
    `name`,
    `complete_name`,
    `create_date`,
    `write_date`
) VALUES (
    'العملاء',
    'الشركة/العملاء',
    NOW(),
    NOW()
);

-- Vendor location
INSERT INTO `stock_location` (
    `name`,
    `complete_name`,
    `create_date`,
    `write_date`
) VALUES (
    'الموردين',
    'الشركة/الموردين',
    NOW(),
    NOW()
);

-- =============================================
-- 9. Setup Default Email Templates
-- =============================================
INSERT INTO `mail_template` (
    `name`,
    `model`,
    `subject`,
    `body_html`,
    `create_date`,
    `write_date`
) VALUES (
    'تأكيد البريد الإلكتروني',
    'res.users',
    'تأكيد عنوان البريد الإلكتروني',
    '<div style="margin: 0px; padding: 0px;">\n    <p>مرحباً ${object.name or ''}،</p>\n    <p>شكراً لتسجيلك في نظامنا.</p>\n    <p>يمكنك تسجيل الدخول باستخدام:</p>\n    <ul>\n        <li>اسم المستخدم: ${object.login}</li>\n    </ul>\n    <p>مع أطيب التحيات،<br/>فريق الدعم الفني</p>\n</div>',
    NOW(),
    NOW()
);

-- =============================================
-- 10. System Configuration
-- =============================================
-- Set default language to Arabic
INSERT INTO `ir_config_parameter` (`key`, `value`, `create_date`)
VALUES 
('base.language.install', 'ar_001', NOW()),
('base.main_company_id', @company_id, NOW()),
('base.main_company_currency_id', (SELECT `id` FROM `res_currency` WHERE `name` = 'SAR' LIMIT 1), NOW()),
('web.base.url', 'http://localhost', NOW()),
('web.base.url.web.assets.common', '/web/assets/', NOW())
ON DUPLICATE KEY UPDATE `value` = VALUES(`value`);

-- Commit all changes
COMMIT;

-- =============================================
-- Display Success Message
-- =============================================
SELECT 'تم إعداد البيانات الأولية بنجاح!' AS message;
