<?php
/**
 * معالج تثبيت قاعدة البيانات - بأسلوب Odoo
 * Database Installation Wizard
 */

session_start();

$step = isset($_GET['step']) ? intval($_GET['step']) : 1;
$error = '';
$success = '';

// معالجة الخطوات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    switch ($step) {
        case 1:
            // اختبار الاتصال بقاعدة البيانات
            $host = isset($_POST['host']) ? $_POST['host'] : 'localhost';
            $dbname = isset($_POST['dbname']) ? $_POST['dbname'] : 'erp_accounting';
            $username = isset($_POST['username']) ? $_POST['username'] : 'root';
            $password = isset($_POST['password']) ? $_POST['password'] : '';
            
            try {
                // اختبار الاتصال
                $dsn = "mysql:host={$host};charset=utf8mb4";
                $pdo = new PDO($dsn, $username, $password);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // إنشاء قاعدة البيانات إذا لم تكن موجودة
                $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$dbname}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                $pdo->exec("USE `{$dbname}`");
                
                // حفظ بيانات الاتصال في الجلسة
                $_SESSION['db_config'] = array(
                    'host' => $host,
                    'dbname' => $dbname,
                    'username' => $username,
                    'password' => $password
                );
                
                $step = 2;
                $success = 'تم الاتصال بقاعدة البيانات بنجاح';
                
            } catch (Exception $e) {
                $error = 'فشل الاتصال بقاعدة البيانات: ' . $e->getMessage();
            }
            break;
            
        case 2:
            // إنشاء الجداول
            try {
                $db_config = $_SESSION['db_config'];
                
                // إنشاء ملف التكوين
                $config_content = "<?php
// إعدادات قاعدة البيانات
define('DB_HOST', '{$db_config['host']}');
define('DB_NAME', '{$db_config['dbname']}');
define('DB_USER', '{$db_config['username']}');
define('DB_PASS', '{$db_config['password']}');
define('DB_CHARSET', 'utf8mb4');

// إعدادات النظام
define('SITE_URL', 'http://localhost/acc/');
define('SITE_NAME', 'نظام ERP المحاسبي');
define('CURRENCY', 'ر.س');
define('DATE_FORMAT', 'Y-m-d');
define('DATETIME_FORMAT', 'Y-m-d H:i:s');

// إعدادات الأمان
define('SESSION_TIMEOUT', 3600);
define('PASSWORD_MIN_LENGTH', 6);
define('MAX_LOGIN_ATTEMPTS', 5);
?>";
                
                file_put_contents('config/database_config.php', $config_content);
                
                // تضمين ملف قاعدة البيانات
                require_once 'config/database_odoo.php';
                
                // تنفيذ ملف إنشاء الجداول
                executeSQLFile('sql/odoo_database.sql');
                
                $step = 3;
                $success = 'تم إنشاء الجداول بنجاح';
                
            } catch (Exception $e) {
                $error = 'فشل في إنشاء الجداول: ' . $e->getMessage();
            }
            break;
            
        case 3:
            // إدراج البيانات الأساسية
            try {
                require_once 'config/database_config.php';
                require_once 'config/database_odoo.php';
                
                // إدراج البيانات الأساسية
                $this->insertBasicData();
                
                $step = 4;
                $success = 'تم إدراج البيانات الأساسية بنجاح';
                
            } catch (Exception $e) {
                $error = 'فشل في إدراج البيانات الأساسية: ' . $e->getMessage();
            }
            break;
            
        case 4:
            // إنشاء المستخدم الإداري
            $name = isset($_POST['name']) ? $_POST['name'] : 'مدير النظام';
            $email = isset($_POST['email']) ? $_POST['email'] : '<EMAIL>';
            $password = isset($_POST['password']) ? $_POST['password'] : 'admin123';
            $confirm_password = isset($_POST['confirm_password']) ? $_POST['confirm_password'] : '';
            
            if ($password !== $confirm_password) {
                $error = 'كلمات المرور غير متطابقة';
            } else {
                try {
                    require_once 'config/database_config.php';
                    require_once 'config/database_odoo.php';
                    require_once 'models/BaseModel.php';
                    require_once 'models/ResUsers.php';
                    
                    $user_model = new ResUsers();
                    $user_id = $user_model->create(array(
                        'name' => $name,
                        'login' => $email,
                        'email' => $email,
                        'password' => hashPassword($password),
                        'active' => true,
                        'company_id' => 1,
                        'groups_id' => json_encode(array(1)) // مجموعة الإدارة
                    ));
                    
                    if ($user_id) {
                        $step = 5;
                        $success = 'تم إنشاء المستخدم الإداري بنجاح';
                    } else {
                        $error = 'فشل في إنشاء المستخدم الإداري';
                    }
                    
                } catch (Exception $e) {
                    $error = 'خطأ في إنشاء المستخدم: ' . $e->getMessage();
                }
            }
            break;
    }
}

/**
 * إدراج البيانات الأساسية
 */
function insertBasicData() {
    global $database;
    
    $database->begin_transaction();
    
    try {
        // إدراج العملات
        $currencies = array(
            array('name' => 'SAR', 'symbol' => 'ر.س', 'rate' => 1.000000, 'active' => 1),
            array('name' => 'USD', 'symbol' => '$', 'rate' => 0.266667, 'active' => 1),
            array('name' => 'EUR', 'symbol' => '€', 'rate' => 0.240000, 'active' => 1)
        );
        
        foreach ($currencies as $currency) {
            $database->insert('res_currency', $currency);
        }
        
        // إدراج الشركة الافتراضية
        $company_id = $database->insert('res_company', array(
            'name' => 'شركتي',
            'display_name' => 'شركتي',
            'code' => 'MYCO',
            'country' => 'السعودية',
            'currency_id' => 1,
            'active' => 1,
            'sequence' => 1
        ));
        
        // إدراج فئات وحدات القياس
        $uom_categories = array(
            array('name' => 'الوزن'),
            array('name' => 'الطول'),
            array('name' => 'الحجم'),
            array('name' => 'الوقت'),
            array('name' => 'الوحدة')
        );
        
        foreach ($uom_categories as $category) {
            $database->insert('uom_category', $category);
        }
        
        // إدراج وحدات القياس
        $uoms = array(
            array('name' => 'قطعة', 'category_id' => 5, 'factor' => 1.0, 'uom_type' => 'reference', 'active' => 1),
            array('name' => 'كيلوجرام', 'category_id' => 1, 'factor' => 1.0, 'uom_type' => 'reference', 'active' => 1),
            array('name' => 'متر', 'category_id' => 2, 'factor' => 1.0, 'uom_type' => 'reference', 'active' => 1),
            array('name' => 'لتر', 'category_id' => 3, 'factor' => 1.0, 'uom_type' => 'reference', 'active' => 1)
        );
        
        foreach ($uoms as $uom) {
            $database->insert('uom_uom', $uom);
        }
        
        // إدراج أنواع الحسابات
        $account_types = array(
            array('name' => 'مدينون', 'type' => 'receivable', 'internal_group' => 'asset'),
            array('name' => 'دائنون', 'type' => 'payable', 'internal_group' => 'liability'),
            array('name' => 'سيولة', 'type' => 'liquidity', 'internal_group' => 'asset'),
            array('name' => 'إيرادات', 'type' => 'income', 'internal_group' => 'income'),
            array('name' => 'مصروفات', 'type' => 'expense', 'internal_group' => 'expense'),
            array('name' => 'أصول', 'type' => 'asset', 'internal_group' => 'asset'),
            array('name' => 'خصوم', 'type' => 'liability', 'internal_group' => 'liability'),
            array('name' => 'حقوق ملكية', 'type' => 'equity', 'internal_group' => 'equity')
        );
        
        foreach ($account_types as $type) {
            $database->insert('account_account_type', $type);
        }
        
        // إدراج فئة المنتجات الافتراضية
        $category_id = $database->insert('product_category', array(
            'name' => 'جميع المنتجات',
            'complete_name' => 'جميع المنتجات',
            'active' => 1,
            'company_id' => $company_id
        ));
        
        $database->commit();
        
    } catch (Exception $e) {
        $database->rollback();
        throw $e;
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت قاعدة البيانات - نظام ERP المحاسبي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/odoo-style.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-database me-2"></i>
                            تثبيت قاعدة البيانات - بأسلوب Odoo
                        </h4>
                    </div>
                    
                    <div class="card-body">
                        <!-- مؤشر التقدم -->
                        <div class="progress mb-4">
                            <div class="progress-bar" style="width: <?php echo ($step / 5) * 100; ?>%">
                                الخطوة <?php echo $step; ?> من 5
                            </div>
                        </div>

                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?php echo $error; ?>
                            </div>
                        <?php endif; ?>

                        <?php if ($success): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <?php echo $success; ?>
                            </div>
                        <?php endif; ?>

                        <?php if ($step == 5): ?>
                            <!-- التثبيت مكتمل -->
                            <div class="text-center">
                                <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                                <h3 class="mt-3">تم تثبيت قاعدة البيانات بنجاح!</h3>
                                <p class="text-muted">النظام جاهز للاستخدام مع قاعدة البيانات</p>
                                
                                <div class="alert alert-info">
                                    <h6>بيانات تسجيل الدخول:</h6>
                                    <strong>البريد الإلكتروني:</strong> <EMAIL><br>
                                    <strong>كلمة المرور:</strong> admin123
                                </div>
                                
                                <a href="demo.php" class="btn btn-primary btn-lg">
                                    <i class="fas fa-arrow-right me-2"></i>
                                    دخول النظام
                                </a>
                            </div>
                        <?php else: ?>
                            <!-- نماذج التثبيت -->
                            <form method="POST">
                                <?php if ($step == 1): ?>
                                    <!-- الخطوة 1: إعداد قاعدة البيانات -->
                                    <h5>إعداد الاتصال بقاعدة البيانات</h5>
                                    <div class="mb-3">
                                        <label class="form-label">خادم قاعدة البيانات</label>
                                        <input type="text" class="form-control" name="host" value="localhost" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">اسم قاعدة البيانات</label>
                                        <input type="text" class="form-control" name="dbname" value="erp_accounting" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">اسم المستخدم</label>
                                        <input type="text" class="form-control" name="username" value="root" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">كلمة المرور</label>
                                        <input type="password" class="form-control" name="password">
                                    </div>
                                    
                                <?php elseif ($step == 2): ?>
                                    <!-- الخطوة 2: إنشاء الجداول -->
                                    <h5>إنشاء جداول قاعدة البيانات</h5>
                                    <p>سيتم إنشاء جميع الجداول المطلوبة للنظام بأسلوب Odoo</p>
                                    
                                <?php elseif ($step == 3): ?>
                                    <!-- الخطوة 3: البيانات الأساسية -->
                                    <h5>إدراج البيانات الأساسية</h5>
                                    <p>سيتم إدراج البيانات الأساسية مثل العملات ووحدات القياس</p>
                                    
                                <?php elseif ($step == 4): ?>
                                    <!-- الخطوة 4: المستخدم الإداري -->
                                    <h5>إنشاء المستخدم الإداري</h5>
                                    <div class="mb-3">
                                        <label class="form-label">الاسم الكامل</label>
                                        <input type="text" class="form-control" name="name" value="مدير النظام" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">البريد الإلكتروني</label>
                                        <input type="email" class="form-control" name="email" value="<EMAIL>" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">كلمة المرور</label>
                                        <input type="password" class="form-control" name="password" value="admin123" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">تأكيد كلمة المرور</label>
                                        <input type="password" class="form-control" name="confirm_password" value="admin123" required>
                                    </div>
                                <?php endif; ?>
                                
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-arrow-right me-2"></i>
                                    <?php echo $step == 4 ? 'إنهاء التثبيت' : 'التالي'; ?>
                                </button>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
