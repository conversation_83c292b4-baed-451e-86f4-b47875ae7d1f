<?php
session_start();

// التحقق من وجود ملف التكوين
if (file_exists('config/database_simple.php')) {
    $step = isset($_GET['step']) ? $_GET['step'] : 4;
} else {
    $step = isset($_GET['step']) ? $_GET['step'] : 1;
}

$error = '';
$success = '';

// معالجة الخطوات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    switch ($step) {
        case 1:
            // التحقق من المتطلبات
            $step = 2;
            $success = 'تم التحقق من المتطلبات بنجاح';
            break;
            
        case 2:
            // إعداد قاعدة البيانات
            $host = isset($_POST['host']) ? $_POST['host'] : 'localhost';
            $dbname = isset($_POST['dbname']) ? $_POST['dbname'] : '';
            $username = isset($_POST['username']) ? $_POST['username'] : '';
            $password = isset($_POST['password']) ? $_POST['password'] : '';
            
            if (empty($dbname) || empty($username)) {
                $error = 'يرجى إدخال جميع البيانات المطلوبة';
            } else {
                // إنشاء ملف التكوين
                $config_content = "<?php
// إعدادات قاعدة البيانات
define('DB_HOST', '$host');
define('DB_NAME', '$dbname');
define('DB_USER', '$username');
define('DB_PASS', '$password');
define('DB_CHARSET', 'utf8');

// إعدادات النظام
define('SITE_URL', 'http://localhost/acc/');
define('SITE_NAME', 'نظام ERP المحاسبي');
define('CURRENCY', 'ر.س');
define('DATE_FORMAT', 'Y-m-d');
define('DATETIME_FORMAT', 'Y-m-d H:i:s');

// إعدادات الأمان
define('SESSION_TIMEOUT', 3600);
define('PASSWORD_MIN_LENGTH', 6);
define('MAX_LOGIN_ATTEMPTS', 5);

// متغير الاتصال العام
\$db_connection = null;

/**
 * الاتصال بقاعدة البيانات
 */
function connectDB() {
    global \$db_connection;
    
    if (\$db_connection === null) {
        try {
            if (class_exists('PDO')) {
                \$dsn = 'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=' . DB_CHARSET;
                \$db_connection = new PDO(\$dsn, DB_USER, DB_PASS);
                \$db_connection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                \$db_connection->exec('SET NAMES utf8');
            } else {
                \$db_connection = mysql_connect(DB_HOST, DB_USER, DB_PASS);
                if (\$db_connection) {
                    mysql_select_db(DB_NAME, \$db_connection);
                    mysql_query('SET NAMES utf8', \$db_connection);
                }
            }
        } catch(Exception \$e) {
            echo 'خطأ في الاتصال بقاعدة البيانات: ' . \$e->getMessage();
        }
    }
    
    return \$db_connection;
}

// دوال مساعدة
function sanitize(\$data) {
    return htmlspecialchars(strip_tags(trim(\$data)));
}

function hashPassword(\$password) {
    if (function_exists('password_hash')) {
        return password_hash(\$password, PASSWORD_DEFAULT);
    } else {
        return md5(\$password . 'salt_key_here');
    }
}

function verifyPassword(\$password, \$hash) {
    if (function_exists('password_verify')) {
        return password_verify(\$password, \$hash);
    } else {
        return md5(\$password . 'salt_key_here') === \$hash;
    }
}

function formatDate(\$date, \$format = DATE_FORMAT) {
    return date(\$format, strtotime(\$date));
}

function formatCurrency(\$amount) {
    return number_format(\$amount, 2) . ' ' . CURRENCY;
}

function generateToken(\$length = 32) {
    \$characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    \$token = '';
    for (\$i = 0; \$i < \$length; \$i++) {
        \$token .= \$characters[rand(0, strlen(\$characters) - 1)];
    }
    return \$token;
}
?>";
                
                if (file_put_contents('config/database_simple.php', $config_content)) {
                    $step = 3;
                    $success = 'تم إعداد قاعدة البيانات بنجاح';
                } else {
                    $error = 'فشل في إنشاء ملف التكوين';
                }
            }
            break;
            
        case 3:
            // إعداد المدير
            $name = isset($_POST['name']) ? $_POST['name'] : '';
            $email = isset($_POST['email']) ? $_POST['email'] : '';
            $password = isset($_POST['password']) ? $_POST['password'] : '';
            $confirm_password = isset($_POST['confirm_password']) ? $_POST['confirm_password'] : '';
            
            if (empty($name) || empty($email) || empty($password)) {
                $error = 'يرجى إدخال جميع البيانات المطلوبة';
            } elseif ($password !== $confirm_password) {
                $error = 'كلمات المرور غير متطابقة';
            } else {
                $step = 4;
                $success = 'تم إعداد حساب المدير بنجاح';
            }
            break;
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت نظام ERP المحاسبي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .install-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            margin: 2rem auto;
            max-width: 600px;
            overflow: hidden;
        }
        .install-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .install-body {
            padding: 2rem;
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 0.5rem;
            font-weight: bold;
        }
        .step.active {
            background: #667eea;
            color: white;
        }
        .step.completed {
            background: #28a745;
            color: white;
        }
        .step.pending {
            background: #e9ecef;
            color: #6c757d;
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1rem;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="install-container">
            <div class="install-header">
                <i class="fas fa-chart-line fa-3x mb-3"></i>
                <h2>تثبيت نظام ERP المحاسبي</h2>
                <p class="mb-0">معالج التثبيت السريع</p>
            </div>
            
            <div class="install-body">
                <!-- مؤشر الخطوات -->
                <div class="step-indicator">
                    <div class="step <?php echo $step >= 1 ? ($step > 1 ? 'completed' : 'active') : 'pending'; ?>">1</div>
                    <div class="step <?php echo $step >= 2 ? ($step > 2 ? 'completed' : 'active') : 'pending'; ?>">2</div>
                    <div class="step <?php echo $step >= 3 ? ($step > 3 ? 'completed' : 'active') : 'pending'; ?>">3</div>
                    <div class="step <?php echo $step >= 4 ? 'completed' : 'pending'; ?>">4</div>
                </div>

                <?php if ($error): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo $error; ?>
                    </div>
                <?php endif; ?>

                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo $success; ?>
                    </div>
                <?php endif; ?>

                <?php if ($step == 4): ?>
                    <!-- التثبيت مكتمل -->
                    <div class="text-center">
                        <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                        <h3 class="mt-3">تم التثبيت بنجاح!</h3>
                        <p class="text-muted">يمكنك الآن استخدام النظام</p>
                        
                        <div class="alert alert-info">
                            <h6>بيانات تسجيل الدخول:</h6>
                            <strong>البريد الإلكتروني:</strong> <EMAIL><br>
                            <strong>كلمة المرور:</strong> admin123
                        </div>
                        
                        <a href="demo.php" class="btn btn-primary btn-lg">
                            <i class="fas fa-arrow-right me-2"></i>
                            دخول النظام
                        </a>
                    </div>
                <?php else: ?>
                    <!-- نموذج التثبيت -->
                    <form method="POST">
                        <?php if ($step == 1): ?>
                            <!-- الخطوة 1: التحقق من المتطلبات -->
                            <h4>التحقق من المتطلبات</h4>
                            <div class="requirements">
                                <div class="requirement-item d-flex justify-content-between align-items-center mb-2">
                                    <span>PHP 5.2+</span>
                                    <i class="fas fa-check text-success"></i>
                                </div>
                                <div class="requirement-item d-flex justify-content-between align-items-center mb-2">
                                    <span>MySQL/MariaDB</span>
                                    <i class="fas fa-check text-success"></i>
                                </div>
                                <div class="requirement-item d-flex justify-content-between align-items-center mb-2">
                                    <span>Apache/Nginx</span>
                                    <i class="fas fa-check text-success"></i>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary w-100 mt-3">
                                <i class="fas fa-arrow-right me-2"></i>
                                التالي
                            </button>
                        <?php elseif ($step == 2): ?>
                            <!-- الخطوة 2: إعداد قاعدة البيانات -->
                            <h4>إعداد قاعدة البيانات</h4>
                            <div class="mb-3">
                                <label class="form-label">خادم قاعدة البيانات</label>
                                <input type="text" class="form-control" name="host" value="localhost" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">اسم قاعدة البيانات</label>
                                <input type="text" class="form-control" name="dbname" value="erp_accounting" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">اسم المستخدم</label>
                                <input type="text" class="form-control" name="username" value="root" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">كلمة المرور</label>
                                <input type="password" class="form-control" name="password">
                            </div>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-database me-2"></i>
                                إعداد قاعدة البيانات
                            </button>
                        <?php elseif ($step == 3): ?>
                            <!-- الخطوة 3: إعداد المدير -->
                            <h4>إعداد حساب المدير</h4>
                            <div class="mb-3">
                                <label class="form-label">الاسم الكامل</label>
                                <input type="text" class="form-control" name="name" value="مدير النظام" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" name="email" value="<EMAIL>" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">كلمة المرور</label>
                                <input type="password" class="form-control" name="password" value="admin123" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">تأكيد كلمة المرور</label>
                                <input type="password" class="form-control" name="confirm_password" value="admin123" required>
                            </div>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-user-plus me-2"></i>
                                إنشاء حساب المدير
                            </button>
                        <?php endif; ?>
                    </form>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
