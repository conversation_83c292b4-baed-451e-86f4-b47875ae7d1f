<?php
/**
 * تشغيل تلقائي فوري للنظام
 * Instant Auto Launch - Zero Configuration
 */

// إيقاف عرض الأخطاء للمستخدم النهائي
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

session_start();

// إعدادات افتراضية
$config = array(
    'db_host' => 'localhost',
    'db_name' => 'erp_accounting',
    'db_user' => 'root',
    'db_pass' => '',
    'admin_email' => '<EMAIL>',
    'admin_password' => 'admin123',
    'company_name' => 'شركتي'
);

$setup_success = false;
$demo_mode = false;

try {
    // إنشاء المجلدات المطلوبة
    $dirs = array('config', 'logs', 'cache', 'uploads', 'tmp', 'sessions');
    foreach ($dirs as $dir) {
        if (!file_exists($dir)) {
            @mkdir($dir, 0755, true);
        }
    }
    
    // التحقق من وجود ملف التكوين
    if (!file_exists('config/database_config.php')) {
        
        // محاولة إعداد قاعدة البيانات
        try {
            if (class_exists('PDO')) {
                $dsn = "mysql:host={$config['db_host']};charset=utf8mb4";
                $pdo = new PDO($dsn, $config['db_user'], $config['db_pass']);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // إنشاء قاعدة البيانات
                $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$config['db_name']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                $pdo->exec("USE `{$config['db_name']}`");
                
                // إنشاء ملف التكوين
                $config_content = "<?php
define('DB_HOST', '{$config['db_host']}');
define('DB_NAME', '{$config['db_name']}');
define('DB_USER', '{$config['db_user']}');
define('DB_PASS', '{$config['db_pass']}');
define('DB_CHARSET', 'utf8mb4');
define('SITE_URL', 'http://localhost/acc/');
define('SITE_NAME', 'نظام ERP المحاسبي');
define('CURRENCY', 'ر.س');
define('DATE_FORMAT', 'Y-m-d');
define('DATETIME_FORMAT', 'Y-m-d H:i:s');
define('SESSION_TIMEOUT', 3600);
define('PASSWORD_MIN_LENGTH', 6);
define('MAX_LOGIN_ATTEMPTS', 5);
define('SYSTEM_INSTALLED', true);
define('INSTALL_DATE', '" . date('Y-m-d H:i:s') . "');
?>";
                
                file_put_contents('config/database_config.php', $config_content);
                
                // إنشاء الجداول الأساسية
                createBasicTables($pdo);
                
                // إدراج البيانات الأساسية
                insertBasicData($pdo, $config);
                
                $setup_success = true;
                
            } else {
                throw new Exception('PDO غير متوفر');
            }
            
        } catch (Exception $e) {
            // إذا فشل إعداد قاعدة البيانات، استخدم الوضع التجريبي
            $demo_config = "<?php
define('SITE_URL', 'http://localhost/acc/');
define('SITE_NAME', 'نظام ERP المحاسبي');
define('CURRENCY', 'ر.س');
define('DATE_FORMAT', 'Y-m-d');
define('DATETIME_FORMAT', 'Y-m-d H:i:s');
define('SESSION_TIMEOUT', 3600);
define('DEMO_MODE', true);
define('SYSTEM_INSTALLED', true);
define('INSTALL_DATE', '" . date('Y-m-d H:i:s') . "');
?>";
            
            file_put_contents('config/database_config.php', $demo_config);
            $demo_mode = true;
        }
    } else {
        $setup_success = true;
    }
    
    // تسجيل دخول تلقائي
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'مدير النظام';
    $_SESSION['email'] = $config['admin_email'];
    $_SESSION['role'] = 'admin';
    $_SESSION['company_id'] = 1;
    $_SESSION['auto_launch'] = true;
    
    if ($demo_mode) {
        $_SESSION['demo_mode'] = true;
    }
    
} catch (Exception $e) {
    error_log("Launch Error: " . $e->getMessage());
    // حتى لو فشل كل شيء، نسجل دخول تجريبي
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'مدير النظام';
    $_SESSION['email'] = '<EMAIL>';
    $_SESSION['role'] = 'admin';
    $_SESSION['company_id'] = 1;
    $_SESSION['demo_mode'] = true;
}

/**
 * إنشاء الجداول الأساسية
 */
function createBasicTables($pdo) {
    $tables = array(
        "CREATE TABLE IF NOT EXISTS res_company (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(255) NOT NULL,
            display_name VARCHAR(255),
            code VARCHAR(10),
            active BOOLEAN DEFAULT TRUE,
            create_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )",
        
        "CREATE TABLE IF NOT EXISTS res_users (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(255) NOT NULL,
            login VARCHAR(100) UNIQUE NOT NULL,
            email VARCHAR(100),
            password VARCHAR(255) NOT NULL,
            active BOOLEAN DEFAULT TRUE,
            company_id INT DEFAULT 1,
            create_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )",
        
        "CREATE TABLE IF NOT EXISTS res_partner (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(255) NOT NULL,
            email VARCHAR(100),
            phone VARCHAR(20),
            customer_rank INT DEFAULT 0,
            supplier_rank INT DEFAULT 0,
            active BOOLEAN DEFAULT TRUE,
            company_id INT DEFAULT 1,
            create_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )"
    );
    
    foreach ($tables as $sql) {
        try {
            $pdo->exec($sql);
        } catch (Exception $e) {
            error_log("Table creation error: " . $e->getMessage());
        }
    }
}

/**
 * إدراج البيانات الأساسية
 */
function insertBasicData($pdo, $config) {
    try {
        // إدراج الشركة
        $pdo->exec("INSERT IGNORE INTO res_company (id, name, display_name, code, active) 
                   VALUES (1, '{$config['company_name']}', '{$config['company_name']}', 'MYCO', 1)");
        
        // إدراج المستخدم الإداري
        $hashed_password = password_hash($config['admin_password'], PASSWORD_DEFAULT);
        $pdo->prepare("INSERT IGNORE INTO res_users (id, name, login, email, password, active, company_id) 
                      VALUES (1, 'مدير النظام', ?, ?, ?, 1, 1)")
            ->execute(array($config['admin_email'], $config['admin_email'], $hashed_password));
        
        // إدراج بعض الشركاء التجريبيين
        $partners = array(
            array('عميل تجريبي 1', '<EMAIL>', '+966501111111', 1, 0),
            array('عميل تجريبي 2', '<EMAIL>', '+966502222222', 1, 0),
            array('مورد تجريبي 1', '<EMAIL>', '+966503333333', 0, 1)
        );
        
        $stmt = $pdo->prepare("INSERT IGNORE INTO res_partner (name, email, phone, customer_rank, supplier_rank, active, company_id) 
                              VALUES (?, ?, ?, ?, ?, 1, 1)");
        
        foreach ($partners as $partner) {
            $stmt->execute($partner);
        }
        
    } catch (Exception $e) {
        error_log("Data insertion error: " . $e->getMessage());
    }
}

// إعادة توجيه فورية للنظام
header('Location: demo.php');
exit();
?>
