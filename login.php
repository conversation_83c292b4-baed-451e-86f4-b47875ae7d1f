<?php
/**
 * صفحة تسجيل الدخول بأسلوب Odoo
 * Odoo-Style Login Page
 */

session_start();

// إذا كان مسجل دخول، توجيه للصفحة الرئيسية
if (isset($_SESSION['user_id'])) {
    header('Location: dashboard.php');
    exit();
}

$error_message = '';
$success_message = '';

// التحقق من وجود رسالة في URL
if (isset($_GET['message'])) {
    $success_message = $_GET['message'];
}

// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $email = isset($_POST['email']) ? trim($_POST['email']) : '';
    $password = isset($_POST['password']) ? $_POST['password'] : '';
    
    // التحقق من البيانات
    if (empty($email) || empty($password)) {
        $error_message = 'يرجى إدخال البريد الإلكتروني وكلمة المرور';
    } else {
        // بيانات تسجيل الدخول الافتراضية
        $valid_users = array(
            '<EMAIL>' => array(
                'password' => 'admin123',
                'name' => 'مدير النظام',
                'role' => 'admin',
                'groups' => array('admin', 'manager', 'user')
            ),
            '<EMAIL>' => array(
                'password' => 'manager123',
                'name' => 'مدير',
                'role' => 'manager',
                'groups' => array('manager', 'user')
            ),
            '<EMAIL>' => array(
                'password' => 'user123',
                'name' => 'مستخدم',
                'role' => 'user',
                'groups' => array('user')
            )
        );
        
        // التحقق من صحة البيانات
        if (isset($valid_users[$email]) && $valid_users[$email]['password'] === $password) {
            $user = $valid_users[$email];
            
            // تسجيل الدخول
            $_SESSION['user_id'] = 1;
            $_SESSION['username'] = $user['name'];
            $_SESSION['email'] = $email;
            $_SESSION['role'] = $user['role'];
            $_SESSION['groups'] = $user['groups'];
            $_SESSION['company_id'] = 1;
            $_SESSION['company_name'] = 'شركتي';
            $_SESSION['odoo_style'] = true;
            $_SESSION['login_time'] = date('Y-m-d H:i:s');
            
            // إنشاء بيانات تجريبية
            $_SESSION['demo_data'] = array(
                'companies' => array(
                    array('id' => 1, 'name' => 'شركتي', 'code' => 'MYCO', 'active' => true),
                    array('id' => 2, 'name' => 'شركة تجريبية', 'code' => 'DEMO', 'active' => true)
                ),
                'partners' => array(
                    array('id' => 1, 'name' => 'عميل تجريبي 1', 'customer_rank' => 1),
                    array('id' => 2, 'name' => 'عميل تجريبي 2', 'customer_rank' => 1),
                    array('id' => 3, 'name' => 'مورد تجريبي 1', 'supplier_rank' => 1)
                ),
                'products' => array(
                    array('id' => 1, 'name' => 'منتج تجريبي 1', 'list_price' => 100.00),
                    array('id' => 2, 'name' => 'منتج تجريبي 2', 'list_price' => 200.00),
                    array('id' => 3, 'name' => 'خدمة تجريبية', 'list_price' => 50.00)
                )
            );
            
            // تسجيل في السجل
            $log_entry = array(
                'timestamp' => date('Y-m-d H:i:s'),
                'action' => 'login',
                'user' => $email,
                'ip' => isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : 'unknown'
            );
            
            if (!file_exists('logs')) {
                mkdir('logs', 0755, true);
            }
            @file_put_contents('logs/login.log', json_encode($log_entry) . "\n", FILE_APPEND | LOCK_EX);
            
            // توجيه للصفحة الرئيسية
            header('Location: dashboard.php');
            exit();
        } else {
            $error_message = 'البريد الإلكتروني أو كلمة المرور غير صحيحة';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام ERP بأسلوب Odoo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
        }
        
        .login-left {
            background: linear-gradient(45deg, #714B67, #8B5A8C);
            color: white;
            padding: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }
        
        .login-right {
            padding: 3rem;
        }
        
        .odoo-logo {
            font-size: 3rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #714B67;
            box-shadow: 0 0 0 0.2rem rgba(113, 75, 103, 0.25);
        }
        
        .btn-odoo {
            background: linear-gradient(45deg, #714B67, #8B5A8C);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: bold;
            color: white;
            transition: all 0.3s ease;
        }
        
        .btn-odoo:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(113, 75, 103, 0.3);
            color: white;
        }
        
        .demo-users {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1rem;
        }
        
        .demo-user {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem;
            margin: 0.25rem 0;
            background: white;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .demo-user:hover {
            background: #e9ecef;
            transform: translateX(5px);
        }
        
        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: -1;
        }
        
        .shape {
            position: absolute;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }
        
        .shape:nth-child(1) { width: 80px; height: 80px; top: 10%; left: 10%; animation-delay: 0s; }
        .shape:nth-child(2) { width: 60px; height: 60px; top: 70%; left: 80%; animation-delay: 2s; }
        .shape:nth-child(3) { width: 100px; height: 100px; top: 40%; left: 70%; animation-delay: 4s; }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
    </style>
</head>
<body>
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>
    
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-12">
                <div class="login-container">
                    <div class="row g-0">
                        <!-- الجانب الأيسر -->
                        <div class="col-md-6 login-left">
                            <div class="floating-shapes">
                                <div class="shape"></div>
                                <div class="shape"></div>
                                <div class="shape"></div>
                            </div>
                            
                            <div class="odoo-logo">
                                <i class="fas fa-cube"></i>
                            </div>
                            <h2 class="mb-3">نظام ERP المحاسبي</h2>
                            <h4 class="mb-4">بأسلوب Odoo الاحترافي</h4>
                            
                            <div class="features">
                                <div class="feature mb-3">
                                    <i class="fas fa-check-circle me-2"></i>
                                    إدارة شاملة للأعمال
                                </div>
                                <div class="feature mb-3">
                                    <i class="fas fa-check-circle me-2"></i>
                                    واجهة عصرية وسهلة
                                </div>
                                <div class="feature mb-3">
                                    <i class="fas fa-check-circle me-2"></i>
                                    تقارير متقدمة
                                </div>
                                <div class="feature">
                                    <i class="fas fa-check-circle me-2"></i>
                                    أمان عالي المستوى
                                </div>
                            </div>
                        </div>
                        
                        <!-- الجانب الأيمن -->
                        <div class="col-md-6 login-right">
                            <div class="text-center mb-4">
                                <h3 class="text-primary">مرحباً بك</h3>
                                <p class="text-muted">سجل دخولك للوصول إلى النظام</p>
                            </div>
                            
                            <?php if ($error_message): ?>
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <?php echo $error_message; ?>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($success_message): ?>
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle me-2"></i>
                                    <?php echo $success_message; ?>
                                </div>
                            <?php endif; ?>
                            
                            <form method="POST" action="">
                                <div class="mb-3">
                                    <label for="email" class="form-label">
                                        <i class="fas fa-envelope me-2"></i>
                                        البريد الإلكتروني
                                    </label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<EMAIL>" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="password" class="form-label">
                                        <i class="fas fa-lock me-2"></i>
                                        كلمة المرور
                                    </label>
                                    <input type="password" class="form-control" id="password" name="password" 
                                           value="admin123" required>
                                </div>
                                
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="remember">
                                    <label class="form-check-label" for="remember">
                                        تذكرني
                                    </label>
                                </div>
                                
                                <button type="submit" class="btn btn-odoo w-100 mb-3">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    تسجيل الدخول
                                </button>
                            </form>
                            
                            <!-- المستخدمين التجريبيين -->
                            <div class="demo-users">
                                <h6 class="text-center mb-3">
                                    <i class="fas fa-users me-2"></i>
                                    مستخدمين تجريبيين
                                </h6>
                                
                                <div class="demo-user" onclick="fillLogin('<EMAIL>', 'admin123')">
                                    <div>
                                        <strong>مدير النظام</strong><br>
                                        <small class="text-muted"><EMAIL></small>
                                    </div>
                                    <span class="badge bg-danger">Admin</span>
                                </div>
                                
                                <div class="demo-user" onclick="fillLogin('<EMAIL>', 'manager123')">
                                    <div>
                                        <strong>مدير</strong><br>
                                        <small class="text-muted"><EMAIL></small>
                                    </div>
                                    <span class="badge bg-warning">Manager</span>
                                </div>
                                
                                <div class="demo-user" onclick="fillLogin('<EMAIL>', 'user123')">
                                    <div>
                                        <strong>مستخدم</strong><br>
                                        <small class="text-muted"><EMAIL></small>
                                    </div>
                                    <span class="badge bg-info">User</span>
                                </div>
                            </div>
                            
                            <div class="text-center mt-3">
                                <small class="text-muted">
                                    <i class="fas fa-shield-alt me-1"></i>
                                    محمي بأمان عالي المستوى
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function fillLogin(email, password) {
            document.getElementById('email').value = email;
            document.getElementById('password').value = password;
        }
        
        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير الظهور التدريجي
            const container = document.querySelector('.login-container');
            container.style.opacity = '0';
            container.style.transform = 'translateY(50px)';
            
            setTimeout(() => {
                container.style.transition = 'all 0.6s ease';
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            }, 100);
            
            // تركيز تلقائي على حقل البريد الإلكتروني
            document.getElementById('email').focus();
        });
        
        // معالجة النموذج
        document.querySelector('form').addEventListener('submit', function(e) {
            const submitBtn = document.querySelector('.btn-odoo');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري تسجيل الدخول...';
            submitBtn.disabled = true;
        });
    </script>
</body>
</html>
