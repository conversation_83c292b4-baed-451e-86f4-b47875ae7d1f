<?php
/**
 * صفحة تسجيل الخروج
 * Logout Page
 */

session_start();

// تسجيل في السجل
if (isset($_SESSION['email'])) {
    $log_entry = array(
        'timestamp' => date('Y-m-d H:i:s'),
        'action' => 'logout',
        'user' => $_SESSION['email'],
        'ip' => isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : 'unknown'
    );
    
    if (!file_exists('logs')) {
        mkdir('logs', 0755, true);
    }
    @file_put_contents('logs/login.log', json_encode($log_entry) . "\n", FILE_APPEND | LOCK_EX);
}

// إنهاء الجلسة
session_destroy();

// توجيه لصفحة تسجيل الدخول
header('Location: login.php?message=تم تسجيل الخروج بنجاح');
exit();
?>
