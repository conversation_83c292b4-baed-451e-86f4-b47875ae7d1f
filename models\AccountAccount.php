<?php
/**
 * نموذج دليل الحسابات بأسلوب Odoo
 * Account Chart of Accounts Model - Odoo Style
 */

require_once 'BaseModel.php';

class AccountAccount extends BaseModel {
    protected $table = 'account_account';
    
    protected $fillable = [
        'name', 'code', 'account_type', 'parent_id', 'level', 
        'reconcile', 'active', 'company_id', 'currency_id', 'note'
    ];
    
    protected $casts = [
        'reconcile' => 'boolean',
        'active' => 'boolean',
        'level' => 'integer',
        'parent_id' => 'integer',
        'company_id' => 'integer',
        'currency_id' => 'integer'
    ];
    
    // العلاقات
    public function parent() {
        return $this->belongsTo('AccountAccount', 'parent_id');
    }
    
    public function children() {
        return $this->hasMany('AccountAccount', 'parent_id');
    }
    
    public function move_lines() {
        return $this->hasMany('AccountMoveLine', 'account_id');
    }
    
    public function company() {
        return $this->belongsTo('ResCompany', 'company_id');
    }
    
    public function currency() {
        return $this->belongsTo('ResCurrency', 'currency_id');
    }
    
    // الدوال المساعدة
    
    /**
     * الحصول على الحسابات الرئيسية
     */
    public function get_root_accounts() {
        return $this->search_read(
            array(array('parent_id', '=', null)),
            null,
            array('order' => 'code ASC')
        );
    }
    
    /**
     * الحصول على الحسابات الفرعية
     */
    public function get_child_accounts($parent_id) {
        return $this->search_read(
            array(array('parent_id', '=', $parent_id)),
            null,
            array('order' => 'code ASC')
        );
    }
    
    /**
     * الحصول على شجرة الحسابات
     */
    public function get_account_tree($parent_id = null, $level = 1) {
        $accounts = $this->search_read(
            array(array('parent_id', '=', $parent_id)),
            null,
            array('order' => 'code ASC')
        );
        
        $tree = array();
        foreach ($accounts as $account) {
            $account['level'] = $level;
            $account['children'] = $this->get_account_tree($account['id'], $level + 1);
            $tree[] = $account;
        }
        
        return $tree;
    }
    
    /**
     * حساب الرصيد
     */
    public function compute_balance($account_id, $date_from = null, $date_to = null) {
        $conditions = array(array('account_id', '=', $account_id));
        
        if ($date_from) {
            $conditions[] = array('date', '>=', $date_from);
        }
        if ($date_to) {
            $conditions[] = array('date', '<=', $date_to);
        }
        
        try {
            $sql = "SELECT 
                        SUM(debit) as total_debit,
                        SUM(credit) as total_credit,
                        SUM(debit - credit) as balance
                    FROM account_move_line 
                    WHERE account_id = ?";
            
            $params = array($account_id);
            
            if ($date_from) {
                $sql .= " AND date >= ?";
                $params[] = $date_from;
            }
            if ($date_to) {
                $sql .= " AND date <= ?";
                $params[] = $date_to;
            }
            
            $result = $this->db->query($sql, $params);
            
            if ($result && count($result) > 0) {
                return array(
                    'debit' => floatval($result[0]['total_debit'] ?? 0),
                    'credit' => floatval($result[0]['total_credit'] ?? 0),
                    'balance' => floatval($result[0]['balance'] ?? 0)
                );
            }
        } catch (Exception $e) {
            // في حالة عدم وجود قاعدة البيانات، إرجاع بيانات تجريبية
        }
        
        return array('debit' => 0, 'credit' => 0, 'balance' => 0);
    }
    
    /**
     * الحصول على الحسابات حسب النوع
     */
    public function get_accounts_by_type($account_type) {
        return $this->search_read(
            array(array('account_type', '=', $account_type)),
            null,
            array('order' => 'code ASC')
        );
    }
    
    /**
     * البحث في الحسابات
     */
    public function search_accounts($search_term) {
        $conditions = array(
            'OR' => array(
                array('name', 'LIKE', '%' . $search_term . '%'),
                array('code', 'LIKE', '%' . $search_term . '%')
            )
        );
        
        return $this->search_read($conditions, null, array('order' => 'code ASC'));
    }
    
    /**
     * التحقق من صحة رمز الحساب
     */
    public function validate_account_code($code, $exclude_id = null) {
        $conditions = array(array('code', '=', $code));
        
        if ($exclude_id) {
            $conditions[] = array('id', '!=', $exclude_id);
        }
        
        $existing = $this->search_read($conditions);
        return count($existing) === 0;
    }
    
    /**
     * الحصول على بيانات تجريبية
     */
    public function get_demo_data() {
        // محاولة الحصول على البيانات من قاعدة البيانات أولاً
        try {
            $accounts = $this->search_read(
                array(array('active', '=', true)),
                null,
                array('order' => 'code ASC')
            );
            
            if (count($accounts) > 0) {
                return $accounts;
            }
        } catch (Exception $e) {
            // في حالة عدم وجود قاعدة البيانات
        }
        
        // بيانات تجريبية احتياطية
        return array(
            array(
                'id' => 1,
                'name' => 'الأصول',
                'code' => '1',
                'account_type' => 'asset',
                'parent_id' => null,
                'level' => 1,
                'reconcile' => false,
                'active' => true,
                'company_id' => 1,
                'currency_id' => 1,
                'note' => 'حسابات الأصول الرئيسية'
            ),
            array(
                'id' => 4,
                'name' => 'الصندوق',
                'code' => '1111',
                'account_type' => 'asset',
                'parent_id' => 3,
                'level' => 4,
                'reconcile' => true,
                'active' => true,
                'company_id' => 1,
                'currency_id' => 1,
                'note' => 'صندوق النقدية'
            ),
            array(
                'id' => 5,
                'name' => 'البنك الأهلي',
                'code' => '1112',
                'account_type' => 'asset',
                'parent_id' => 3,
                'level' => 4,
                'reconcile' => true,
                'active' => true,
                'company_id' => 1,
                'currency_id' => 1,
                'note' => 'حساب البنك الأهلي'
            ),
            array(
                'id' => 8,
                'name' => 'حسابات العملاء',
                'code' => '1121',
                'account_type' => 'asset',
                'parent_id' => 7,
                'level' => 4,
                'reconcile' => true,
                'active' => true,
                'company_id' => 1,
                'currency_id' => 1,
                'note' => 'حسابات العملاء'
            ),
            array(
                'id' => 19,
                'name' => 'الخصوم',
                'code' => '2',
                'account_type' => 'liability',
                'parent_id' => null,
                'level' => 1,
                'reconcile' => false,
                'active' => true,
                'company_id' => 1,
                'currency_id' => 1,
                'note' => 'حسابات الخصوم الرئيسية'
            ),
            array(
                'id' => 22,
                'name' => 'حسابات الموردين',
                'code' => '2111',
                'account_type' => 'liability',
                'parent_id' => 21,
                'level' => 4,
                'reconcile' => true,
                'active' => true,
                'company_id' => 1,
                'currency_id' => 1,
                'note' => 'حسابات الموردين'
            ),
            array(
                'id' => 34,
                'name' => 'الإيرادات',
                'code' => '4',
                'account_type' => 'income',
                'parent_id' => null,
                'level' => 1,
                'reconcile' => false,
                'active' => true,
                'company_id' => 1,
                'currency_id' => 1,
                'note' => 'حسابات الإيرادات الرئيسية'
            ),
            array(
                'id' => 36,
                'name' => 'مبيعات البضائع',
                'code' => '411',
                'account_type' => 'income',
                'parent_id' => 35,
                'level' => 3,
                'reconcile' => true,
                'active' => true,
                'company_id' => 1,
                'currency_id' => 1,
                'note' => 'إيرادات مبيعات البضائع'
            ),
            array(
                'id' => 40,
                'name' => 'المصروفات',
                'code' => '5',
                'account_type' => 'expense',
                'parent_id' => null,
                'level' => 1,
                'reconcile' => false,
                'active' => true,
                'company_id' => 1,
                'currency_id' => 1,
                'note' => 'حسابات المصروفات الرئيسية'
            ),
            array(
                'id' => 42,
                'name' => 'تكلفة المبيعات',
                'code' => '511',
                'account_type' => 'expense',
                'parent_id' => 41,
                'level' => 3,
                'reconcile' => true,
                'active' => true,
                'company_id' => 1,
                'currency_id' => 1,
                'note' => 'تكلفة البضاعة المباعة'
            )
        );
    }
    
    /**
     * إنشاء حساب جديد
     */
    public function create_account($data) {
        // التحقق من صحة البيانات
        if (empty($data['name']) || empty($data['code']) || empty($data['account_type'])) {
            throw new Exception('البيانات المطلوبة مفقودة');
        }
        
        // التحقق من عدم تكرار رمز الحساب
        if (!$this->validate_account_code($data['code'])) {
            throw new Exception('رمز الحساب موجود مسبقاً');
        }
        
        // تحديد المستوى تلقائياً
        if (isset($data['parent_id']) && $data['parent_id']) {
            $parent = $this->read($data['parent_id']);
            if ($parent) {
                $data['level'] = $parent['level'] + 1;
            }
        } else {
            $data['level'] = 1;
        }
        
        return $this->create($data);
    }
}
?>
