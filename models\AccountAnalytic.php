<?php
/**
 * نموذج المحاسبة التحليلية بأسلوب Odoo
 * Account Analytic Model - Odoo Style
 */

require_once 'BaseModel.php';

class AccountAnalyticAccount extends BaseModel {
    protected $table = 'account_analytic_account';
    
    protected $fillable = [
        'name', 'code', 'active', 'group_id', 'line_ids', 'company_id',
        'partner_id', 'balance', 'debit', 'credit', 'currency_id',
        'plan_id', 'root_plan_id'
    ];
    
    protected $casts = [
        'active' => 'boolean',
        'group_id' => 'integer',
        'company_id' => 'integer',
        'partner_id' => 'integer',
        'balance' => 'decimal',
        'debit' => 'decimal',
        'credit' => 'decimal',
        'currency_id' => 'integer',
        'plan_id' => 'integer',
        'root_plan_id' => 'integer'
    ];
    
    // العلاقات
    public function analytic_lines() {
        return $this->hasMany('AccountAnalyticLine', 'account_id');
    }
    
    public function company() {
        return $this->belongsTo('ResCompany', 'company_id');
    }
    
    public function partner() {
        return $this->belongsTo('ResPartner', 'partner_id');
    }
    
    public function currency() {
        return $this->belongsTo('ResCurrency', 'currency_id');
    }
    
    public function group() {
        return $this->belongsTo('AccountAnalyticGroup', 'group_id');
    }
    
    /**
     * حساب الرصيد التحليلي
     */
    public function compute_balance($account_id, $date_from = null, $date_to = null) {
        require_once 'AccountAnalyticLine.php';
        $line_model = new AccountAnalyticLine($this->db);
        
        $conditions = array('account_id' => $account_id);
        
        if ($date_from) {
            $conditions['date'] = array('>=', $date_from);
        }
        if ($date_to) {
            if (isset($conditions['date'])) {
                $conditions['date'] = array('BETWEEN', array($date_from, $date_to));
            } else {
                $conditions['date'] = array('<=', $date_to);
            }
        }
        
        $lines = $line_model->search($conditions);
        
        $balance = 0;
        $debit = 0;
        $credit = 0;
        
        foreach ($lines as $line) {
            $amount = $line['amount'];
            if ($amount > 0) {
                $debit += $amount;
            } else {
                $credit += abs($amount);
            }
            $balance += $amount;
        }
        
        // تحديث الأرصدة
        $this->update($account_id, array(
            'balance' => $balance,
            'debit' => $debit,
            'credit' => $credit
        ));
        
        return array(
            'balance' => $balance,
            'debit' => $debit,
            'credit' => $credit
        );
    }
    
    /**
     * تحليل الربحية
     */
    public function analyze_profitability($account_id, $period = 'month') {
        require_once 'AccountAnalyticLine.php';
        $line_model = new AccountAnalyticLine($this->db);
        
        $lines = $line_model->search(array('account_id' => $account_id));
        
        $revenue = 0;
        $costs = 0;
        $hours = 0;
        
        foreach ($lines as $line) {
            if ($line['amount'] > 0) {
                $revenue += $line['amount'];
            } else {
                $costs += abs($line['amount']);
            }
            
            if ($line['unit_amount']) {
                $hours += $line['unit_amount'];
            }
        }
        
        $profit = $revenue - $costs;
        $margin = $revenue > 0 ? ($profit / $revenue) * 100 : 0;
        $hourly_rate = $hours > 0 ? $revenue / $hours : 0;
        
        return array(
            'revenue' => $revenue,
            'costs' => $costs,
            'profit' => $profit,
            'margin_percentage' => $margin,
            'total_hours' => $hours,
            'hourly_rate' => $hourly_rate
        );
    }
    
    /**
     * الحصول على بيانات تجريبية
     */
    public function get_demo_data() {
        return array(
            array(
                'id' => 1,
                'name' => 'مشروع تطوير الموقع الإلكتروني',
                'code' => 'WEB001',
                'active' => true,
                'group_id' => 1,
                'company_id' => 1,
                'partner_id' => 1,
                'balance' => 25000.00,
                'debit' => 50000.00,
                'credit' => 25000.00,
                'currency_id' => 1,
                'plan_id' => 1
            ),
            array(
                'id' => 2,
                'name' => 'مشروع تطوير التطبيق المحمول',
                'code' => 'MOB001',
                'active' => true,
                'group_id' => 1,
                'company_id' => 1,
                'partner_id' => 2,
                'balance' => 15000.00,
                'debit' => 40000.00,
                'credit' => 25000.00,
                'currency_id' => 1,
                'plan_id' => 1
            ),
            array(
                'id' => 3,
                'name' => 'قسم التسويق',
                'code' => 'MKT',
                'active' => true,
                'group_id' => 2,
                'company_id' => 1,
                'balance' => -30000.00,
                'debit' => 0.00,
                'credit' => 30000.00,
                'currency_id' => 1,
                'plan_id' => 1
            ),
            array(
                'id' => 4,
                'name' => 'قسم البحث والتطوير',
                'code' => 'RND',
                'active' => true,
                'group_id' => 2,
                'company_id' => 1,
                'balance' => -45000.00,
                'debit' => 0.00,
                'credit' => 45000.00,
                'currency_id' => 1,
                'plan_id' => 1
            )
        );
    }
}

/**
 * نموذج بنود المحاسبة التحليلية
 */
class AccountAnalyticLine extends BaseModel {
    protected $table = 'account_analytic_line';
    
    protected $fillable = [
        'name', 'date', 'amount', 'unit_amount', 'account_id', 'partner_id',
        'user_id', 'tag_ids', 'company_id', 'currency_id', 'general_account_id',
        'move_id', 'ref', 'so_line', 'task_id', 'project_id', 'employee_id',
        'department_id', 'category'
    ];
    
    protected $casts = [
        'date' => 'date',
        'amount' => 'decimal',
        'unit_amount' => 'decimal',
        'account_id' => 'integer',
        'partner_id' => 'integer',
        'user_id' => 'integer',
        'company_id' => 'integer',
        'currency_id' => 'integer',
        'general_account_id' => 'integer',
        'move_id' => 'integer',
        'task_id' => 'integer',
        'project_id' => 'integer',
        'employee_id' => 'integer',
        'department_id' => 'integer'
    ];
    
    // فئات البنود التحليلية
    private $line_categories = array(
        'revenue' => 'إيرادات',
        'expense' => 'مصروفات',
        'timesheet' => 'ساعات العمل',
        'other' => 'أخرى'
    );
    
    // العلاقات
    public function analytic_account() {
        return $this->belongsTo('AccountAnalyticAccount', 'account_id');
    }
    
    public function partner() {
        return $this->belongsTo('ResPartner', 'partner_id');
    }
    
    public function user() {
        return $this->belongsTo('ResUsers', 'user_id');
    }
    
    public function general_account() {
        return $this->belongsTo('AccountAccount', 'general_account_id');
    }
    
    public function move() {
        return $this->belongsTo('AccountMove', 'move_id');
    }
    
    /**
     * إنشاء بند تحليلي من قيد محاسبي
     */
    public function create_from_move_line($move_line_id) {
        require_once 'AccountMoveLine.php';
        $move_line_model = new AccountMoveLine($this->db);
        
        $move_line = $move_line_model->read($move_line_id);
        if (!$move_line || !$move_line['analytic_account_id']) {
            return false;
        }
        
        $amount = $move_line['debit'] - $move_line['credit'];
        
        $analytic_data = array(
            'name' => $move_line['name'],
            'date' => $move_line['date'],
            'amount' => $amount,
            'account_id' => $move_line['analytic_account_id'],
            'partner_id' => $move_line['partner_id'],
            'user_id' => $_SESSION['user_id'] ?? 1,
            'company_id' => $move_line['company_id'],
            'currency_id' => $move_line['currency_id'],
            'general_account_id' => $move_line['account_id'],
            'move_id' => $move_line['move_id'],
            'ref' => $move_line['ref'],
            'category' => $amount > 0 ? 'revenue' : 'expense'
        );
        
        return $this->create($analytic_data);
    }
    
    /**
     * تجميع البنود حسب الفترة
     */
    public function group_by_period($account_id, $period = 'month', $date_from = null, $date_to = null) {
        $conditions = array('account_id' => $account_id);
        
        if ($date_from && $date_to) {
            $conditions['date'] = array('BETWEEN', array($date_from, $date_to));
        }
        
        $lines = $this->search($conditions, 'date ASC');
        $grouped = array();
        
        foreach ($lines as $line) {
            $date = new DateTime($line['date']);
            
            switch ($period) {
                case 'day':
                    $key = $date->format('Y-m-d');
                    break;
                case 'week':
                    $key = $date->format('Y-W');
                    break;
                case 'month':
                    $key = $date->format('Y-m');
                    break;
                case 'quarter':
                    $quarter = ceil($date->format('n') / 3);
                    $key = $date->format('Y') . '-Q' . $quarter;
                    break;
                case 'year':
                    $key = $date->format('Y');
                    break;
                default:
                    $key = $date->format('Y-m');
            }
            
            if (!isset($grouped[$key])) {
                $grouped[$key] = array(
                    'period' => $key,
                    'amount' => 0,
                    'unit_amount' => 0,
                    'count' => 0
                );
            }
            
            $grouped[$key]['amount'] += $line['amount'];
            $grouped[$key]['unit_amount'] += $line['unit_amount'];
            $grouped[$key]['count']++;
        }
        
        return array_values($grouped);
    }
    
    /**
     * الحصول على بيانات تجريبية
     */
    public function get_demo_data() {
        return array(
            // بنود مشروع تطوير الموقع
            array(
                'id' => 1,
                'name' => 'تطوير الواجهة الأمامية',
                'date' => '2024-01-15',
                'amount' => 15000.00,
                'unit_amount' => 40.0,
                'account_id' => 1,
                'partner_id' => 1,
                'user_id' => 1,
                'company_id' => 1,
                'currency_id' => 1,
                'general_account_id' => 41,
                'category' => 'revenue'
            ),
            array(
                'id' => 2,
                'name' => 'تكلفة المطورين',
                'date' => '2024-01-15',
                'amount' => -8000.00,
                'unit_amount' => 40.0,
                'account_id' => 1,
                'user_id' => 1,
                'company_id' => 1,
                'currency_id' => 1,
                'general_account_id' => 521,
                'category' => 'expense'
            ),
            
            // بنود مشروع التطبيق المحمول
            array(
                'id' => 3,
                'name' => 'تطوير التطبيق',
                'date' => '2024-02-01',
                'amount' => 20000.00,
                'unit_amount' => 50.0,
                'account_id' => 2,
                'partner_id' => 2,
                'user_id' => 1,
                'company_id' => 1,
                'currency_id' => 1,
                'general_account_id' => 41,
                'category' => 'revenue'
            ),
            array(
                'id' => 4,
                'name' => 'تكلفة التطوير',
                'date' => '2024-02-01',
                'amount' => -12000.00,
                'unit_amount' => 50.0,
                'account_id' => 2,
                'user_id' => 1,
                'company_id' => 1,
                'currency_id' => 1,
                'general_account_id' => 521,
                'category' => 'expense'
            ),
            
            // بنود قسم التسويق
            array(
                'id' => 5,
                'name' => 'حملة إعلانية',
                'date' => '2024-01-20',
                'amount' => -15000.00,
                'account_id' => 3,
                'user_id' => 1,
                'company_id' => 1,
                'currency_id' => 1,
                'general_account_id' => 524,
                'category' => 'expense'
            ),
            array(
                'id' => 6,
                'name' => 'مصروفات المبيعات',
                'date' => '2024-02-10',
                'amount' => -15000.00,
                'account_id' => 3,
                'user_id' => 1,
                'company_id' => 1,
                'currency_id' => 1,
                'general_account_id' => 525,
                'category' => 'expense'
            )
        );
    }
}

/**
 * نموذج مجموعات المحاسبة التحليلية
 */
class AccountAnalyticGroup extends BaseModel {
    protected $table = 'account_analytic_group';
    
    protected $fillable = [
        'name', 'description', 'parent_id', 'parent_path', 'company_id'
    ];
    
    protected $casts = [
        'parent_id' => 'integer',
        'company_id' => 'integer'
    ];
    
    // العلاقات
    public function parent() {
        return $this->belongsTo('AccountAnalyticGroup', 'parent_id');
    }
    
    public function children() {
        return $this->hasMany('AccountAnalyticGroup', 'parent_id');
    }
    
    public function analytic_accounts() {
        return $this->hasMany('AccountAnalyticAccount', 'group_id');
    }
    
    /**
     * الحصول على بيانات تجريبية
     */
    public function get_demo_data() {
        return array(
            array(
                'id' => 1,
                'name' => 'المشاريع',
                'description' => 'مجموعة حسابات المشاريع',
                'parent_id' => null,
                'parent_path' => '1/',
                'company_id' => 1
            ),
            array(
                'id' => 2,
                'name' => 'الأقسام',
                'description' => 'مجموعة حسابات الأقسام',
                'parent_id' => null,
                'parent_path' => '2/',
                'company_id' => 1
            ),
            array(
                'id' => 3,
                'name' => 'مشاريع التطوير',
                'description' => 'مشاريع تطوير البرمجيات',
                'parent_id' => 1,
                'parent_path' => '1/3/',
                'company_id' => 1
            ),
            array(
                'id' => 4,
                'name' => 'الأقسام الإدارية',
                'description' => 'الأقسام الإدارية والدعم',
                'parent_id' => 2,
                'parent_path' => '2/4/',
                'company_id' => 1
            )
        );
    }
}

/**
 * نموذج علامات المحاسبة التحليلية
 */
class AccountAnalyticTag extends BaseModel {
    protected $table = 'account_analytic_tag';
    
    protected $fillable = [
        'name', 'color', 'active', 'company_id'
    ];
    
    protected $casts = [
        'color' => 'integer',
        'active' => 'boolean',
        'company_id' => 'integer'
    ];
    
    /**
     * الحصول على بيانات تجريبية
     */
    public function get_demo_data() {
        return array(
            array(
                'id' => 1,
                'name' => 'عاجل',
                'color' => 1, // أحمر
                'active' => true,
                'company_id' => 1
            ),
            array(
                'id' => 2,
                'name' => 'مهم',
                'color' => 3, // أصفر
                'active' => true,
                'company_id' => 1
            ),
            array(
                'id' => 3,
                'name' => 'عادي',
                'color' => 5, // أزرق
                'active' => true,
                'company_id' => 1
            ),
            array(
                'id' => 4,
                'name' => 'مكتمل',
                'color' => 7, // أخضر
                'active' => true,
                'company_id' => 1
            )
        );
    }
}
?>
