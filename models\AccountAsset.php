<?php
/**
 * نموذج إدارة الأصول الثابتة بأسلوب Odoo
 * Account Asset Management Model - Odoo Style
 */

require_once 'BaseModel.php';

class AccountAsset extends BaseModel {
    protected $table = 'account_asset';
    
    protected $fillable = [
        'name', 'code', 'category_id', 'original_value', 'salvage_value',
        'depreciation_method', 'depreciation_number_years', 'depreciation_number_months',
        'acquisition_date', 'first_depreciation_date', 'last_depreciation_date',
        'state', 'asset_account_id', 'depreciation_account_id', 'expense_account_id',
        'journal_id', 'company_id', 'currency_id', 'partner_id', 'invoice_id',
        'note', 'active'
    ];
    
    protected $casts = [
        'category_id' => 'integer',
        'original_value' => 'decimal',
        'salvage_value' => 'decimal',
        'depreciation_number_years' => 'integer',
        'depreciation_number_months' => 'integer',
        'acquisition_date' => 'date',
        'first_depreciation_date' => 'date',
        'last_depreciation_date' => 'date',
        'asset_account_id' => 'integer',
        'depreciation_account_id' => 'integer',
        'expense_account_id' => 'integer',
        'journal_id' => 'integer',
        'company_id' => 'integer',
        'currency_id' => 'integer',
        'partner_id' => 'integer',
        'invoice_id' => 'integer',
        'active' => 'boolean'
    ];
    
    // طرق الاستهلاك
    private $depreciation_methods = array(
        'linear' => 'القسط الثابت',
        'degressive' => 'القسط المتناقص',
        'accelerated' => 'الاستهلاك المعجل',
        'manual' => 'يدوي'
    );
    
    // حالات الأصل
    private $asset_states = array(
        'draft' => 'مسودة',
        'open' => 'قيد التشغيل',
        'close' => 'مستهلك بالكامل',
        'cancelled' => 'ملغي'
    );
    
    // العلاقات
    public function category() {
        return $this->belongsTo('AccountAssetCategory', 'category_id');
    }
    
    public function depreciation_lines() {
        return $this->hasMany('AccountAssetDepreciationLine', 'asset_id');
    }
    
    public function asset_account() {
        return $this->belongsTo('AccountAccount', 'asset_account_id');
    }
    
    public function depreciation_account() {
        return $this->belongsTo('AccountAccount', 'depreciation_account_id');
    }
    
    public function expense_account() {
        return $this->belongsTo('AccountAccount', 'expense_account_id');
    }
    
    public function company() {
        return $this->belongsTo('ResCompany', 'company_id');
    }
    
    public function partner() {
        return $this->belongsTo('ResPartner', 'partner_id');
    }
    
    /**
     * حساب خطوط الاستهلاك
     */
    public function compute_depreciation_board($asset_id) {
        $asset = $this->read($asset_id);
        if (!$asset) {
            throw new Exception('الأصل غير موجود');
        }
        
        $lines = array();
        $depreciation_value = $asset['original_value'] - $asset['salvage_value'];
        
        switch ($asset['depreciation_method']) {
            case 'linear':
                $lines = $this->compute_linear_depreciation($asset, $depreciation_value);
                break;
            case 'degressive':
                $lines = $this->compute_degressive_depreciation($asset, $depreciation_value);
                break;
            case 'accelerated':
                $lines = $this->compute_accelerated_depreciation($asset, $depreciation_value);
                break;
            default:
                throw new Exception('طريقة الاستهلاك غير مدعومة');
        }
        
        // حفظ خطوط الاستهلاك
        require_once 'AccountAssetDepreciationLine.php';
        $line_model = new AccountAssetDepreciationLine($this->db);
        
        // حذف الخطوط السابقة
        $line_model->delete_where(array('asset_id' => $asset_id));
        
        // إنشاء خطوط جديدة
        foreach ($lines as $line) {
            $line['asset_id'] = $asset_id;
            $line_model->create($line);
        }
        
        return $lines;
    }
    
    /**
     * حساب الاستهلاك بالقسط الثابت
     */
    private function compute_linear_depreciation($asset, $depreciation_value) {
        $lines = array();
        $monthly_depreciation = $depreciation_value / $asset['depreciation_number_months'];
        
        $current_date = new DateTime($asset['first_depreciation_date']);
        $accumulated_depreciation = 0;
        
        for ($i = 1; $i <= $asset['depreciation_number_months']; $i++) {
            $accumulated_depreciation += $monthly_depreciation;
            $remaining_value = $asset['original_value'] - $accumulated_depreciation;
            
            $lines[] = array(
                'sequence' => $i,
                'depreciation_date' => $current_date->format('Y-m-d'),
                'depreciation_amount' => $monthly_depreciation,
                'accumulated_depreciation' => $accumulated_depreciation,
                'remaining_value' => $remaining_value,
                'move_posted' => false
            );
            
            $current_date->add(new DateInterval('P1M'));
        }
        
        return $lines;
    }
    
    /**
     * حساب الاستهلاك بالقسط المتناقص
     */
    private function compute_degressive_depreciation($asset, $depreciation_value) {
        $lines = array();
        $rate = 2 / $asset['depreciation_number_years']; // معدل الاستهلاك المضاعف
        
        $current_date = new DateTime($asset['first_depreciation_date']);
        $book_value = $asset['original_value'];
        $accumulated_depreciation = 0;
        
        for ($i = 1; $i <= $asset['depreciation_number_months']; $i++) {
            $monthly_depreciation = ($book_value * $rate) / 12;
            
            // التأكد من عدم تجاوز القيمة المتبقية للقيمة الإنقاذية
            if ($accumulated_depreciation + $monthly_depreciation > $depreciation_value) {
                $monthly_depreciation = $depreciation_value - $accumulated_depreciation;
            }
            
            $accumulated_depreciation += $monthly_depreciation;
            $book_value = $asset['original_value'] - $accumulated_depreciation;
            
            $lines[] = array(
                'sequence' => $i,
                'depreciation_date' => $current_date->format('Y-m-d'),
                'depreciation_amount' => $monthly_depreciation,
                'accumulated_depreciation' => $accumulated_depreciation,
                'remaining_value' => $book_value,
                'move_posted' => false
            );
            
            $current_date->add(new DateInterval('P1M'));
            
            // إيقاف الحساب إذا وصلنا للقيمة الإنقاذية
            if ($book_value <= $asset['salvage_value']) {
                break;
            }
        }
        
        return $lines;
    }
    
    /**
     * حساب الاستهلاك المعجل
     */
    private function compute_accelerated_depreciation($asset, $depreciation_value) {
        // تطبيق طريقة مجموع سنوات الاستخدام
        $lines = array();
        $total_years = $asset['depreciation_number_years'];
        $sum_of_years = ($total_years * ($total_years + 1)) / 2;
        
        $current_date = new DateTime($asset['first_depreciation_date']);
        $accumulated_depreciation = 0;
        
        for ($year = 1; $year <= $total_years; $year++) {
            $year_fraction = ($total_years - $year + 1) / $sum_of_years;
            $yearly_depreciation = $depreciation_value * $year_fraction;
            $monthly_depreciation = $yearly_depreciation / 12;
            
            for ($month = 1; $month <= 12; $month++) {
                $accumulated_depreciation += $monthly_depreciation;
                $remaining_value = $asset['original_value'] - $accumulated_depreciation;
                
                $sequence = (($year - 1) * 12) + $month;
                
                $lines[] = array(
                    'sequence' => $sequence,
                    'depreciation_date' => $current_date->format('Y-m-d'),
                    'depreciation_amount' => $monthly_depreciation,
                    'accumulated_depreciation' => $accumulated_depreciation,
                    'remaining_value' => $remaining_value,
                    'move_posted' => false
                );
                
                $current_date->add(new DateInterval('P1M'));
                
                if ($sequence >= $asset['depreciation_number_months']) {
                    break 2;
                }
            }
        }
        
        return $lines;
    }
    
    /**
     * ترحيل استهلاك شهري
     */
    public function post_depreciation_line($line_id) {
        require_once 'AccountAssetDepreciationLine.php';
        $line_model = new AccountAssetDepreciationLine($this->db);
        
        $line = $line_model->read($line_id);
        if (!$line || $line['move_posted']) {
            throw new Exception('خط الاستهلاك غير صالح أو مرحل مسبقاً');
        }
        
        $asset = $this->read($line['asset_id']);
        if (!$asset) {
            throw new Exception('الأصل غير موجود');
        }
        
        // إنشاء قيد الاستهلاك
        require_once 'AccountMove.php';
        $move_model = new AccountMove($this->db);
        
        $move_data = array(
            'journal_id' => $asset['journal_id'],
            'date' => $line['depreciation_date'],
            'ref' => "استهلاك {$asset['name']} - {$line['depreciation_date']}",
            'state' => 'draft',
            'company_id' => $asset['company_id']
        );
        
        $move_id = $move_model->create($move_data);
        
        // إنشاء بنود القيد
        require_once 'AccountMoveLine.php';
        $move_line_model = new AccountMoveLine($this->db);
        
        // بند مصروف الاستهلاك (مدين)
        $debit_line = array(
            'move_id' => $move_id,
            'account_id' => $asset['expense_account_id'],
            'name' => "مصروف استهلاك {$asset['name']}",
            'debit' => $line['depreciation_amount'],
            'credit' => 0,
            'date' => $line['depreciation_date']
        );
        $move_line_model->create($debit_line);
        
        // بند مجمع الاستهلاك (دائن)
        $credit_line = array(
            'move_id' => $move_id,
            'account_id' => $asset['depreciation_account_id'],
            'name' => "مجمع استهلاك {$asset['name']}",
            'debit' => 0,
            'credit' => $line['depreciation_amount'],
            'date' => $line['depreciation_date']
        );
        $move_line_model->create($credit_line);
        
        // ترحيل القيد
        $move_model->post_move($move_id);
        
        // تحديث خط الاستهلاك
        $line_model->update($line_id, array(
            'move_id' => $move_id,
            'move_posted' => true
        ));
        
        return $move_id;
    }
    
    /**
     * بيع أو التخلص من الأصل
     */
    public function dispose_asset($asset_id, $disposal_data) {
        $asset = $this->read($asset_id);
        if (!$asset) {
            throw new Exception('الأصل غير موجود');
        }
        
        // حساب القيمة الدفترية الحالية
        $book_value = $this->get_current_book_value($asset_id);
        
        // إنشاء قيد التخلص
        require_once 'AccountMove.php';
        $move_model = new AccountMove($this->db);
        
        $move_data = array(
            'journal_id' => $disposal_data['journal_id'],
            'date' => $disposal_data['disposal_date'],
            'ref' => "التخلص من {$asset['name']}",
            'state' => 'draft',
            'company_id' => $asset['company_id']
        );
        
        $move_id = $move_model->create($move_data);
        
        // إنشاء بنود القيد
        require_once 'AccountMoveLine.php';
        $move_line_model = new AccountMoveLine($this->db);
        
        // إزالة الأصل من الدفاتر
        $lines = array(
            // إقفال مجمع الاستهلاك
            array(
                'account_id' => $asset['depreciation_account_id'],
                'name' => "إقفال مجمع استهلاك {$asset['name']}",
                'debit' => $asset['original_value'] - $book_value,
                'credit' => 0
            ),
            // إقفال حساب الأصل
            array(
                'account_id' => $asset['asset_account_id'],
                'name' => "إقفال {$asset['name']}",
                'debit' => 0,
                'credit' => $asset['original_value']
            )
        );
        
        // إضافة المتحصلات إن وجدت
        if (isset($disposal_data['proceeds']) && $disposal_data['proceeds'] > 0) {
            $lines[] = array(
                'account_id' => $disposal_data['proceeds_account_id'],
                'name' => "متحصلات بيع {$asset['name']}",
                'debit' => $disposal_data['proceeds'],
                'credit' => 0
            );
        }
        
        // حساب الربح أو الخسارة
        $gain_loss = ($disposal_data['proceeds'] ?? 0) - $book_value;
        
        if ($gain_loss != 0) {
            $lines[] = array(
                'account_id' => $disposal_data['gain_loss_account_id'],
                'name' => ($gain_loss > 0 ? "ربح" : "خسارة") . " بيع {$asset['name']}",
                'debit' => $gain_loss < 0 ? abs($gain_loss) : 0,
                'credit' => $gain_loss > 0 ? $gain_loss : 0
            );
        }
        
        // إنشاء بنود القيد
        foreach ($lines as $line) {
            $line['move_id'] = $move_id;
            $line['date'] = $disposal_data['disposal_date'];
            $move_line_model->create($line);
        }
        
        // ترحيل القيد
        $move_model->post_move($move_id);
        
        // تحديث حالة الأصل
        $this->update($asset_id, array(
            'state' => 'close',
            'disposal_date' => $disposal_data['disposal_date'],
            'disposal_move_id' => $move_id
        ));
        
        return $move_id;
    }
    
    /**
     * الحصول على القيمة الدفترية الحالية
     */
    public function get_current_book_value($asset_id) {
        require_once 'AccountAssetDepreciationLine.php';
        $line_model = new AccountAssetDepreciationLine($this->db);
        
        $posted_lines = $line_model->search(array(
            'asset_id' => $asset_id,
            'move_posted' => true
        ));
        
        $total_depreciation = 0;
        foreach ($posted_lines as $line) {
            $total_depreciation += $line['depreciation_amount'];
        }
        
        $asset = $this->read($asset_id);
        return $asset['original_value'] - $total_depreciation;
    }
    
    /**
     * الحصول على بيانات تجريبية
     */
    public function get_demo_data() {
        return array(
            array(
                'id' => 1,
                'name' => 'سيارة الشركة - تويوتا كامري 2023',
                'code' => 'CAR001',
                'category_id' => 1,
                'original_value' => 120000.00,
                'salvage_value' => 20000.00,
                'depreciation_method' => 'linear',
                'depreciation_number_years' => 5,
                'depreciation_number_months' => 60,
                'acquisition_date' => '2024-01-01',
                'first_depreciation_date' => '2024-01-31',
                'state' => 'open',
                'asset_account_id' => 15,
                'depreciation_account_id' => 16,
                'expense_account_id' => 25,
                'company_id' => 1,
                'active' => true
            ),
            array(
                'id' => 2,
                'name' => 'أجهزة كمبيوتر مكتبية',
                'code' => 'COMP001',
                'category_id' => 2,
                'original_value' => 50000.00,
                'salvage_value' => 5000.00,
                'depreciation_method' => 'degressive',
                'depreciation_number_years' => 3,
                'depreciation_number_months' => 36,
                'acquisition_date' => '2024-01-15',
                'first_depreciation_date' => '2024-01-31',
                'state' => 'open',
                'asset_account_id' => 17,
                'depreciation_account_id' => 18,
                'expense_account_id' => 26,
                'company_id' => 1,
                'active' => true
            )
        );
    }
}
