<?php
/**
 * نموذج خطوط استهلاك الأصول الثابتة بأسلوب Odoo
 * Account Asset Depreciation Line Model - Odoo Style
 */

require_once 'BaseModel.php';

class AccountAssetDepreciationLine extends BaseModel {
    protected $table = 'account_asset_depreciation_line';
    
    protected $fillable = [
        'asset_id', 'sequence', 'depreciation_date', 'depreciation_amount',
        'accumulated_depreciation', 'remaining_value', 'move_id', 'move_posted'
    ];
    
    protected $casts = [
        'asset_id' => 'integer',
        'sequence' => 'integer',
        'depreciation_date' => 'date',
        'depreciation_amount' => 'decimal',
        'accumulated_depreciation' => 'decimal',
        'remaining_value' => 'decimal',
        'move_id' => 'integer',
        'move_posted' => 'boolean'
    ];
    
    // العلاقات
    public function asset() {
        return $this->belongsTo('AccountAsset', 'asset_id');
    }
    
    public function move() {
        return $this->belongsTo('AccountMove', 'move_id');
    }
    
    /**
     * الحصول على خطوط الاستهلاك للأصل
     */
    public function get_asset_depreciation_lines($asset_id) {
        return $this->search(array('asset_id' => $asset_id), 'sequence ASC');
    }
    
    /**
     * الحصول على خطوط الاستهلاك المستحقة
     */
    public function get_due_depreciation_lines($date = null) {
        if (!$date) {
            $date = date('Y-m-d');
        }
        
        return $this->search(array(
            'depreciation_date' => array('<=', $date),
            'move_posted' => false
        ), 'depreciation_date ASC');
    }
    
    /**
     * ترحيل خطوط الاستهلاك المستحقة
     */
    public function post_due_depreciation_lines($date = null) {
        $due_lines = $this->get_due_depreciation_lines($date);
        $posted_count = 0;
        
        require_once 'AccountAsset.php';
        $asset_model = new AccountAsset($this->db);
        
        foreach ($due_lines as $line) {
            try {
                $asset_model->post_depreciation_line($line['id']);
                $posted_count++;
            } catch (Exception $e) {
                error_log("خطأ في ترحيل خط الاستهلاك {$line['id']}: " . $e->getMessage());
            }
        }
        
        return $posted_count;
    }
    
    /**
     * الحصول على بيانات تجريبية
     */
    public function get_demo_data() {
        return array(
            // خطوط استهلاك السيارة
            array(
                'id' => 1,
                'asset_id' => 1,
                'sequence' => 1,
                'depreciation_date' => '2024-01-31',
                'depreciation_amount' => 1666.67,
                'accumulated_depreciation' => 1666.67,
                'remaining_value' => 118333.33,
                'move_posted' => true
            ),
            array(
                'id' => 2,
                'asset_id' => 1,
                'sequence' => 2,
                'depreciation_date' => '2024-02-29',
                'depreciation_amount' => 1666.67,
                'accumulated_depreciation' => 3333.34,
                'remaining_value' => 116666.66,
                'move_posted' => true
            ),
            array(
                'id' => 3,
                'asset_id' => 1,
                'sequence' => 3,
                'depreciation_date' => '2024-03-31',
                'depreciation_amount' => 1666.67,
                'accumulated_depreciation' => 5000.01,
                'remaining_value' => 114999.99,
                'move_posted' => false
            ),
            
            // خطوط استهلاك الكمبيوتر
            array(
                'id' => 4,
                'asset_id' => 2,
                'sequence' => 1,
                'depreciation_date' => '2024-01-31',
                'depreciation_amount' => 2500.00,
                'accumulated_depreciation' => 2500.00,
                'remaining_value' => 47500.00,
                'move_posted' => true
            ),
            array(
                'id' => 5,
                'asset_id' => 2,
                'sequence' => 2,
                'depreciation_date' => '2024-02-29',
                'depreciation_amount' => 2291.67,
                'accumulated_depreciation' => 4791.67,
                'remaining_value' => 45208.33,
                'move_posted' => false
            )
        );
    }
}

/**
 * نموذج فئات الأصول الثابتة
 */
class AccountAssetCategory extends BaseModel {
    protected $table = 'account_asset_category';
    
    protected $fillable = [
        'name', 'code', 'depreciation_method', 'depreciation_number_years',
        'asset_account_id', 'depreciation_account_id', 'expense_account_id',
        'journal_id', 'company_id', 'active'
    ];
    
    protected $casts = [
        'depreciation_number_years' => 'integer',
        'asset_account_id' => 'integer',
        'depreciation_account_id' => 'integer',
        'expense_account_id' => 'integer',
        'journal_id' => 'integer',
        'company_id' => 'integer',
        'active' => 'boolean'
    ];
    
    // العلاقات
    public function assets() {
        return $this->hasMany('AccountAsset', 'category_id');
    }
    
    public function asset_account() {
        return $this->belongsTo('AccountAccount', 'asset_account_id');
    }
    
    public function depreciation_account() {
        return $this->belongsTo('AccountAccount', 'depreciation_account_id');
    }
    
    public function expense_account() {
        return $this->belongsTo('AccountAccount', 'expense_account_id');
    }
    
    /**
     * الحصول على بيانات تجريبية
     */
    public function get_demo_data() {
        return array(
            array(
                'id' => 1,
                'name' => 'المركبات',
                'code' => 'VEH',
                'depreciation_method' => 'linear',
                'depreciation_number_years' => 5,
                'asset_account_id' => 15,
                'depreciation_account_id' => 16,
                'expense_account_id' => 25,
                'journal_id' => 7,
                'company_id' => 1,
                'active' => true
            ),
            array(
                'id' => 2,
                'name' => 'أجهزة الكمبيوتر',
                'code' => 'COMP',
                'depreciation_method' => 'degressive',
                'depreciation_number_years' => 3,
                'asset_account_id' => 17,
                'depreciation_account_id' => 18,
                'expense_account_id' => 26,
                'journal_id' => 7,
                'company_id' => 1,
                'active' => true
            ),
            array(
                'id' => 3,
                'name' => 'الأثاث والمعدات المكتبية',
                'code' => 'FURN',
                'depreciation_method' => 'linear',
                'depreciation_number_years' => 7,
                'asset_account_id' => 19,
                'depreciation_account_id' => 20,
                'expense_account_id' => 27,
                'journal_id' => 7,
                'company_id' => 1,
                'active' => true
            ),
            array(
                'id' => 4,
                'name' => 'المباني والإنشاءات',
                'code' => 'BUILD',
                'depreciation_method' => 'linear',
                'depreciation_number_years' => 25,
                'asset_account_id' => 21,
                'depreciation_account_id' => 22,
                'expense_account_id' => 28,
                'journal_id' => 7,
                'company_id' => 1,
                'active' => true
            ),
            array(
                'id' => 5,
                'name' => 'الآلات والمعدات',
                'code' => 'MACH',
                'depreciation_method' => 'linear',
                'depreciation_number_years' => 10,
                'asset_account_id' => 23,
                'depreciation_account_id' => 24,
                'expense_account_id' => 29,
                'journal_id' => 7,
                'company_id' => 1,
                'active' => true
            )
        );
    }
}
?>
