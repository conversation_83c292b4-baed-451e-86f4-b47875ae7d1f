<?php
/**
 * نموذج إدارة الميزانيات بأسلوب Odoo
 * Account Budget Management Model - Odoo Style
 */

require_once 'BaseModel.php';

class AccountBudget extends BaseModel {
    protected $table = 'crossovered_budget';
    
    protected $fillable = [
        'name', 'code', 'date_from', 'date_to', 'state', 'user_id',
        'company_id', 'creating_user_id', 'validating_user_id'
    ];
    
    protected $casts = [
        'date_from' => 'date',
        'date_to' => 'date',
        'user_id' => 'integer',
        'company_id' => 'integer',
        'creating_user_id' => 'integer',
        'validating_user_id' => 'integer'
    ];
    
    // حالات الميزانية
    private $budget_states = array(
        'draft' => array('name' => 'مسودة', 'color' => 'secondary'),
        'confirm' => array('name' => 'مؤكدة', 'color' => 'info'),
        'validate' => array('name' => 'معتمدة', 'color' => 'success'),
        'cancel' => array('name' => 'ملغية', 'color' => 'danger'),
        'done' => array('name' => 'منتهية', 'color' => 'dark')
    );
    
    // العلاقات
    public function budget_lines() {
        return $this->hasMany('CrossoveredBudgetLines', 'crossovered_budget_id');
    }
    
    public function company() {
        return $this->belongsTo('ResCompany', 'company_id');
    }
    
    public function user() {
        return $this->belongsTo('ResUsers', 'user_id');
    }
    
    /**
     * تأكيد الميزانية
     */
    public function confirm_budget($budget_id) {
        $budget = $this->read($budget_id);
        if (!$budget || $budget['state'] !== 'draft') {
            throw new Exception('لا يمكن تأكيد هذه الميزانية');
        }
        
        return $this->update($budget_id, array(
            'state' => 'confirm',
            'confirming_user_id' => $_SESSION['user_id'] ?? 1
        ));
    }
    
    /**
     * اعتماد الميزانية
     */
    public function validate_budget($budget_id) {
        $budget = $this->read($budget_id);
        if (!$budget || $budget['state'] !== 'confirm') {
            throw new Exception('لا يمكن اعتماد هذه الميزانية');
        }
        
        return $this->update($budget_id, array(
            'state' => 'validate',
            'validating_user_id' => $_SESSION['user_id'] ?? 1
        ));
    }
    
    /**
     * إلغاء الميزانية
     */
    public function cancel_budget($budget_id) {
        return $this->update($budget_id, array('state' => 'cancel'));
    }
    
    /**
     * إنهاء الميزانية
     */
    public function done_budget($budget_id) {
        $budget = $this->read($budget_id);
        if (!$budget || $budget['state'] !== 'validate') {
            throw new Exception('لا يمكن إنهاء هذه الميزانية');
        }
        
        return $this->update($budget_id, array('state' => 'done'));
    }
    
    /**
     * حساب إجمالي الميزانية المخططة
     */
    public function compute_planned_amount($budget_id) {
        require_once 'CrossoveredBudgetLines.php';
        $lines_model = new CrossoveredBudgetLines($this->db);
        
        $lines = $lines_model->search(array('crossovered_budget_id' => $budget_id));
        $total = 0;
        
        foreach ($lines as $line) {
            $total += $line['planned_amount'];
        }
        
        return $total;
    }
    
    /**
     * حساب إجمالي المبلغ الفعلي
     */
    public function compute_practical_amount($budget_id) {
        require_once 'CrossoveredBudgetLines.php';
        $lines_model = new CrossoveredBudgetLines($this->db);
        
        $lines = $lines_model->search(array('crossovered_budget_id' => $budget_id));
        $total = 0;
        
        foreach ($lines as $line) {
            $total += $line['practical_amount'];
        }
        
        return $total;
    }
    
    /**
     * تحليل أداء الميزانية
     */
    public function analyze_budget_performance($budget_id) {
        $planned = $this->compute_planned_amount($budget_id);
        $practical = $this->compute_practical_amount($budget_id);
        
        $variance = $practical - $planned;
        $variance_percentage = $planned > 0 ? ($variance / $planned) * 100 : 0;
        
        return array(
            'planned_amount' => $planned,
            'practical_amount' => $practical,
            'variance' => $variance,
            'variance_percentage' => $variance_percentage,
            'performance_status' => $this->get_performance_status($variance_percentage)
        );
    }
    
    /**
     * تحديد حالة الأداء
     */
    private function get_performance_status($variance_percentage) {
        if ($variance_percentage <= -10) {
            return array('status' => 'excellent', 'color' => 'success', 'text' => 'ممتاز');
        } elseif ($variance_percentage <= 0) {
            return array('status' => 'good', 'color' => 'info', 'text' => 'جيد');
        } elseif ($variance_percentage <= 10) {
            return array('status' => 'acceptable', 'color' => 'warning', 'text' => 'مقبول');
        } else {
            return array('status' => 'poor', 'color' => 'danger', 'text' => 'ضعيف');
        }
    }
    
    /**
     * الحصول على بيانات تجريبية
     */
    public function get_demo_data() {
        return array(
            array(
                'id' => 1,
                'name' => 'ميزانية 2024',
                'code' => 'BUD2024',
                'date_from' => '2024-01-01',
                'date_to' => '2024-12-31',
                'state' => 'validate',
                'user_id' => 1,
                'company_id' => 1,
                'creating_user_id' => 1,
                'validating_user_id' => 1
            ),
            array(
                'id' => 2,
                'name' => 'ميزانية الربع الأول 2024',
                'code' => 'BUD2024Q1',
                'date_from' => '2024-01-01',
                'date_to' => '2024-03-31',
                'state' => 'done',
                'user_id' => 1,
                'company_id' => 1,
                'creating_user_id' => 1,
                'validating_user_id' => 1
            ),
            array(
                'id' => 3,
                'name' => 'ميزانية الربع الثاني 2024',
                'code' => 'BUD2024Q2',
                'date_from' => '2024-04-01',
                'date_to' => '2024-06-30',
                'state' => 'validate',
                'user_id' => 1,
                'company_id' => 1,
                'creating_user_id' => 1,
                'validating_user_id' => 1
            )
        );
    }
}

/**
 * نموذج بنود الميزانية
 */
class CrossoveredBudgetLines extends BaseModel {
    protected $table = 'crossovered_budget_lines';
    
    protected $fillable = [
        'crossovered_budget_id', 'analytic_account_id', 'general_budget_id',
        'date_from', 'date_to', 'planned_amount', 'practical_amount',
        'theoritical_amount', 'percentage', 'company_id'
    ];
    
    protected $casts = [
        'crossovered_budget_id' => 'integer',
        'analytic_account_id' => 'integer',
        'general_budget_id' => 'integer',
        'date_from' => 'date',
        'date_to' => 'date',
        'planned_amount' => 'decimal',
        'practical_amount' => 'decimal',
        'theoritical_amount' => 'decimal',
        'percentage' => 'decimal',
        'company_id' => 'integer'
    ];
    
    // العلاقات
    public function budget() {
        return $this->belongsTo('AccountBudget', 'crossovered_budget_id');
    }
    
    public function analytic_account() {
        return $this->belongsTo('AccountAnalyticAccount', 'analytic_account_id');
    }
    
    public function budget_post() {
        return $this->belongsTo('AccountBudgetPost', 'general_budget_id');
    }
    
    /**
     * حساب المبلغ الفعلي من القيود المحاسبية
     */
    public function compute_practical_amount($line_id) {
        $line = $this->read($line_id);
        if (!$line) {
            return 0;
        }
        
        // الحصول على الحسابات المرتبطة بمنصب الميزانية
        require_once 'AccountBudgetPost.php';
        $budget_post_model = new AccountBudgetPost($this->db);
        $accounts = $budget_post_model->get_budget_post_accounts($line['general_budget_id']);
        
        if (empty($accounts)) {
            return 0;
        }
        
        // حساب المبلغ من بنود القيود
        require_once 'AccountMoveLine.php';
        $move_line_model = new AccountMoveLine($this->db);
        
        $account_ids = array_column($accounts, 'id');
        $conditions = array(
            'account_id' => array('IN', $account_ids),
            'date' => array('BETWEEN', array($line['date_from'], $line['date_to']))
        );
        
        if ($line['analytic_account_id']) {
            $conditions['analytic_account_id'] = $line['analytic_account_id'];
        }
        
        $move_lines = $move_line_model->search($conditions);
        
        $total = 0;
        foreach ($move_lines as $move_line) {
            $total += ($move_line['debit'] - $move_line['credit']);
        }
        
        // تحديث المبلغ الفعلي
        $this->update($line_id, array('practical_amount' => $total));
        
        return $total;
    }
    
    /**
     * حساب المبلغ النظري (المتوقع حتى تاريخ اليوم)
     */
    public function compute_theoritical_amount($line_id) {
        $line = $this->read($line_id);
        if (!$line) {
            return 0;
        }
        
        $start_date = new DateTime($line['date_from']);
        $end_date = new DateTime($line['date_to']);
        $current_date = new DateTime();
        
        // إذا لم تبدأ الفترة بعد
        if ($current_date < $start_date) {
            return 0;
        }
        
        // إذا انتهت الفترة
        if ($current_date > $end_date) {
            return $line['planned_amount'];
        }
        
        // حساب النسبة المنقضية من الفترة
        $total_days = $start_date->diff($end_date)->days + 1;
        $elapsed_days = $start_date->diff($current_date)->days + 1;
        $percentage = $elapsed_days / $total_days;
        
        $theoritical_amount = $line['planned_amount'] * $percentage;
        
        // تحديث المبلغ النظري
        $this->update($line_id, array(
            'theoritical_amount' => $theoritical_amount,
            'percentage' => $percentage * 100
        ));
        
        return $theoritical_amount;
    }
    
    /**
     * الحصول على بيانات تجريبية
     */
    public function get_demo_data() {
        return array(
            // بنود ميزانية 2024
            array(
                'id' => 1,
                'crossovered_budget_id' => 1,
                'analytic_account_id' => 1,
                'general_budget_id' => 1,
                'date_from' => '2024-01-01',
                'date_to' => '2024-12-31',
                'planned_amount' => 500000.00,
                'practical_amount' => 125000.00,
                'theoritical_amount' => 125000.00,
                'percentage' => 25.0,
                'company_id' => 1
            ),
            array(
                'id' => 2,
                'crossovered_budget_id' => 1,
                'analytic_account_id' => 2,
                'general_budget_id' => 2,
                'date_from' => '2024-01-01',
                'date_to' => '2024-12-31',
                'planned_amount' => 300000.00,
                'practical_amount' => 80000.00,
                'theoritical_amount' => 75000.00,
                'percentage' => 25.0,
                'company_id' => 1
            ),
            
            // بنود ميزانية الربع الأول
            array(
                'id' => 3,
                'crossovered_budget_id' => 2,
                'analytic_account_id' => 1,
                'general_budget_id' => 1,
                'date_from' => '2024-01-01',
                'date_to' => '2024-03-31',
                'planned_amount' => 125000.00,
                'practical_amount' => 125000.00,
                'theoritical_amount' => 125000.00,
                'percentage' => 100.0,
                'company_id' => 1
            ),
            array(
                'id' => 4,
                'crossovered_budget_id' => 2,
                'analytic_account_id' => 2,
                'general_budget_id' => 2,
                'date_from' => '2024-01-01',
                'date_to' => '2024-03-31',
                'planned_amount' => 75000.00,
                'practical_amount' => 80000.00,
                'theoritical_amount' => 75000.00,
                'percentage' => 100.0,
                'company_id' => 1
            )
        );
    }
}

/**
 * نموذج مناصب الميزانية
 */
class AccountBudgetPost extends BaseModel {
    protected $table = 'account_budget_post';
    
    protected $fillable = [
        'name', 'code', 'company_id'
    ];
    
    protected $casts = [
        'company_id' => 'integer'
    ];
    
    // العلاقات
    public function budget_lines() {
        return $this->hasMany('CrossoveredBudgetLines', 'general_budget_id');
    }
    
    public function accounts() {
        return $this->belongsToMany('AccountAccount', 'account_budget_rel', 'budget_id', 'account_id');
    }
    
    /**
     * الحصول على حسابات منصب الميزانية
     */
    public function get_budget_post_accounts($budget_post_id) {
        // في التطبيق الحقيقي، سيتم الحصول على الحسابات من جدول العلاقة
        // هنا نعيد بيانات تجريبية
        $demo_accounts = array(
            1 => array( // مصروفات التشغيل
                array('id' => 25, 'code' => '521', 'name' => 'مصروفات الرواتب'),
                array('id' => 26, 'code' => '522', 'name' => 'مصروفات الإيجار'),
                array('id' => 27, 'code' => '523', 'name' => 'مصروفات الاستهلاك')
            ),
            2 => array( // مصروفات التسويق
                array('id' => 28, 'code' => '524', 'name' => 'مصروفات الإعلان'),
                array('id' => 29, 'code' => '525', 'name' => 'مصروفات المبيعات')
            )
        );
        
        return $demo_accounts[$budget_post_id] ?? array();
    }
    
    /**
     * الحصول على بيانات تجريبية
     */
    public function get_demo_data() {
        return array(
            array(
                'id' => 1,
                'name' => 'مصروفات التشغيل',
                'code' => 'OPE',
                'company_id' => 1
            ),
            array(
                'id' => 2,
                'name' => 'مصروفات التسويق',
                'code' => 'MKT',
                'company_id' => 1
            ),
            array(
                'id' => 3,
                'name' => 'مصروفات البحث والتطوير',
                'code' => 'RND',
                'company_id' => 1
            ),
            array(
                'id' => 4,
                'name' => 'الاستثمارات الرأسمالية',
                'code' => 'CAP',
                'company_id' => 1
            )
        );
    }
}
?>
