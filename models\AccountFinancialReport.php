<?php
/**
 * نموذج التقارير المالية بأسلوب Odoo
 * Financial Reports Model - Odoo Style
 */

require_once 'BaseModel.php';

class AccountFinancialReport extends BaseModel {
    protected $table = 'account_financial_report';
    
    protected $fillable = [
        'name', 'parent_id', 'sequence', 'level', 'type', 'account_ids',
        'account_type_ids', 'sign', 'display_detail', 'style_overwrite', 'active'
    ];
    
    protected $casts = [
        'parent_id' => 'integer',
        'sequence' => 'integer',
        'level' => 'integer',
        'active' => 'boolean'
    ];
    
    // العلاقات
    public function parent() {
        return $this->belongsTo('AccountFinancialReport', 'parent_id');
    }
    
    public function children() {
        return $this->hasMany('AccountFinancialReport', 'parent_id');
    }
    
    // الدوال المساعدة
    
    /**
     * الحصول على التقارير الرئيسية
     */
    public function get_root_reports() {
        return $this->search_read(
            array(array('parent_id', '=', null)),
            null,
            array('order' => 'sequence ASC')
        );
    }
    
    /**
     * الحصول على التقارير الفرعية
     */
    public function get_child_reports($parent_id) {
        return $this->search_read(
            array(array('parent_id', '=', $parent_id)),
            null,
            array('order' => 'sequence ASC')
        );
    }
    
    /**
     * حساب قيمة التقرير
     */
    public function compute_report_value($report_id, $date_from = null, $date_to = null) {
        $report = $this->read($report_id);
        if (!$report) {
            return 0;
        }
        
        $value = 0;
        
        switch ($report['type']) {
            case 'accounts':
                // حساب من حسابات محددة
                if ($report['account_ids']) {
                    $account_ids = explode(',', $report['account_ids']);
                    require_once 'AccountAccount.php';
                    $account_model = new AccountAccount($this->db);
                    
                    foreach ($account_ids as $account_id) {
                        $balance = $account_model->compute_balance($account_id, $date_from, $date_to);
                        $value += $balance['balance'];
                    }
                }
                break;
                
            case 'account_type':
                // حساب من نوع حسابات
                if ($report['account_type_ids']) {
                    require_once 'AccountAccount.php';
                    $account_model = new AccountAccount($this->db);
                    $accounts = $account_model->get_accounts_by_type($report['account_type_ids']);
                    
                    foreach ($accounts as $account) {
                        $balance = $account_model->compute_balance($account['id'], $date_from, $date_to);
                        $value += $balance['balance'];
                    }
                }
                break;
                
            case 'sum':
                // مجموع التقارير الفرعية
                $child_reports = $this->get_child_reports($report_id);
                foreach ($child_reports as $child) {
                    $value += $this->compute_report_value($child['id'], $date_from, $date_to);
                }
                break;
                
            case 'account_report':
                // تقرير آخر
                // يمكن تطويره لاحقاً
                break;
        }
        
        // تطبيق الإشارة
        if ($report['sign'] === '-1') {
            $value = -$value;
        }
        
        return $value;
    }
    
    /**
     * إنشاء تقرير الميزانية العمومية
     */
    public function generate_balance_sheet($date_from = null, $date_to = null) {
        require_once 'AccountAccount.php';
        $account_model = new AccountAccount($this->db);
        
        $balance_sheet = array(
            'assets' => array(),
            'liabilities' => array(),
            'equity' => array(),
            'totals' => array()
        );
        
        // الأصول
        $assets = $account_model->get_accounts_by_type('asset');
        $total_assets = 0;
        foreach ($assets as $account) {
            $balance = $account_model->compute_balance($account['id'], $date_from, $date_to);
            $balance_sheet['assets'][] = array(
                'account' => $account,
                'balance' => $balance['balance']
            );
            $total_assets += $balance['balance'];
        }
        
        // الخصوم
        $liabilities = $account_model->get_accounts_by_type('liability');
        $total_liabilities = 0;
        foreach ($liabilities as $account) {
            $balance = $account_model->compute_balance($account['id'], $date_from, $date_to);
            $balance_sheet['liabilities'][] = array(
                'account' => $account,
                'balance' => $balance['balance']
            );
            $total_liabilities += $balance['balance'];
        }
        
        // حقوق الملكية
        $equity = $account_model->get_accounts_by_type('equity');
        $total_equity = 0;
        foreach ($equity as $account) {
            $balance = $account_model->compute_balance($account['id'], $date_from, $date_to);
            $balance_sheet['equity'][] = array(
                'account' => $account,
                'balance' => $balance['balance']
            );
            $total_equity += $balance['balance'];
        }
        
        $balance_sheet['totals'] = array(
            'assets' => $total_assets,
            'liabilities' => abs($total_liabilities),
            'equity' => abs($total_equity)
        );
        
        return $balance_sheet;
    }
    
    /**
     * إنشاء قائمة الدخل
     */
    public function generate_income_statement($date_from = null, $date_to = null) {
        require_once 'AccountAccount.php';
        $account_model = new AccountAccount($this->db);
        
        $income_statement = array(
            'income' => array(),
            'expenses' => array(),
            'totals' => array()
        );
        
        // الإيرادات
        $income = $account_model->get_accounts_by_type('income');
        $total_income = 0;
        foreach ($income as $account) {
            $balance = $account_model->compute_balance($account['id'], $date_from, $date_to);
            $income_statement['income'][] = array(
                'account' => $account,
                'balance' => $balance['balance']
            );
            $total_income += $balance['balance'];
        }
        
        // المصروفات
        $expenses = $account_model->get_accounts_by_type('expense');
        $total_expenses = 0;
        foreach ($expenses as $account) {
            $balance = $account_model->compute_balance($account['id'], $date_from, $date_to);
            $income_statement['expenses'][] = array(
                'account' => $account,
                'balance' => $balance['balance']
            );
            $total_expenses += $balance['balance'];
        }
        
        $net_income = abs($total_income) - $total_expenses;
        
        $income_statement['totals'] = array(
            'income' => abs($total_income),
            'expenses' => $total_expenses,
            'net_income' => $net_income
        );
        
        return $income_statement;
    }
    
    /**
     * إنشاء ميزان المراجعة
     */
    public function generate_trial_balance($date_from = null, $date_to = null) {
        require_once 'AccountAccount.php';
        $account_model = new AccountAccount($this->db);
        
        $trial_balance = array(
            'accounts' => array(),
            'totals' => array('debit' => 0, 'credit' => 0)
        );
        
        $all_accounts = $account_model->get_demo_data();
        
        foreach ($all_accounts as $account) {
            $balance = $account_model->compute_balance($account['id'], $date_from, $date_to);
            
            if ($balance['debit'] != 0 || $balance['credit'] != 0) {
                $trial_balance['accounts'][] = array(
                    'account' => $account,
                    'debit' => $balance['debit'],
                    'credit' => $balance['credit'],
                    'balance' => $balance['balance']
                );
                
                $trial_balance['totals']['debit'] += $balance['debit'];
                $trial_balance['totals']['credit'] += $balance['credit'];
            }
        }
        
        return $trial_balance;
    }
    
    /**
     * الحصول على بيانات تجريبية
     */
    public function get_demo_data() {
        // محاولة الحصول على البيانات من قاعدة البيانات أولاً
        try {
            $reports = $this->search_read(
                array(array('active', '=', true)),
                null,
                array('order' => 'sequence ASC')
            );
            
            if (count($reports) > 0) {
                return $reports;
            }
        } catch (Exception $e) {
            // في حالة عدم وجود قاعدة البيانات
        }
        
        // بيانات تجريبية احتياطية
        return array(
            array(
                'id' => 1,
                'name' => 'الميزانية العمومية',
                'parent_id' => null,
                'sequence' => 1,
                'level' => 1,
                'type' => 'sum',
                'account_ids' => null,
                'account_type_ids' => null,
                'sign' => '1',
                'display_detail' => 'detail_flat',
                'active' => true
            ),
            array(
                'id' => 2,
                'name' => 'الأصول',
                'parent_id' => 1,
                'sequence' => 1,
                'level' => 2,
                'type' => 'account_type',
                'account_ids' => null,
                'account_type_ids' => 'asset',
                'sign' => '1',
                'display_detail' => 'detail_flat',
                'active' => true
            ),
            array(
                'id' => 6,
                'name' => 'قائمة الدخل',
                'parent_id' => null,
                'sequence' => 2,
                'level' => 1,
                'type' => 'sum',
                'account_ids' => null,
                'account_type_ids' => null,
                'sign' => '1',
                'display_detail' => 'detail_flat',
                'active' => true
            ),
            array(
                'id' => 7,
                'name' => 'الإيرادات',
                'parent_id' => 6,
                'sequence' => 1,
                'level' => 2,
                'type' => 'account_type',
                'account_ids' => null,
                'account_type_ids' => 'income',
                'sign' => '-1',
                'display_detail' => 'detail_flat',
                'active' => true
            ),
            array(
                'id' => 8,
                'name' => 'المصروفات',
                'parent_id' => 6,
                'sequence' => 2,
                'level' => 2,
                'type' => 'account_type',
                'account_ids' => null,
                'account_type_ids' => 'expense',
                'sign' => '1',
                'display_detail' => 'detail_flat',
                'active' => true
            )
        );
    }
}
?>
