<?php
/**
 * نموذج اليوميات المحاسبية بأسلوب Odoo
 * Account Journal Model - Odoo Style
 */

require_once 'BaseModel.php';

class AccountJournal extends BaseModel {
    protected $table = 'account_journal';
    
    protected $fillable = [
        'name', 'code', 'type', 'default_account_id', 'sequence_id',
        'active', 'company_id', 'currency_id'
    ];
    
    protected $casts = [
        'active' => 'boolean',
        'default_account_id' => 'integer',
        'sequence_id' => 'integer',
        'company_id' => 'integer',
        'currency_id' => 'integer'
    ];
    
    // العلاقات
    public function default_account() {
        return $this->belongsTo('AccountAccount', 'default_account_id');
    }
    
    public function moves() {
        return $this->hasMany('AccountMove', 'journal_id');
    }
    
    public function company() {
        return $this->belongsTo('ResCompany', 'company_id');
    }
    
    public function currency() {
        return $this->belongsTo('ResCurrency', 'currency_id');
    }
    
    // الدوال المساعدة
    
    /**
     * الحصول على اليوميات حسب النوع
     */
    public function get_journals_by_type($type) {
        return $this->search_read(
            array(array('type', '=', $type)),
            null,
            array('order' => 'name ASC')
        );
    }
    
    /**
     * الحصول على الرقم التالي للقيد
     */
    public function get_next_sequence($journal_id) {
        try {
            $sql = "SELECT COUNT(*) + 1 as next_number 
                    FROM account_move 
                    WHERE journal_id = ? 
                    AND YEAR(date) = YEAR(CURDATE())";
            
            $result = $this->db->query($sql, array($journal_id));
            
            if ($result && count($result) > 0) {
                $journal = $this->read($journal_id);
                $year = date('Y');
                return $journal['code'] . '/' . $year . '/' . str_pad($result[0]['next_number'], 4, '0', STR_PAD_LEFT);
            }
        } catch (Exception $e) {
            // في حالة عدم وجود قاعدة البيانات
        }
        
        return 'MISC/2024/0001';
    }
    
    /**
     * التحقق من صحة رمز اليومية
     */
    public function validate_journal_code($code, $exclude_id = null) {
        $conditions = array(array('code', '=', $code));
        
        if ($exclude_id) {
            $conditions[] = array('id', '!=', $exclude_id);
        }
        
        $existing = $this->search_read($conditions);
        return count($existing) === 0;
    }
    
    /**
     * الحصول على بيانات تجريبية
     */
    public function get_demo_data() {
        // محاولة الحصول على البيانات من قاعدة البيانات أولاً
        try {
            $journals = $this->search_read(
                array(array('active', '=', true)),
                null,
                array('order' => 'name ASC')
            );
            
            if (count($journals) > 0) {
                return $journals;
            }
        } catch (Exception $e) {
            // في حالة عدم وجود قاعدة البيانات
        }
        
        // بيانات تجريبية احتياطية
        return array(
            array(
                'id' => 1,
                'name' => 'يومية المبيعات',
                'code' => 'SAL',
                'type' => 'sale',
                'default_account_id' => 36,
                'sequence_id' => null,
                'active' => true,
                'company_id' => 1,
                'currency_id' => 1
            ),
            array(
                'id' => 2,
                'name' => 'يومية المشتريات',
                'code' => 'PUR',
                'type' => 'purchase',
                'default_account_id' => 22,
                'sequence_id' => null,
                'active' => true,
                'company_id' => 1,
                'currency_id' => 1
            ),
            array(
                'id' => 3,
                'name' => 'يومية الصندوق',
                'code' => 'CSH',
                'type' => 'cash',
                'default_account_id' => 4,
                'sequence_id' => null,
                'active' => true,
                'company_id' => 1,
                'currency_id' => 1
            ),
            array(
                'id' => 4,
                'name' => 'يومية البنك الأهلي',
                'code' => 'BNK1',
                'type' => 'bank',
                'default_account_id' => 5,
                'sequence_id' => null,
                'active' => true,
                'company_id' => 1,
                'currency_id' => 1
            ),
            array(
                'id' => 5,
                'name' => 'يومية البنك الراجحي',
                'code' => 'BNK2',
                'type' => 'bank',
                'default_account_id' => 6,
                'sequence_id' => null,
                'active' => true,
                'company_id' => 1,
                'currency_id' => 1
            ),
            array(
                'id' => 6,
                'name' => 'اليومية العامة',
                'code' => 'MISC',
                'type' => 'general',
                'default_account_id' => null,
                'sequence_id' => null,
                'active' => true,
                'company_id' => 1,
                'currency_id' => 1
            )
        );
    }
    
    /**
     * إنشاء يومية جديدة
     */
    public function create_journal($data) {
        // التحقق من صحة البيانات
        if (empty($data['name']) || empty($data['code']) || empty($data['type'])) {
            throw new Exception('البيانات المطلوبة مفقودة');
        }
        
        // التحقق من عدم تكرار رمز اليومية
        if (!$this->validate_journal_code($data['code'])) {
            throw new Exception('رمز اليومية موجود مسبقاً');
        }
        
        return $this->create($data);
    }
    
    /**
     * الحصول على إحصائيات اليومية
     */
    public function get_journal_statistics($journal_id, $date_from = null, $date_to = null) {
        try {
            $conditions = array('journal_id = ?');
            $params = array($journal_id);
            
            if ($date_from) {
                $conditions[] = 'date >= ?';
                $params[] = $date_from;
            }
            if ($date_to) {
                $conditions[] = 'date <= ?';
                $params[] = $date_to;
            }
            
            $where_clause = implode(' AND ', $conditions);
            
            $sql = "SELECT 
                        COUNT(*) as total_moves,
                        SUM(amount_total) as total_amount,
                        COUNT(CASE WHEN state = 'posted' THEN 1 END) as posted_moves,
                        COUNT(CASE WHEN state = 'draft' THEN 1 END) as draft_moves
                    FROM account_move 
                    WHERE {$where_clause}";
            
            $result = $this->db->query($sql, $params);
            
            if ($result && count($result) > 0) {
                return array(
                    'total_moves' => intval($result[0]['total_moves']),
                    'total_amount' => floatval($result[0]['total_amount']),
                    'posted_moves' => intval($result[0]['posted_moves']),
                    'draft_moves' => intval($result[0]['draft_moves'])
                );
            }
        } catch (Exception $e) {
            // في حالة عدم وجود قاعدة البيانات
        }
        
        return array(
            'total_moves' => 0,
            'total_amount' => 0,
            'posted_moves' => 0,
            'draft_moves' => 0
        );
    }
}
?>
