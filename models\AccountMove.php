<?php
/**
 * نموذج القيود المحاسبية بأسلوب Odoo
 * Account Move Model - Odoo Style
 */

require_once 'BaseModel.php';

class AccountMove extends BaseModel {
    protected $table = 'account_move';
    
    protected $fillable = [
        'name', 'ref', 'date', 'journal_id', 'state', 'amount_total',
        'narration', 'company_id', 'currency_id', 'created_by', 'posted_by', 'posted_at'
    ];
    
    protected $casts = [
        'date' => 'date',
        'amount_total' => 'decimal',
        'journal_id' => 'integer',
        'company_id' => 'integer',
        'currency_id' => 'integer',
        'created_by' => 'integer',
        'posted_by' => 'integer'
    ];
    
    // العلاقات
    public function journal() {
        return $this->belongsTo('AccountJournal', 'journal_id');
    }
    
    public function move_lines() {
        return $this->hasMany('AccountMoveLine', 'move_id');
    }
    
    public function company() {
        return $this->belongsTo('ResCompany', 'company_id');
    }
    
    public function currency() {
        return $this->belongsTo('ResCurrency', 'currency_id');
    }
    
    // الدوال المساعدة
    
    /**
     * إنشاء قيد محاسبي جديد
     */
    public function create_move($data) {
        // التحقق من صحة البيانات
        if (empty($data['journal_id']) || empty($data['date'])) {
            throw new Exception('البيانات المطلوبة مفقودة');
        }
        
        // الحصول على الرقم التالي
        if (empty($data['name'])) {
            require_once 'AccountJournal.php';
            $journal_model = new AccountJournal($this->db);
            $data['name'] = $journal_model->get_next_sequence($data['journal_id']);
        }
        
        // تعيين القيم الافتراضية
        $data['state'] = $data['state'] ?? 'draft';
        $data['company_id'] = $data['company_id'] ?? 1;
        $data['currency_id'] = $data['currency_id'] ?? 1;
        $data['created_by'] = $data['created_by'] ?? ($_SESSION['user_id'] ?? 1);
        
        return $this->create($data);
    }
    
    /**
     * إضافة بند للقيد
     */
    public function add_move_line($move_id, $line_data) {
        require_once 'AccountMoveLine.php';
        $move_line_model = new AccountMoveLine($this->db);
        
        $line_data['move_id'] = $move_id;
        
        // الحصول على تاريخ القيد
        $move = $this->read($move_id);
        if ($move) {
            $line_data['date'] = $move['date'];
            $line_data['ref'] = $move['ref'];
        }
        
        return $move_line_model->create($line_data);
    }
    
    /**
     * اعتماد القيد
     */
    public function post_move($move_id) {
        // التحقق من توازن القيد
        if (!$this->is_balanced($move_id)) {
            throw new Exception('القيد غير متوازن');
        }
        
        $data = array(
            'state' => 'posted',
            'posted_by' => $_SESSION['user_id'] ?? 1,
            'posted_at' => date('Y-m-d H:i:s')
        );
        
        return $this->update($move_id, $data);
    }
    
    /**
     * التحقق من توازن القيد
     */
    public function is_balanced($move_id) {
        try {
            $sql = "SELECT 
                        SUM(debit) as total_debit,
                        SUM(credit) as total_credit
                    FROM account_move_line 
                    WHERE move_id = ?";
            
            $result = $this->db->query($sql, array($move_id));
            
            if ($result && count($result) > 0) {
                $debit = floatval($result[0]['total_debit']);
                $credit = floatval($result[0]['total_credit']);
                return abs($debit - $credit) < 0.01; // تسامح صغير للأرقام العشرية
            }
        } catch (Exception $e) {
            // في حالة عدم وجود قاعدة البيانات
        }
        
        return true; // افتراض التوازن في الوضع التجريبي
    }
    
    /**
     * حساب إجمالي القيد
     */
    public function compute_amount_total($move_id) {
        try {
            $sql = "SELECT SUM(debit) as total FROM account_move_line WHERE move_id = ?";
            $result = $this->db->query($sql, array($move_id));
            
            if ($result && count($result) > 0) {
                $total = floatval($result[0]['total']);
                $this->update($move_id, array('amount_total' => $total));
                return $total;
            }
        } catch (Exception $e) {
            // في حالة عدم وجود قاعدة البيانات
        }
        
        return 0;
    }
    
    /**
     * إلغاء القيد
     */
    public function cancel_move($move_id) {
        return $this->update($move_id, array('state' => 'cancelled'));
    }
    
    /**
     * الحصول على القيود حسب الفترة
     */
    public function get_moves_by_period($date_from, $date_to, $journal_id = null) {
        $conditions = array(
            array('date', '>=', $date_from),
            array('date', '<=', $date_to)
        );
        
        if ($journal_id) {
            $conditions[] = array('journal_id', '=', $journal_id);
        }
        
        return $this->search_read($conditions, null, array('order' => 'date DESC, name DESC'));
    }
    
    /**
     * الحصول على بيانات تجريبية
     */
    public function get_demo_data() {
        // محاولة الحصول على البيانات من قاعدة البيانات أولاً
        try {
            $moves = $this->search_read(
                array(),
                null,
                array('order' => 'date DESC, name DESC', 'limit' => 20)
            );
            
            if (count($moves) > 0) {
                return $moves;
            }
        } catch (Exception $e) {
            // في حالة عدم وجود قاعدة البيانات
        }
        
        // بيانات تجريبية احتياطية
        return array(
            array(
                'id' => 1,
                'name' => 'SAL/2024/0001',
                'ref' => 'INV-001',
                'date' => '2024-01-15',
                'journal_id' => 1,
                'state' => 'posted',
                'amount_total' => 11500.00,
                'narration' => 'فاتورة مبيعات رقم INV-001',
                'company_id' => 1,
                'currency_id' => 1,
                'created_by' => 1,
                'posted_by' => 1,
                'posted_at' => '2024-01-15 10:30:00'
            ),
            array(
                'id' => 2,
                'name' => 'PUR/2024/0001',
                'ref' => 'BILL-001',
                'date' => '2024-01-14',
                'journal_id' => 2,
                'state' => 'posted',
                'amount_total' => 5750.00,
                'narration' => 'فاتورة مشتريات رقم BILL-001',
                'company_id' => 1,
                'currency_id' => 1,
                'created_by' => 1,
                'posted_by' => 1,
                'posted_at' => '2024-01-14 14:20:00'
            ),
            array(
                'id' => 3,
                'name' => 'CSH/2024/0001',
                'ref' => 'CASH-001',
                'date' => '2024-01-13',
                'journal_id' => 3,
                'state' => 'posted',
                'amount_total' => 2000.00,
                'narration' => 'تحصيل نقدي من العميل',
                'company_id' => 1,
                'currency_id' => 1,
                'created_by' => 1,
                'posted_by' => 1,
                'posted_at' => '2024-01-13 16:45:00'
            ),
            array(
                'id' => 4,
                'name' => 'MISC/2024/0001',
                'ref' => 'ADJ-001',
                'date' => '2024-01-12',
                'journal_id' => 6,
                'state' => 'draft',
                'amount_total' => 1200.00,
                'narration' => 'قيد تسوية مصروفات',
                'company_id' => 1,
                'currency_id' => 1,
                'created_by' => 1,
                'posted_by' => null,
                'posted_at' => null
            ),
            array(
                'id' => 5,
                'name' => 'BNK1/2024/0001',
                'ref' => 'TRF-001',
                'date' => '2024-01-11',
                'journal_id' => 4,
                'state' => 'posted',
                'amount_total' => 15000.00,
                'narration' => 'تحويل بنكي من العميل',
                'company_id' => 1,
                'currency_id' => 1,
                'created_by' => 1,
                'posted_by' => 1,
                'posted_at' => '2024-01-11 11:30:00'
            )
        );
    }
    
    /**
     * البحث في القيود
     */
    public function search_moves($search_term, $date_from = null, $date_to = null) {
        $conditions = array(
            'OR' => array(
                array('name', 'LIKE', '%' . $search_term . '%'),
                array('ref', 'LIKE', '%' . $search_term . '%'),
                array('narration', 'LIKE', '%' . $search_term . '%')
            )
        );
        
        if ($date_from) {
            $conditions['date'] = array('>=', $date_from);
        }
        if ($date_to) {
            $conditions['date'] = array('<=', $date_to);
        }
        
        return $this->search_read($conditions, null, array('order' => 'date DESC'));
    }
}
?>
