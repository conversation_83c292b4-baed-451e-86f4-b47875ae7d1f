<?php
/**
 * نموذج بنود القيود المحاسبية بأسلوب Odoo
 * Account Move Line Model - Odoo Style
 */

require_once 'BaseModel.php';

class AccountMoveLine extends BaseModel {
    protected $table = 'account_move_line';
    
    protected $fillable = [
        'move_id', 'account_id', 'name', 'debit', 'credit', 'balance',
        'partner_id', 'date', 'ref', 'reconciled', 'reconcile_ref',
        'company_id', 'currency_id'
    ];
    
    protected $casts = [
        'move_id' => 'integer',
        'account_id' => 'integer',
        'debit' => 'decimal',
        'credit' => 'decimal',
        'balance' => 'decimal',
        'partner_id' => 'integer',
        'date' => 'date',
        'reconciled' => 'boolean',
        'company_id' => 'integer',
        'currency_id' => 'integer'
    ];
    
    // العلاقات
    public function move() {
        return $this->belongsTo('AccountMove', 'move_id');
    }
    
    public function account() {
        return $this->belongsTo('AccountAccount', 'account_id');
    }
    
    public function partner() {
        return $this->belongsTo('ResPartner', 'partner_id');
    }
    
    public function company() {
        return $this->belongsTo('ResCompany', 'company_id');
    }
    
    public function currency() {
        return $this->belongsTo('ResCurrency', 'currency_id');
    }
    
    // الدوال المساعدة
    
    /**
     * إنشاء بند قيد جديد
     */
    public function create_move_line($data) {
        // التحقق من صحة البيانات
        if (empty($data['move_id']) || empty($data['account_id']) || empty($data['name'])) {
            throw new Exception('البيانات المطلوبة مفقودة');
        }
        
        // التحقق من أن أحد الحقلين مدين أو دائن له قيمة
        $debit = floatval($data['debit'] ?? 0);
        $credit = floatval($data['credit'] ?? 0);
        
        if ($debit == 0 && $credit == 0) {
            throw new Exception('يجب إدخال قيمة في المدين أو الدائن');
        }
        
        if ($debit > 0 && $credit > 0) {
            throw new Exception('لا يمكن إدخال قيمة في المدين والدائن معاً');
        }
        
        // حساب الرصيد
        $data['balance'] = $debit - $credit;
        
        // تعيين القيم الافتراضية
        $data['company_id'] = $data['company_id'] ?? 1;
        $data['currency_id'] = $data['currency_id'] ?? 1;
        $data['reconciled'] = $data['reconciled'] ?? false;
        
        return $this->create($data);
    }
    
    /**
     * الحصول على بنود القيد
     */
    public function get_move_lines($move_id) {
        return $this->search_read(
            array(array('move_id', '=', $move_id)),
            null,
            array('order' => 'id ASC')
        );
    }
    
    /**
     * الحصول على بنود الحساب
     */
    public function get_account_lines($account_id, $date_from = null, $date_to = null) {
        $conditions = array(array('account_id', '=', $account_id));
        
        if ($date_from) {
            $conditions[] = array('date', '>=', $date_from);
        }
        if ($date_to) {
            $conditions[] = array('date', '<=', $date_to);
        }
        
        return $this->search_read($conditions, null, array('order' => 'date DESC, id DESC'));
    }
    
    /**
     * تسوية البنود
     */
    public function reconcile_lines($line_ids, $reconcile_ref = null) {
        if (empty($line_ids) || count($line_ids) < 2) {
            throw new Exception('يجب اختيار بندين على الأقل للتسوية');
        }
        
        // التحقق من أن البنود متوازنة
        $total_balance = 0;
        foreach ($line_ids as $line_id) {
            $line = $this->read($line_id);
            if ($line) {
                $total_balance += $line['balance'];
            }
        }
        
        if (abs($total_balance) > 0.01) {
            throw new Exception('البنود غير متوازنة للتسوية');
        }
        
        // تحديث البنود
        $reconcile_ref = $reconcile_ref ?? 'REC-' . date('YmdHis');
        
        foreach ($line_ids as $line_id) {
            $this->update($line_id, array(
                'reconciled' => true,
                'reconcile_ref' => $reconcile_ref
            ));
        }
        
        return $reconcile_ref;
    }
    
    /**
     * إلغاء التسوية
     */
    public function unreconcile_lines($reconcile_ref) {
        $conditions = array(array('reconcile_ref', '=', $reconcile_ref));
        $lines = $this->search_read($conditions);
        
        foreach ($lines as $line) {
            $this->update($line['id'], array(
                'reconciled' => false,
                'reconcile_ref' => null
            ));
        }
        
        return count($lines);
    }
    
    /**
     * الحصول على البنود غير المسواة
     */
    public function get_unreconciled_lines($account_id = null, $partner_id = null) {
        $conditions = array(array('reconciled', '=', false));
        
        if ($account_id) {
            $conditions[] = array('account_id', '=', $account_id);
        }
        if ($partner_id) {
            $conditions[] = array('partner_id', '=', $partner_id);
        }
        
        return $this->search_read($conditions, null, array('order' => 'date ASC'));
    }
    
    /**
     * الحصول على بيانات تجريبية
     */
    public function get_demo_data() {
        // محاولة الحصول على البيانات من قاعدة البيانات أولاً
        try {
            $lines = $this->search_read(
                array(),
                null,
                array('order' => 'date DESC, id DESC', 'limit' => 50)
            );
            
            if (count($lines) > 0) {
                return $lines;
            }
        } catch (Exception $e) {
            // في حالة عدم وجود قاعدة البيانات
        }
        
        // بيانات تجريبية احتياطية
        return array(
            // قيد مبيعات
            array(
                'id' => 1,
                'move_id' => 1,
                'account_id' => 8, // حسابات العملاء
                'name' => 'فاتورة مبيعات INV-001',
                'debit' => 11500.00,
                'credit' => 0.00,
                'balance' => 11500.00,
                'partner_id' => 1,
                'date' => '2024-01-15',
                'ref' => 'INV-001',
                'reconciled' => false,
                'reconcile_ref' => null
            ),
            array(
                'id' => 2,
                'move_id' => 1,
                'account_id' => 36, // مبيعات البضائع
                'name' => 'فاتورة مبيعات INV-001',
                'debit' => 0.00,
                'credit' => 10000.00,
                'balance' => -10000.00,
                'partner_id' => null,
                'date' => '2024-01-15',
                'ref' => 'INV-001',
                'reconciled' => false,
                'reconcile_ref' => null
            ),
            array(
                'id' => 3,
                'move_id' => 1,
                'account_id' => 25, // ضريبة القيمة المضافة
                'name' => 'ضريبة القيمة المضافة',
                'debit' => 0.00,
                'credit' => 1500.00,
                'balance' => -1500.00,
                'partner_id' => null,
                'date' => '2024-01-15',
                'ref' => 'INV-001',
                'reconciled' => false,
                'reconcile_ref' => null
            ),
            
            // قيد مشتريات
            array(
                'id' => 4,
                'move_id' => 2,
                'account_id' => 42, // تكلفة المبيعات
                'name' => 'فاتورة مشتريات BILL-001',
                'debit' => 5000.00,
                'credit' => 0.00,
                'balance' => 5000.00,
                'partner_id' => null,
                'date' => '2024-01-14',
                'ref' => 'BILL-001',
                'reconciled' => false,
                'reconcile_ref' => null
            ),
            array(
                'id' => 5,
                'move_id' => 2,
                'account_id' => 25, // ضريبة القيمة المضافة
                'name' => 'ضريبة القيمة المضافة',
                'debit' => 750.00,
                'credit' => 0.00,
                'balance' => 750.00,
                'partner_id' => null,
                'date' => '2024-01-14',
                'ref' => 'BILL-001',
                'reconciled' => false,
                'reconcile_ref' => null
            ),
            array(
                'id' => 6,
                'move_id' => 2,
                'account_id' => 22, // حسابات الموردين
                'name' => 'فاتورة مشتريات BILL-001',
                'debit' => 0.00,
                'credit' => 5750.00,
                'balance' => -5750.00,
                'partner_id' => 2,
                'date' => '2024-01-14',
                'ref' => 'BILL-001',
                'reconciled' => false,
                'reconcile_ref' => null
            ),
            
            // قيد تحصيل نقدي
            array(
                'id' => 7,
                'move_id' => 3,
                'account_id' => 4, // الصندوق
                'name' => 'تحصيل نقدي من العميل',
                'debit' => 2000.00,
                'credit' => 0.00,
                'balance' => 2000.00,
                'partner_id' => null,
                'date' => '2024-01-13',
                'ref' => 'CASH-001',
                'reconciled' => false,
                'reconcile_ref' => null
            ),
            array(
                'id' => 8,
                'move_id' => 3,
                'account_id' => 8, // حسابات العملاء
                'name' => 'تحصيل نقدي من العميل',
                'debit' => 0.00,
                'credit' => 2000.00,
                'balance' => -2000.00,
                'partner_id' => 1,
                'date' => '2024-01-13',
                'ref' => 'CASH-001',
                'reconciled' => false,
                'reconcile_ref' => null
            )
        );
    }
    
    /**
     * البحث في بنود القيود
     */
    public function search_lines($search_term, $account_id = null, $date_from = null, $date_to = null) {
        $conditions = array(
            'OR' => array(
                array('name', 'LIKE', '%' . $search_term . '%'),
                array('ref', 'LIKE', '%' . $search_term . '%')
            )
        );
        
        if ($account_id) {
            $conditions['account_id'] = array('=', $account_id);
        }
        if ($date_from) {
            $conditions['date'] = array('>=', $date_from);
        }
        if ($date_to) {
            $conditions['date'] = array('<=', $date_to);
        }
        
        return $this->search_read($conditions, null, array('order' => 'date DESC'));
    }
}
?>
