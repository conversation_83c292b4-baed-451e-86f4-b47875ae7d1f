<?php
/**
 * نموذج إدارة المدفوعات بأسلوب Odoo
 * Account Payment Management Model - Odoo Style
 */

require_once 'BaseModel.php';

class AccountPayment extends BaseModel {
    protected $table = 'account_payment';
    
    protected $fillable = [
        'name', 'payment_type', 'partner_type', 'partner_id', 'amount',
        'currency_id', 'payment_date', 'communication', 'journal_id',
        'payment_method_id', 'payment_method_line_id', 'state',
        'move_id', 'reconciled_invoice_ids', 'reconciled_invoices_count',
        'company_id', 'destination_account_id', 'outstanding_account_id'
    ];
    
    protected $casts = [
        'partner_id' => 'integer',
        'amount' => 'decimal',
        'currency_id' => 'integer',
        'payment_date' => 'date',
        'journal_id' => 'integer',
        'payment_method_id' => 'integer',
        'payment_method_line_id' => 'integer',
        'move_id' => 'integer',
        'reconciled_invoices_count' => 'integer',
        'company_id' => 'integer',
        'destination_account_id' => 'integer',
        'outstanding_account_id' => 'integer'
    ];
    
    // أنواع المدفوعات
    private $payment_types = array(
        'outbound' => 'صادر',
        'inbound' => 'وارد',
        'transfer' => 'تحويل'
    );
    
    // أنواع الشركاء
    private $partner_types = array(
        'customer' => 'عميل',
        'supplier' => 'مورد'
    );
    
    // حالات المدفوعات
    private $payment_states = array(
        'draft' => array('name' => 'مسودة', 'color' => 'secondary'),
        'posted' => array('name' => 'مرحل', 'color' => 'success'),
        'sent' => array('name' => 'مرسل', 'color' => 'info'),
        'reconciled' => array('name' => 'مسوى', 'color' => 'primary'),
        'cancelled' => array('name' => 'ملغي', 'color' => 'danger')
    );
    
    // العلاقات
    public function partner() {
        return $this->belongsTo('ResPartner', 'partner_id');
    }
    
    public function currency() {
        return $this->belongsTo('ResCurrency', 'currency_id');
    }
    
    public function journal() {
        return $this->belongsTo('AccountJournal', 'journal_id');
    }
    
    public function payment_method() {
        return $this->belongsTo('AccountPaymentMethod', 'payment_method_id');
    }
    
    public function move() {
        return $this->belongsTo('AccountMove', 'move_id');
    }
    
    public function company() {
        return $this->belongsTo('ResCompany', 'company_id');
    }
    
    /**
     * إنشاء مدفوعة جديدة
     */
    public function create_payment($data) {
        // التحقق من صحة البيانات
        if (empty($data['amount']) || $data['amount'] <= 0) {
            throw new Exception('مبلغ المدفوعة غير صحيح');
        }
        
        if (empty($data['partner_id']) || empty($data['journal_id'])) {
            throw new Exception('بيانات الشريك أو اليومية مفقودة');
        }
        
        // تعيين القيم الافتراضية
        $data['state'] = $data['state'] ?? 'draft';
        $data['payment_date'] = $data['payment_date'] ?? date('Y-m-d');
        $data['company_id'] = $data['company_id'] ?? 1;
        $data['currency_id'] = $data['currency_id'] ?? 1;
        
        // إنشاء رقم المدفوعة
        if (empty($data['name'])) {
            $data['name'] = $this->generate_payment_number($data['journal_id']);
        }
        
        return $this->create($data);
    }
    
    /**
     * ترحيل المدفوعة
     */
    public function post_payment($payment_id) {
        $payment = $this->read($payment_id);
        if (!$payment || $payment['state'] !== 'draft') {
            throw new Exception('لا يمكن ترحيل هذه المدفوعة');
        }
        
        // إنشاء القيد المحاسبي
        $move_id = $this->create_payment_move($payment);
        
        // تحديث حالة المدفوعة
        $this->update($payment_id, array(
            'state' => 'posted',
            'move_id' => $move_id
        ));
        
        return $move_id;
    }
    
    /**
     * إنشاء القيد المحاسبي للمدفوعة
     */
    private function create_payment_move($payment) {
        require_once 'AccountMove.php';
        require_once 'AccountMoveLine.php';
        
        $move_model = new AccountMove($this->db);
        $move_line_model = new AccountMoveLine($this->db);
        
        // إنشاء القيد
        $move_data = array(
            'journal_id' => $payment['journal_id'],
            'date' => $payment['payment_date'],
            'ref' => $payment['name'],
            'state' => 'draft',
            'company_id' => $payment['company_id']
        );
        
        $move_id = $move_model->create($move_data);
        
        // تحديد الحسابات
        $accounts = $this->get_payment_accounts($payment);
        
        // إنشاء بنود القيد
        $lines = array();
        
        if ($payment['payment_type'] === 'inbound') {
            // مدفوعة واردة (من العميل)
            $lines[] = array(
                'account_id' => $accounts['liquidity_account'],
                'name' => $payment['communication'] ?: "دفعة من {$payment['partner_name']}",
                'debit' => $payment['amount'],
                'credit' => 0,
                'partner_id' => $payment['partner_id']
            );
            
            $lines[] = array(
                'account_id' => $accounts['counterpart_account'],
                'name' => $payment['communication'] ?: "دفعة من {$payment['partner_name']}",
                'debit' => 0,
                'credit' => $payment['amount'],
                'partner_id' => $payment['partner_id']
            );
            
        } elseif ($payment['payment_type'] === 'outbound') {
            // مدفوعة صادرة (للمورد)
            $lines[] = array(
                'account_id' => $accounts['counterpart_account'],
                'name' => $payment['communication'] ?: "دفعة إلى {$payment['partner_name']}",
                'debit' => $payment['amount'],
                'credit' => 0,
                'partner_id' => $payment['partner_id']
            );
            
            $lines[] = array(
                'account_id' => $accounts['liquidity_account'],
                'name' => $payment['communication'] ?: "دفعة إلى {$payment['partner_name']}",
                'debit' => 0,
                'credit' => $payment['amount'],
                'partner_id' => $payment['partner_id']
            );
        }
        
        // إنشاء بنود القيد
        foreach ($lines as $line) {
            $line['move_id'] = $move_id;
            $line['date'] = $payment['payment_date'];
            $move_line_model->create($line);
        }
        
        // ترحيل القيد
        $move_model->post_move($move_id);
        
        return $move_id;
    }
    
    /**
     * الحصول على حسابات المدفوعة
     */
    private function get_payment_accounts($payment) {
        require_once 'AccountJournal.php';
        require_once 'ResPartner.php';
        
        $journal_model = new AccountJournal($this->db);
        $partner_model = new ResPartner($this->db);
        
        $journal = $journal_model->read($payment['journal_id']);
        $partner = $partner_model->read($payment['partner_id']);
        
        $accounts = array();
        
        // حساب السيولة (من اليومية)
        $accounts['liquidity_account'] = $journal['default_account_id'];
        
        // الحساب المقابل (من الشريك)
        if ($payment['partner_type'] === 'customer') {
            $accounts['counterpart_account'] = $partner['property_account_receivable_id'] ?? 4; // حساب العملاء
        } else {
            $accounts['counterpart_account'] = $partner['property_account_payable_id'] ?? 11; // حساب الموردين
        }
        
        return $accounts;
    }
    
    /**
     * تسوية المدفوعة مع الفواتير
     */
    public function reconcile_payment($payment_id, $invoice_ids) {
        $payment = $this->read($payment_id);
        if (!$payment || $payment['state'] !== 'posted') {
            throw new Exception('المدفوعة غير صالحة للتسوية');
        }
        
        require_once 'AccountMoveLine.php';
        $move_line_model = new AccountMoveLine($this->db);
        
        // الحصول على بنود المدفوعة
        $payment_lines = $move_line_model->search(array(
            'move_id' => $payment['move_id'],
            'account_id' => $payment['destination_account_id'],
            'reconciled' => false
        ));
        
        // الحصول على بنود الفواتير
        $invoice_lines = array();
        foreach ($invoice_ids as $invoice_id) {
            $lines = $move_line_model->search(array(
                'move_id' => $invoice_id,
                'account_id' => $payment['destination_account_id'],
                'reconciled' => false
            ));
            $invoice_lines = array_merge($invoice_lines, $lines);
        }
        
        // تنفيذ التسوية
        $all_lines = array_merge($payment_lines, $invoice_lines);
        $reconcile_ref = 'REC' . date('YmdHis');
        
        foreach ($all_lines as $line) {
            $move_line_model->update($line['id'], array(
                'reconciled' => true,
                'reconcile_ref' => $reconcile_ref
            ));
        }
        
        // تحديث حالة المدفوعة
        $this->update($payment_id, array(
            'state' => 'reconciled',
            'reconciled_invoice_ids' => implode(',', $invoice_ids),
            'reconciled_invoices_count' => count($invoice_ids)
        ));
        
        return true;
    }
    
    /**
     * إلغاء المدفوعة
     */
    public function cancel_payment($payment_id) {
        $payment = $this->read($payment_id);
        if (!$payment) {
            throw new Exception('المدفوعة غير موجودة');
        }
        
        if ($payment['state'] === 'reconciled') {
            throw new Exception('لا يمكن إلغاء مدفوعة مسواة');
        }
        
        // إلغاء القيد المحاسبي إن وجد
        if ($payment['move_id']) {
            require_once 'AccountMove.php';
            $move_model = new AccountMove($this->db);
            $move_model->update($payment['move_id'], array('state' => 'cancel'));
        }
        
        // تحديث حالة المدفوعة
        return $this->update($payment_id, array('state' => 'cancelled'));
    }
    
    /**
     * إنشاء رقم المدفوعة
     */
    private function generate_payment_number($journal_id) {
        require_once 'AccountJournal.php';
        $journal_model = new AccountJournal($this->db);
        
        return $journal_model->get_next_sequence($journal_id);
    }
    
    /**
     * الحصول على بيانات تجريبية
     */
    public function get_demo_data() {
        return array(
            array(
                'id' => 1,
                'name' => 'PAY/2024/001',
                'payment_type' => 'inbound',
                'partner_type' => 'customer',
                'partner_id' => 1,
                'amount' => 15000.00,
                'currency_id' => 1,
                'payment_date' => '2024-01-15',
                'communication' => 'دفعة مقابل فاتورة INV/2024/001',
                'journal_id' => 3,
                'payment_method_id' => 1,
                'state' => 'posted',
                'company_id' => 1,
                'destination_account_id' => 4,
                'outstanding_account_id' => 4
            ),
            array(
                'id' => 2,
                'name' => 'PAY/2024/002',
                'payment_type' => 'outbound',
                'partner_type' => 'supplier',
                'partner_id' => 3,
                'amount' => 8000.00,
                'currency_id' => 1,
                'payment_date' => '2024-01-20',
                'communication' => 'دفعة مقابل فاتورة BILL/2024/001',
                'journal_id' => 4,
                'payment_method_id' => 2,
                'state' => 'posted',
                'company_id' => 1,
                'destination_account_id' => 11,
                'outstanding_account_id' => 11
            ),
            array(
                'id' => 3,
                'name' => 'PAY/2024/003',
                'payment_type' => 'transfer',
                'partner_type' => 'customer',
                'partner_id' => null,
                'amount' => 5000.00,
                'currency_id' => 1,
                'payment_date' => '2024-02-01',
                'communication' => 'تحويل من الصندوق إلى البنك',
                'journal_id' => 3,
                'payment_method_id' => 3,
                'state' => 'posted',
                'company_id' => 1
            )
        );
    }
}

/**
 * نموذج طرق الدفع
 */
class AccountPaymentMethod extends BaseModel {
    protected $table = 'account_payment_method';
    
    protected $fillable = [
        'name', 'code', 'payment_type'
    ];
    
    /**
     * الحصول على بيانات تجريبية
     */
    public function get_demo_data() {
        return array(
            array(
                'id' => 1,
                'name' => 'نقدي',
                'code' => 'manual',
                'payment_type' => 'inbound'
            ),
            array(
                'id' => 2,
                'name' => 'تحويل بنكي',
                'code' => 'electronic',
                'payment_type' => 'outbound'
            ),
            array(
                'id' => 3,
                'name' => 'شيك',
                'code' => 'check_printing',
                'payment_type' => 'outbound'
            ),
            array(
                'id' => 4,
                'name' => 'بطاقة ائتمان',
                'code' => 'credit_card',
                'payment_type' => 'inbound'
            )
        );
    }
}

/**
 * نموذج شروط الدفع
 */
class AccountPaymentTerm extends BaseModel {
    protected $table = 'account_payment_term';
    
    protected $fillable = [
        'name', 'active', 'note', 'sequence', 'company_id'
    ];
    
    protected $casts = [
        'active' => 'boolean',
        'sequence' => 'integer',
        'company_id' => 'integer'
    ];
    
    // العلاقات
    public function payment_term_lines() {
        return $this->hasMany('AccountPaymentTermLine', 'payment_id');
    }
    
    /**
     * حساب تواريخ الاستحقاق
     */
    public function compute_due_dates($payment_term_id, $invoice_date, $invoice_amount) {
        require_once 'AccountPaymentTermLine.php';
        $line_model = new AccountPaymentTermLine($this->db);
        
        $lines = $line_model->search(array('payment_id' => $payment_term_id), 'sequence ASC');
        $due_dates = array();
        
        $invoice_date_obj = new DateTime($invoice_date);
        
        foreach ($lines as $line) {
            $due_date = clone $invoice_date_obj;
            $due_date->add(new DateInterval("P{$line['days']}D"));
            
            $amount = ($line['value_amount'] / 100) * $invoice_amount;
            
            $due_dates[] = array(
                'due_date' => $due_date->format('Y-m-d'),
                'amount' => $amount,
                'percentage' => $line['value_amount']
            );
        }
        
        return $due_dates;
    }
    
    /**
     * الحصول على بيانات تجريبية
     */
    public function get_demo_data() {
        return array(
            array(
                'id' => 1,
                'name' => 'فوري',
                'active' => true,
                'note' => 'الدفع فور الاستلام',
                'sequence' => 1,
                'company_id' => 1
            ),
            array(
                'id' => 2,
                'name' => '15 يوم',
                'active' => true,
                'note' => 'الدفع خلال 15 يوم',
                'sequence' => 2,
                'company_id' => 1
            ),
            array(
                'id' => 3,
                'name' => '30 يوم',
                'active' => true,
                'note' => 'الدفع خلال 30 يوم',
                'sequence' => 3,
                'company_id' => 1
            ),
            array(
                'id' => 4,
                'name' => '60 يوم',
                'active' => true,
                'note' => 'الدفع خلال 60 يوم',
                'sequence' => 4,
                'company_id' => 1
            ),
            array(
                'id' => 5,
                'name' => '50% فوري - 50% خلال 30 يوم',
                'active' => true,
                'note' => 'دفع مقسط على دفعتين',
                'sequence' => 5,
                'company_id' => 1
            )
        );
    }
}

/**
 * نموذج بنود شروط الدفع
 */
class AccountPaymentTermLine extends BaseModel {
    protected $table = 'account_payment_term_line';
    
    protected $fillable = [
        'payment_id', 'value', 'value_amount', 'sequence', 'days', 'option'
    ];
    
    protected $casts = [
        'payment_id' => 'integer',
        'value_amount' => 'decimal',
        'sequence' => 'integer',
        'days' => 'integer'
    ];
    
    // العلاقات
    public function payment_term() {
        return $this->belongsTo('AccountPaymentTerm', 'payment_id');
    }
    
    /**
     * الحصول على بيانات تجريبية
     */
    public function get_demo_data() {
        return array(
            // شروط الدفع الفوري
            array(
                'id' => 1,
                'payment_id' => 1,
                'value' => 'percent',
                'value_amount' => 100.0,
                'sequence' => 1,
                'days' => 0,
                'option' => 'day_after_invoice_date'
            ),
            
            // شروط الدفع خلال 15 يوم
            array(
                'id' => 2,
                'payment_id' => 2,
                'value' => 'percent',
                'value_amount' => 100.0,
                'sequence' => 1,
                'days' => 15,
                'option' => 'day_after_invoice_date'
            ),
            
            // شروط الدفع خلال 30 يوم
            array(
                'id' => 3,
                'payment_id' => 3,
                'value' => 'percent',
                'value_amount' => 100.0,
                'sequence' => 1,
                'days' => 30,
                'option' => 'day_after_invoice_date'
            ),
            
            // شروط الدفع خلال 60 يوم
            array(
                'id' => 4,
                'payment_id' => 4,
                'value' => 'percent',
                'value_amount' => 100.0,
                'sequence' => 1,
                'days' => 60,
                'option' => 'day_after_invoice_date'
            ),
            
            // شروط الدفع المقسط - الدفعة الأولى
            array(
                'id' => 5,
                'payment_id' => 5,
                'value' => 'percent',
                'value_amount' => 50.0,
                'sequence' => 1,
                'days' => 0,
                'option' => 'day_after_invoice_date'
            ),
            
            // شروط الدفع المقسط - الدفعة الثانية
            array(
                'id' => 6,
                'payment_id' => 5,
                'value' => 'percent',
                'value_amount' => 50.0,
                'sequence' => 2,
                'days' => 30,
                'option' => 'day_after_invoice_date'
            )
        );
    }
}
?>
