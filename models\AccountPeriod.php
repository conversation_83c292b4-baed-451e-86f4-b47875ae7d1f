<?php
/**
 * نموذج الفترات المحاسبية بأسلوب Odoo
 * Accounting Periods Model - Odoo Style
 */

require_once 'BaseModel.php';

class AccountPeriod extends BaseModel {
    protected $table = 'account_period';
    
    protected $fillable = [
        'name', 'code', 'date_start', 'date_end', 'state', 'company_id', 'fiscalyear_id'
    ];
    
    protected $casts = [
        'date_start' => 'date',
        'date_end' => 'date',
        'company_id' => 'integer',
        'fiscalyear_id' => 'integer'
    ];
    
    // العلاقات
    public function company() {
        return $this->belongsTo('ResCompany', 'company_id');
    }
    
    public function fiscalyear() {
        return $this->belongsTo('AccountFiscalyear', 'fiscalyear_id');
    }
    
    public function moves() {
        return $this->hasMany('AccountMove', 'period_id');
    }
    
    // الدوال المساعدة
    
    /**
     * الحصول على الفترات المفتوحة
     */
    public function get_open_periods() {
        return $this->search_read(
            array(array('state', '=', 'open')),
            null,
            array('order' => 'date_start ASC')
        );
    }
    
    /**
     * الحصول على الفترات المقفلة
     */
    public function get_closed_periods() {
        return $this->search_read(
            array(array('state', '=', 'closed')),
            null,
            array('order' => 'date_start DESC')
        );
    }
    
    /**
     * الحصول على الفترة الحالية
     */
    public function get_current_period($date = null) {
        if (!$date) {
            $date = date('Y-m-d');
        }
        
        $conditions = array(
            array('date_start', '<=', $date),
            array('date_end', '>=', $date),
            array('state', '=', 'open')
        );
        
        $periods = $this->search_read($conditions, null, array('limit' => 1));
        
        return count($periods) > 0 ? $periods[0] : null;
    }
    
    /**
     * التحقق من تداخل الفترات
     */
    public function check_period_overlap($date_start, $date_end, $exclude_id = null) {
        $conditions = array(
            'OR' => array(
                array(
                    'AND' => array(
                        array('date_start', '<=', $date_start),
                        array('date_end', '>=', $date_start)
                    )
                ),
                array(
                    'AND' => array(
                        array('date_start', '<=', $date_end),
                        array('date_end', '>=', $date_end)
                    )
                ),
                array(
                    'AND' => array(
                        array('date_start', '>=', $date_start),
                        array('date_end', '<=', $date_end)
                    )
                )
            )
        );
        
        if ($exclude_id) {
            $conditions['id'] = array('!=', $exclude_id);
        }
        
        $overlapping = $this->search_read($conditions);
        return count($overlapping) > 0;
    }
    
    /**
     * إنشاء فترة محاسبية جديدة
     */
    public function create_period($data) {
        // التحقق من صحة البيانات
        if (empty($data['name']) || empty($data['code']) || 
            empty($data['date_start']) || empty($data['date_end'])) {
            throw new Exception('جميع الحقول مطلوبة');
        }
        
        // التحقق من صحة التواريخ
        $start_date = strtotime($data['date_start']);
        $end_date = strtotime($data['date_end']);
        
        if ($start_date >= $end_date) {
            throw new Exception('تاريخ النهاية يجب أن يكون بعد تاريخ البداية');
        }
        
        // التحقق من عدم تداخل الفترات
        if ($this->check_period_overlap($data['date_start'], $data['date_end'])) {
            throw new Exception('هناك تداخل مع فترة محاسبية أخرى');
        }
        
        // التحقق من تفرد الكود
        $existing = $this->search_read(array(array('code', '=', $data['code'])));
        if (count($existing) > 0) {
            throw new Exception('كود الفترة موجود مسبقاً');
        }
        
        // تعيين القيم الافتراضية
        $data['state'] = $data['state'] ?? 'draft';
        $data['company_id'] = $data['company_id'] ?? 1;
        
        return $this->create($data);
    }
    
    /**
     * إقفال فترة محاسبية
     */
    public function close_period($period_id) {
        $period = $this->read($period_id);
        if (!$period) {
            throw new Exception('الفترة المحاسبية غير موجودة');
        }
        
        if ($period['state'] === 'closed') {
            throw new Exception('الفترة مقفلة مسبقاً');
        }
        
        // التحقق من وجود قيود غير معتمدة في الفترة
        require_once 'AccountMove.php';
        $move_model = new AccountMove($this->db);
        
        $draft_moves = $move_model->search_read(array(
            array('date', '>=', $period['date_start']),
            array('date', '<=', $period['date_end']),
            array('state', '=', 'draft')
        ));
        
        if (count($draft_moves) > 0) {
            throw new Exception('يوجد قيود غير معتمدة في هذه الفترة. يجب اعتماد جميع القيود قبل الإقفال');
        }
        
        return $this->update($period_id, array('state' => 'closed'));
    }
    
    /**
     * إعادة فتح فترة محاسبية
     */
    public function reopen_period($period_id) {
        $period = $this->read($period_id);
        if (!$period) {
            throw new Exception('الفترة المحاسبية غير موجودة');
        }
        
        if ($period['state'] !== 'closed') {
            throw new Exception('الفترة ليست مقفلة');
        }
        
        return $this->update($period_id, array('state' => 'open'));
    }
    
    /**
     * الحصول على إحصائيات الفترة
     */
    public function get_period_statistics($period_id) {
        $period = $this->read($period_id);
        if (!$period) {
            return null;
        }
        
        try {
            require_once 'AccountMove.php';
            $move_model = new AccountMove($this->db);
            
            // عدد القيود في الفترة
            $moves = $move_model->search_read(array(
                array('date', '>=', $period['date_start']),
                array('date', '<=', $period['date_end'])
            ));
            
            $total_moves = count($moves);
            $posted_moves = 0;
            $draft_moves = 0;
            $total_amount = 0;
            
            foreach ($moves as $move) {
                if ($move['state'] === 'posted') {
                    $posted_moves++;
                    $total_amount += $move['amount_total'];
                } elseif ($move['state'] === 'draft') {
                    $draft_moves++;
                }
            }
            
            return array(
                'total_moves' => $total_moves,
                'posted_moves' => $posted_moves,
                'draft_moves' => $draft_moves,
                'total_amount' => $total_amount,
                'period_days' => (strtotime($period['date_end']) - strtotime($period['date_start'])) / (24 * 60 * 60) + 1
            );
            
        } catch (Exception $e) {
            return array(
                'total_moves' => 0,
                'posted_moves' => 0,
                'draft_moves' => 0,
                'total_amount' => 0,
                'period_days' => 0
            );
        }
    }
    
    /**
     * الحصول على بيانات تجريبية
     */
    public function get_demo_data() {
        // محاولة الحصول على البيانات من قاعدة البيانات أولاً
        try {
            $periods = $this->search_read(
                array(),
                null,
                array('order' => 'date_start ASC')
            );
            
            if (count($periods) > 0) {
                return $periods;
            }
        } catch (Exception $e) {
            // في حالة عدم وجود قاعدة البيانات
        }
        
        // بيانات تجريبية احتياطية
        return array(
            array(
                'id' => 1,
                'name' => 'يناير 2024',
                'code' => '2024-01',
                'date_start' => '2024-01-01',
                'date_end' => '2024-01-31',
                'state' => 'closed',
                'company_id' => 1,
                'fiscalyear_id' => 1
            ),
            array(
                'id' => 2,
                'name' => 'فبراير 2024',
                'code' => '2024-02',
                'date_start' => '2024-02-01',
                'date_end' => '2024-02-29',
                'state' => 'closed',
                'company_id' => 1,
                'fiscalyear_id' => 1
            ),
            array(
                'id' => 3,
                'name' => 'مارس 2024',
                'code' => '2024-03',
                'date_start' => '2024-03-01',
                'date_end' => '2024-03-31',
                'state' => 'closed',
                'company_id' => 1,
                'fiscalyear_id' => 1
            ),
            array(
                'id' => 4,
                'name' => 'أبريل 2024',
                'code' => '2024-04',
                'date_start' => '2024-04-01',
                'date_end' => '2024-04-30',
                'state' => 'open',
                'company_id' => 1,
                'fiscalyear_id' => 1
            ),
            array(
                'id' => 5,
                'name' => 'مايو 2024',
                'code' => '2024-05',
                'date_start' => '2024-05-01',
                'date_end' => '2024-05-31',
                'state' => 'open',
                'company_id' => 1,
                'fiscalyear_id' => 1
            ),
            array(
                'id' => 6,
                'name' => 'يونيو 2024',
                'code' => '2024-06',
                'date_start' => '2024-06-01',
                'date_end' => '2024-06-30',
                'state' => 'draft',
                'company_id' => 1,
                'fiscalyear_id' => 1
            )
        );
    }
}
?>
