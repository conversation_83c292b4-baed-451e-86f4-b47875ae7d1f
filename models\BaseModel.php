<?php
/**
 * النموذج الأساسي - بأسلوب Odoo
 * Base Model Class
 */

class BaseModel {
    protected $db;
    protected $table;
    protected $fields = array();
    protected $required_fields = array();
    protected $readonly_fields = array();
    protected $default_values = array();
    
    // Odoo-style system fields
    protected $system_fields = array(
        'id', 'create_uid', 'write_uid', 'create_date', 'write_date'
    );
    
    public function __construct($db = null) {
        global $database;
        $this->db = $db ? $db : $database;
        $this->init();
    }
    
    /**
     * تهيئة النموذج
     */
    protected function init() {
        // Override in child classes
    }
    
    /**
     * إنشاء سجل جديد (create في Odoo)
     */
    public function create($values) {
        // تنظيف البيانات
        $values = $this->prepare_values($values);
        
        // إضافة القيم الافتراضية
        $values = array_merge($this->get_default_values(), $values);
        
        // إضافة الحقول النظامية
        $values['create_uid'] = $this->get_current_user_id();
        $values['write_uid'] = $this->get_current_user_id();
        $values['create_date'] = date('Y-m-d H:i:s');
        $values['write_date'] = date('Y-m-d H:i:s');
        
        // التحقق من الحقول المطلوبة
        $this->validate_required_fields($values);
        
        // تنفيذ الإدراج
        $id = $this->db->insert($this->table, $values);
        
        if ($id) {
            // تنفيذ hooks بعد الإنشاء
            $this->post_create($id, $values);
            return $id;
        }
        
        return false;
    }
    
    /**
     * قراءة السجلات (read في Odoo)
     */
    public function read($ids, $fields = null) {
        if (!is_array($ids)) {
            $ids = array($ids);
        }
        
        $fields_str = $fields ? implode(',', $fields) : '*';
        $ids_str = implode(',', array_map('intval', $ids));
        
        $sql = "SELECT {$fields_str} FROM {$this->table} WHERE id IN ({$ids_str})";
        return $this->db->fetchAll($sql);
    }
    
    /**
     * البحث (search في Odoo)
     */
    public function search($domain = array(), $options = array()) {
        $where_clause = $this->build_where_clause($domain);
        $order_clause = isset($options['order']) ? "ORDER BY {$options['order']}" : '';
        $limit_clause = isset($options['limit']) ? "LIMIT {$options['limit']}" : '';
        $offset_clause = isset($options['offset']) ? "OFFSET {$options['offset']}" : '';
        
        $sql = "SELECT id FROM {$this->table} {$where_clause} {$order_clause} {$limit_clause} {$offset_clause}";
        $results = $this->db->fetchAll($sql);
        
        return array_column($results, 'id');
    }
    
    /**
     * البحث والقراءة (search_read في Odoo)
     */
    public function search_read($domain = array(), $fields = null, $options = array()) {
        $ids = $this->search($domain, $options);
        if (empty($ids)) {
            return array();
        }
        
        return $this->read($ids, $fields);
    }
    
    /**
     * تحديث السجلات (write في Odoo)
     */
    public function write($ids, $values) {
        if (!is_array($ids)) {
            $ids = array($ids);
        }
        
        // تنظيف البيانات
        $values = $this->prepare_values($values);
        
        // إضافة حقل التحديث
        $values['write_uid'] = $this->get_current_user_id();
        $values['write_date'] = date('Y-m-d H:i:s');
        
        $success = true;
        foreach ($ids as $id) {
            $where = "id = " . intval($id);
            $result = $this->db->update($this->table, $values, $where);
            if (!$result) {
                $success = false;
            } else {
                // تنفيذ hooks بعد التحديث
                $this->post_write($id, $values);
            }
        }
        
        return $success;
    }
    
    /**
     * حذف السجلات (unlink في Odoo)
     */
    public function unlink($ids) {
        if (!is_array($ids)) {
            $ids = array($ids);
        }
        
        $success = true;
        foreach ($ids as $id) {
            // تنفيذ hooks قبل الحذف
            $this->pre_unlink($id);
            
            $where = "id = " . intval($id);
            $result = $this->db->delete($this->table, $where);
            if (!$result) {
                $success = false;
            }
        }
        
        return $success;
    }
    
    /**
     * عد السجلات (search_count في Odoo)
     */
    public function search_count($domain = array()) {
        $where_clause = $this->build_where_clause($domain);
        $sql = "SELECT COUNT(*) as count FROM {$this->table} {$where_clause}";
        $result = $this->db->fetch($sql);
        return $result ? $result['count'] : 0;
    }
    
    /**
     * بناء جملة WHERE من domain
     */
    protected function build_where_clause($domain) {
        if (empty($domain)) {
            return 'WHERE 1=1';
        }
        
        $conditions = array();
        foreach ($domain as $condition) {
            if (is_array($condition) && count($condition) == 3) {
                list($field, $operator, $value) = $condition;
                $conditions[] = $this->build_condition($field, $operator, $value);
            }
        }
        
        return empty($conditions) ? 'WHERE 1=1' : 'WHERE ' . implode(' AND ', $conditions);
    }
    
    /**
     * بناء شرط واحد
     */
    protected function build_condition($field, $operator, $value) {
        $field = $this->db->quote($field);
        
        switch ($operator) {
            case '=':
                return "{$field} = " . $this->quote_value($value);
            case '!=':
                return "{$field} != " . $this->quote_value($value);
            case '>':
                return "{$field} > " . $this->quote_value($value);
            case '>=':
                return "{$field} >= " . $this->quote_value($value);
            case '<':
                return "{$field} < " . $this->quote_value($value);
            case '<=':
                return "{$field} <= " . $this->quote_value($value);
            case 'like':
                return "{$field} LIKE " . $this->quote_value($value);
            case 'ilike':
                return "LOWER({$field}) LIKE LOWER(" . $this->quote_value($value) . ")";
            case 'in':
                if (is_array($value)) {
                    $values = array_map(array($this, 'quote_value'), $value);
                    return "{$field} IN (" . implode(',', $values) . ")";
                }
                return "{$field} = " . $this->quote_value($value);
            case 'not in':
                if (is_array($value)) {
                    $values = array_map(array($this, 'quote_value'), $value);
                    return "{$field} NOT IN (" . implode(',', $values) . ")";
                }
                return "{$field} != " . $this->quote_value($value);
            default:
                return "{$field} = " . $this->quote_value($value);
        }
    }
    
    /**
     * تنظيف القيم
     */
    protected function prepare_values($values) {
        $clean_values = array();
        foreach ($values as $key => $value) {
            if (in_array($key, $this->readonly_fields)) {
                continue; // تخطي الحقول للقراءة فقط
            }
            $clean_values[$key] = $this->sanitize_value($value);
        }
        return $clean_values;
    }
    
    /**
     * تنظيف قيمة واحدة
     */
    protected function sanitize_value($value) {
        if (is_string($value)) {
            return htmlspecialchars(strip_tags(trim($value)));
        }
        return $value;
    }
    
    /**
     * اقتباس القيمة للاستعلام
     */
    protected function quote_value($value) {
        if (is_null($value)) {
            return 'NULL';
        }
        if (is_bool($value)) {
            return $value ? '1' : '0';
        }
        if (is_numeric($value)) {
            return $value;
        }
        return "'" . addslashes($value) . "'";
    }
    
    /**
     * الحصول على القيم الافتراضية
     */
    protected function get_default_values() {
        return $this->default_values;
    }
    
    /**
     * التحقق من الحقول المطلوبة
     */
    protected function validate_required_fields($values) {
        foreach ($this->required_fields as $field) {
            if (!isset($values[$field]) || empty($values[$field])) {
                throw new Exception("الحقل '{$field}' مطلوب");
            }
        }
    }
    
    /**
     * الحصول على معرف المستخدم الحالي
     */
    protected function get_current_user_id() {
        return isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 1;
    }
    
    /**
     * Hooks للتخصيص
     */
    protected function post_create($id, $values) {
        // Override in child classes
    }
    
    protected function post_write($id, $values) {
        // Override in child classes
    }
    
    protected function pre_unlink($id) {
        // Override in child classes
    }
}
?>
