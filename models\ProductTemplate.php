<?php
/**
 * نموذج قوالب المنتجات (product.template في Odoo)
 * Product Template Model
 */

require_once 'BaseModel.php';

class ProductTemplate extends BaseModel {
    
    protected function init() {
        $this->table = 'product_template';
        
        $this->fields = array(
            'id', 'name', 'sequence', 'description', 'description_purchase', 'description_sale',
            'type', 'rental', 'categ_id', 'list_price', 'volume', 'weight',
            'sale_ok', 'purchase_ok', 'uom_id', 'uom_po_id', 'company_id',
            'active', 'color', 'image_1920', 'can_image_1024_be_zoomed',
            'has_configurable_attributes', 'create_uid', 'write_uid', 'create_date', 'write_date'
        );
        
        $this->required_fields = array('name', 'categ_id', 'uom_id', 'uom_po_id');
        
        $this->readonly_fields = array('id', 'create_uid', 'create_date');
        
        $this->default_values = array(
            'active' => true,
            'sequence' => 1,
            'type' => 'consu',
            'rental' => false,
            'list_price' => 0.00,
            'volume' => 0.000,
            'weight' => 0.000,
            'sale_ok' => true,
            'purchase_ok' => true,
            'color' => 0,
            'can_image_1024_be_zoomed' => false,
            'has_configurable_attributes' => false
        );
    }
    
    /**
     * إنشاء منتج جديد
     */
    public function create($values) {
        // التحقق من وجود الفئة
        if (isset($values['categ_id'])) {
            $this->validate_category($values['categ_id']);
        }
        
        // التحقق من وحدات القياس
        if (isset($values['uom_id'])) {
            $this->validate_uom($values['uom_id']);
        }
        if (isset($values['uom_po_id'])) {
            $this->validate_uom($values['uom_po_id']);
        }
        
        $template_id = parent::create($values);
        
        if ($template_id) {
            // إنشاء منتج افتراضي
            $this->create_default_product($template_id, $values);
        }
        
        return $template_id;
    }
    
    /**
     * إنشاء منتج افتراضي
     */
    private function create_default_product($template_id, $template_values) {
        require_once 'ProductProduct.php';
        $product_model = new ProductProduct($this->db);
        
        $product_values = array(
            'product_tmpl_id' => $template_id,
            'active' => isset($template_values['active']) ? $template_values['active'] : true
        );
        
        // إضافة الكود الداخلي إذا كان موجوداً
        if (isset($template_values['default_code'])) {
            $product_values['default_code'] = $template_values['default_code'];
        }
        
        // إضافة الباركود إذا كان موجوداً
        if (isset($template_values['barcode'])) {
            $product_values['barcode'] = $template_values['barcode'];
        }
        
        return $product_model->create($product_values);
    }
    
    /**
     * البحث عن المنتجات القابلة للبيع
     */
    public function search_saleable($domain = array(), $options = array()) {
        $sale_domain = array(array('sale_ok', '=', true));
        $domain = array_merge($sale_domain, $domain);
        
        return $this->search_read($domain, null, $options);
    }
    
    /**
     * البحث عن المنتجات القابلة للشراء
     */
    public function search_purchaseable($domain = array(), $options = array()) {
        $purchase_domain = array(array('purchase_ok', '=', true));
        $domain = array_merge($purchase_domain, $domain);
        
        return $this->search_read($domain, null, $options);
    }
    
    /**
     * الحصول على منتجات الفئة
     */
    public function get_category_products($category_id, $options = array()) {
        $domain = array(array('categ_id', '=', $category_id));
        return $this->search_read($domain, null, $options);
    }
    
    /**
     * تحديث السعر
     */
    public function update_price($template_ids, $new_price) {
        if (!is_array($template_ids)) {
            $template_ids = array($template_ids);
        }
        
        return $this->write($template_ids, array('list_price' => $new_price));
    }
    
    /**
     * الحصول على معلومات المخزون
     */
    public function get_stock_info($template_id) {
        // في النسخة المبسطة، نعيد بيانات وهمية
        // في النسخة الكاملة، سيتم الربط مع نظام المخزون
        
        $sql = "SELECT pp.id as product_id, pp.default_code, pt.name
                FROM product_product pp
                JOIN product_template pt ON pp.product_tmpl_id = pt.id
                WHERE pt.id = ?";
        
        $products = $this->db->fetchAll($sql, array($template_id));
        
        $stock_info = array();
        foreach ($products as $product) {
            $stock_info[] = array(
                'product_id' => $product['product_id'],
                'product_code' => $product['default_code'],
                'product_name' => $product['name'],
                'qty_available' => rand(0, 100), // بيانات وهمية
                'qty_reserved' => rand(0, 10),
                'qty_incoming' => rand(0, 20),
                'qty_outgoing' => rand(0, 15)
            );
        }
        
        return $stock_info;
    }
    
    /**
     * البحث بالاسم أو الكود
     */
    public function name_search($name, $domain = array(), $limit = 10) {
        $search_domain = array(
            '|', 
            array('name', 'ilike', '%' . $name . '%'),
            '|',
            array('default_code', 'ilike', '%' . $name . '%'),
            array('barcode', 'ilike', '%' . $name . '%')
        );
        
        if (!empty($domain)) {
            $search_domain = array_merge($domain, $search_domain);
        }
        
        // البحث في جدول المنتجات أيضاً
        $sql = "SELECT DISTINCT pt.id, pt.name, pp.default_code, pp.barcode
                FROM product_template pt
                LEFT JOIN product_product pp ON pt.id = pp.product_tmpl_id
                WHERE (pt.name LIKE ? OR pp.default_code LIKE ? OR pp.barcode LIKE ?)
                AND pt.active = 1
                ORDER BY pt.name ASC
                LIMIT ?";
        
        $search_term = '%' . $name . '%';
        $results = $this->db->fetchAll($sql, array($search_term, $search_term, $search_term, $limit));
        
        $formatted_results = array();
        foreach ($results as $result) {
            $display_name = $result['name'];
            if (!empty($result['default_code'])) {
                $display_name .= ' [' . $result['default_code'] . ']';
            }
            $formatted_results[] = array($result['id'], $display_name);
        }
        
        return $formatted_results;
    }
    
    /**
     * الحصول على تكلفة المنتج
     */
    public function get_product_cost($template_id) {
        // في النسخة المبسطة، نحسب متوسط تكلفة الشراء
        $sql = "SELECT AVG(price_unit) as avg_cost
                FROM purchase_order_line pol
                JOIN purchase_order po ON pol.order_id = po.id
                WHERE pol.product_id IN (
                    SELECT id FROM product_product WHERE product_tmpl_id = ?
                )
                AND po.state = 'purchase'
                AND pol.create_date >= DATE_SUB(NOW(), INTERVAL 6 MONTH)";
        
        $result = $this->db->fetch($sql, array($template_id));
        
        return $result ? floatval($result['avg_cost']) : 0.0;
    }
    
    /**
     * تحديث معلومات المنتج
     */
    public function update_product_info($template_id, $info) {
        $allowed_fields = array(
            'description', 'description_sale', 'description_purchase',
            'weight', 'volume', 'list_price'
        );
        
        $update_data = array();
        foreach ($info as $field => $value) {
            if (in_array($field, $allowed_fields)) {
                $update_data[$field] = $value;
            }
        }
        
        if (!empty($update_data)) {
            return $this->write(array($template_id), $update_data);
        }
        
        return true;
    }
    
    /**
     * التحقق من صحة الفئة
     */
    private function validate_category($category_id) {
        $sql = "SELECT id FROM product_category WHERE id = ? AND active = 1";
        $result = $this->db->fetch($sql, array($category_id));
        
        if (!$result) {
            throw new Exception('فئة المنتج غير موجودة أو غير نشطة');
        }
        
        return true;
    }
    
    /**
     * التحقق من صحة وحدة القياس
     */
    private function validate_uom($uom_id) {
        $sql = "SELECT id FROM uom_uom WHERE id = ? AND active = 1";
        $result = $this->db->fetch($sql, array($uom_id));
        
        if (!$result) {
            throw new Exception('وحدة القياس غير موجودة أو غير نشطة');
        }
        
        return true;
    }
    
    /**
     * الحصول على المنتجات الأكثر مبيعاً
     */
    public function get_best_sellers($limit = 10) {
        $sql = "SELECT pt.id, pt.name, SUM(sol.product_uom_qty) as total_sold
                FROM product_template pt
                JOIN product_product pp ON pt.id = pp.product_tmpl_id
                JOIN sale_order_line sol ON pp.id = sol.product_id
                JOIN sale_order so ON sol.order_id = so.id
                WHERE so.state = 'sale'
                AND so.date_order >= DATE_SUB(NOW(), INTERVAL 3 MONTH)
                GROUP BY pt.id, pt.name
                ORDER BY total_sold DESC
                LIMIT ?";
        
        return $this->db->fetchAll($sql, array($limit));
    }
    
    /**
     * hooks بعد الإنشاء
     */
    protected function post_create($id, $values) {
        // تحديث تسلسل الفئة إذا لزم الأمر
        if (isset($values['categ_id'])) {
            $this->update_category_sequence($values['categ_id']);
        }
    }
    
    /**
     * تحديث تسلسل الفئة
     */
    private function update_category_sequence($category_id) {
        $sql = "UPDATE product_category 
                SET product_count = (
                    SELECT COUNT(*) 
                    FROM product_template 
                    WHERE categ_id = ? AND active = 1
                )
                WHERE id = ?";
        
        $this->db->query($sql, array($category_id, $category_id));
    }
}
?>
