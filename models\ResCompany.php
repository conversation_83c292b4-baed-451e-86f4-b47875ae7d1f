<?php
/**
 * نموذج الشركات (res.company في Odoo)
 * Company Model
 */

require_once 'BaseModel.php';

class ResCompany extends BaseModel {
    
    protected function init() {
        $this->table = 'res_company';
        
        $this->fields = array(
            'id', 'name', 'display_name', 'code', 'tax_number', 'commercial_register',
            'phone', 'mobile', 'email', 'website', 'address', 'street', 'street2',
            'city', 'state', 'country', 'postal_code', 'logo', 'currency_id',
            'parent_id', 'active', 'sequence', 'create_uid', 'write_uid',
            'create_date', 'write_date'
        );
        
        $this->required_fields = array('name');
        
        $this->readonly_fields = array('id', 'create_uid', 'create_date');
        
        $this->default_values = array(
            'active' => true,
            'sequence' => 10,
            'country' => 'السعودية',
            'currency_id' => 1
        );
    }
    
    /**
     * إنشاء اسم العرض
     */
    public function create($values) {
        if (isset($values['name']) && !isset($values['display_name'])) {
            $values['display_name'] = $values['name'];
        }
        
        return parent::create($values);
    }
    
    /**
     * تحديث اسم العرض عند التحديث
     */
    public function write($ids, $values) {
        if (isset($values['name'])) {
            $values['display_name'] = $values['name'];
        }
        
        return parent::write($ids, $values);
    }
    
    /**
     * الحصول على الشركة الافتراضية
     */
    public function get_default_company() {
        $companies = $this->search_read(array(array('active', '=', true)), null, array('limit' => 1, 'order' => 'sequence ASC'));
        return !empty($companies) ? $companies[0] : null;
    }
    
    /**
     * الحصول على شركات المستخدم
     */
    public function get_user_companies($user_id) {
        // في النسخة المبسطة، نعيد جميع الشركات النشطة
        return $this->search_read(array(array('active', '=', true)), null, array('order' => 'sequence ASC'));
    }
    
    /**
     * التحقق من صحة الرقم الضريبي
     */
    protected function validate_tax_number($tax_number) {
        if (empty($tax_number)) {
            return true; // اختياري
        }
        
        // التحقق من الرقم الضريبي السعودي (15 رقم)
        if (!preg_match('/^[0-9]{15}$/', $tax_number)) {
            throw new Exception('الرقم الضريبي يجب أن يكون 15 رقماً');
        }
        
        return true;
    }
    
    /**
     * التحقق من تفرد الكود
     */
    protected function validate_unique_code($code, $exclude_id = null) {
        if (empty($code)) {
            return true;
        }
        
        $domain = array(array('code', '=', $code));
        if ($exclude_id) {
            $domain[] = array('id', '!=', $exclude_id);
        }
        
        $count = $this->search_count($domain);
        if ($count > 0) {
            throw new Exception('كود الشركة موجود مسبقاً');
        }
        
        return true;
    }
    
    /**
     * hooks التخصيص
     */
    protected function post_create($id, $values) {
        // إنشاء اليوميات الافتراضية للشركة
        $this->create_default_journals($id);
        
        // إنشاء الحسابات الافتراضية
        $this->create_default_accounts($id);
    }
    
    /**
     * إنشاء اليوميات الافتراضية
     */
    private function create_default_journals($company_id) {
        require_once 'AccountJournal.php';
        $journal_model = new AccountJournal($this->db);
        
        $default_journals = array(
            array(
                'name' => 'يومية المبيعات',
                'code' => 'SAL',
                'type' => 'sale',
                'company_id' => $company_id,
                'sequence' => 1
            ),
            array(
                'name' => 'يومية المشتريات',
                'code' => 'PUR',
                'type' => 'purchase',
                'company_id' => $company_id,
                'sequence' => 2
            ),
            array(
                'name' => 'يومية النقدية',
                'code' => 'CSH',
                'type' => 'cash',
                'company_id' => $company_id,
                'sequence' => 3
            ),
            array(
                'name' => 'يومية البنك',
                'code' => 'BNK',
                'type' => 'bank',
                'company_id' => $company_id,
                'sequence' => 4
            ),
            array(
                'name' => 'يومية عامة',
                'code' => 'MISC',
                'type' => 'general',
                'company_id' => $company_id,
                'sequence' => 5
            )
        );
        
        foreach ($default_journals as $journal_data) {
            try {
                $journal_model->create($journal_data);
            } catch (Exception $e) {
                // تسجيل الخطأ ومتابعة
                error_log("خطأ في إنشاء اليومية: " . $e->getMessage());
            }
        }
    }
    
    /**
     * إنشاء الحسابات الافتراضية
     */
    private function create_default_accounts($company_id) {
        require_once 'AccountAccount.php';
        $account_model = new AccountAccount($this->db);
        
        $default_accounts = array(
            array(
                'name' => 'العملاء',
                'code' => '1210',
                'user_type_id' => 1, // receivable
                'internal_type' => 'receivable',
                'reconcile' => true,
                'company_id' => $company_id
            ),
            array(
                'name' => 'الموردين',
                'code' => '2110',
                'user_type_id' => 2, // payable
                'internal_type' => 'payable',
                'reconcile' => true,
                'company_id' => $company_id
            ),
            array(
                'name' => 'النقدية',
                'code' => '1110',
                'user_type_id' => 3, // liquidity
                'internal_type' => 'liquidity',
                'company_id' => $company_id
            ),
            array(
                'name' => 'المبيعات',
                'code' => '4110',
                'user_type_id' => 4, // income
                'internal_type' => 'other',
                'company_id' => $company_id
            ),
            array(
                'name' => 'تكلفة البضاعة المباعة',
                'code' => '5110',
                'user_type_id' => 5, // expense
                'internal_type' => 'other',
                'company_id' => $company_id
            )
        );
        
        foreach ($default_accounts as $account_data) {
            try {
                $account_model->create($account_data);
            } catch (Exception $e) {
                // تسجيل الخطأ ومتابعة
                error_log("خطأ في إنشاء الحساب: " . $e->getMessage());
            }
        }
    }
    
    /**
     * الحصول على معلومات الشركة للتقارير
     */
    public function get_company_info($company_id) {
        $company = $this->read(array($company_id));
        if (empty($company)) {
            return null;
        }
        
        $company_data = $company[0];
        
        // إضافة معلومات إضافية
        $company_data['formatted_address'] = $this->format_address($company_data);
        $company_data['display_phone'] = $this->format_phone($company_data);
        
        return $company_data;
    }
    
    /**
     * تنسيق العنوان
     */
    private function format_address($company) {
        $address_parts = array();
        
        if (!empty($company['street'])) {
            $address_parts[] = $company['street'];
        }
        if (!empty($company['street2'])) {
            $address_parts[] = $company['street2'];
        }
        if (!empty($company['city'])) {
            $address_parts[] = $company['city'];
        }
        if (!empty($company['postal_code'])) {
            $address_parts[] = $company['postal_code'];
        }
        if (!empty($company['country'])) {
            $address_parts[] = $company['country'];
        }
        
        return implode(', ', $address_parts);
    }
    
    /**
     * تنسيق رقم الهاتف
     */
    private function format_phone($company) {
        $phones = array();
        
        if (!empty($company['phone'])) {
            $phones[] = 'هاتف: ' . $company['phone'];
        }
        if (!empty($company['mobile'])) {
            $phones[] = 'جوال: ' . $company['mobile'];
        }
        
        return implode(' | ', $phones);
    }
    
    /**
     * التحقق قبل الحذف
     */
    protected function pre_unlink($id) {
        // التحقق من وجود مستخدمين مرتبطين بالشركة
        $sql = "SELECT COUNT(*) as count FROM res_users WHERE company_id = ?";
        $result = $this->db->fetch($sql, array($id));
        
        if ($result && $result['count'] > 0) {
            throw new Exception('لا يمكن حذف الشركة لوجود مستخدمين مرتبطين بها');
        }
        
        // التحقق من وجود معاملات مالية
        $sql = "SELECT COUNT(*) as count FROM account_move WHERE company_id = ?";
        $result = $this->db->fetch($sql, array($id));
        
        if ($result && $result['count'] > 0) {
            throw new Exception('لا يمكن حذف الشركة لوجود معاملات مالية مرتبطة بها');
        }
    }

    /**
     * الحصول على بيانات تجريبية للشركات
     */
    public function get_demo_data() {
        return array(
            array(
                'id' => 1,
                'name' => 'شركتي',
                'display_name' => 'شركتي',
                'code' => 'MYCO',
                'tax_number' => '***************',
                'commercial_register' => '**********',
                'phone' => '+************',
                'mobile' => '+************',
                'email' => '<EMAIL>',
                'website' => 'www.mycompany.com',
                'address' => 'الرياض، المملكة العربية السعودية',
                'street' => 'شارع الملك فهد',
                'street2' => 'حي العليا',
                'city' => 'الرياض',
                'state' => 'منطقة الرياض',
                'country' => 'السعودية',
                'postal_code' => '11564',
                'currency_id' => 1,
                'active' => 1,
                'sequence' => 10,
                'create_uid' => 1,
                'write_uid' => 1,
                'create_date' => date('Y-m-d H:i:s'),
                'write_date' => date('Y-m-d H:i:s')
            ),
            array(
                'id' => 2,
                'name' => 'شركة الخليج للتجارة',
                'display_name' => 'شركة الخليج للتجارة',
                'code' => 'GULF',
                'tax_number' => '987654321098765',
                'commercial_register' => '2020987654',
                'phone' => '+************',
                'mobile' => '+************',
                'email' => '<EMAIL>',
                'website' => 'www.gulf-trading.com',
                'address' => 'جدة، المملكة العربية السعودية',
                'street' => 'شارع الأمير محمد بن عبدالعزيز',
                'street2' => 'حي الروضة',
                'city' => 'جدة',
                'state' => 'منطقة مكة المكرمة',
                'country' => 'السعودية',
                'postal_code' => '21432',
                'currency_id' => 1,
                'active' => 1,
                'sequence' => 20,
                'create_uid' => 1,
                'write_uid' => 1,
                'create_date' => date('Y-m-d H:i:s'),
                'write_date' => date('Y-m-d H:i:s')
            ),
            array(
                'id' => 3,
                'name' => 'مؤسسة الشرق للمقاولات',
                'display_name' => 'مؤسسة الشرق للمقاولات',
                'code' => 'EAST',
                'tax_number' => '456789123456789',
                'commercial_register' => '3030456789',
                'phone' => '+************',
                'mobile' => '+************',
                'email' => '<EMAIL>',
                'website' => 'www.east-contracting.com',
                'address' => 'الدمام، المملكة العربية السعودية',
                'street' => 'شارع الملك عبدالعزيز',
                'street2' => 'حي الفيصلية',
                'city' => 'الدمام',
                'state' => 'المنطقة الشرقية',
                'country' => 'السعودية',
                'postal_code' => '31952',
                'currency_id' => 1,
                'active' => 1,
                'sequence' => 30,
                'create_uid' => 1,
                'write_uid' => 1,
                'create_date' => date('Y-m-d H:i:s'),
                'write_date' => date('Y-m-d H:i:s')
            )
        );
    }

    /**
     * تحديث دالة search_read للعمل مع البيانات التجريبية والحقيقية
     */
    public function search_read($domain = array(), $fields = null, $options = array()) {
        // إذا كانت قاعدة البيانات في الوضع التجريبي، استخدم البيانات التجريبية
        if ($this->db && $this->db->isDemoMode()) {
            $demo_data = $this->get_demo_data();

            // تطبيق الفلاتر
            if (!empty($domain)) {
                $filtered_data = array();
                foreach ($demo_data as $record) {
                    $match = true;
                    foreach ($domain as $condition) {
                        if (is_array($condition) && count($condition) == 3) {
                            list($field, $operator, $value) = $condition;
                            if (!$this->evaluate_condition($record, $field, $operator, $value)) {
                                $match = false;
                                break;
                            }
                        }
                    }
                    if ($match) {
                        $filtered_data[] = $record;
                    }
                }
                $demo_data = $filtered_data;
            }

            // تطبيق الترتيب
            if (isset($options['order'])) {
                $order_parts = explode(' ', $options['order']);
                $field = $order_parts[0];
                $direction = isset($order_parts[1]) ? strtoupper($order_parts[1]) : 'ASC';

                // ترتيب بسيط
                if ($direction === 'DESC') {
                    usort($demo_data, array($this, 'sort_desc_callback'));
                } else {
                    usort($demo_data, array($this, 'sort_asc_callback'));
                }

                // حفظ معلومات الترتيب للاستخدام في الدوال
                $this->sort_field = $field;
            }

            // تطبيق الحد الأقصى
            if (isset($options['limit'])) {
                $demo_data = array_slice($demo_data, 0, $options['limit']);
            }

            return $demo_data;
        }

        // استخدام قاعدة البيانات الحقيقية
        return parent::search_read($domain, $fields, $options);
    }

    /**
     * تقييم شرط واحد للبيانات التجريبية
     */
    private function evaluate_condition($record, $field, $operator, $value) {
        if (!isset($record[$field])) {
            return false;
        }

        $field_value = $record[$field];

        switch ($operator) {
            case '=':
                return $field_value == $value;
            case '!=':
                return $field_value != $value;
            case '>':
                return $field_value > $value;
            case '>=':
                return $field_value >= $value;
            case '<':
                return $field_value < $value;
            case '<=':
                return $field_value <= $value;
            case 'like':
                return strpos(strtolower($field_value), strtolower($value)) !== false;
            case 'ilike':
                return strpos(strtolower($field_value), strtolower($value)) !== false;
            case 'in':
                return in_array($field_value, (array)$value);
            case 'not in':
                return !in_array($field_value, (array)$value);
            default:
                return false;
        }
    }

    /**
     * متغير مساعد للترتيب
     */
    private $sort_field = 'id';

    /**
     * دالة ترتيب تصاعدي
     */
    private function sort_asc_callback($a, $b) {
        $field = $this->sort_field;
        if (isset($a[$field]) && isset($b[$field])) {
            if (is_numeric($a[$field]) && is_numeric($b[$field])) {
                return $a[$field] - $b[$field];
            } else {
                return strcmp($a[$field], $b[$field]);
            }
        }
        return 0;
    }

    /**
     * دالة ترتيب تنازلي
     */
    private function sort_desc_callback($a, $b) {
        $field = $this->sort_field;
        if (isset($a[$field]) && isset($b[$field])) {
            if (is_numeric($a[$field]) && is_numeric($b[$field])) {
                return $b[$field] - $a[$field];
            } else {
                return strcmp($b[$field], $a[$field]);
            }
        }
        return 0;
    }
}
?>
