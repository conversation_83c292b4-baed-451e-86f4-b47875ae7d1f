<?php
/**
 * نموذج العملات بأسلوب Odoo
 * Currency Model - Odoo Style
 */

require_once 'BaseModel.php';

class ResCurrency extends BaseModel {
    protected $table = 'res_currency';
    
    protected $fillable = [
        'name', 'symbol', 'rate', 'active', 'position'
    ];
    
    protected $casts = [
        'rate' => 'decimal',
        'active' => 'boolean'
    ];
    
    // الدوال المساعدة
    
    /**
     * تنسيق المبلغ بالعملة
     */
    public function format_amount($amount, $currency_id = 1) {
        $currency = $this->read($currency_id);
        
        if (!$currency) {
            // العملة الافتراضية
            $currency = array(
                'symbol' => 'ر.س',
                'position' => 'after'
            );
        }
        
        $formatted_amount = number_format($amount, 2);
        
        if ($currency['position'] === 'before') {
            return $currency['symbol'] . ' ' . $formatted_amount;
        } else {
            return $formatted_amount . ' ' . $currency['symbol'];
        }
    }
    
    /**
     * تحويل المبلغ بين العملات
     */
    public function convert_amount($amount, $from_currency_id, $to_currency_id) {
        if ($from_currency_id == $to_currency_id) {
            return $amount;
        }
        
        $from_currency = $this->read($from_currency_id);
        $to_currency = $this->read($to_currency_id);
        
        if (!$from_currency || !$to_currency) {
            return $amount;
        }
        
        // تحويل إلى العملة الأساسية أولاً ثم إلى العملة المطلوبة
        $base_amount = $amount / $from_currency['rate'];
        return $base_amount * $to_currency['rate'];
    }
    
    /**
     * الحصول على العملة الافتراضية
     */
    public function get_default_currency() {
        $currencies = $this->search_read(
            array(array('active', '=', true)),
            null,
            array('order' => 'id ASC', 'limit' => 1)
        );
        
        if (count($currencies) > 0) {
            return $currencies[0];
        }
        
        // العملة الافتراضية
        return array(
            'id' => 1,
            'name' => 'الريال السعودي',
            'symbol' => 'ر.س',
            'rate' => 1.000000,
            'position' => 'after',
            'active' => true
        );
    }
    
    /**
     * الحصول على بيانات تجريبية
     */
    public function get_demo_data() {
        // محاولة الحصول على البيانات من قاعدة البيانات أولاً
        try {
            $currencies = $this->search_read(
                array(array('active', '=', true)),
                null,
                array('order' => 'name ASC')
            );
            
            if (count($currencies) > 0) {
                return $currencies;
            }
        } catch (Exception $e) {
            // في حالة عدم وجود قاعدة البيانات
        }
        
        // بيانات تجريبية احتياطية
        return array(
            array(
                'id' => 1,
                'name' => 'الريال السعودي',
                'symbol' => 'ر.س',
                'rate' => 1.000000,
                'active' => true,
                'position' => 'after'
            ),
            array(
                'id' => 2,
                'name' => 'الدولار الأمريكي',
                'symbol' => '$',
                'rate' => 3.750000,
                'active' => true,
                'position' => 'before'
            ),
            array(
                'id' => 3,
                'name' => 'اليورو',
                'symbol' => '€',
                'rate' => 4.100000,
                'active' => true,
                'position' => 'before'
            ),
            array(
                'id' => 4,
                'name' => 'الجنيه الإسترليني',
                'symbol' => '£',
                'rate' => 4.800000,
                'active' => true,
                'position' => 'before'
            )
        );
    }
}
?>
