<?php
/**
 * نموذج الشركاء (res.partner في Odoo)
 * Partner Model - العملاء والموردين
 */

require_once 'BaseModel.php';

class ResPartner extends BaseModel {
    
    protected function init() {
        $this->table = 'res_partner';
        
        $this->fields = array(
            'id', 'name', 'display_name', 'ref', 'lang', 'tz',
            'email', 'phone', 'mobile', 'website',
            'street', 'street2', 'city', 'state_id', 'country_id', 'zip',
            'is_company', 'parent_id', 'child_ids', 'category_id',
            'customer_rank', 'supplier_rank', 'employee',
            'property_account_receivable_id', 'property_account_payable_id',
            'property_payment_term_id', 'property_supplier_payment_term_id',
            'credit_limit', 'vat', 'commercial_partner_id', 'commercial_company_name',
            'title', 'function', 'comment', 'image', 'color',
            'active', 'company_id', 'create_uid', 'write_uid', 'create_date', 'write_date'
        );
        
        $this->required_fields = array('name');
        
        $this->readonly_fields = array('id', 'create_uid', 'create_date', 'commercial_partner_id');
        
        $this->default_values = array(
            'active' => true,
            'lang' => 'ar_SA',
            'tz' => 'Asia/Riyadh',
            'country_id' => 1,
            'customer_rank' => 0,
            'supplier_rank' => 0,
            'employee' => false,
            'is_company' => false,
            'credit_limit' => 0.00,
            'color' => 0
        );
    }
    
    /**
     * إنشاء شريك جديد
     */
    public function create($values) {
        // إنشاء اسم العرض
        if (isset($values['name']) && !isset($values['display_name'])) {
            $values['display_name'] = $values['name'];
        }
        
        // تحديد الشريك التجاري
        if (!isset($values['commercial_partner_id'])) {
            if (isset($values['parent_id']) && $values['parent_id']) {
                $parent = $this->read(array($values['parent_id']));
                if (!empty($parent)) {
                    $values['commercial_partner_id'] = $parent[0]['commercial_partner_id'] ?: $values['parent_id'];
                }
            }
        }
        
        // إنشاء المرجع التلقائي إذا لم يكن موجوداً
        if (!isset($values['ref']) || empty($values['ref'])) {
            $values['ref'] = $this->generate_partner_ref($values);
        }
        
        return parent::create($values);
    }
    
    /**
     * تحديث الشريك
     */
    public function write($ids, $values) {
        // تحديث اسم العرض
        if (isset($values['name'])) {
            $values['display_name'] = $values['name'];
        }
        
        return parent::write($ids, $values);
    }
    
    /**
     * إنشاء مرجع تلقائي للشريك
     */
    private function generate_partner_ref($values) {
        $prefix = 'PART';
        
        if (isset($values['customer_rank']) && $values['customer_rank'] > 0) {
            $prefix = 'CUST';
        } elseif (isset($values['supplier_rank']) && $values['supplier_rank'] > 0) {
            $prefix = 'SUPP';
        }
        
        // الحصول على آخر رقم
        $sql = "SELECT MAX(CAST(SUBSTRING(ref, " . (strlen($prefix) + 1) . ") AS UNSIGNED)) as max_num 
                FROM {$this->table} 
                WHERE ref LIKE '{$prefix}%' AND ref REGEXP '^{$prefix}[0-9]+$'";
        
        $result = $this->db->fetch($sql);
        $next_num = ($result && $result['max_num']) ? $result['max_num'] + 1 : 1;
        
        return $prefix . str_pad($next_num, 6, '0', STR_PAD_LEFT);
    }
    
    /**
     * البحث عن العملاء
     */
    public function search_customers($domain = array(), $options = array()) {
        $customer_domain = array(array('customer_rank', '>', 0));
        $domain = array_merge($customer_domain, $domain);
        
        return $this->search_read($domain, null, $options);
    }
    
    /**
     * البحث عن الموردين
     */
    public function search_suppliers($domain = array(), $options = array()) {
        $supplier_domain = array(array('supplier_rank', '>', 0));
        $domain = array_merge($supplier_domain, $domain);
        
        return $this->search_read($domain, null, $options);
    }
    
    /**
     * تحويل إلى عميل
     */
    public function make_customer($ids) {
        return $this->write($ids, array('customer_rank' => 1));
    }
    
    /**
     * تحويل إلى مورد
     */
    public function make_supplier($ids) {
        return $this->write($ids, array('supplier_rank' => 1));
    }
    
    /**
     * الحصول على رصيد العميل
     */
    public function get_partner_balance($partner_id) {
        $sql = "SELECT 
                    SUM(CASE WHEN aml.account_id IN (
                        SELECT id FROM account_account WHERE internal_type = 'receivable'
                    ) THEN aml.debit - aml.credit ELSE 0 END) as receivable_balance,
                    SUM(CASE WHEN aml.account_id IN (
                        SELECT id FROM account_account WHERE internal_type = 'payable'
                    ) THEN aml.credit - aml.debit ELSE 0 END) as payable_balance
                FROM account_move_line aml
                JOIN account_move am ON aml.move_id = am.id
                WHERE aml.partner_id = ? AND am.state = 'posted'";
        
        $result = $this->db->fetch($sql, array($partner_id));
        
        return array(
            'receivable' => $result ? floatval($result['receivable_balance']) : 0.0,
            'payable' => $result ? floatval($result['payable_balance']) : 0.0
        );
    }
    
    /**
     * الحصول على الفواتير المستحقة
     */
    public function get_overdue_invoices($partner_id) {
        $sql = "SELECT am.id, am.name, am.invoice_date, am.invoice_date_due, 
                       am.amount_total, am.amount_residual
                FROM account_move am
                WHERE am.partner_id = ? 
                AND am.move_type IN ('out_invoice', 'in_invoice')
                AND am.state = 'posted'
                AND am.payment_state != 'paid'
                AND am.invoice_date_due < CURDATE()
                ORDER BY am.invoice_date_due ASC";
        
        return $this->db->fetchAll($sql, array($partner_id));
    }
    
    /**
     * تنسيق العنوان الكامل
     */
    public function format_address($partner_id) {
        $partner = $this->read(array($partner_id));
        if (empty($partner)) {
            return '';
        }
        
        $partner_data = $partner[0];
        $address_parts = array();
        
        if (!empty($partner_data['street'])) {
            $address_parts[] = $partner_data['street'];
        }
        if (!empty($partner_data['street2'])) {
            $address_parts[] = $partner_data['street2'];
        }
        if (!empty($partner_data['city'])) {
            $address_parts[] = $partner_data['city'];
        }
        if (!empty($partner_data['zip'])) {
            $address_parts[] = $partner_data['zip'];
        }
        
        return implode(', ', $address_parts);
    }
    
    /**
     * الحصول على معلومات الاتصال
     */
    public function get_contact_info($partner_id) {
        $partner = $this->read(array($partner_id));
        if (empty($partner)) {
            return array();
        }
        
        $partner_data = $partner[0];
        $contact_info = array();
        
        if (!empty($partner_data['email'])) {
            $contact_info['email'] = $partner_data['email'];
        }
        if (!empty($partner_data['phone'])) {
            $contact_info['phone'] = $partner_data['phone'];
        }
        if (!empty($partner_data['mobile'])) {
            $contact_info['mobile'] = $partner_data['mobile'];
        }
        if (!empty($partner_data['website'])) {
            $contact_info['website'] = $partner_data['website'];
        }
        
        return $contact_info;
    }
    
    /**
     * البحث بالاسم أو المرجع
     */
    public function name_search($name, $domain = array(), $limit = 10) {
        $search_domain = array(
            '|', 
            array('name', 'ilike', '%' . $name . '%'),
            array('ref', 'ilike', '%' . $name . '%')
        );
        
        if (!empty($domain)) {
            $search_domain = array_merge($domain, $search_domain);
        }
        
        $options = array('limit' => $limit, 'order' => 'name ASC');
        $partners = $this->search_read($search_domain, array('id', 'name', 'ref'), $options);
        
        $result = array();
        foreach ($partners as $partner) {
            $display_name = $partner['name'];
            if (!empty($partner['ref'])) {
                $display_name .= ' [' . $partner['ref'] . ']';
            }
            $result[] = array($partner['id'], $display_name);
        }
        
        return $result;
    }
    
    /**
     * التحقق من الحد الائتماني
     */
    public function check_credit_limit($partner_id, $amount = 0) {
        $partner = $this->read(array($partner_id));
        if (empty($partner)) {
            return false;
        }
        
        $partner_data = $partner[0];
        $credit_limit = floatval($partner_data['credit_limit']);
        
        if ($credit_limit <= 0) {
            return true; // لا يوجد حد ائتماني
        }
        
        $balance = $this->get_partner_balance($partner_id);
        $current_balance = $balance['receivable'];
        
        return ($current_balance + $amount) <= $credit_limit;
    }
    
    /**
     * hooks بعد الإنشاء
     */
    protected function post_create($id, $values) {
        // تحديث الشريك التجاري إذا لم يكن محدداً
        if (!isset($values['commercial_partner_id'])) {
            $this->write(array($id), array('commercial_partner_id' => $id));
        }
        
        // إنشاء حسابات افتراضية إذا كان عميل أو مورد
        if ((isset($values['customer_rank']) && $values['customer_rank'] > 0) ||
            (isset($values['supplier_rank']) && $values['supplier_rank'] > 0)) {
            $this->create_partner_accounts($id, $values);
        }
    }
    
    /**
     * إنشاء حسابات الشريك
     */
    private function create_partner_accounts($partner_id, $values) {
        // في النسخة المبسطة، نستخدم الحسابات الافتراضية
        $updates = array();
        
        if (isset($values['customer_rank']) && $values['customer_rank'] > 0) {
            // حساب العملاء الافتراضي
            $updates['property_account_receivable_id'] = 1; // يجب أن يكون معرف حساب العملاء
        }
        
        if (isset($values['supplier_rank']) && $values['supplier_rank'] > 0) {
            // حساب الموردين الافتراضي
            $updates['property_account_payable_id'] = 2; // يجب أن يكون معرف حساب الموردين
        }
        
        if (!empty($updates)) {
            $this->write(array($partner_id), $updates);
        }
    }

    /**
     * الحصول على بيانات وهمية للعرض
     * Demo data for display purposes
     */
    public function get_demo_data() {
        return array(
            array(
                'id' => 1,
                'name' => 'شركة التقنية المتقدمة',
                'email' => '<EMAIL>',
                'phone' => '+966 11 234 5678',
                'mobile' => '+966 50 123 4567',
                'street' => 'شارع الملك فهد',
                'city' => 'الرياض',
                'is_company' => 1,
                'customer_rank' => 1,
                'supplier_rank' => 0,
                'vat' => '***************',
                'website' => 'https://advanced-tech.com',
                'active' => 1,
                'create_date' => date('Y-m-d H:i:s')
            ),
            array(
                'id' => 2,
                'name' => 'أحمد محمد العلي',
                'email' => '<EMAIL>',
                'phone' => '+966 11 987 6543',
                'mobile' => '+966 55 987 6543',
                'street' => 'حي النخيل',
                'city' => 'جدة',
                'is_company' => 0,
                'customer_rank' => 1,
                'supplier_rank' => 0,
                'vat' => '',
                'website' => '',
                'active' => 1,
                'create_date' => date('Y-m-d H:i:s')
            ),
            array(
                'id' => 3,
                'name' => 'مؤسسة الخدمات التجارية',
                'email' => '<EMAIL>',
                'phone' => '+966 13 456 7890',
                'mobile' => '+966 56 456 7890',
                'street' => 'طريق الدمام',
                'city' => 'الدمام',
                'is_company' => 1,
                'customer_rank' => 0,
                'supplier_rank' => 1,
                'vat' => '987654321098765',
                'website' => 'https://commercial-services.com',
                'active' => 1,
                'create_date' => date('Y-m-d H:i:s')
            ),
            array(
                'id' => 4,
                'name' => 'فاطمة أحمد السالم',
                'email' => '<EMAIL>',
                'phone' => '+966 12 345 6789',
                'mobile' => '+966 54 345 6789',
                'street' => 'شارع التحلية',
                'city' => 'جدة',
                'is_company' => 0,
                'customer_rank' => 1,
                'supplier_rank' => 0,
                'vat' => '',
                'website' => '',
                'active' => 1,
                'create_date' => date('Y-m-d H:i:s')
            ),
            array(
                'id' => 5,
                'name' => 'شركة الإنشاءات الحديثة',
                'email' => '<EMAIL>',
                'phone' => '+966 14 567 8901',
                'mobile' => '+966 57 567 8901',
                'street' => 'طريق الملك عبدالعزيز',
                'city' => 'الخبر',
                'is_company' => 1,
                'customer_rank' => 1,
                'supplier_rank' => 1,
                'vat' => '456789012345678',
                'website' => 'https://modern-construction.com',
                'active' => 1,
                'create_date' => date('Y-m-d H:i:s')
            )
        );
    }
}
?>
