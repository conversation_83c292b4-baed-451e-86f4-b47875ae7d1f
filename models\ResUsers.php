<?php
/**
 * نموذج المستخدمين (res.users في Odoo)
 * Users Model
 */

require_once 'BaseModel.php';

class ResUsers extends BaseModel {
    
    protected function init() {
        $this->table = 'res_users';
        
        $this->fields = array(
            'id', 'name', 'login', 'email', 'password', 'active', 'company_id',
            'company_ids', 'groups_id', 'lang', 'tz', 'signature', 'avatar',
            'last_login', 'login_count', 'create_uid', 'write_uid', 'create_date', 'write_date'
        );
        
        $this->required_fields = array('name', 'login', 'password');
        
        $this->readonly_fields = array('id', 'create_uid', 'create_date', 'last_login', 'login_count');
        
        $this->default_values = array(
            'active' => true,
            'lang' => 'ar_SA',
            'tz' => 'Asia/Riyadh',
            'login_count' => 0,
            'company_ids' => '[]',
            'groups_id' => '[]'
        );
    }
    
    /**
     * إنشاء مستخدم جديد
     */
    public function create($values) {
        // التحقق من تفرد اسم المستخدم
        if (isset($values['login'])) {
            $this->validate_unique_login($values['login']);
        }
        
        // تشفير كلمة المرور
        if (isset($values['password'])) {
            $values['password'] = $this->hash_password($values['password']);
        }
        
        // تحديد الشركة الافتراضية
        if (!isset($values['company_id'])) {
            $values['company_id'] = $this->get_default_company_id();
        }
        
        // إضافة الشركة إلى قائمة الشركات
        if (!isset($values['company_ids'])) {
            $values['company_ids'] = json_encode(array($values['company_id']));
        }
        
        return parent::create($values);
    }
    
    /**
     * تحديث المستخدم
     */
    public function write($ids, $values) {
        // تشفير كلمة المرور إذا تم تحديثها
        if (isset($values['password'])) {
            $values['password'] = $this->hash_password($values['password']);
        }
        
        // التحقق من تفرد اسم المستخدم عند التحديث
        if (isset($values['login'])) {
            foreach ((array)$ids as $id) {
                $this->validate_unique_login($values['login'], $id);
            }
        }
        
        return parent::write($ids, $values);
    }
    
    /**
     * تسجيل الدخول
     */
    public function authenticate($login, $password) {
        $user = $this->search_read(
            array(
                array('login', '=', $login),
                array('active', '=', true)
            ),
            array('id', 'name', 'login', 'email', 'password', 'company_id', 'groups_id', 'lang', 'tz'),
            array('limit' => 1)
        );
        
        if (empty($user)) {
            return false;
        }
        
        $user_data = $user[0];
        
        // التحقق من كلمة المرور
        if (!$this->verify_password($password, $user_data['password'])) {
            return false;
        }
        
        // تحديث معلومات تسجيل الدخول
        $this->update_login_info($user_data['id']);
        
        // إرجاع بيانات المستخدم (بدون كلمة المرور)
        unset($user_data['password']);
        return $user_data;
    }
    
    /**
     * تحديث معلومات تسجيل الدخول
     */
    private function update_login_info($user_id) {
        $this->write(array($user_id), array(
            'last_login' => date('Y-m-d H:i:s'),
            'login_count' => 'login_count + 1' // سيتم معالجتها خصيصاً
        ));
        
        // تحديث عداد تسجيل الدخول
        $sql = "UPDATE {$this->table} SET login_count = login_count + 1 WHERE id = ?";
        $this->db->query($sql, array($user_id));
    }
    
    /**
     * الحصول على معلومات المستخدم الحالي
     */
    public function get_current_user() {
        if (!isset($_SESSION['user_id'])) {
            return null;
        }
        
        $user = $this->read(array($_SESSION['user_id']));
        if (empty($user)) {
            return null;
        }
        
        $user_data = $user[0];
        unset($user_data['password']); // إزالة كلمة المرور من البيانات المرجعة
        
        return $user_data;
    }
    
    /**
     * الحصول على شركات المستخدم
     */
    public function get_user_companies($user_id = null) {
        if (!$user_id) {
            $user_id = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;
        }
        
        if (!$user_id) {
            return array();
        }
        
        $user = $this->read(array($user_id));
        if (empty($user)) {
            return array();
        }
        
        $user_data = $user[0];
        $company_ids = json_decode($user_data['company_ids'], true);
        
        if (empty($company_ids)) {
            $company_ids = array($user_data['company_id']);
        }
        
        // جلب بيانات الشركات
        require_once 'ResCompany.php';
        $company_model = new ResCompany($this->db);
        
        return $company_model->read($company_ids);
    }
    
    /**
     * الحصول على مجموعات المستخدم
     */
    public function get_user_groups($user_id = null) {
        if (!$user_id) {
            $user_id = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;
        }
        
        if (!$user_id) {
            return array();
        }
        
        $user = $this->read(array($user_id));
        if (empty($user)) {
            return array();
        }
        
        $user_data = $user[0];
        $group_ids = json_decode($user_data['groups_id'], true);
        
        if (empty($group_ids)) {
            return array();
        }
        
        // جلب بيانات المجموعات
        $sql = "SELECT * FROM res_groups WHERE id IN (" . implode(',', array_map('intval', $group_ids)) . ")";
        return $this->db->fetchAll($sql);
    }
    
    /**
     * التحقق من الصلاحيات
     */
    public function has_permission($permission, $user_id = null) {
        if (!$user_id) {
            $user_id = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;
        }
        
        if (!$user_id) {
            return false;
        }
        
        // في النسخة المبسطة، نعطي صلاحيات كاملة للمدير
        $user = $this->read(array($user_id));
        if (empty($user)) {
            return false;
        }
        
        $user_data = $user[0];
        $groups = json_decode($user_data['groups_id'], true);
        
        // إذا كان في مجموعة الإدارة (ID = 1)، يحصل على جميع الصلاحيات
        if (in_array(1, $groups)) {
            return true;
        }
        
        // التحقق من الصلاحيات المحددة للمجموعات
        // في النسخة الكاملة، سيتم التحقق من جدول الصلاحيات
        
        return false;
    }
    
    /**
     * تغيير كلمة المرور
     */
    public function change_password($user_id, $old_password, $new_password) {
        $user = $this->read(array($user_id));
        if (empty($user)) {
            throw new Exception('المستخدم غير موجود');
        }
        
        $user_data = $user[0];
        
        // التحقق من كلمة المرور القديمة
        if (!$this->verify_password($old_password, $user_data['password'])) {
            throw new Exception('كلمة المرور القديمة غير صحيحة');
        }
        
        // تحديث كلمة المرور
        return $this->write(array($user_id), array('password' => $new_password));
    }
    
    /**
     * إعادة تعيين كلمة المرور
     */
    public function reset_password($user_id, $new_password) {
        return $this->write(array($user_id), array('password' => $new_password));
    }
    
    /**
     * البحث عن المستخدمين النشطين
     */
    public function search_active_users($domain = array(), $options = array()) {
        $active_domain = array(array('active', '=', true));
        $domain = array_merge($active_domain, $domain);
        
        return $this->search_read($domain, null, $options);
    }
    
    /**
     * تفعيل/إلغاء تفعيل المستخدم
     */
    public function toggle_active($user_ids) {
        if (!is_array($user_ids)) {
            $user_ids = array($user_ids);
        }
        
        foreach ($user_ids as $user_id) {
            $user = $this->read(array($user_id));
            if (!empty($user)) {
                $current_status = $user[0]['active'];
                $this->write(array($user_id), array('active' => !$current_status));
            }
        }
        
        return true;
    }
    
    /**
     * دوال مساعدة خاصة
     */
    private function hash_password($password) {
        if (function_exists('password_hash')) {
            return password_hash($password, PASSWORD_DEFAULT);
        } else {
            return md5($password . 'erp_salt_key_2024');
        }
    }
    
    private function verify_password($password, $hash) {
        if (function_exists('password_verify')) {
            return password_verify($password, $hash);
        } else {
            return md5($password . 'erp_salt_key_2024') === $hash;
        }
    }
    
    private function validate_unique_login($login, $exclude_id = null) {
        $domain = array(array('login', '=', $login));
        if ($exclude_id) {
            $domain[] = array('id', '!=', $exclude_id);
        }
        
        $count = $this->search_count($domain);
        if ($count > 0) {
            throw new Exception('اسم المستخدم موجود مسبقاً');
        }
        
        return true;
    }
    
    private function get_default_company_id() {
        $sql = "SELECT id FROM res_company WHERE active = 1 ORDER BY sequence ASC LIMIT 1";
        $result = $this->db->fetch($sql);
        return $result ? $result['id'] : 1;
    }
    
    /**
     * hooks بعد الإنشاء
     */
    protected function post_create($id, $values) {
        // إنشاء شريك مرتبط بالمستخدم
        if (isset($values['name']) && isset($values['email'])) {
            require_once 'ResPartner.php';
            $partner_model = new ResPartner($this->db);
            
            $partner_data = array(
                'name' => $values['name'],
                'email' => $values['email'],
                'is_company' => false,
                'employee' => true,
                'active' => isset($values['active']) ? $values['active'] : true,
                'company_id' => $values['company_id']
            );
            
            try {
                $partner_model->create($partner_data);
            } catch (Exception $e) {
                // تسجيل الخطأ ومتابعة
                error_log("خطأ في إنشاء شريك للمستخدم: " . $e->getMessage());
            }
        }
    }
    
    /**
     * التحقق قبل الحذف
     */
    protected function pre_unlink($id) {
        // التحقق من عدم حذف المستخدم الإداري الوحيد
        $admin_count = $this->search_count(array(
            array('active', '=', true),
            array('groups_id', 'like', '%"1"%') // مجموعة الإدارة
        ));
        
        if ($admin_count <= 1) {
            $user = $this->read(array($id));
            if (!empty($user)) {
                $groups = json_decode($user[0]['groups_id'], true);
                if (in_array(1, $groups)) {
                    throw new Exception('لا يمكن حذف المستخدم الإداري الوحيد');
                }
            }
        }
    }
}
?>
