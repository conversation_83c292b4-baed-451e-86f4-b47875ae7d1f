<?php
/**
 * نموذج طلبات المبيعات
 * Sale Order Model - Odoo Style
 */

require_once __DIR__ . '/BaseModel.php';

class SaleOrder extends BaseModel {
    
    protected $table = 'sale_orders';
    protected $primaryKey = 'id';
    
    protected $fillable = [
        'name', 'partner_id', 'date_order', 'validity_date', 'state',
        'amount_untaxed', 'amount_tax', 'amount_total', 'currency_id',
        'payment_term', 'note', 'user_id', 'team_id', 'company_id',
        'invoice_status', 'delivery_status'
    ];
    
    protected $casts = [
        'date_order' => 'date',
        'validity_date' => 'date',
        'amount_untaxed' => 'decimal:2',
        'amount_tax' => 'decimal:2',
        'amount_total' => 'decimal:2'
    ];
    
    // حالات طلب المبيعات
    const STATE_DRAFT = 'draft';
    const STATE_SENT = 'sent';
    const STATE_SALE = 'sale';
    const STATE_DONE = 'done';
    const STATE_CANCEL = 'cancel';
    
    // حالات الفوترة
    const INVOICE_STATUS_NO = 'no';
    const INVOICE_STATUS_TO_INVOICE = 'to_invoice';
    const INVOICE_STATUS_INVOICED = 'invoiced';
    
    // حالات التسليم
    const DELIVERY_STATUS_NO = 'no';
    const DELIVERY_STATUS_PARTIAL = 'partial';
    const DELIVERY_STATUS_FULL = 'full';
    
    /**
     * العلاقة مع الشريك (العميل)
     */
    public function partner() {
        $sql = "SELECT * FROM partners WHERE id = :partner_id";
        $result = $this->db->fetch($sql, ['partner_id' => $this->partner_id]);
        return $result;
    }
    
    /**
     * العلاقة مع المستخدم
     */
    public function user() {
        $sql = "SELECT * FROM users WHERE id = :user_id";
        $result = $this->db->fetch($sql, ['user_id' => $this->user_id]);
        return $result;
    }
    
    /**
     * العلاقة مع فريق المبيعات
     */
    public function team() {
        $sql = "SELECT * FROM crm_teams WHERE id = :team_id";
        $result = $this->db->fetch($sql, ['team_id' => $this->team_id]);
        return $result;
    }
    
    /**
     * العلاقة مع الشركة
     */
    public function company() {
        $sql = "SELECT * FROM companies WHERE id = :company_id";
        $result = $this->db->fetch($sql, ['company_id' => $this->company_id]);
        return $result;
    }
    
    /**
     * العلاقة مع بنود الطلب
     */
    public function orderLines() {
        $sql = "SELECT sol.*, p.name as product_name, p.default_code 
                FROM sale_order_lines sol 
                LEFT JOIN products p ON sol.product_id = p.id 
                WHERE sol.order_id = :order_id 
                ORDER BY sol.sequence";
        $result = $this->db->fetchAll($sql, ['order_id' => $this->id]);
        return $result;
    }
    
    /**
     * إنشاء طلب مبيعات جديد
     */
    public function create($data) {
        // توليد رقم الطلب
        if (empty($data['name'])) {
            $data['name'] = $this->generateOrderNumber();
        }
        
        // تعيين القيم الافتراضية
        $data['state'] = $data['state'] ?? self::STATE_DRAFT;
        $data['date_order'] = $data['date_order'] ?? date('Y-m-d');
        $data['currency_id'] = $data['currency_id'] ?? 'SAR';
        $data['company_id'] = $data['company_id'] ?? 1;
        $data['invoice_status'] = $data['invoice_status'] ?? self::INVOICE_STATUS_NO;
        $data['delivery_status'] = $data['delivery_status'] ?? self::DELIVERY_STATUS_NO;
        
        return parent::create($data);
    }
    
    /**
     * تحديث طلب المبيعات
     */
    public function update($id, $data) {
        // التحقق من الحالة
        $order = $this->find($id);
        if (!$order) {
            throw new Exception('طلب المبيعات غير موجود');
        }
        
        if ($order['state'] == self::STATE_DONE) {
            throw new Exception('لا يمكن تعديل طلب مكتمل');
        }
        
        return parent::update($id, $data);
    }
    
    /**
     * تأكيد طلب المبيعات
     */
    public function confirm($id) {
        $order = $this->find($id);
        if (!$order) {
            throw new Exception('طلب المبيعات غير موجود');
        }
        
        if ($order['state'] != self::STATE_DRAFT && $order['state'] != self::STATE_SENT) {
            throw new Exception('لا يمكن تأكيد هذا الطلب');
        }
        
        // تحديث الحالة
        $this->update($id, [
            'state' => self::STATE_SALE,
            'invoice_status' => self::INVOICE_STATUS_TO_INVOICE
        ]);
        
        // إنشاء حركات المخزون إذا لزم الأمر
        $this->createStockMoves($id);
        
        return true;
    }
    
    /**
     * إلغاء طلب المبيعات
     */
    public function cancel($id) {
        $order = $this->find($id);
        if (!$order) {
            throw new Exception('طلب المبيعات غير موجود');
        }
        
        if ($order['state'] == self::STATE_DONE) {
            throw new Exception('لا يمكن إلغاء طلب مكتمل');
        }
        
        return $this->update($id, ['state' => self::STATE_CANCEL]);
    }
    
    /**
     * حساب إجمالي الطلب
     */
    public function calculateTotals($id) {
        $lines = $this->orderLines();
        
        $amount_untaxed = 0;
        $amount_tax = 0;
        
        foreach ($lines as $line) {
            $amount_untaxed += $line['price_subtotal'];
            $amount_tax += ($line['price_total'] - $line['price_subtotal']);
        }
        
        $amount_total = $amount_untaxed + $amount_tax;
        
        // تحديث الطلب
        $this->update($id, [
            'amount_untaxed' => $amount_untaxed,
            'amount_tax' => $amount_tax,
            'amount_total' => $amount_total
        ]);
        
        return [
            'amount_untaxed' => $amount_untaxed,
            'amount_tax' => $amount_tax,
            'amount_total' => $amount_total
        ];
    }
    
    /**
     * إنشاء فاتورة من طلب المبيعات
     */
    public function createInvoice($id) {
        $order = $this->find($id);
        if (!$order) {
            throw new Exception('طلب المبيعات غير موجود');
        }
        
        if ($order['state'] != self::STATE_SALE) {
            throw new Exception('يجب تأكيد الطلب أولاً');
        }
        
        if ($order['invoice_status'] == self::INVOICE_STATUS_INVOICED) {
            throw new Exception('تم إنشاء فاتورة لهذا الطلب مسبقاً');
        }
        
        // إنشاء الفاتورة
        $invoice_data = [
            'move_type' => 'out_invoice',
            'partner_id' => $order['partner_id'],
            'date' => date('Y-m-d'),
            'invoice_date' => date('Y-m-d'),
            'ref' => $order['name'],
            'journal_id' => 1, // دفتر المبيعات
            'company_id' => $order['company_id'],
            'user_id' => $order['user_id'],
            'amount_untaxed' => $order['amount_untaxed'],
            'amount_tax' => $order['amount_tax'],
            'amount_total' => $order['amount_total'],
            'currency_id' => $order['currency_id']
        ];
        
        // هنا يجب استخدام نموذج AccountMove
        // $invoice_id = $account_move->create($invoice_data);
        
        // تحديث حالة الفوترة
        $this->update($id, ['invoice_status' => self::INVOICE_STATUS_INVOICED]);
        
        return true;
    }
    
    /**
     * إنشاء حركات المخزون
     */
    private function createStockMoves($order_id) {
        $lines = $this->orderLines();
        
        foreach ($lines as $line) {
            if ($line['product_qty'] > 0) {
                // إنشاء حركة مخزون للمنتج
                $move_data = [
                    'name' => "تسليم: " . $line['name'],
                    'product_id' => $line['product_id'],
                    'product_qty' => $line['product_qty'],
                    'location_id' => 2, // المستودع الرئيسي
                    'location_dest_id' => 3, // العملاء
                    'partner_id' => $this->partner_id,
                    'origin' => $this->name,
                    'state' => 'confirmed',
                    'date' => date('Y-m-d'),
                    'company_id' => $this->company_id
                ];
                
                // هنا يجب استخدام نموذج StockMove
                // $stock_move->create($move_data);
            }
        }
    }
    
    /**
     * توليد رقم طلب المبيعات
     */
    private function generateOrderNumber() {
        $year = date('Y');
        $sql = "SELECT COUNT(*) as count FROM sale_orders WHERE YEAR(created_at) = :year";
        $result = $this->db->fetch($sql, ['year' => $year]);
        $sequence = $result['count'] + 1;
        
        return "SO{$year}" . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }
    
    /**
     * البحث في طلبات المبيعات
     */
    public function search($filters = []) {
        $sql = "SELECT so.*, p.name as partner_name, u.name as user_name, ct.name as team_name 
                FROM sale_orders so 
                LEFT JOIN partners p ON so.partner_id = p.id 
                LEFT JOIN users u ON so.user_id = u.id 
                LEFT JOIN crm_teams ct ON so.team_id = ct.id 
                WHERE 1=1";
        
        $params = [];
        
        if (!empty($filters['partner_id'])) {
            $sql .= " AND so.partner_id = :partner_id";
            $params['partner_id'] = $filters['partner_id'];
        }
        
        if (!empty($filters['state'])) {
            $sql .= " AND so.state = :state";
            $params['state'] = $filters['state'];
        }
        
        if (!empty($filters['date_from'])) {
            $sql .= " AND so.date_order >= :date_from";
            $params['date_from'] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $sql .= " AND so.date_order <= :date_to";
            $params['date_to'] = $filters['date_to'];
        }
        
        if (!empty($filters['search'])) {
            $sql .= " AND (so.name LIKE :search OR p.name LIKE :search)";
            $params['search'] = '%' . $filters['search'] . '%';
        }
        
        $sql .= " ORDER BY so.date_order DESC, so.id DESC";
        
        if (!empty($filters['limit'])) {
            $sql .= " LIMIT " . intval($filters['limit']);
        }
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * إحصائيات طلبات المبيعات
     */
    public function getStatistics($filters = []) {
        $sql = "SELECT 
                    COUNT(*) as total_orders,
                    SUM(CASE WHEN state = 'draft' THEN 1 ELSE 0 END) as draft_orders,
                    SUM(CASE WHEN state = 'sale' THEN 1 ELSE 0 END) as confirmed_orders,
                    SUM(CASE WHEN state = 'done' THEN 1 ELSE 0 END) as done_orders,
                    SUM(amount_total) as total_amount,
                    AVG(amount_total) as average_amount
                FROM sale_orders 
                WHERE 1=1";
        
        $params = [];
        
        if (!empty($filters['date_from'])) {
            $sql .= " AND date_order >= :date_from";
            $params['date_from'] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $sql .= " AND date_order <= :date_to";
            $params['date_to'] = $filters['date_to'];
        }
        
        return $this->db->fetch($sql, $params);
    }
}
?>
