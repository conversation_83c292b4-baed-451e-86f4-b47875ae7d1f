# -*- coding: utf-8 -*-
"""
إعدادات النظام بأسلوب Odoo
Odoo-Style System Configuration
"""

# إعدادات قاعدة البيانات
DATABASE_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'name': 'erp_accounting',
    'user': 'root',
    'password': '',
    'charset': 'utf8mb4',
    'autocommit': True,
    'pool_size': 10,
    'max_overflow': 20
}

# إعدادات النظام
SYSTEM_CONFIG = {
    'name': 'نظام ERP المحاسبي',
    'version': '1.0.0',
    'author': 'Odoo Style ERP',
    'website': 'http://localhost/acc/',
    'currency': 'SAR',
    'currency_symbol': 'ر.س',
    'timezone': 'Asia/Riyadh',
    'language': 'ar_SA',
    'date_format': '%Y-%m-%d',
    'datetime_format': '%Y-%m-%d %H:%M:%S',
    'decimal_precision': 2
}

# إعدادات الأمان
SECURITY_CONFIG = {
    'session_timeout': 3600,
    'password_min_length': 6,
    'max_login_attempts': 5,
    'password_hash_method': 'pbkdf2_sha256',
    'secret_key': 'odoo_erp_secret_key_2024',
    'csrf_protection': True,
    'secure_cookies': False  # True في الإنتاج
}

# إعدادات الملفات
FILE_CONFIG = {
    'upload_max_size': 32 * 1024 * 1024,  # 32MB
    'allowed_extensions': ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'jpg', 'png', 'gif'],
    'upload_path': 'uploads/',
    'temp_path': 'tmp/',
    'backup_path': 'backups/'
}

# إعدادات البريد الإلكتروني
EMAIL_CONFIG = {
    'smtp_server': 'localhost',
    'smtp_port': 587,
    'smtp_user': '',
    'smtp_password': '',
    'smtp_encryption': 'tls',
    'default_from': 'noreply@localhost'
}

# إعدادات التقارير
REPORT_CONFIG = {
    'default_format': 'pdf',
    'page_size': 'A4',
    'margin_top': 20,
    'margin_bottom': 20,
    'margin_left': 20,
    'margin_right': 20,
    'font_family': 'Arial',
    'font_size': 12
}

# إعدادات الوحدات (Modules)
MODULES_CONFIG = {
    'base': {
        'name': 'الوحدة الأساسية',
        'description': 'الوحدة الأساسية للنظام',
        'version': '1.0.0',
        'depends': [],
        'auto_install': True
    },
    'account': {
        'name': 'المحاسبة',
        'description': 'وحدة المحاسبة والحسابات',
        'version': '1.0.0',
        'depends': ['base'],
        'auto_install': True
    },
    'sale': {
        'name': 'المبيعات',
        'description': 'وحدة إدارة المبيعات',
        'version': '1.0.0',
        'depends': ['base', 'account'],
        'auto_install': True
    },
    'purchase': {
        'name': 'المشتريات',
        'description': 'وحدة إدارة المشتريات',
        'version': '1.0.0',
        'depends': ['base', 'account'],
        'auto_install': True
    },
    'stock': {
        'name': 'المخزون',
        'description': 'وحدة إدارة المخزون',
        'version': '1.0.0',
        'depends': ['base'],
        'auto_install': True
    },
    'hr': {
        'name': 'الموارد البشرية',
        'description': 'وحدة إدارة الموارد البشرية',
        'version': '1.0.0',
        'depends': ['base'],
        'auto_install': False
    }
}

# إعدادات الصلاحيات (Access Rights)
ACCESS_RIGHTS = {
    'admin': {
        'name': 'مدير النظام',
        'permissions': ['read', 'write', 'create', 'unlink', 'admin']
    },
    'manager': {
        'name': 'مدير',
        'permissions': ['read', 'write', 'create', 'unlink']
    },
    'user': {
        'name': 'مستخدم',
        'permissions': ['read', 'write', 'create']
    },
    'readonly': {
        'name': 'قراءة فقط',
        'permissions': ['read']
    }
}

# إعدادات القوائم (Menus)
MENU_CONFIG = {
    'main_menu': [
        {
            'id': 'dashboard',
            'name': 'لوحة التحكم',
            'icon': 'fas fa-tachometer-alt',
            'url': 'demo.php',
            'sequence': 1,
            'groups': ['admin', 'manager', 'user']
        },
        {
            'id': 'companies',
            'name': 'الشركات',
            'icon': 'fas fa-building',
            'url': 'pages/companies.php',
            'sequence': 2,
            'groups': ['admin', 'manager']
        },
        {
            'id': 'customers',
            'name': 'العملاء',
            'icon': 'fas fa-users',
            'url': 'pages/customers.php',
            'sequence': 3,
            'groups': ['admin', 'manager', 'user']
        },
        {
            'id': 'products',
            'name': 'المنتجات',
            'icon': 'fas fa-box',
            'url': 'pages/products.php',
            'sequence': 4,
            'groups': ['admin', 'manager', 'user']
        },
        {
            'id': 'invoices',
            'name': 'الفواتير',
            'icon': 'fas fa-file-invoice',
            'url': 'pages/invoices.php',
            'sequence': 5,
            'groups': ['admin', 'manager', 'user']
        },
        {
            'id': 'reports',
            'name': 'التقارير',
            'icon': 'fas fa-chart-bar',
            'url': 'pages/reports.php',
            'sequence': 6,
            'groups': ['admin', 'manager']
        },
        {
            'id': 'settings',
            'name': 'الإعدادات',
            'icon': 'fas fa-cog',
            'url': 'pages/settings.php',
            'sequence': 7,
            'groups': ['admin']
        }
    ]
}

# إعدادات الحقول (Fields)
FIELD_TYPES = {
    'char': 'نص',
    'text': 'نص طويل',
    'integer': 'رقم صحيح',
    'float': 'رقم عشري',
    'boolean': 'صح/خطأ',
    'date': 'تاريخ',
    'datetime': 'تاريخ ووقت',
    'selection': 'اختيار',
    'many2one': 'علاقة واحد لكثير',
    'one2many': 'علاقة كثير لواحد',
    'many2many': 'علاقة كثير لكثير',
    'binary': 'ملف',
    'html': 'HTML',
    'monetary': 'مبلغ مالي'
}

# إعدادات الحالات (States)
RECORD_STATES = {
    'draft': 'مسودة',
    'confirmed': 'مؤكد',
    'done': 'منجز',
    'cancelled': 'ملغي'
}

# إعدادات التسلسل (Sequences)
SEQUENCE_CONFIG = {
    'company': {
        'name': 'تسلسل الشركات',
        'prefix': 'COMP',
        'padding': 4,
        'next_number': 1
    },
    'partner': {
        'name': 'تسلسل الشركاء',
        'prefix': 'PART',
        'padding': 4,
        'next_number': 1
    },
    'product': {
        'name': 'تسلسل المنتجات',
        'prefix': 'PROD',
        'padding': 4,
        'next_number': 1
    },
    'invoice': {
        'name': 'تسلسل الفواتير',
        'prefix': 'INV',
        'padding': 4,
        'next_number': 1
    }
}

# إعدادات الترجمة
TRANSLATION_CONFIG = {
    'default_language': 'ar_SA',
    'available_languages': {
        'ar_SA': 'العربية',
        'en_US': 'English'
    },
    'rtl_languages': ['ar_SA', 'ar', 'he', 'fa']
}

# إعدادات التخزين المؤقت
CACHE_CONFIG = {
    'enabled': True,
    'type': 'file',  # file, redis, memcached
    'timeout': 3600,
    'path': 'cache/',
    'prefix': 'odoo_erp_'
}

# إعدادات السجلات (Logging)
LOGGING_CONFIG = {
    'enabled': True,
    'level': 'INFO',  # DEBUG, INFO, WARNING, ERROR, CRITICAL
    'file': 'logs/system.log',
    'max_size': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5,
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
}

# إعدادات النسخ الاحتياطي
BACKUP_CONFIG = {
    'enabled': True,
    'auto_backup': True,
    'backup_interval': 24,  # ساعات
    'keep_backups': 7,  # عدد النسخ المحفوظة
    'backup_path': 'backups/',
    'compress': True
}
