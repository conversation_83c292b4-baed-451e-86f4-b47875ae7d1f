<?php
/**
 * مشغل نظام Odoo - حل جميع المشاكل
 * Odoo System Launcher - Fix All Issues
 */

// إيقاف عرض الأخطاء للمستخدم النهائي
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

session_start();

// تحميل نظام Odoo
require_once 'config/odoo_config.php';
require_once 'config/odoo_database.php';

// تحميل نظام Odoo المتقدم
if (file_exists('includes/odoo_registry.php')) {
    require_once 'includes/odoo_registry.php';
    require_once 'includes/odoo_menu.php';
    require_once 'includes/odoo_actions.php';
}

/**
 * كلاس مشغل Odoo
 */
class OdooLauncher {
    private $db;
    private $config_created = false;
    private $database_ready = false;
    private $demo_mode = false;
    
    public function __construct() {
        $this->db = OdooDatabase::getInstance();
        $this->demo_mode = $this->db->isDemoMode();
        $this->checkSystem();
    }
    
    /**
     * فحص النظام
     */
    private function checkSystem() {
        // إنشاء ملف التكوين إذا لم يكن موجود
        if (!file_exists('config/database_config.php')) {
            $this->createConfig();
        }
        
        // التحقق من جاهزية قاعدة البيانات
        $this->database_ready = !$this->demo_mode;
        
        $this->logInfo("System check completed - Demo mode: " . ($this->demo_mode ? 'Yes' : 'No'));
    }
    
    /**
     * إنشاء ملف التكوين
     */
    private function createConfig() {
        $config_content = "<?php
// إعدادات النظام بأسلوب Odoo - تم إنشاؤها تلقائياً
if (!defined('ODOO_SYSTEM_CONFIG_LOADED')) {
    define('ODOO_SYSTEM_CONFIG_LOADED', true);
    
    // إعدادات أساسية
    define('SITE_NAME', '" . ODOO_SITE_NAME . "');
    define('CURRENCY', '" . ODOO_CURRENCY_SYMBOL . "');
    define('DEMO_MODE', " . ($this->demo_mode ? 'true' : 'false') . ");
    define('SYSTEM_INSTALLED', true);
    define('ODOO_STYLE', true);
    define('INSTALL_DATE', '" . date(ODOO_DATETIME_FORMAT) . "');
    
    // إعدادات قاعدة البيانات
    define('DB_HOST', '" . ODOO_DB_HOST . "');
    define('DB_NAME', '" . ODOO_DB_NAME . "');
    define('DB_USER', '" . ODOO_DB_USER . "');
    define('DB_PASS', '" . ODOO_DB_PASS . "');
    define('DB_CHARSET', '" . ODOO_DB_CHARSET . "');
}
?>";
        
        if (@file_put_contents('config/database_config.php', $config_content)) {
            $this->config_created = true;
            $this->logInfo("Configuration file created successfully");
        } else {
            $this->logError("Failed to create configuration file");
        }
    }
    
    /**
     * تسجيل دخول تلقائي
     */
    public function autoLogin($user_type = 'admin') {
        $users = array(
            'admin' => array(
                'id' => 1,
                'name' => 'مدير النظام',
                'email' => '<EMAIL>',
                'role' => 'admin',
                'groups' => array('admin', 'manager', 'user')
            ),
            'manager' => array(
                'id' => 2,
                'name' => 'مدير',
                'email' => '<EMAIL>',
                'role' => 'manager',
                'groups' => array('manager', 'user')
            ),
            'user' => array(
                'id' => 3,
                'name' => 'مستخدم',
                'email' => '<EMAIL>',
                'role' => 'user',
                'groups' => array('user')
            )
        );
        
        $user = $users[$user_type] ?? $users['admin'];
        
        // تسجيل الدخول
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['name'];
        $_SESSION['email'] = $user['email'];
        $_SESSION['role'] = $user['role'];
        $_SESSION['groups'] = $user['groups'];
        $_SESSION['company_id'] = 1;
        $_SESSION['company_name'] = 'شركتي';
        $_SESSION['odoo_style'] = true;
        $_SESSION['login_time'] = date(ODOO_DATETIME_FORMAT);
        $_SESSION['demo_mode'] = $this->demo_mode;
        $_SESSION['auto_login'] = true;
        
        // إنشاء بيانات تجريبية
        $this->createDemoData();
        
        $this->logInfo("Auto login completed for user: " . $user['email']);
        
        return true;
    }
    
    /**
     * إنشاء بيانات تجريبية
     */
    private function createDemoData() {
        $demo_data = array(
            'companies' => array(
                array('id' => 1, 'name' => 'شركتي', 'code' => 'MYCO', 'active' => true, 'currency' => 'SAR'),
                array('id' => 2, 'name' => 'شركة تجريبية', 'code' => 'DEMO', 'active' => true, 'currency' => 'SAR'),
                array('id' => 3, 'name' => 'شركة الخليج للتجارة', 'code' => 'GULF', 'active' => true, 'currency' => 'SAR')
            ),
            'partners' => array(
                array('id' => 1, 'name' => 'عميل تجريبي 1', 'email' => '<EMAIL>', 'customer_rank' => 1, 'supplier_rank' => 0),
                array('id' => 2, 'name' => 'عميل تجريبي 2', 'email' => '<EMAIL>', 'customer_rank' => 1, 'supplier_rank' => 0),
                array('id' => 3, 'name' => 'مورد تجريبي 1', 'email' => '<EMAIL>', 'customer_rank' => 0, 'supplier_rank' => 1),
                array('id' => 4, 'name' => 'شركة الرياض للمقاولات', 'email' => '<EMAIL>', 'customer_rank' => 1, 'supplier_rank' => 0),
                array('id' => 5, 'name' => 'مؤسسة جدة التجارية', 'email' => '<EMAIL>', 'customer_rank' => 1, 'supplier_rank' => 1)
            ),
            'products' => array(
                array('id' => 1, 'name' => 'منتج تجريبي 1', 'type' => 'product', 'list_price' => 100.00, 'standard_price' => 80.00),
                array('id' => 2, 'name' => 'منتج تجريبي 2', 'type' => 'product', 'list_price' => 200.00, 'standard_price' => 150.00),
                array('id' => 3, 'name' => 'خدمة تجريبية', 'type' => 'service', 'list_price' => 50.00, 'standard_price' => 30.00),
                array('id' => 4, 'name' => 'جهاز كمبيوتر محمول', 'type' => 'product', 'list_price' => 3500.00, 'standard_price' => 3000.00),
                array('id' => 5, 'name' => 'خدمة استشارية', 'type' => 'service', 'list_price' => 500.00, 'standard_price' => 400.00)
            ),
            'invoices' => array(
                array('id' => 1, 'partner_id' => 1, 'amount_total' => 1150.00, 'state' => 'posted', 'date' => date('Y-m-d')),
                array('id' => 2, 'partner_id' => 2, 'amount_total' => 2300.00, 'state' => 'draft', 'date' => date('Y-m-d')),
                array('id' => 3, 'partner_id' => 4, 'amount_total' => 4025.00, 'state' => 'posted', 'date' => date('Y-m-d', strtotime('-1 day')))
            ),
            'statistics' => array(
                'total_companies' => 3,
                'total_customers' => 3,
                'total_suppliers' => 2,
                'total_products' => 5,
                'total_invoices' => 3,
                'total_revenue' => 7475.00,
                'monthly_revenue' => 7475.00,
                'pending_invoices' => 1
            )
        );
        
        $_SESSION['demo_data'] = $demo_data;
        $_SESSION['odoo_modules'] = array('base', 'account', 'sale', 'purchase', 'stock');
        
        $this->logInfo("Demo data created successfully");
    }
    
    /**
     * الحصول على حالة النظام
     */
    public function getSystemStatus() {
        return array(
            'config_created' => $this->config_created,
            'database_ready' => $this->database_ready,
            'demo_mode' => $this->demo_mode,
            'odoo_version' => ODOO_VERSION,
            'system_version' => ODOO_SYSTEM_VERSION,
            'php_version' => PHP_VERSION,
            'timestamp' => date(ODOO_DATETIME_FORMAT)
        );
    }
    
    /**
     * تسجيل معلومة
     */
    private function logInfo($message) {
        if (ODOO_LOG_ENABLED) {
            $log_message = date(ODOO_DATETIME_FORMAT) . " - LAUNCHER INFO: " . $message . "\n";
            @file_put_contents(odoo_get_path('root') . ODOO_LOG_FILE, $log_message, FILE_APPEND | LOCK_EX);
        }
    }
    
    /**
     * تسجيل خطأ
     */
    private function logError($message) {
        if (ODOO_LOG_ENABLED) {
            $log_message = date(ODOO_DATETIME_FORMAT) . " - LAUNCHER ERROR: " . $message . "\n";
            @file_put_contents(odoo_get_path('root') . ODOO_LOG_FILE, $log_message, FILE_APPEND | LOCK_EX);
        }
    }
}

// تشغيل النظام
try {
    $launcher = new OdooLauncher();
    
    // تسجيل دخول تلقائي
    $launcher->autoLogin('admin');
    
    // تسجيل في السجل
    $log_entry = array(
        'timestamp' => date(ODOO_DATETIME_FORMAT),
        'action' => 'odoo_launcher',
        'user' => '<EMAIL>',
        'ip' => isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : 'unknown',
        'user_agent' => isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : 'unknown',
        'status' => $launcher->getSystemStatus()
    );
    
    if (ODOO_LOG_ENABLED) {
        @file_put_contents(odoo_get_path('root') . ODOO_LOG_FILE, json_encode($log_entry) . "\n", FILE_APPEND | LOCK_EX);
    }
    
    // توجيه للوحة التحكم
    header('Location: dashboard.php');
    exit();
    
} catch (Exception $e) {
    // في حالة حدوث خطأ، تسجيل دخول بسيط
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'مدير النظام';
    $_SESSION['email'] = '<EMAIL>';
    $_SESSION['role'] = 'admin';
    $_SESSION['groups'] = array('admin', 'manager', 'user');
    $_SESSION['company_id'] = 1;
    $_SESSION['company_name'] = 'شركتي';
    $_SESSION['odoo_style'] = true;
    $_SESSION['demo_mode'] = true;
    $_SESSION['error_recovery'] = true;
    
    // تسجيل الخطأ
    if (ODOO_LOG_ENABLED) {
        $error_log = date(ODOO_DATETIME_FORMAT) . " - LAUNCHER CRITICAL ERROR: " . $e->getMessage() . "\n";
        @file_put_contents('logs/critical_errors.log', $error_log, FILE_APPEND | LOCK_EX);
    }
    
    // توجيه للوحة التحكم مع وضع الطوارئ
    header('Location: dashboard.php?emergency=1');
    exit();
}
?>
