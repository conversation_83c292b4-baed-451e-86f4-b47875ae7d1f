<?php
/**
 * صفحة دفتر الأستاذ بأسلوب Odoo
 * Account Ledger Page - Odoo Style
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// تحميل نظام Odoo
require_once '../config/odoo_config.php';
require_once '../config/odoo_database.php';

$odoo_db = OdooDatabase::getInstance();

// تحميل النماذج
require_once '../models/BaseModel.php';
require_once '../models/AccountAccount.php';
require_once '../models/AccountMove.php';
require_once '../models/AccountMoveLine.php';
require_once '../models/AccountJournal.php';
require_once '../models/ResCurrency.php';

$account_model = new AccountAccount($odoo_db);
$move_model = new AccountMove($odoo_db);
$move_line_model = new AccountMoveLine($odoo_db);
$journal_model = new AccountJournal($odoo_db);
$currency_model = new ResCurrency($odoo_db);

// معالجة المعاملات
$account_id = isset($_GET['account_id']) ? intval($_GET['account_id']) : null;
$date_from = isset($_GET['date_from']) ? $_GET['date_from'] : date('Y-01-01');
$date_to = isset($_GET['date_to']) ? $_GET['date_to'] : date('Y-m-d');
$partner_id = isset($_GET['partner_id']) ? intval($_GET['partner_id']) : null;

// جلب بيانات الحساب
$account = null;
if ($account_id) {
    $account = $account_model->read($account_id);
    if (!$account) {
        // البحث في البيانات التجريبية
        $demo_accounts = $account_model->get_demo_data();
        foreach ($demo_accounts as $demo_account) {
            if ($demo_account['id'] == $account_id) {
                $account = $demo_account;
                break;
            }
        }
    }
}

// إذا لم يتم تحديد حساب، اختر أول حساب
if (!$account) {
    $demo_accounts = $account_model->get_demo_data();
    if (count($demo_accounts) > 0) {
        $account = $demo_accounts[0];
        $account_id = $account['id'];
    }
}

// جلب بنود الحساب
$account_lines = array();
$running_balance = 0;

if ($account_id) {
    // محاولة جلب البيانات من قاعدة البيانات
    try {
        $account_lines = $move_line_model->get_account_lines($account_id, $date_from, $date_to);
    } catch (Exception $e) {
        // استخدام البيانات التجريبية
        $demo_lines = $move_line_model->get_demo_data();
        foreach ($demo_lines as $line) {
            if ($line['account_id'] == $account_id) {
                $line_date = strtotime($line['date']);
                $from_date = strtotime($date_from);
                $to_date = strtotime($date_to);
                
                if ($line_date >= $from_date && $line_date <= $to_date) {
                    $account_lines[] = $line;
                }
            }
        }
    }
}

// حساب الرصيد الجاري
foreach ($account_lines as &$line) {
    $running_balance += ($line['debit'] - $line['credit']);
    $line['running_balance'] = $running_balance;
}

// حساب الأرصدة
$total_debit = array_sum(array_column($account_lines, 'debit'));
$total_credit = array_sum(array_column($account_lines, 'credit'));
$final_balance = $total_debit - $total_credit;

// جلب جميع الحسابات للقائمة المنسدلة
$all_accounts = $account_model->get_demo_data();

// أنواع الحسابات
$account_types = array(
    'asset' => array('name' => 'أصول', 'color' => 'success', 'icon' => 'fas fa-coins'),
    'liability' => array('name' => 'خصوم', 'color' => 'danger', 'icon' => 'fas fa-credit-card'),
    'equity' => array('name' => 'حقوق ملكية', 'color' => 'primary', 'icon' => 'fas fa-balance-scale'),
    'income' => array('name' => 'إيرادات', 'color' => 'info', 'icon' => 'fas fa-arrow-up'),
    'expense' => array('name' => 'مصروفات', 'color' => 'warning', 'icon' => 'fas fa-arrow-down')
);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دفتر الأستاذ - نظام ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --odoo-primary: #714B67;
            --odoo-secondary: #8B5A8C;
        }
        
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 0.85rem;
        }
        
        .navbar-odoo {
            background: linear-gradient(45deg, var(--odoo-primary), var(--odoo-secondary));
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .page-header {
            background: linear-gradient(45deg, #9B59B6, #8E44AD);
            color: white;
            padding: 1.5rem;
            border-radius: 15px;
            margin-bottom: 1.5rem;
        }
        
        .ledger-controls {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        
        .account-info {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
            border-left: 4px solid #9B59B6;
        }
        
        .ledger-table {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
        }
        
        .ledger-table th {
            background: linear-gradient(45deg, #9B59B6, #8E44AD);
            color: white;
            border: none;
            padding: 0.8rem;
            font-size: 0.8rem;
        }
        
        .ledger-table td {
            padding: 0.6rem 0.8rem;
            border-color: #f8f9fa;
            vertical-align: middle;
            font-size: 0.75rem;
        }
        
        .breadcrumb-odoo {
            background: white;
            border-radius: 8px;
            padding: 0.8rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            font-size: 0.8rem;
        }
        
        .balance-summary {
            background: white;
            border-radius: 12px;
            padding: 1.2rem;
            margin-bottom: 1rem;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
        }
        
        .amount-debit { color: #28a745; font-weight: bold; }
        .amount-credit { color: #dc3545; font-weight: bold; }
        .amount-balance { color: #007bff; font-weight: bold; }
        .amount-zero { color: #6c757d; }
        
        .balance-positive { color: #28a745; }
        .balance-negative { color: #dc3545; }
        
        .ledger-row:hover {
            background: #f8f9fa;
        }
        
        .no-data {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }
        
        .account-badge {
            font-size: 0.7rem;
            padding: 0.2rem 0.5rem;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-odoo">
        <div class="container-fluid">
            <a class="navbar-brand text-white" href="../dashboard.php">
                <i class="fas fa-cube me-2"></i>
                نظام ERP - دفتر الأستاذ
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="../dashboard.php">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
                <a class="nav-link text-white" href="chart_of_accounts.php">
                    <i class="fas fa-sitemap me-1"></i>دليل الحسابات
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-3">
        <!-- مسار التنقل -->
        <nav class="breadcrumb-odoo">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="../dashboard.php">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="#">المحاسبة</a></li>
                <li class="breadcrumb-item"><a href="chart_of_accounts.php">دليل الحسابات</a></li>
                <li class="breadcrumb-item active">دفتر الأستاذ</li>
            </ol>
        </nav>

        <!-- العنوان الرئيسي -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h3><i class="fas fa-book me-2"></i>دفتر الأستاذ</h3>
                    <p class="mb-0 small">تفاصيل حركات الحساب المحاسبي</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-light btn-sm" onclick="window.print()">
                        <i class="fas fa-print me-2"></i>طباعة
                    </button>
                    <button class="btn btn-outline-light btn-sm" onclick="exportLedger()">
                        <i class="fas fa-download me-2"></i>تصدير
                    </button>
                </div>
            </div>
        </div>

        <!-- أدوات التحكم -->
        <div class="ledger-controls">
            <form method="GET" class="row align-items-end">
                <div class="col-md-4">
                    <label class="form-label small">الحساب:</label>
                    <select name="account_id" class="form-select form-select-sm" onchange="this.form.submit()">
                        <option value="">اختر الحساب</option>
                        <?php foreach ($all_accounts as $acc): ?>
                            <option value="<?php echo $acc['id']; ?>" <?php echo $account_id == $acc['id'] ? 'selected' : ''; ?>>
                                <?php echo $acc['code']; ?> - <?php echo $acc['name']; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label small">من تاريخ:</label>
                    <input type="date" name="date_from" value="<?php echo $date_from; ?>" class="form-control form-control-sm">
                </div>
                <div class="col-md-2">
                    <label class="form-label small">إلى تاريخ:</label>
                    <input type="date" name="date_to" value="<?php echo $date_to; ?>" class="form-control form-control-sm">
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary btn-sm w-100">
                        <i class="fas fa-search me-1"></i>عرض
                    </button>
                </div>
                <div class="col-md-2">
                    <a href="chart_of_accounts.php" class="btn btn-outline-secondary btn-sm w-100">
                        <i class="fas fa-arrow-right me-1"></i>العودة
                    </a>
                </div>
            </form>
        </div>

        <?php if ($account): ?>
            <!-- معلومات الحساب -->
            <div class="account-info">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="d-flex align-items-center">
                            <?php $type_info = $account_types[$account['account_type']]; ?>
                            <div class="me-3">
                                <i class="<?php echo $type_info['icon']; ?> text-<?php echo $type_info['color']; ?>" style="font-size: 2rem;"></i>
                            </div>
                            <div>
                                <h5 class="mb-1"><?php echo $account['name']; ?></h5>
                                <p class="mb-0">
                                    <span class="badge bg-<?php echo $type_info['color']; ?> account-badge"><?php echo $account['code']; ?></span>
                                    <span class="badge bg-secondary account-badge ms-1"><?php echo $type_info['name']; ?></span>
                                    <?php if ($account['reconcile']): ?>
                                        <span class="badge bg-info account-badge ms-1">قابل للتسوية</span>
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 text-end">
                        <small class="text-muted">الفترة: <?php echo $date_from; ?> إلى <?php echo $date_to; ?></small>
                    </div>
                </div>
            </div>

            <!-- ملخص الأرصدة -->
            <div class="row mb-3">
                <div class="col-md-3">
                    <div class="balance-summary border-success">
                        <div class="text-center">
                            <i class="fas fa-plus-circle text-success" style="font-size: 1.5rem;"></i>
                            <h4 class="text-success mt-2"><?php echo number_format($total_debit, 2); ?> ر.س</h4>
                            <p class="mb-0 small">إجمالي المدين</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="balance-summary border-danger">
                        <div class="text-center">
                            <i class="fas fa-minus-circle text-danger" style="font-size: 1.5rem;"></i>
                            <h4 class="text-danger mt-2"><?php echo number_format($total_credit, 2); ?> ر.س</h4>
                            <p class="mb-0 small">إجمالي الدائن</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="balance-summary border-primary">
                        <div class="text-center">
                            <i class="fas fa-balance-scale text-primary" style="font-size: 1.5rem;"></i>
                            <h4 class="text-primary mt-2"><?php echo number_format($final_balance, 2); ?> ر.س</h4>
                            <p class="mb-0 small">الرصيد النهائي</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="balance-summary border-info">
                        <div class="text-center">
                            <i class="fas fa-list-ol text-info" style="font-size: 1.5rem;"></i>
                            <h4 class="text-info mt-2"><?php echo count($account_lines); ?></h4>
                            <p class="mb-0 small">عدد الحركات</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول الحركات -->
            <?php if (count($account_lines) > 0): ?>
                <div class="ledger-table">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>رقم القيد</th>
                                <th>المرجع</th>
                                <th>البيان</th>
                                <th>مدين</th>
                                <th>دائن</th>
                                <th>الرصيد الجاري</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($account_lines as $line): ?>
                                <tr class="ledger-row">
                                    <td><?php echo date('Y-m-d', strtotime($line['date'])); ?></td>
                                    <td>
                                        <a href="journal_entries.php?move_id=<?php echo $line['move_id']; ?>" class="text-decoration-none">
                                            <?php
                                            // البحث عن اسم القيد
                                            $move_name = 'N/A';
                                            try {
                                                $move = $move_model->read($line['move_id']);
                                                if ($move) {
                                                    $move_name = $move['name'];
                                                } else {
                                                    // البحث في البيانات التجريبية
                                                    $demo_moves = $move_model->get_demo_data();
                                                    foreach ($demo_moves as $demo_move) {
                                                        if ($demo_move['id'] == $line['move_id']) {
                                                            $move_name = $demo_move['name'];
                                                            break;
                                                        }
                                                    }
                                                }
                                            } catch (Exception $e) {
                                                // استخدام البيانات التجريبية
                                                $demo_moves = $move_model->get_demo_data();
                                                foreach ($demo_moves as $demo_move) {
                                                    if ($demo_move['id'] == $line['move_id']) {
                                                        $move_name = $demo_move['name'];
                                                        break;
                                                    }
                                                }
                                            }
                                            echo $move_name;
                                            ?>
                                        </a>
                                    </td>
                                    <td><?php echo isset($line['ref']) ? $line['ref'] : '-'; ?></td>
                                    <td><?php echo $line['name']; ?></td>
                                    <td>
                                        <?php if ($line['debit'] > 0): ?>
                                            <span class="amount-debit"><?php echo number_format($line['debit'], 2); ?></span>
                                        <?php else: ?>
                                            <span class="amount-zero">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($line['credit'] > 0): ?>
                                            <span class="amount-credit"><?php echo number_format($line['credit'], 2); ?></span>
                                        <?php else: ?>
                                            <span class="amount-zero">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="amount-balance <?php echo $line['running_balance'] >= 0 ? 'balance-positive' : 'balance-negative'; ?>">
                                            <?php echo number_format($line['running_balance'], 2); ?> ر.س
                                        </span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                        <tfoot>
                            <tr style="background: #f8f9fa; font-weight: bold;">
                                <td colspan="4"><strong>الإجمالي:</strong></td>
                                <td><strong class="amount-debit"><?php echo number_format($total_debit, 2); ?></strong></td>
                                <td><strong class="amount-credit"><?php echo number_format($total_credit, 2); ?></strong></td>
                                <td>
                                    <strong class="amount-balance <?php echo $final_balance >= 0 ? 'balance-positive' : 'balance-negative'; ?>">
                                        <?php echo number_format($final_balance, 2); ?> ر.س
                                    </strong>
                                </td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            <?php else: ?>
                <div class="ledger-table">
                    <div class="no-data">
                        <i class="fas fa-inbox" style="font-size: 3rem; color: #dee2e6;"></i>
                        <h5 class="mt-3">لا توجد حركات</h5>
                        <p>لا توجد حركات محاسبية لهذا الحساب في الفترة المحددة</p>
                    </div>
                </div>
            <?php endif; ?>

        <?php else: ?>
            <!-- لم يتم اختيار حساب -->
            <div class="ledger-table">
                <div class="no-data">
                    <i class="fas fa-search" style="font-size: 3rem; color: #dee2e6;"></i>
                    <h5 class="mt-3">اختر حساباً</h5>
                    <p>يرجى اختيار حساب من القائمة أعلاه لعرض دفتر الأستاذ</p>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function exportLedger() {
            alert('سيتم تطوير ميزة التصدير قريباً');
        }

        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const elements = document.querySelectorAll('.account-info, .balance-summary, .ledger-table');
            elements.forEach((element, index) => {
                element.style.opacity = '0';
                element.style.transform = 'translateY(15px)';

                setTimeout(() => {
                    element.style.transition = 'all 0.5s ease';
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });

        // تمييز الصفوف عند التمرير
        document.querySelectorAll('.ledger-row').forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.backgroundColor = '#e3f2fd';
            });

            row.addEventListener('mouseleave', function() {
                this.style.backgroundColor = '';
            });
        });
    </script>
</body>
</html>
