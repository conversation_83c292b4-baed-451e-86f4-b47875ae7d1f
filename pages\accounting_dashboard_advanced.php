<?php
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// تحميل النماذج
require_once '../models/AccountingCore.php';
require_once '../models/AccountMove.php';
require_once '../models/AccountPayment.php';
require_once '../models/AccountAsset.php';
require_once '../models/AccountBudget.php';
require_once '../models/AccountAnalytic.php';

// تهيئة النماذج
$accounting_core = new AccountingCore();
$move_model = new AccountMove();
$payment_model = new AccountPayment();
$asset_model = new AccountAsset();
$budget_model = new AccountBudget();
$analytic_model = new AccountAnalyticAccount();

// الحصول على البيانات للوحة التحكم
$dashboard_data = array(
    'moves_count' => count($move_model->get_demo_data()),
    'payments_count' => count($payment_model->get_demo_data()),
    'assets_count' => count($asset_model->get_demo_data()),
    'budgets_count' => count($budget_model->get_demo_data()),
    'analytic_accounts_count' => count($analytic_model->get_demo_data()),
    'sub_modules' => $accounting_core->getSubModules(),
    'financial_reports' => $accounting_core->getFinancialReports()
);

// حساب المؤشرات المالية
$financial_kpis = array(
    'total_revenue' => 150000.00,
    'total_expenses' => 95000.00,
    'net_profit' => 55000.00,
    'cash_balance' => 75000.00,
    'receivables' => 45000.00,
    'payables' => 32000.00
);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم المحاسبة المتقدمة - نظام ERP</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Odoo Style -->
    <link href="../assets/css/odoo-style.css" rel="stylesheet">
    <link href="../assets/css/odoo-advanced.css" rel="stylesheet">
    
    <style>
        .dashboard-card {
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }
        
        .kpi-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
        }
        
        .module-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border-left: 4px solid #875a7b;
        }
        
        .module-card:hover {
            border-left-color: #d73527;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        
        .quick-action-btn {
            background: linear-gradient(45deg, #875a7b, #d73527);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        
        .quick-action-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .stats-widget {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .stats-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .progress-ring {
            width: 120px;
            height: 120px;
            margin: 0 auto;
        }
        
        .activity-item {
            padding: 15px;
            border-bottom: 1px solid #eee;
            transition: background-color 0.3s ease;
        }
        
        .activity-item:hover {
            background-color: #f8f9fa;
        }
        
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 15px;
        }
    </style>
</head>
<body class="bg-light">
    <!-- شريط التنقل -->
    <?php include '../includes/navbar.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي -->
            <nav class="col-md-2 d-md-block bg-white sidebar collapse">
                <div class="position-sticky pt-3">
                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span>الوحدات الفرعية</span>
                    </h6>
                    <ul class="nav flex-column">
                        <?php foreach ($dashboard_data['sub_modules'] as $key => $module): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="#" onclick="loadModule('<?php echo $key; ?>')">
                                    <i class="fas fa-cube me-2"></i>
                                    <?php echo $module['name']; ?>
                                </a>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                    
                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span>التقارير المالية</span>
                    </h6>
                    <ul class="nav flex-column">
                        <?php foreach ($dashboard_data['financial_reports'] as $key => $report): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="financial_reports_advanced.php?type=<?php echo $key; ?>">
                                    <i class="fas fa-chart-line me-2"></i>
                                    <?php echo $report['name']; ?>
                                </a>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            </nav>
            
            <!-- المحتوى الرئيسي -->
            <main class="col-md-10 ms-sm-auto px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-tachometer-alt me-2 text-primary"></i>
                        لوحة تحكم المحاسبة المتقدمة
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshDashboard()">
                                <i class="fas fa-sync-alt me-1"></i>تحديث
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportDashboard()">
                                <i class="fas fa-download me-1"></i>تصدير
                            </button>
                        </div>
                    </div>
                </div>

                <!-- المؤشرات المالية الرئيسية -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stats-widget dashboard-card">
                            <div class="stats-number text-success"><?php echo number_format($financial_kpis['total_revenue']); ?></div>
                            <div class="stats-label">إجمالي الإيرادات</div>
                            <small class="text-success"><i class="fas fa-arrow-up"></i> +12.5%</small>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stats-widget dashboard-card">
                            <div class="stats-number text-danger"><?php echo number_format($financial_kpis['total_expenses']); ?></div>
                            <div class="stats-label">إجمالي المصروفات</div>
                            <small class="text-danger"><i class="fas fa-arrow-up"></i> +8.3%</small>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stats-widget dashboard-card">
                            <div class="stats-number text-primary"><?php echo number_format($financial_kpis['net_profit']); ?></div>
                            <div class="stats-label">صافي الربح</div>
                            <small class="text-success"><i class="fas fa-arrow-up"></i> +18.7%</small>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stats-widget dashboard-card">
                            <div class="stats-number text-info"><?php echo number_format($financial_kpis['cash_balance']); ?></div>
                            <div class="stats-label">رصيد النقدية</div>
                            <small class="text-info"><i class="fas fa-minus"></i> -2.1%</small>
                        </div>
                    </div>
                </div>

                <!-- الرسوم البيانية والتحليلات -->
                <div class="row mb-4">
                    <div class="col-lg-8">
                        <div class="card dashboard-card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-area me-2"></i>تحليل الإيرادات والمصروفات
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="revenueExpenseChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="card dashboard-card">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-pie me-2"></i>توزيع المصروفات
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="expenseDistributionChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الوحدات الفرعية -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card dashboard-card">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-cubes me-2"></i>الوحدات الفرعية للمحاسبة
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <?php foreach ($dashboard_data['sub_modules'] as $key => $module): ?>
                                        <div class="col-lg-4 col-md-6 mb-3">
                                            <div class="module-card" onclick="loadModule('<?php echo $key; ?>')">
                                                <h6 class="fw-bold"><?php echo $module['name']; ?></h6>
                                                <p class="text-muted small mb-2"><?php echo $module['description']; ?></p>
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <small class="text-primary"><?php echo count($module['models']); ?> نماذج</small>
                                                    <i class="fas fa-arrow-left text-primary"></i>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الإجراءات السريعة -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card dashboard-card">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="mb-0">
                                    <i class="fas fa-bolt me-2"></i>الإجراءات السريعة
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-lg-3 col-md-6 mb-3">
                                        <button class="btn quick-action-btn w-100" onclick="window.location.href='create_entry.php'">
                                            <i class="fas fa-plus me-2"></i>قيد جديد
                                        </button>
                                    </div>
                                    <div class="col-lg-3 col-md-6 mb-3">
                                        <button class="btn quick-action-btn w-100" onclick="window.location.href='create_payment.php'">
                                            <i class="fas fa-money-bill me-2"></i>مدفوعة جديدة
                                        </button>
                                    </div>
                                    <div class="col-lg-3 col-md-6 mb-3">
                                        <button class="btn quick-action-btn w-100" onclick="window.location.href='create_asset.php'">
                                            <i class="fas fa-building me-2"></i>أصل جديد
                                        </button>
                                    </div>
                                    <div class="col-lg-3 col-md-6 mb-3">
                                        <button class="btn quick-action-btn w-100" onclick="window.location.href='create_budget.php'">
                                            <i class="fas fa-chart-line me-2"></i>ميزانية جديدة
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- النشاطات الأخيرة -->
                <div class="row">
                    <div class="col-lg-6">
                        <div class="card dashboard-card">
                            <div class="card-header bg-secondary text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-history me-2"></i>النشاطات الأخيرة
                                </h5>
                            </div>
                            <div class="card-body p-0">
                                <div class="activity-item">
                                    <div class="d-flex align-items-center">
                                        <div class="activity-icon bg-success text-white">
                                            <i class="fas fa-plus"></i>
                                        </div>
                                        <div>
                                            <div class="fw-bold">قيد محاسبي جديد</div>
                                            <small class="text-muted">تم إنشاء قيد رقم JE/2024/001</small>
                                        </div>
                                        <small class="text-muted ms-auto">منذ 5 دقائق</small>
                                    </div>
                                </div>
                                <div class="activity-item">
                                    <div class="d-flex align-items-center">
                                        <div class="activity-icon bg-primary text-white">
                                            <i class="fas fa-money-bill"></i>
                                        </div>
                                        <div>
                                            <div class="fw-bold">مدفوعة جديدة</div>
                                            <small class="text-muted">تم استلام دفعة بقيمة 15,000 ر.س</small>
                                        </div>
                                        <small class="text-muted ms-auto">منذ 15 دقيقة</small>
                                    </div>
                                </div>
                                <div class="activity-item">
                                    <div class="d-flex align-items-center">
                                        <div class="activity-icon bg-info text-white">
                                            <i class="fas fa-chart-line"></i>
                                        </div>
                                        <div>
                                            <div class="fw-bold">تقرير مالي</div>
                                            <small class="text-muted">تم إنشاء تقرير الميزانية العمومية</small>
                                        </div>
                                        <small class="text-muted ms-auto">منذ 30 دقيقة</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="card dashboard-card">
                            <div class="card-header bg-dark text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-exclamation-triangle me-2"></i>التنبيهات والمهام
                                </h5>
                            </div>
                            <div class="card-body p-0">
                                <div class="activity-item">
                                    <div class="d-flex align-items-center">
                                        <div class="activity-icon bg-warning text-dark">
                                            <i class="fas fa-clock"></i>
                                        </div>
                                        <div>
                                            <div class="fw-bold">فواتير مستحقة</div>
                                            <small class="text-muted">5 فواتير تستحق الدفع خلال 3 أيام</small>
                                        </div>
                                        <small class="text-muted ms-auto">عاجل</small>
                                    </div>
                                </div>
                                <div class="activity-item">
                                    <div class="d-flex align-items-center">
                                        <div class="activity-icon bg-danger text-white">
                                            <i class="fas fa-exclamation"></i>
                                        </div>
                                        <div>
                                            <div class="fw-bold">تجاوز الميزانية</div>
                                            <small class="text-muted">قسم التسويق تجاوز الميزانية بنسبة 15%</small>
                                        </div>
                                        <small class="text-muted ms-auto">مهم</small>
                                    </div>
                                </div>
                                <div class="activity-item">
                                    <div class="d-flex align-items-center">
                                        <div class="activity-icon bg-success text-white">
                                            <i class="fas fa-check"></i>
                                        </div>
                                        <div>
                                            <div class="fw-bold">اكتمال التسوية</div>
                                            <small class="text-muted">تم تسوية جميع حسابات العملاء لشهر يناير</small>
                                        </div>
                                        <small class="text-muted ms-auto">مكتمل</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تهيئة الرسوم البيانية
        document.addEventListener('DOMContentLoaded', function() {
            initializeCharts();
            animateCounters();
        });
        
        function initializeCharts() {
            // رسم بياني للإيرادات والمصروفات
            const revenueExpenseCtx = document.getElementById('revenueExpenseChart').getContext('2d');
            new Chart(revenueExpenseCtx, {
                type: 'line',
                data: {
                    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                    datasets: [{
                        label: 'الإيرادات',
                        data: [25000, 30000, 28000, 35000, 32000, 38000],
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        tension: 0.4
                    }, {
                        label: 'المصروفات',
                        data: [18000, 22000, 20000, 25000, 23000, 27000],
                        borderColor: '#dc3545',
                        backgroundColor: 'rgba(220, 53, 69, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
            
            // رسم بياني دائري لتوزيع المصروفات
            const expenseDistributionCtx = document.getElementById('expenseDistributionChart').getContext('2d');
            new Chart(expenseDistributionCtx, {
                type: 'doughnut',
                data: {
                    labels: ['الرواتب', 'الإيجار', 'التسويق', 'المرافق', 'أخرى'],
                    datasets: [{
                        data: [45, 20, 15, 10, 10],
                        backgroundColor: [
                            '#875a7b',
                            '#d73527',
                            '#f0ad4e',
                            '#5bc0de',
                            '#5cb85c'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                        }
                    }
                }
            });
        }
        
        function animateCounters() {
            const counters = document.querySelectorAll('.stats-number');
            counters.forEach(counter => {
                const target = parseInt(counter.textContent.replace(/,/g, ''));
                let current = 0;
                const increment = target / 100;
                
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    counter.textContent = Math.floor(current).toLocaleString();
                }, 20);
            });
        }
        
        function loadModule(moduleKey) {
            // تحميل الوحدة الفرعية
            showMessage(`جاري تحميل وحدة ${moduleKey}...`, 'info');
            
            // هنا يمكن إضافة كود تحميل الوحدة الفعلي
            setTimeout(() => {
                showMessage(`تم تحميل الوحدة بنجاح`, 'success');
            }, 1000);
        }
        
        function refreshDashboard() {
            showMessage('جاري تحديث لوحة التحكم...', 'info');
            setTimeout(() => {
                location.reload();
            }, 1000);
        }
        
        function exportDashboard() {
            showMessage('جاري تصدير لوحة التحكم...', 'info');
            // هنا يمكن إضافة كود التصدير الفعلي
        }
        
        function showMessage(message, type = 'info') {
            const alert = document.createElement('div');
            alert.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alert.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(alert);
            
            setTimeout(() => {
                alert.remove();
            }, 4000);
        }
    </script>
</body>
</html>
