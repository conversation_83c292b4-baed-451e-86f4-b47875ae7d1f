<?php
/**
 * صفحة إدارة الفترات المحاسبية بأسلوب Odoo
 * Accounting Periods Management Page - Odoo Style
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// تحميل نظام Odoo
require_once '../config/odoo_config.php';
require_once '../config/odoo_database.php';

$odoo_db = OdooDatabase::getInstance();

// تحميل النماذج
require_once '../models/BaseModel.php';
require_once '../models/AccountPeriod.php';

$period_model = new AccountPeriod($odoo_db);

$message = '';
$message_type = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = isset($_POST['action']) ? $_POST['action'] : '';
    
    try {
        switch ($action) {
            case 'create_period':
                $data = array(
                    'name' => $_POST['name'],
                    'code' => $_POST['code'],
                    'date_start' => $_POST['date_start'],
                    'date_end' => $_POST['date_end'],
                    'state' => $_POST['state'] ?? 'draft'
                );
                
                $period_model->create_period($data);
                $message = "تم إنشاء الفترة المحاسبية بنجاح";
                $message_type = 'success';
                break;
                
            case 'close_period':
                $period_id = $_POST['period_id'];
                $period_model->close_period($period_id);
                $message = "تم إقفال الفترة المحاسبية بنجاح";
                $message_type = 'success';
                break;
                
            case 'reopen_period':
                $period_id = $_POST['period_id'];
                $period_model->reopen_period($period_id);
                $message = "تم إعادة فتح الفترة المحاسبية بنجاح";
                $message_type = 'success';
                break;
                
            default:
                throw new Exception('إجراء غير صحيح');
        }
    } catch (Exception $e) {
        $message = 'خطأ: ' . $e->getMessage();
        $message_type = 'error';
    }
}

// جلب البيانات
$periods = $period_model->get_demo_data();

// فلترة حسب الحالة
$filter_state = isset($_GET['state']) ? $_GET['state'] : 'all';
if ($filter_state !== 'all') {
    $filtered_periods = array();
    foreach ($periods as $period) {
        if ($period['state'] === $filter_state) {
            $filtered_periods[] = $period;
        }
    }
    $periods = $filtered_periods;
}

// حالات الفترات
$period_states = array(
    'draft' => array('name' => 'مسودة', 'color' => 'secondary', 'icon' => 'fas fa-edit'),
    'open' => array('name' => 'مفتوحة', 'color' => 'success', 'icon' => 'fas fa-unlock'),
    'closed' => array('name' => 'مقفلة', 'color' => 'danger', 'icon' => 'fas fa-lock')
);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الفترات المحاسبية - نظام ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --odoo-primary: #714B67;
            --odoo-secondary: #8B5A8C;
        }
        
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 0.85rem;
        }
        
        .navbar-odoo {
            background: linear-gradient(45deg, var(--odoo-primary), var(--odoo-secondary));
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .page-header {
            background: linear-gradient(45deg, #E67E22, #D35400);
            color: white;
            padding: 1.5rem;
            border-radius: 15px;
            margin-bottom: 1.5rem;
        }
        
        .view-controls {
            background: white;
            border-radius: 10px;
            padding: 0.8rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        
        .filter-btn {
            border: 1px solid #dee2e6;
            background: white;
            color: #6c757d;
            padding: 0.4rem 0.8rem;
            border-radius: 5px;
            transition: all 0.3s ease;
            text-decoration: none;
            font-size: 0.8rem;
        }
        
        .filter-btn.active {
            background: #E67E22;
            color: white;
            border-color: #E67E22;
        }
        
        .filter-btn:hover {
            background: #D35400;
            color: white;
            border-color: #D35400;
            text-decoration: none;
        }
        
        .period-card {
            background: white;
            border-radius: 12px;
            padding: 1.2rem;
            margin-bottom: 0.8rem;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
            transition: all 0.3s ease;
            border-left: 3px solid;
        }
        
        .period-card.draft { border-left-color: #6c757d; }
        .period-card.open { border-left-color: #28a745; }
        .period-card.closed { border-left-color: #dc3545; }
        
        .period-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.12);
        }
        
        .table-odoo {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
        }
        
        .table-odoo th {
            background: linear-gradient(45deg, #E67E22, #D35400);
            color: white;
            border: none;
            padding: 0.8rem;
            font-size: 0.8rem;
        }
        
        .table-odoo td {
            padding: 0.8rem;
            border-color: #f8f9fa;
            vertical-align: middle;
            font-size: 0.8rem;
        }
        
        .breadcrumb-odoo {
            background: white;
            border-radius: 8px;
            padding: 0.8rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            font-size: 0.8rem;
        }
        
        .stats-card {
            background: white;
            border-radius: 12px;
            padding: 1.2rem;
            text-align: center;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
            border-left: 3px solid;
            margin-bottom: 1rem;
        }
        
        .period-timeline {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
        }
        
        .timeline-item {
            display: flex;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .timeline-item:last-child {
            border-bottom: none;
        }
        
        .timeline-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-left: 1rem;
        }
        
        .period-duration {
            font-size: 0.75rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-odoo">
        <div class="container-fluid">
            <a class="navbar-brand text-white" href="../dashboard.php">
                <i class="fas fa-cube me-2"></i>
                نظام ERP - الفترات المحاسبية
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="../dashboard.php">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-3">
        <!-- مسار التنقل -->
        <nav class="breadcrumb-odoo">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="../dashboard.php">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="#">المحاسبة</a></li>
                <li class="breadcrumb-item active">الفترات المحاسبية</li>
            </ol>
        </nav>

        <!-- العنوان الرئيسي -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h3><i class="fas fa-calendar-alt me-2"></i>إدارة الفترات المحاسبية</h3>
                    <p class="mb-0 small">إدارة وتنظيم الفترات المحاسبية والإقفال</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#addPeriodModal">
                        <i class="fas fa-plus me-2"></i>إضافة فترة جديدة
                    </button>
                </div>
            </div>
        </div>

        <!-- رسائل النظام -->
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $message_type === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
                <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- إحصائيات الفترات -->
        <div class="row mb-3">
            <?php foreach ($period_states as $state => $info): ?>
                <div class="col-md-3">
                    <div class="stats-card border-<?php echo $info['color']; ?>">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <i class="<?php echo $info['icon']; ?> text-<?php echo $info['color']; ?>" style="font-size: 1.5rem;"></i>
                        </div>
                        <h4 class="text-<?php echo $info['color']; ?>"><?php 
                            $count = 0;
                            foreach($period_model->get_demo_data() as $period) { 
                                if($period['state'] === $state) $count++; 
                            }
                            echo $count;
                        ?></h4>
                        <p class="mb-0 small"><?php echo $info['name']; ?></p>
                    </div>
                </div>
            <?php endforeach; ?>
            <div class="col-md-3">
                <div class="stats-card border-primary">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <i class="fas fa-calendar text-primary" style="font-size: 1.5rem;"></i>
                    </div>
                    <h4 class="text-primary"><?php echo count($period_model->get_demo_data()); ?></h4>
                    <p class="mb-0 small">إجمالي الفترات</p>
                </div>
            </div>
        </div>

        <!-- أدوات التحكم -->
        <div class="view-controls">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="btn-group" role="group">
                        <a href="?state=all" class="filter-btn <?php echo $filter_state === 'all' ? 'active' : ''; ?>">
                            جميع الفترات
                        </a>
                        <?php foreach ($period_states as $state => $info): ?>
                            <a href="?state=<?php echo $state; ?>" class="filter-btn <?php echo $filter_state === $state ? 'active' : ''; ?>">
                                <?php echo $info['name']; ?>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="d-flex justify-content-end">
                        <input type="text" class="form-control search-box" placeholder="البحث في الفترات..." style="max-width: 200px; font-size: 0.8rem;">
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول الفترات -->
        <div class="table-odoo">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>الكود</th>
                        <th>اسم الفترة</th>
                        <th>تاريخ البداية</th>
                        <th>تاريخ النهاية</th>
                        <th>المدة</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($periods as $period): 
                        $state_info = $period_states[$period['state']];
                        $start_date = new DateTime($period['date_start']);
                        $end_date = new DateTime($period['date_end']);
                        $duration = $start_date->diff($end_date)->days + 1;
                    ?>
                        <tr>
                            <td><strong><?php echo $period['code']; ?></strong></td>
                            <td><?php echo $period['name']; ?></td>
                            <td><?php echo date('Y-m-d', strtotime($period['date_start'])); ?></td>
                            <td><?php echo date('Y-m-d', strtotime($period['date_end'])); ?></td>
                            <td>
                                <span class="period-duration"><?php echo $duration; ?> يوم</span>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo $state_info['color']; ?>">
                                    <i class="<?php echo $state_info['icon']; ?> me-1"></i><?php echo $state_info['name']; ?>
                                </span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <?php if ($period['state'] === 'open'): ?>
                                        <button class="btn btn-outline-danger btn-sm" onclick="closePeriod(<?php echo $period['id']; ?>)">
                                            <i class="fas fa-lock"></i> إقفال
                                        </button>
                                    <?php elseif ($period['state'] === 'closed'): ?>
                                        <button class="btn btn-outline-success btn-sm" onclick="reopenPeriod(<?php echo $period['id']; ?>)">
                                            <i class="fas fa-unlock"></i> إعادة فتح
                                        </button>
                                    <?php endif; ?>
                                    <button class="btn btn-outline-info btn-sm" onclick="viewPeriod(<?php echo $period['id']; ?>)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" onclick="editPeriod(<?php echo $period['id']; ?>)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <!-- الخط الزمني للفترات -->
        <div class="period-timeline mt-4">
            <h6><i class="fas fa-timeline me-2"></i>الخط الزمني للفترات المحاسبية</h6>
            <?php foreach ($periods as $period): 
                $state_info = $period_states[$period['state']];
            ?>
                <div class="timeline-item">
                    <div class="timeline-icon bg-<?php echo $state_info['color']; ?>">
                        <i class="<?php echo $state_info['icon']; ?>"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong><?php echo $period['name']; ?></strong>
                                <span class="badge bg-<?php echo $state_info['color']; ?> ms-2"><?php echo $state_info['name']; ?></span>
                            </div>
                            <div class="text-muted small">
                                <?php echo date('Y-m-d', strtotime($period['date_start'])); ?> - 
                                <?php echo date('Y-m-d', strtotime($period['date_end'])); ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function closePeriod(periodId) {
            if (confirm('هل أنت متأكد من إقفال هذه الفترة المحاسبية؟')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="close_period">
                    <input type="hidden" name="period_id" value="${periodId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
        
        function reopenPeriod(periodId) {
            if (confirm('هل أنت متأكد من إعادة فتح هذه الفترة المحاسبية؟')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="reopen_period">
                    <input type="hidden" name="period_id" value="${periodId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
        
        function viewPeriod(periodId) {
            alert('سيتم تطوير صفحة عرض تفاصيل الفترة قريباً');
        }
        
        function editPeriod(periodId) {
            alert('سيتم تطوير نافذة تعديل الفترة قريباً');
        }
        
        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const elements = document.querySelectorAll('.stats-card, .table-odoo, .period-timeline');
            elements.forEach((element, index) => {
                element.style.opacity = '0';
                element.style.transform = 'translateY(15px)';
                
                setTimeout(() => {
                    element.style.transition = 'all 0.5s ease';
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
        
        // البحث المباشر
        document.querySelector('.search-box').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const rows = document.querySelectorAll('tbody tr');
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
