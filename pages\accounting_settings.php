<?php
/**
 * صفحة إعدادات المحاسبة المتقدمة بأسلوب Odoo
 * Advanced Accounting Settings Page - Odoo Style
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// تحميل نظام Odoo
require_once '../config/odoo_config.php';
require_once '../config/odoo_database.php';

$odoo_db = OdooDatabase::getInstance();

$message = '';
$message_type = '';

// معالجة حفظ الإعدادات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // حفظ الإعدادات في ملف أو قاعدة البيانات
        $settings = array(
            'company_name' => $_POST['company_name'],
            'company_currency' => $_POST['company_currency'],
            'fiscal_year_start' => $_POST['fiscal_year_start'],
            'decimal_precision' => $_POST['decimal_precision'],
            'auto_reconcile' => isset($_POST['auto_reconcile']),
            'multi_currency' => isset($_POST['multi_currency']),
            'analytic_accounting' => isset($_POST['analytic_accounting']),
            'budget_management' => isset($_POST['budget_management']),
            'asset_management' => isset($_POST['asset_management']),
            'cost_center' => isset($_POST['cost_center']),
            'invoice_policy' => $_POST['invoice_policy'],
            'payment_terms' => $_POST['payment_terms'],
            'tax_calculation' => $_POST['tax_calculation'],
            'journal_sequence' => $_POST['journal_sequence']
        );
        
        // حفظ في ملف JSON
        file_put_contents('../config/accounting_settings.json', json_encode($settings, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        
        $message = "تم حفظ الإعدادات بنجاح";
        $message_type = 'success';
        
    } catch (Exception $e) {
        $message = 'خطأ في حفظ الإعدادات: ' . $e->getMessage();
        $message_type = 'error';
    }
}

// تحميل الإعدادات الحالية
$settings = array(
    'company_name' => 'شركة النظام المتقدم',
    'company_currency' => 'SAR',
    'fiscal_year_start' => '01-01',
    'decimal_precision' => 2,
    'auto_reconcile' => true,
    'multi_currency' => true,
    'analytic_accounting' => false,
    'budget_management' => false,
    'asset_management' => false,
    'cost_center' => false,
    'invoice_policy' => 'order',
    'payment_terms' => 'immediate',
    'tax_calculation' => 'exclusive',
    'journal_sequence' => 'monthly'
);

// تحميل من ملف JSON إذا كان موجوداً
if (file_exists('../config/accounting_settings.json')) {
    $saved_settings = json_decode(file_get_contents('../config/accounting_settings.json'), true);
    if ($saved_settings) {
        $settings = array_merge($settings, $saved_settings);
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات المحاسبة - نظام ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --odoo-primary: #714B67;
            --odoo-secondary: #8B5A8C;
        }
        
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 0.85rem;
        }
        
        .navbar-odoo {
            background: linear-gradient(45deg, var(--odoo-primary), var(--odoo-secondary));
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .page-header {
            background: linear-gradient(45deg, #9B59B6, #8E44AD);
            color: white;
            padding: 1.5rem;
            border-radius: 15px;
            margin-bottom: 1.5rem;
        }
        
        .settings-section {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
            border-left: 4px solid #9B59B6;
        }
        
        .breadcrumb-odoo {
            background: white;
            border-radius: 8px;
            padding: 0.8rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            font-size: 0.8rem;
        }
        
        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.5rem;
        }
        
        .form-control, .form-select {
            border-radius: 8px;
            border: 1px solid #dee2e6;
            padding: 0.6rem 0.8rem;
            font-size: 0.85rem;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #9B59B6;
            box-shadow: 0 0 0 0.2rem rgba(155, 89, 182, 0.25);
        }
        
        .form-check-input:checked {
            background-color: #9B59B6;
            border-color: #9B59B6;
        }
        
        .section-title {
            color: #9B59B6;
            font-weight: 600;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .feature-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.5rem;
            border-left: 3px solid #9B59B6;
        }
        
        .feature-description {
            font-size: 0.75rem;
            color: #6c757d;
            margin-top: 0.25rem;
        }
        
        .btn-save {
            background: linear-gradient(45deg, #9B59B6, #8E44AD);
            border: none;
            color: white;
            padding: 0.8rem 2rem;
            border-radius: 8px;
            font-weight: 600;
        }
        
        .btn-save:hover {
            background: linear-gradient(45deg, #8E44AD, #7D3C98);
            color: white;
        }
        
        .settings-preview {
            background: #e8f5e8;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-odoo">
        <div class="container-fluid">
            <a class="navbar-brand text-white" href="../dashboard.php">
                <i class="fas fa-cube me-2"></i>
                نظام ERP - إعدادات المحاسبة
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="../dashboard.php">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-3">
        <!-- مسار التنقل -->
        <nav class="breadcrumb-odoo">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="../dashboard.php">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="#">المحاسبة</a></li>
                <li class="breadcrumb-item active">إعدادات المحاسبة</li>
            </ol>
        </nav>

        <!-- العنوان الرئيسي -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h3><i class="fas fa-cogs me-2"></i>إعدادات المحاسبة المتقدمة</h3>
                    <p class="mb-0 small">تخصيص وإعداد النظام المحاسبي حسب احتياجات الشركة</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-light btn-sm" onclick="resetSettings()">
                        <i class="fas fa-undo me-2"></i>إعادة تعيين
                    </button>
                </div>
            </div>
        </div>

        <!-- رسائل النظام -->
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $message_type === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
                <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <form method="POST" id="settingsForm">
            <div class="row">
                <!-- الإعدادات الأساسية -->
                <div class="col-md-6">
                    <div class="settings-section">
                        <h5 class="section-title"><i class="fas fa-building me-2"></i>الإعدادات الأساسية</h5>
                        
                        <div class="mb-3">
                            <label class="form-label">اسم الشركة</label>
                            <input type="text" name="company_name" class="form-control" value="<?php echo $settings['company_name']; ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">العملة الأساسية</label>
                            <select name="company_currency" class="form-select" required>
                                <option value="SAR" <?php echo $settings['company_currency'] === 'SAR' ? 'selected' : ''; ?>>ريال سعودي (SAR)</option>
                                <option value="USD" <?php echo $settings['company_currency'] === 'USD' ? 'selected' : ''; ?>>دولار أمريكي (USD)</option>
                                <option value="EUR" <?php echo $settings['company_currency'] === 'EUR' ? 'selected' : ''; ?>>يورو (EUR)</option>
                                <option value="GBP" <?php echo $settings['company_currency'] === 'GBP' ? 'selected' : ''; ?>>جنيه إسترليني (GBP)</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">بداية السنة المالية</label>
                            <input type="text" name="fiscal_year_start" class="form-control" value="<?php echo $settings['fiscal_year_start']; ?>" placeholder="MM-DD" required>
                            <div class="feature-description">مثال: 01-01 للسنة الميلادية، 07-01 للسنة المالية</div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">دقة الأرقام العشرية</label>
                            <select name="decimal_precision" class="form-select">
                                <option value="0" <?php echo $settings['decimal_precision'] == 0 ? 'selected' : ''; ?>>بدون أرقام عشرية</option>
                                <option value="2" <?php echo $settings['decimal_precision'] == 2 ? 'selected' : ''; ?>>رقمان عشريان</option>
                                <option value="3" <?php echo $settings['decimal_precision'] == 3 ? 'selected' : ''; ?>>ثلاثة أرقام عشرية</option>
                                <option value="4" <?php echo $settings['decimal_precision'] == 4 ? 'selected' : ''; ?>>أربعة أرقام عشرية</option>
                            </select>
                        </div>
                    </div>

                    <!-- إعدادات الفواتير والمدفوعات -->
                    <div class="settings-section">
                        <h5 class="section-title"><i class="fas fa-file-invoice me-2"></i>الفواتير والمدفوعات</h5>
                        
                        <div class="mb-3">
                            <label class="form-label">سياسة الفوترة</label>
                            <select name="invoice_policy" class="form-select">
                                <option value="order" <?php echo $settings['invoice_policy'] === 'order' ? 'selected' : ''; ?>>عند الطلب</option>
                                <option value="delivery" <?php echo $settings['invoice_policy'] === 'delivery' ? 'selected' : ''; ?>>عند التسليم</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">شروط الدفع الافتراضية</label>
                            <select name="payment_terms" class="form-select">
                                <option value="immediate" <?php echo $settings['payment_terms'] === 'immediate' ? 'selected' : ''; ?>>فوري</option>
                                <option value="15_days" <?php echo $settings['payment_terms'] === '15_days' ? 'selected' : ''; ?>>15 يوم</option>
                                <option value="30_days" <?php echo $settings['payment_terms'] === '30_days' ? 'selected' : ''; ?>>30 يوم</option>
                                <option value="45_days" <?php echo $settings['payment_terms'] === '45_days' ? 'selected' : ''; ?>>45 يوم</option>
                                <option value="60_days" <?php echo $settings['payment_terms'] === '60_days' ? 'selected' : ''; ?>>60 يوم</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">حساب الضرائب</label>
                            <select name="tax_calculation" class="form-select">
                                <option value="exclusive" <?php echo $settings['tax_calculation'] === 'exclusive' ? 'selected' : ''; ?>>خارج السعر</option>
                                <option value="inclusive" <?php echo $settings['tax_calculation'] === 'inclusive' ? 'selected' : ''; ?>>داخل السعر</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- الميزات المتقدمة -->
                <div class="col-md-6">
                    <div class="settings-section">
                        <h5 class="section-title"><i class="fas fa-star me-2"></i>الميزات المتقدمة</h5>
                        
                        <div class="feature-card">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="auto_reconcile" id="autoReconcile" <?php echo $settings['auto_reconcile'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="autoReconcile">
                                    <strong>التسوية التلقائية</strong>
                                </label>
                            </div>
                            <div class="feature-description">تسوية القيود المحاسبية تلقائياً عند التطابق</div>
                        </div>
                        
                        <div class="feature-card">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="multi_currency" id="multiCurrency" <?php echo $settings['multi_currency'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="multiCurrency">
                                    <strong>العملات المتعددة</strong>
                                </label>
                            </div>
                            <div class="feature-description">دعم التعامل بعملات متعددة مع أسعار الصرف</div>
                        </div>
                        
                        <div class="feature-card">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="analytic_accounting" id="analyticAccounting" <?php echo $settings['analytic_accounting'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="analyticAccounting">
                                    <strong>المحاسبة التحليلية</strong>
                                </label>
                            </div>
                            <div class="feature-description">تتبع التكاليف والإيرادات حسب المشاريع والأقسام</div>
                        </div>
                        
                        <div class="feature-card">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="budget_management" id="budgetManagement" <?php echo $settings['budget_management'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="budgetManagement">
                                    <strong>إدارة الميزانيات</strong>
                                </label>
                            </div>
                            <div class="feature-description">إنشاء ومتابعة الميزانيات والمقارنة مع الفعلي</div>
                        </div>
                        
                        <div class="feature-card">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="asset_management" id="assetManagement" <?php echo $settings['asset_management'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="assetManagement">
                                    <strong>إدارة الأصول</strong>
                                </label>
                            </div>
                            <div class="feature-description">تتبع الأصول الثابتة وحساب الاستهلاك</div>
                        </div>
                        
                        <div class="feature-card">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="cost_center" id="costCenter" <?php echo $settings['cost_center'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="costCenter">
                                    <strong>مراكز التكلفة</strong>
                                </label>
                            </div>
                            <div class="feature-description">تقسيم التكاليف على مراكز التكلفة المختلفة</div>
                        </div>
                    </div>

                    <!-- إعدادات اليوميات -->
                    <div class="settings-section">
                        <h5 class="section-title"><i class="fas fa-book me-2"></i>إعدادات اليوميات</h5>
                        
                        <div class="mb-3">
                            <label class="form-label">تسلسل ترقيم اليوميات</label>
                            <select name="journal_sequence" class="form-select">
                                <option value="yearly" <?php echo $settings['journal_sequence'] === 'yearly' ? 'selected' : ''; ?>>سنوي</option>
                                <option value="monthly" <?php echo $settings['journal_sequence'] === 'monthly' ? 'selected' : ''; ?>>شهري</option>
                                <option value="daily" <?php echo $settings['journal_sequence'] === 'daily' ? 'selected' : ''; ?>>يومي</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معاينة الإعدادات -->
            <div class="settings-preview">
                <h6><i class="fas fa-eye me-2"></i>معاينة الإعدادات الحالية</h6>
                <div class="row">
                    <div class="col-md-6">
                        <small><strong>الشركة:</strong> <?php echo $settings['company_name']; ?></small><br>
                        <small><strong>العملة:</strong> <?php echo $settings['company_currency']; ?></small><br>
                        <small><strong>السنة المالية:</strong> تبدأ في <?php echo $settings['fiscal_year_start']; ?></small><br>
                    </div>
                    <div class="col-md-6">
                        <small><strong>الدقة العشرية:</strong> <?php echo $settings['decimal_precision']; ?> أرقام</small><br>
                        <small><strong>شروط الدفع:</strong> <?php echo $settings['payment_terms']; ?></small><br>
                        <small><strong>حساب الضرائب:</strong> <?php echo $settings['tax_calculation']; ?></small><br>
                    </div>
                </div>
            </div>

            <!-- أزرار الحفظ -->
            <div class="text-center mt-4 mb-4">
                <button type="submit" class="btn btn-save">
                    <i class="fas fa-save me-2"></i>حفظ الإعدادات
                </button>
                <a href="../dashboard.php" class="btn btn-outline-secondary ms-2">
                    <i class="fas fa-times me-2"></i>إلغاء
                </a>
            </div>
        </form>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function resetSettings() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
                document.getElementById('settingsForm').reset();
            }
        }

        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const sections = document.querySelectorAll('.settings-section');
            sections.forEach((section, index) => {
                section.style.opacity = '0';
                section.style.transform = 'translateY(15px)';
                
                setTimeout(() => {
                    section.style.transition = 'all 0.5s ease';
                    section.style.opacity = '1';
                    section.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
