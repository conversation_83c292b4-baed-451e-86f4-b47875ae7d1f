<?php
/**
 * صفحة إدارة النسخ الاحتياطية بأسلوب Odoo
 * Backup Management Page - Odoo Style
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// التحقق من الصلاحيات
$user_groups = isset($_SESSION['groups']) ? $_SESSION['groups'] : array();
if (!in_array('admin', $user_groups)) {
    header('Location: ../dashboard.php?error=access_denied');
    exit();
}

// تحميل نظام قاعدة البيانات
require_once '../config/odoo_config.php';
require_once '../config/odoo_database.php';

$db = OdooDatabase::getInstance();
$message = '';
$message_type = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = isset($_POST['action']) ? $_POST['action'] : '';
    
    try {
        switch ($action) {
            case 'create_backup':
                $backup_type = isset($_POST['backup_type']) ? $_POST['backup_type'] : 'full';
                $include_files = isset($_POST['include_files']) ? true : false;
                
                // محاكاة إنشاء نسخة احتياطية
                $backup_name = 'backup_' . date('Y-m-d_H-i-s') . '.sql';
                
                $message = "تم إنشاء النسخة الاحتياطية '{$backup_name}' بنجاح";
                $message_type = 'success';
                break;
                
            case 'restore_backup':
                $backup_file = isset($_POST['backup_file']) ? $_POST['backup_file'] : '';
                
                if (empty($backup_file)) {
                    throw new Exception('يجب اختيار ملف النسخة الاحتياطية');
                }
                
                $message = "تم استعادة النسخة الاحتياطية '{$backup_file}' بنجاح";
                $message_type = 'success';
                break;
                
            case 'delete_backup':
                $backup_id = isset($_POST['backup_id']) ? $_POST['backup_id'] : '';
                
                $message = "تم حذف النسخة الاحتياطية بنجاح";
                $message_type = 'success';
                break;
                
            default:
                throw new Exception('إجراء غير صحيح');
        }
    } catch (Exception $e) {
        $message = 'خطأ: ' . $e->getMessage();
        $message_type = 'error';
    }
}

// النسخ الاحتياطية المتاحة (بيانات تجريبية)
$backups = array(
    array(
        'id' => 1,
        'name' => 'backup_2024-01-15_10-30-00.sql',
        'type' => 'full',
        'size' => '2.5 MB',
        'created_at' => '2024-01-15 10:30:00',
        'created_by' => 'مدير النظام',
        'status' => 'completed',
        'description' => 'نسخة احتياطية كاملة تلقائية'
    ),
    array(
        'id' => 2,
        'name' => 'backup_2024-01-14_23-00-00.sql',
        'type' => 'full',
        'size' => '2.3 MB',
        'created_at' => '2024-01-14 23:00:00',
        'created_by' => 'النظام',
        'status' => 'completed',
        'description' => 'نسخة احتياطية يومية مجدولة'
    ),
    array(
        'id' => 3,
        'name' => 'backup_2024-01-13_15-45-00.sql',
        'type' => 'partial',
        'size' => '1.8 MB',
        'created_at' => '2024-01-13 15:45:00',
        'created_by' => 'أحمد محمد',
        'status' => 'completed',
        'description' => 'نسخة احتياطية جزئية قبل التحديث'
    ),
    array(
        'id' => 4,
        'name' => 'backup_2024-01-12_09-15-00.sql',
        'type' => 'full',
        'size' => '2.1 MB',
        'created_at' => '2024-01-12 09:15:00',
        'created_by' => 'مدير النظام',
        'status' => 'completed',
        'description' => 'نسخة احتياطية أسبوعية'
    ),
    array(
        'id' => 5,
        'name' => 'backup_2024-01-11_16-20-00.sql',
        'type' => 'full',
        'size' => '2.0 MB',
        'created_at' => '2024-01-11 16:20:00',
        'created_by' => 'النظام',
        'status' => 'failed',
        'description' => 'نسخة احتياطية فاشلة - خطأ في القرص'
    )
);

// إعدادات النسخ الاحتياطي
$backup_settings = array(
    'auto_backup' => true,
    'backup_frequency' => 'daily',
    'backup_time' => '23:00',
    'retention_days' => 30,
    'max_backups' => 10,
    'include_files' => true,
    'compress_backups' => true,
    'backup_location' => '/var/backups/odoo/'
);

// طريقة العرض المحددة
$view_mode = isset($_GET['view']) ? $_GET['view'] : 'list';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة النسخ الاحتياطية - نظام ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --odoo-primary: #714B67;
            --odoo-secondary: #8B5A8C;
        }
        
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 0.85rem;
        }
        
        .navbar-odoo {
            background: linear-gradient(45deg, var(--odoo-primary), var(--odoo-secondary));
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .page-header {
            background: linear-gradient(45deg, #9B59B6, #8E44AD);
            color: white;
            padding: 1.5rem;
            border-radius: 15px;
            margin-bottom: 1.5rem;
        }
        
        .view-controls {
            background: white;
            border-radius: 10px;
            padding: 0.8rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        
        .view-btn {
            border: 1px solid #dee2e6;
            background: white;
            color: #6c757d;
            padding: 0.4rem 0.8rem;
            border-radius: 5px;
            transition: all 0.3s ease;
            text-decoration: none;
            font-size: 0.8rem;
        }
        
        .view-btn.active {
            background: #9B59B6;
            color: white;
            border-color: #9B59B6;
        }
        
        .view-btn:hover {
            background: #8E44AD;
            color: white;
            border-color: #8E44AD;
            text-decoration: none;
        }
        
        .backup-card {
            background: white;
            border-radius: 12px;
            padding: 1.2rem;
            margin-bottom: 0.8rem;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
            transition: all 0.3s ease;
            border-left: 3px solid;
        }
        
        .backup-card.completed {
            border-left-color: #28a745;
        }
        
        .backup-card.failed {
            border-left-color: #dc3545;
        }
        
        .backup-card.running {
            border-left-color: #ffc107;
        }
        
        .backup-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.12);
        }
        
        .backup-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }
        
        .table-odoo {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
        }
        
        .table-odoo th {
            background: linear-gradient(45deg, #9B59B6, #8E44AD);
            color: white;
            border: none;
            padding: 0.8rem;
            font-size: 0.8rem;
        }
        
        .table-odoo td {
            padding: 0.8rem;
            border-color: #f8f9fa;
            vertical-align: middle;
            font-size: 0.8rem;
        }
        
        .breadcrumb-odoo {
            background: white;
            border-radius: 8px;
            padding: 0.8rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            font-size: 0.8rem;
        }
        
        .stats-card {
            background: white;
            border-radius: 12px;
            padding: 1.2rem;
            text-align: center;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
            border-left: 3px solid;
            margin-bottom: 1rem;
        }
        
        .settings-card {
            background: white;
            border-radius: 12px;
            padding: 1.2rem;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
            border-left: 3px solid #17a2b8;
        }
        
        .progress-ring {
            width: 60px;
            height: 60px;
        }
        
        .progress-ring circle {
            fill: transparent;
            stroke: #e9ecef;
            stroke-width: 4;
        }
        
        .progress-ring .progress {
            stroke: #28a745;
            stroke-dasharray: 157;
            stroke-dashoffset: 157;
            transition: stroke-dashoffset 0.5s;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-odoo">
        <div class="container-fluid">
            <a class="navbar-brand text-white" href="../dashboard.php">
                <i class="fas fa-cube me-2"></i>
                نظام ERP - إدارة النسخ الاحتياطية
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="../dashboard.php">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
                <a class="nav-link text-white" href="system_admin.php">
                    <i class="fas fa-tools me-1"></i>إدارة النظام
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-3">
        <!-- مسار التنقل -->
        <nav class="breadcrumb-odoo">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="../dashboard.php">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="system_admin.php">إدارة النظام</a></li>
                <li class="breadcrumb-item active">إدارة النسخ الاحتياطية</li>
            </ol>
        </nav>

        <!-- العنوان الرئيسي -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h3><i class="fas fa-database me-2"></i>إدارة النسخ الاحتياطية</h3>
                    <p class="mb-0 small">إنشاء واستعادة وإدارة النسخ الاحتياطية للنظام</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-light btn-sm me-2" data-bs-toggle="modal" data-bs-target="#createBackupModal">
                        <i class="fas fa-plus me-2"></i>إنشاء نسخة احتياطية
                    </button>
                    <button class="btn btn-outline-light btn-sm" data-bs-toggle="modal" data-bs-target="#restoreBackupModal">
                        <i class="fas fa-upload me-2"></i>استعادة
                    </button>
                </div>
            </div>
        </div>

        <!-- رسائل النظام -->
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $message_type === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
                <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- إحصائيات النسخ الاحتياطية -->
        <div class="row mb-3">
            <div class="col-md-3">
                <div class="stats-card border-success">
                    <h3 class="text-success"><?php
                        $completed_count = 0;
                        foreach($backups as $backup) { if($backup['status'] === 'completed') $completed_count++; }
                        echo $completed_count;
                    ?></h3>
                    <p class="mb-0">نسخ مكتملة</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card border-danger">
                    <h3 class="text-danger"><?php
                        $failed_count = 0;
                        foreach($backups as $backup) { if($backup['status'] === 'failed') $failed_count++; }
                        echo $failed_count;
                    ?></h3>
                    <p class="mb-0">نسخ فاشلة</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card border-primary">
                    <h3 class="text-primary"><?php
                        $total_size = 0;
                        foreach($backups as $backup) {
                            if($backup['status'] === 'completed') {
                                $size_mb = floatval(str_replace(' MB', '', $backup['size']));
                                $total_size += $size_mb;
                            }
                        }
                        echo number_format($total_size, 1) . ' MB';
                    ?></h3>
                    <p class="mb-0">إجمالي الحجم</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card border-info">
                    <h3 class="text-info"><?php echo $backup_settings['retention_days']; ?></h3>
                    <p class="mb-0">أيام الاحتفاظ</p>
                </div>
            </div>
        </div>

        <!-- أدوات التحكم والعرض -->
        <div class="view-controls">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="btn-group" role="group">
                        <a href="?view=list" class="view-btn <?php echo $view_mode === 'list' ? 'active' : ''; ?>">
                            <i class="fas fa-list me-1"></i>قائمة النسخ
                        </a>
                        <a href="?view=settings" class="view-btn <?php echo $view_mode === 'settings' ? 'active' : ''; ?>">
                            <i class="fas fa-cogs me-1"></i>الإعدادات
                        </a>
                        <a href="?view=schedule" class="view-btn <?php echo $view_mode === 'schedule' ? 'active' : ''; ?>">
                            <i class="fas fa-calendar me-1"></i>الجدولة
                        </a>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="d-flex justify-content-end">
                        <input type="text" class="form-control search-box" placeholder="البحث في النسخ..." style="max-width: 200px; font-size: 0.8rem;">
                    </div>
                </div>
            </div>
        </div>

        <!-- محتوى العرض -->
        <?php if ($view_mode === 'list'): ?>
            <!-- قائمة النسخ الاحتياطية -->
            <div class="table-odoo">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>اسم النسخة</th>
                            <th>النوع</th>
                            <th>الحجم</th>
                            <th>تاريخ الإنشاء</th>
                            <th>المنشئ</th>
                            <th>الحالة</th>
                            <th>الوصف</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($backups as $backup): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="backup-icon me-2 bg-<?php echo $backup['status'] === 'completed' ? 'success' : ($backup['status'] === 'failed' ? 'danger' : 'warning'); ?>" style="width: 35px; height: 35px; font-size: 0.9rem;">
                                            <i class="fas fa-<?php echo $backup['type'] === 'full' ? 'database' : 'file-archive'; ?>"></i>
                                        </div>
                                        <div>
                                            <strong style="font-size: 0.8rem;"><?php echo $backup['name']; ?></strong>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo $backup['type'] === 'full' ? 'primary' : 'info'; ?>">
                                        <?php echo $backup['type'] === 'full' ? 'كاملة' : 'جزئية'; ?>
                                    </span>
                                </td>
                                <td><?php echo $backup['size']; ?></td>
                                <td style="font-size: 0.75rem;"><?php echo date('Y-m-d H:i', strtotime($backup['created_at'])); ?></td>
                                <td><?php echo $backup['created_by']; ?></td>
                                <td>
                                    <?php if ($backup['status'] === 'completed'): ?>
                                        <span class="badge bg-success">مكتملة</span>
                                    <?php elseif ($backup['status'] === 'failed'): ?>
                                        <span class="badge bg-danger">فاشلة</span>
                                    <?php else: ?>
                                        <span class="badge bg-warning">قيد التشغيل</span>
                                    <?php endif; ?>
                                </td>
                                <td style="font-size: 0.75rem;"><?php echo $backup['description']; ?></td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <?php if ($backup['status'] === 'completed'): ?>
                                            <button class="btn btn-outline-success btn-sm" title="تحميل" style="font-size: 0.7rem;">
                                                <i class="fas fa-download"></i>
                                            </button>
                                            <button class="btn btn-outline-primary btn-sm" title="استعادة" style="font-size: 0.7rem;">
                                                <i class="fas fa-undo"></i>
                                            </button>
                                        <?php endif; ?>
                                        <button class="btn btn-outline-info btn-sm" title="تفاصيل" style="font-size: 0.7rem;">
                                            <i class="fas fa-info"></i>
                                        </button>
                                        <button class="btn btn-outline-danger btn-sm" title="حذف" style="font-size: 0.7rem;">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

        <?php elseif ($view_mode === 'settings'): ?>
            <!-- إعدادات النسخ الاحتياطي -->
            <div class="row">
                <div class="col-md-8">
                    <div class="settings-card">
                        <h5><i class="fas fa-cogs me-2"></i>إعدادات النسخ الاحتياطي</h5>

                        <form method="POST">
                            <input type="hidden" name="action" value="update_settings">

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">النسخ الاحتياطي التلقائي</label>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="autoBackup" <?php echo $backup_settings['auto_backup'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="autoBackup">تفعيل النسخ التلقائي</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">تكرار النسخ</label>
                                    <select class="form-select form-select-sm">
                                        <option value="daily" <?php echo $backup_settings['backup_frequency'] === 'daily' ? 'selected' : ''; ?>>يومي</option>
                                        <option value="weekly" <?php echo $backup_settings['backup_frequency'] === 'weekly' ? 'selected' : ''; ?>>أسبوعي</option>
                                        <option value="monthly" <?php echo $backup_settings['backup_frequency'] === 'monthly' ? 'selected' : ''; ?>>شهري</option>
                                    </select>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">وقت النسخ</label>
                                    <input type="time" class="form-control form-control-sm" value="<?php echo $backup_settings['backup_time']; ?>">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">أيام الاحتفاظ</label>
                                    <input type="number" class="form-control form-control-sm" value="<?php echo $backup_settings['retention_days']; ?>" min="1" max="365">
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">الحد الأقصى للنسخ</label>
                                    <input type="number" class="form-control form-control-sm" value="<?php echo $backup_settings['max_backups']; ?>" min="1" max="100">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">مجلد النسخ</label>
                                    <input type="text" class="form-control form-control-sm" value="<?php echo $backup_settings['backup_location']; ?>" readonly>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="includeFiles" <?php echo $backup_settings['include_files'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="includeFiles">تضمين الملفات</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="compressBackups" <?php echo $backup_settings['compress_backups'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="compressBackups">ضغط النسخ</label>
                                    </div>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary btn-sm">
                                <i class="fas fa-save me-2"></i>حفظ الإعدادات
                            </button>
                        </form>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="settings-card">
                        <h6><i class="fas fa-info-circle me-2"></i>معلومات النظام</h6>
                        <ul class="list-unstyled small">
                            <li><strong>مساحة القرص المتاحة:</strong> 15.2 GB</li>
                            <li><strong>مساحة النسخ المستخدمة:</strong> 25.8 MB</li>
                            <li><strong>آخر نسخة احتياطية:</strong> 2024-01-15 10:30</li>
                            <li><strong>النسخة التالية:</strong> 2024-01-16 23:00</li>
                            <li><strong>حالة الخدمة:</strong> <span class="text-success">نشطة</span></li>
                        </ul>

                        <div class="mt-3">
                            <h6>استخدام المساحة</h6>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-info" style="width: 15%"></div>
                            </div>
                            <small class="text-muted">15% من المساحة المخصصة</small>
                        </div>
                    </div>
                </div>
            </div>

        <?php else: ?>
            <!-- جدولة النسخ الاحتياطي -->
            <div class="row">
                <div class="col-md-12">
                    <div class="settings-card">
                        <h5><i class="fas fa-calendar me-2"></i>جدولة النسخ الاحتياطية</h5>

                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>النوع</th>
                                        <th>التكرار</th>
                                        <th>الوقت</th>
                                        <th>آخر تشغيل</th>
                                        <th>التشغيل التالي</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><span class="badge bg-primary">نسخة كاملة</span></td>
                                        <td>يومي</td>
                                        <td>23:00</td>
                                        <td>2024-01-15 23:00</td>
                                        <td>2024-01-16 23:00</td>
                                        <td><span class="badge bg-success">نشط</span></td>
                                        <td>
                                            <button class="btn btn-outline-primary btn-sm">تعديل</button>
                                            <button class="btn btn-outline-danger btn-sm">إيقاف</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><span class="badge bg-info">نسخة جزئية</span></td>
                                        <td>كل 6 ساعات</td>
                                        <td>06:00, 12:00, 18:00, 00:00</td>
                                        <td>2024-01-15 18:00</td>
                                        <td>2024-01-16 00:00</td>
                                        <td><span class="badge bg-warning">معلق</span></td>
                                        <td>
                                            <button class="btn btn-outline-success btn-sm">تفعيل</button>
                                            <button class="btn btn-outline-primary btn-sm">تعديل</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <button class="btn btn-primary btn-sm mt-3">
                            <i class="fas fa-plus me-2"></i>إضافة جدولة جديدة
                        </button>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const items = document.querySelectorAll('.backup-card, .settings-card');
            items.forEach((item, index) => {
                item.style.opacity = '0';
                item.style.transform = 'translateY(15px)';

                setTimeout(() => {
                    item.style.transition = 'all 0.5s ease';
                    item.style.opacity = '1';
                    item.style.transform = 'translateY(0)';
                }, index * 80);
            });
        });

        // البحث المباشر
        document.querySelector('.search-box').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const rows = document.querySelectorAll('tbody tr');

            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
