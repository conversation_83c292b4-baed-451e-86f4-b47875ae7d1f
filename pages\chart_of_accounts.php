<?php
/**
 * صفحة دليل الحسابات بأسلوب Odoo
 * Chart of Accounts Page - Odoo Style
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// تحميل نظام Odoo
require_once '../config/odoo_config.php';
require_once '../config/odoo_database.php';

$odoo_db = OdooDatabase::getInstance();

// تحميل النماذج
require_once '../models/BaseModel.php';
require_once '../models/AccountAccount.php';
require_once '../models/ResCurrency.php';

$account_model = new AccountAccount($odoo_db);
$currency_model = new ResCurrency($odoo_db);

$message = '';
$message_type = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = isset($_POST['action']) ? $_POST['action'] : '';
    
    try {
        switch ($action) {
            case 'create_account':
                $data = array(
                    'name' => $_POST['name'],
                    'code' => $_POST['code'],
                    'account_type' => $_POST['account_type'],
                    'parent_id' => !empty($_POST['parent_id']) ? $_POST['parent_id'] : null,
                    'reconcile' => isset($_POST['reconcile']),
                    'note' => isset($_POST['note']) ? $_POST['note'] : ''
                );
                
                $account_model->create_account($data);
                $message = "تم إنشاء الحساب بنجاح";
                $message_type = 'success';
                break;
                
            case 'update_account':
                $account_id = $_POST['account_id'];
                $data = array(
                    'name' => $_POST['name'],
                    'account_type' => $_POST['account_type'],
                    'reconcile' => isset($_POST['reconcile']),
                    'note' => isset($_POST['note']) ? $_POST['note'] : ''
                );
                
                $account_model->update($account_id, $data);
                $message = "تم تحديث الحساب بنجاح";
                $message_type = 'success';
                break;
                
            default:
                throw new Exception('إجراء غير صحيح');
        }
    } catch (Exception $e) {
        $message = 'خطأ: ' . $e->getMessage();
        $message_type = 'error';
    }
}

// جلب الحسابات
$accounts = $account_model->get_demo_data();
$currencies = $currency_model->get_demo_data();

// فلترة حسب النوع
$filter_type = isset($_GET['filter']) ? $_GET['filter'] : 'all';
if ($filter_type !== 'all') {
    $filtered_accounts = array();
    foreach ($accounts as $account) {
        if ($account['account_type'] === $filter_type) {
            $filtered_accounts[] = $account;
        }
    }
    $accounts = $filtered_accounts;
}

// طريقة العرض المحددة
$view_mode = isset($_GET['view']) ? $_GET['view'] : 'tree';

// أنواع الحسابات
$account_types = array(
    'asset' => array('name' => 'أصول', 'color' => 'success', 'icon' => 'fas fa-coins'),
    'liability' => array('name' => 'خصوم', 'color' => 'danger', 'icon' => 'fas fa-credit-card'),
    'equity' => array('name' => 'حقوق ملكية', 'color' => 'primary', 'icon' => 'fas fa-balance-scale'),
    'income' => array('name' => 'إيرادات', 'color' => 'info', 'icon' => 'fas fa-arrow-up'),
    'expense' => array('name' => 'مصروفات', 'color' => 'warning', 'icon' => 'fas fa-arrow-down')
);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دليل الحسابات - نظام ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/odoo-advanced.css" rel="stylesheet">
    <style>
        :root {
            --odoo-primary: #714B67;
            --odoo-secondary: #8B5A8C;
        }
        
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 0.85rem;
        }
        
        .navbar-odoo {
            background: linear-gradient(45deg, var(--odoo-primary), var(--odoo-secondary));
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .page-header {
            background: linear-gradient(45deg, #2ECC71, #27AE60);
            color: white;
            padding: 1.5rem;
            border-radius: 15px;
            margin-bottom: 1.5rem;
        }
        
        .view-controls {
            background: white;
            border-radius: 10px;
            padding: 0.8rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        
        .view-btn, .filter-btn {
            border: 1px solid #dee2e6;
            background: white;
            color: #6c757d;
            padding: 0.4rem 0.8rem;
            border-radius: 5px;
            transition: all 0.3s ease;
            text-decoration: none;
            font-size: 0.8rem;
        }
        
        .view-btn.active, .filter-btn.active {
            background: #2ECC71;
            color: white;
            border-color: #2ECC71;
        }
        
        .view-btn:hover, .filter-btn:hover {
            background: #27AE60;
            color: white;
            border-color: #27AE60;
            text-decoration: none;
        }
        
        .account-card {
            background: white;
            border-radius: 12px;
            padding: 1.2rem;
            margin-bottom: 0.8rem;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
            transition: all 0.3s ease;
            border-left: 3px solid;
        }
        
        .account-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.12);
        }
        
        .account-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }
        
        .table-odoo {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
        }
        
        .table-odoo th {
            background: linear-gradient(45deg, #2ECC71, #27AE60);
            color: white;
            border: none;
            padding: 0.8rem;
            font-size: 0.8rem;
        }
        
        .table-odoo td {
            padding: 0.8rem;
            border-color: #f8f9fa;
            vertical-align: middle;
            font-size: 0.8rem;
        }
        
        .breadcrumb-odoo {
            background: white;
            border-radius: 8px;
            padding: 0.8rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            font-size: 0.8rem;
        }
        
        .stats-card {
            background: white;
            border-radius: 12px;
            padding: 1.2rem;
            text-align: center;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
            border-left: 3px solid;
            margin-bottom: 1rem;
        }
        
        .account-tree {
            background: white;
            border-radius: 12px;
            padding: 1rem;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
        }
        
        .tree-item {
            padding: 0.5rem;
            border-radius: 5px;
            margin-bottom: 0.2rem;
            transition: all 0.3s ease;
        }
        
        .tree-item:hover {
            background: #f8f9fa;
        }
        
        .tree-item.level-1 { padding-right: 0rem; font-weight: bold; }
        .tree-item.level-2 { padding-right: 1rem; }
        .tree-item.level-3 { padding-right: 2rem; }
        .tree-item.level-4 { padding-right: 3rem; }
        
        .balance-positive { color: #28a745; }
        .balance-negative { color: #dc3545; }
        .balance-zero { color: #6c757d; }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-odoo">
        <div class="container-fluid">
            <a class="navbar-brand text-white" href="../dashboard.php">
                <i class="fas fa-cube me-2"></i>
                نظام ERP - دليل الحسابات
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="../dashboard.php">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-3">
        <!-- مسار التنقل -->
        <nav class="breadcrumb-odoo">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="../dashboard.php">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="#">المحاسبة</a></li>
                <li class="breadcrumb-item active">دليل الحسابات</li>
            </ol>
        </nav>

        <!-- العنوان الرئيسي -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h3><i class="fas fa-sitemap me-2"></i>دليل الحسابات</h3>
                    <p class="mb-0 small">إدارة وتنظيم الحسابات المحاسبية</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#addAccountModal">
                        <i class="fas fa-plus me-2"></i>إضافة حساب جديد
                    </button>
                </div>
            </div>
        </div>

        <!-- رسائل النظام -->
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $message_type === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
                <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- إحصائيات الحسابات -->
        <div class="row mb-3">
            <?php foreach ($account_types as $type => $info): ?>
                <div class="col-md-2">
                    <div class="stats-card border-<?php echo $info['color']; ?>">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <i class="<?php echo $info['icon']; ?> text-<?php echo $info['color']; ?>" style="font-size: 1.5rem;"></i>
                        </div>
                        <h4 class="text-<?php echo $info['color']; ?>"><?php
                            $count = 0;
                            foreach($account_model->get_demo_data() as $acc) {
                                if($acc['account_type'] === $type) $count++;
                            }
                            echo $count;
                        ?></h4>
                        <p class="mb-0 small"><?php echo $info['name']; ?></p>
                    </div>
                </div>
            <?php endforeach; ?>
            <div class="col-md-2">
                <div class="stats-card border-secondary">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <i class="fas fa-list text-secondary" style="font-size: 1.5rem;"></i>
                    </div>
                    <h4 class="text-secondary"><?php echo count($account_model->get_demo_data()); ?></h4>
                    <p class="mb-0 small">إجمالي الحسابات</p>
                </div>
            </div>
        </div>

        <!-- أدوات التحكم والعرض -->
        <div class="view-controls">
            <div class="row align-items-center">
                <div class="col-md-4">
                    <div class="btn-group" role="group">
                        <a href="?view=tree&filter=<?php echo $filter_type; ?>" class="view-btn <?php echo $view_mode === 'tree' ? 'active' : ''; ?>">
                            <i class="fas fa-sitemap me-1"></i>شجرة
                        </a>
                        <a href="?view=table&filter=<?php echo $filter_type; ?>" class="view-btn <?php echo $view_mode === 'table' ? 'active' : ''; ?>">
                            <i class="fas fa-table me-1"></i>جدول
                        </a>
                        <a href="?view=cards&filter=<?php echo $filter_type; ?>" class="view-btn <?php echo $view_mode === 'cards' ? 'active' : ''; ?>">
                            <i class="fas fa-th-large me-1"></i>بطاقات
                        </a>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="btn-group" role="group">
                        <a href="?view=<?php echo $view_mode; ?>&filter=all" class="filter-btn <?php echo $filter_type === 'all' ? 'active' : ''; ?>">
                            الكل
                        </a>
                        <?php foreach ($account_types as $type => $info): ?>
                            <a href="?view=<?php echo $view_mode; ?>&filter=<?php echo $type; ?>" class="filter-btn <?php echo $filter_type === $type ? 'active' : ''; ?>">
                                <?php echo $info['name']; ?>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="d-flex justify-content-end">
                        <input type="text" class="form-control search-box" placeholder="البحث في الحسابات..." style="max-width: 250px; font-size: 0.8rem;">
                    </div>
                </div>
            </div>
        </div>

        <!-- محتوى العرض -->
        <?php if ($view_mode === 'tree'): ?>
            <!-- عرض الشجرة -->
            <div class="account-tree">
                <h6><i class="fas fa-sitemap me-2"></i>شجرة الحسابات</h6>
                <div class="tree-container">
                    <?php
                    function display_account_tree($accounts, $parent_id = null, $level = 1) {
                        global $account_types, $account_model;

                        foreach ($accounts as $account) {
                            if ($account['parent_id'] == $parent_id) {
                                $type_info = $account_types[$account['account_type']];
                                $balance = $account_model->compute_balance($account['id']);

                                echo '<div class="tree-item level-' . $level . '" data-account-id="' . $account['id'] . '">';
                                echo '<div class="d-flex justify-content-between align-items-center">';
                                echo '<div>';
                                echo '<i class="' . $type_info['icon'] . ' text-' . $type_info['color'] . ' me-2"></i>';
                                echo '<strong>' . $account['code'] . '</strong> - ' . $account['name'];
                                if ($account['reconcile']) {
                                    echo ' <span class="badge bg-info ms-2">قابل للتسوية</span>';
                                }
                                echo '</div>';
                                echo '<div>';
                                $balance_class = $balance['balance'] > 0 ? 'balance-positive' : ($balance['balance'] < 0 ? 'balance-negative' : 'balance-zero');
                                echo '<span class="' . $balance_class . '">' . number_format($balance['balance'], 2) . ' ر.س</span>';
                                echo '</div>';
                                echo '</div>';
                                echo '</div>';

                                // عرض الحسابات الفرعية
                                display_account_tree($accounts, $account['id'], $level + 1);
                            }
                        }
                    }

                    display_account_tree($accounts);
                    ?>
                </div>
            </div>

        <?php elseif ($view_mode === 'table'): ?>
            <!-- عرض الجدول المتقدم -->
            <div class="odoo-table-container">
                <table id="accounts-table" class="odoo-table odoo-table-auto" data-selectable="true">
                    <thead>
                        <tr>
                            <th class="sortable" data-column="0">الكود</th>
                            <th class="sortable" data-column="1">اسم الحساب</th>
                            <th class="sortable" data-column="2">النوع</th>
                            <th class="sortable" data-column="3">الحساب الأب</th>
                            <th class="sortable" data-column="4">المستوى</th>
                            <th class="sortable" data-column="5">الرصيد</th>
                            <th class="sortable" data-column="6">قابل للتسوية</th>
                            <th class="no-sort">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($accounts as $account):
                            $type_info = $account_types[$account['account_type']];
                            $balance = $account_model->compute_balance($account['id']);
                            $parent_name = '';
                            if ($account['parent_id']) {
                                foreach ($account_model->get_demo_data() as $parent) {
                                    if ($parent['id'] == $account['parent_id']) {
                                        $parent_name = $parent['name'];
                                        break;
                                    }
                                }
                            }
                        ?>
                            <tr>
                                <td><strong><?php echo $account['code']; ?></strong></td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="<?php echo $type_info['icon']; ?> text-<?php echo $type_info['color']; ?> me-2"></i>
                                        <?php echo $account['name']; ?>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo $type_info['color']; ?>"><?php echo $type_info['name']; ?></span>
                                </td>
                                <td><?php echo $parent_name; ?></td>
                                <td><?php echo $account['level']; ?></td>
                                <td>
                                    <?php
                                    $balance_class = $balance['balance'] > 0 ? 'balance-positive' : ($balance['balance'] < 0 ? 'balance-negative' : 'balance-zero');
                                    ?>
                                    <span class="<?php echo $balance_class; ?>"><?php echo number_format($balance['balance'], 2); ?> ر.س</span>
                                </td>
                                <td>
                                    <?php if ($account['reconcile']): ?>
                                        <span class="badge bg-success">نعم</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">لا</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary btn-sm" onclick="editAccount(<?php echo $account['id']; ?>)">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-info btn-sm" onclick="viewAccount(<?php echo $account['id']; ?>)">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-success btn-sm" onclick="viewLedger(<?php echo $account['id']; ?>)">
                                            <i class="fas fa-book"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

        <?php else: ?>
            <!-- عرض البطاقات -->
            <div class="row">
                <?php foreach ($accounts as $account):
                    $type_info = $account_types[$account['account_type']];
                    $balance = $account_model->compute_balance($account['id']);
                ?>
                    <div class="col-md-6 col-lg-4">
                        <div class="account-card border-<?php echo $type_info['color']; ?>">
                            <div class="d-flex align-items-center mb-2">
                                <div class="account-icon me-3 bg-<?php echo $type_info['color']; ?>">
                                    <i class="<?php echo $type_info['icon']; ?>"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1"><?php echo $account['name']; ?></h6>
                                    <p class="text-muted mb-0 small"><?php echo $account['code']; ?></p>
                                </div>
                            </div>

                            <div class="mb-2">
                                <span class="badge bg-<?php echo $type_info['color']; ?>"><?php echo $type_info['name']; ?></span>
                                <?php if ($account['reconcile']): ?>
                                    <span class="badge bg-info">قابل للتسوية</span>
                                <?php endif; ?>
                            </div>

                            <div class="mb-2">
                                <small class="text-muted">الرصيد الحالي:</small><br>
                                <?php
                                $balance_class = $balance['balance'] > 0 ? 'balance-positive' : ($balance['balance'] < 0 ? 'balance-negative' : 'balance-zero');
                                ?>
                                <strong class="<?php echo $balance_class; ?>"><?php echo number_format($balance['balance'], 2); ?> ر.س</strong>
                            </div>

                            <div class="d-flex justify-content-between">
                                <button class="btn btn-outline-primary btn-sm" onclick="editAccount(<?php echo $account['id']; ?>)">
                                    <i class="fas fa-edit me-1"></i>تعديل
                                </button>
                                <button class="btn btn-outline-info btn-sm" onclick="viewAccount(<?php echo $account['id']; ?>)">
                                    <i class="fas fa-eye me-1"></i>عرض
                                </button>
                                <button class="btn btn-outline-success btn-sm" onclick="viewLedger(<?php echo $account['id']; ?>)">
                                    <i class="fas fa-book me-1"></i>دفتر الأستاذ
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/odoo-table-manager.js"></script>
    <script src="../assets/js/odoo-print-manager.js"></script>
    <script src="../assets/js/odoo-buttons-manager.js"></script>
    <script src="../assets/js/odoo-export-manager.js"></script>
    <script>
        // تهيئة مدير الجدول والطباعة
        let tableManager;
        let printManager;

        document.addEventListener('DOMContentLoaded', function() {
            // تهيئة مدير الجدول
            tableManager = new OdooTableManager('accounts-table', {
                sortable: true,
                filterable: true,
                searchable: true,
                exportable: true,
                printable: true,
                columnControl: true,
                fontControl: true,
                selectable: true
            });

            // تهيئة مدير الطباعة
            printManager = new OdooPrintManager({
                companyName: 'شركة النظام المتقدم',
                companyAddress: 'المملكة العربية السعودية',
                companyPhone: '+966 11 234 5678',
                companyEmail: '<EMAIL>',
                currency: 'SAR'
            });

            // تهيئة مدير الأزرار
            odooButtons.addTableButtons('accounts-table', {
                enableAdd: true,
                enableEdit: true,
                enableDelete: true,
                enablePrint: true,
                enableExport: true,
                enableRefresh: true
            });

            // تفعيل أزرار التصدير المتقدمة
            setupAdvancedExportButtons();

            // تطبيق تأثيرات بصرية
            animateElements();
        });

        function setupAdvancedExportButtons() {
            // إضافة معالجات التصدير المتقدمة
            window.exportAccountsToExcel = function() {
                odooExport.exportToExcel('accounts-table', 'دليل_الحسابات.xlsx')
                    .then(filename => showMessage('تم تصدير دليل الحسابات إلى Excel بنجاح', 'success'))
                    .catch(error => showMessage('خطأ في التصدير: ' + error.message, 'error'));
            };

            window.exportAccountsToXLS = function() {
                odooExport.exportToXLS('accounts-table', 'دليل_الحسابات.xls')
                    .then(filename => showMessage('تم تصدير دليل الحسابات إلى XLS بنجاح', 'success'))
                    .catch(error => showMessage('خطأ في التصدير: ' + error.message, 'error'));
            };

            window.exportAccountsToPDF = function() {
                odooExport.exportToPDF('accounts-table', 'دليل_الحسابات.pdf', {
                    title: 'دليل الحسابات',
                    orientation: 'landscape'
                })
                    .then(filename => showMessage('تم تصدير دليل الحسابات إلى PDF بنجاح', 'success'))
                    .catch(error => showMessage('خطأ في التصدير: ' + error.message, 'error'));
            };

            window.exportAccountsToCSV = function() {
                odooExport.exportToCSV('accounts-table', 'دليل_الحسابات.csv')
                    .then(filename => showMessage('تم تصدير دليل الحسابات إلى CSV بنجاح', 'success'))
                    .catch(error => showMessage('خطأ في التصدير: ' + error.message, 'error'));
            };

            window.exportAccountsToWord = function() {
                odooExport.exportToWord('accounts-table', 'دليل_الحسابات.doc')
                    .then(filename => showMessage('تم تصدير دليل الحسابات إلى Word بنجاح', 'success'))
                    .catch(error => showMessage('خطأ في التصدير: ' + error.message, 'error'));
            };
        }

        function addAdvancedPrintButtons() {
            const toolbar = document.querySelector('.table-toolbar-right');
            if (toolbar) {
                const printDropdown = document.createElement('div');
                printDropdown.className = 'dropdown';
                printDropdown.innerHTML = `
                    <button class="btn btn-info btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-print me-2"></i>طباعة متقدمة
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="printAccountsTable()">
                            <i class="fas fa-table me-2"></i>طباعة الجدول
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="printAccountsReport()">
                            <i class="fas fa-file-alt me-2"></i>تقرير دليل الحسابات
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="printAccountsTree()">
                            <i class="fas fa-sitemap me-2"></i>الهيكل الشجري
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="exportAccountsToExcel()">
                            <i class="fas fa-file-excel me-2"></i>تصدير Excel
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="exportAccountsToPDF()">
                            <i class="fas fa-file-pdf me-2"></i>تصدير PDF
                        </a></li>
                    </ul>
                `;
                toolbar.insertBefore(printDropdown, toolbar.firstChild);
            }
        }

        function printAccountsTable() {
            printManager.printTable('accounts-table', 'دليل الحسابات', {
                orientation: 'landscape',
                showHeader: true,
                showFooter: true,
                showPageNumbers: true,
                showDate: true
            });
        }

        function printAccountsReport() {
            const accountsData = getAccountsData();
            printManager.printFinancialReport(accountsData, 'chart_of_accounts', {
                showSummary: true,
                groupByCategory: true,
                showBalances: true
            });
        }

        function printAccountsTree() {
            // طباعة الهيكل الشجري للحسابات
            const treeData = generateAccountsTree();
            const printContent = generateTreePrintContent(treeData);

            const printWindow = window.open('', '_blank');
            printWindow.document.write(printContent);
            printWindow.document.close();
            printWindow.print();
        }

        // دوال التصدير المحدثة
        function exportAccountsToExcel() {
            exportAccountsToExcel();
        }

        function exportAccountsToPDF() {
            exportAccountsToPDF();
        }

        // دوال إضافية للأزرار الجديدة
        function addNewAccount() {
            odooButtons.handleAdd('accounts-table');
        }

        function editSelectedAccount() {
            const selectedRows = document.querySelectorAll('#accounts-table .selected-row');
            if (selectedRows.length === 0) {
                showMessage('يرجى تحديد حساب للتعديل', 'warning');
                return;
            }
            if (selectedRows.length > 1) {
                showMessage('يرجى تحديد حساب واحد فقط للتعديل', 'warning');
                return;
            }

            // فتح نافذة التعديل
            const accountId = selectedRows[0].getAttribute('data-id');
            openEditAccountModal(accountId);
        }

        function deleteSelectedAccounts() {
            const selectedRows = document.querySelectorAll('#accounts-table .selected-row');
            if (selectedRows.length === 0) {
                showMessage('يرجى تحديد حساب أو أكثر للحذف', 'warning');
                return;
            }

            if (confirm(`هل أنت متأكد من حذف ${selectedRows.length} حساب؟`)) {
                // تنفيذ عملية الحذف
                selectedRows.forEach(row => {
                    const accountId = row.getAttribute('data-id');
                    // هنا يمكن إضافة كود الحذف الفعلي
                    row.remove();
                });

                showMessage(`تم حذف ${selectedRows.length} حساب بنجاح`, 'success');
            }
        }

        function openEditAccountModal(accountId) {
            // إنشاء نافذة التعديل
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.id = 'editAccountModal';
            modal.innerHTML = `
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-primary text-white">
                            <h5 class="modal-title">
                                <i class="fas fa-edit me-2"></i>تعديل الحساب
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="editAccountForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">كود الحساب</label>
                                            <input type="text" class="form-control" name="code" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">اسم الحساب</label>
                                            <input type="text" class="form-control" name="name" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">نوع الحساب</label>
                                            <select class="form-select" name="account_type" required>
                                                <option value="">اختر النوع</option>
                                                <option value="asset">أصول</option>
                                                <option value="liability">خصوم</option>
                                                <option value="equity">حقوق ملكية</option>
                                                <option value="income">إيرادات</option>
                                                <option value="expense">مصروفات</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الحساب الأب</label>
                                            <select class="form-select" name="parent_id">
                                                <option value="">بدون حساب أب</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="reconcile" id="editReconcile">
                                        <label class="form-check-label" for="editReconcile">
                                            قابل للتسوية
                                        </label>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times me-1"></i>إلغاء
                            </button>
                            <button type="button" class="btn btn-primary" onclick="saveAccountChanges('${accountId}')">
                                <i class="fas fa-save me-1"></i>حفظ التغييرات
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // عرض النافذة
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();

            // إزالة النافذة عند الإغلاق
            modal.addEventListener('hidden.bs.modal', function() {
                modal.remove();
            });
        }

        function saveAccountChanges(accountId) {
            const form = document.getElementById('editAccountForm');
            const formData = new FormData(form);

            // محاكاة حفظ البيانات
            showMessage('جاري حفظ التغييرات...', 'info');

            setTimeout(() => {
                showMessage('تم حفظ التغييرات بنجاح', 'success');
                const modal = bootstrap.Modal.getInstance(document.getElementById('editAccountModal'));
                modal.hide();

                // تحديث الجدول
                refreshAccounts();
            }, 1000);
        }

        function getAccountsData() {
            const table = document.getElementById('accounts-table');
            const rows = table.querySelectorAll('tbody tr');
            const data = [];

            rows.forEach(row => {
                const cells = row.querySelectorAll('td');
                if (cells.length > 0) {
                    data.push({
                        code: cells[0]?.textContent?.trim() || '',
                        name: cells[1]?.textContent?.trim() || '',
                        type: cells[2]?.textContent?.trim() || '',
                        parent: cells[3]?.textContent?.trim() || '',
                        level: parseInt(cells[4]?.textContent?.trim()) || 0,
                        balance: parseFloat(cells[5]?.textContent?.replace(/[^0-9.-]/g, '')) || 0
                    });
                }
            });

            return data;
        }

        function generateAccountsTree() {
            // إنشاء هيكل شجري للحسابات
            const accountsData = getAccountsData();
            const tree = {};

            accountsData.forEach(account => {
                if (!tree[account.type]) {
                    tree[account.type] = [];
                }
                tree[account.type].push(account);
            });

            return tree;
        }

        function generateTreePrintContent(treeData) {
            let content = `
                <!DOCTYPE html>
                <html dir="rtl" lang="ar">
                <head>
                    <meta charset="UTF-8">
                    <title>الهيكل الشجري لدليل الحسابات</title>
                    <style>
                        body { font-family: Arial, sans-serif; font-size: 12px; }
                        .tree-container { margin: 20px; }
                        .tree-category { margin-bottom: 20px; }
                        .category-title { font-size: 16px; font-weight: bold; color: #2c3e50; margin-bottom: 10px; }
                        .account-item { margin: 5px 0; padding: 5px; border-left: 3px solid #3498db; }
                        .level-0 { margin-right: 0; }
                        .level-1 { margin-right: 20px; }
                        .level-2 { margin-right: 40px; }
                        .level-3 { margin-right: 60px; }
                        .account-code { font-weight: bold; color: #e74c3c; }
                        .account-balance { float: left; font-family: monospace; }
                    </style>
                </head>
                <body>
                    <div class="tree-container">
                        <h1>الهيكل الشجري لدليل الحسابات</h1>
            `;

            Object.keys(treeData).forEach(category => {
                content += `<div class="tree-category">`;
                content += `<div class="category-title">${category}</div>`;

                treeData[category].forEach(account => {
                    content += `
                        <div class="account-item level-${account.level}">
                            <span class="account-code">${account.code}</span>
                            <span class="account-name">${account.name}</span>
                            <span class="account-balance">${account.balance.toLocaleString('ar-SA')} ر.س</span>
                        </div>
                    `;
                });

                content += `</div>`;
            });

            content += `
                    </div>
                </body>
                </html>
            `;

            return content;
        }

        function animateElements() {
            const elements = document.querySelectorAll('.odoo-table-container, .view-controls, .stats-card');
            elements.forEach((element, index) => {
                element.style.opacity = '0';
                element.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    element.style.transition = 'all 0.6s ease';
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, index * 100);
            });
        }

        // دوال إضافية للتفاعل
        function refreshAccounts() {
            showMessage('جاري تحديث دليل الحسابات...', 'info');
            setTimeout(() => {
                location.reload();
            }, 1000);
        }

        function showMessage(message, type = 'info') {
            const alert = document.createElement('div');
            alert.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alert.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(alert);

            setTimeout(() => {
                alert.remove();
            }, 4000);
        }
    </script>
    <script>
        function editAccount(accountId) {
            alert('سيتم تطوير نافذة تعديل الحساب قريباً');
        }

        function viewAccount(accountId) {
            alert('سيتم تطوير صفحة عرض تفاصيل الحساب قريباً');
        }

        function viewLedger(accountId) {
            window.location.href = 'account_ledger.php?account_id=' + accountId;
        }

        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const items = document.querySelectorAll('.account-card, .tree-item');
            items.forEach((item, index) => {
                item.style.opacity = '0';
                item.style.transform = 'translateY(15px)';

                setTimeout(() => {
                    item.style.transition = 'all 0.5s ease';
                    item.style.opacity = '1';
                    item.style.transform = 'translateY(0)';
                }, index * 50);
            });
        });

        // البحث المباشر
        document.querySelector('.search-box').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const items = document.querySelectorAll('.account-card, .tree-item, tbody tr');

            items.forEach(item => {
                const text = item.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    item.style.display = '';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
