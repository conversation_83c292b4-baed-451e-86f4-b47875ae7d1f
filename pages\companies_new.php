<?php
/**
 * صفحة إدارة الشركات المحسنة بأسلوب Odoo
 * Enhanced Companies Management Page - Odoo Style
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// تحميل نظام Odoo
require_once '../config/odoo_config.php';
require_once '../config/odoo_database.php';

// Get the database instance and pass it to the model
$odoo_db = OdooDatabase::getInstance();

// تحميل النماذج
require_once '../models/BaseModel.php';
require_once '../models/ResCompany.php';

$company_model = new ResCompany($odoo_db);

// جلب الشركات من قاعدة البيانات
try {
    $companies = $company_model->search_read(
        array(array('active', '=', true)),
        null,
        array('order' => 'name ASC')
    );
} catch (Exception $e) {
    $companies = $company_model->get_demo_data();
}

// طريقة العرض المحددة (جدول أو بطاقات)
$view_mode = $_GET['view'] ?? 'cards';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الشركات - نظام ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --odoo-primary: #714B67;
            --odoo-secondary: #8B5A8C;
        }
        
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar-odoo {
            background: linear-gradient(45deg, var(--odoo-primary), var(--odoo-secondary));
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .page-header {
            background: linear-gradient(45deg, var(--odoo-primary), var(--odoo-secondary));
            color: white;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
        }
        
        .view-controls {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        
        .view-btn {
            border: 1px solid #dee2e6;
            background: white;
            color: #6c757d;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: all 0.3s ease;
            text-decoration: none;
        }
        
        .view-btn.active {
            background: var(--odoo-primary);
            color: white;
            border-color: var(--odoo-primary);
        }
        
        .view-btn:hover {
            background: var(--odoo-secondary);
            color: white;
            border-color: var(--odoo-secondary);
            text-decoration: none;
        }
        
        .company-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            border-left: 4px solid var(--odoo-primary);
        }
        
        .company-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }
        
        .company-logo {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(45deg, var(--odoo-primary), var(--odoo-secondary));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
        }
        
        .table-odoo {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .table-odoo th {
            background: linear-gradient(45deg, var(--odoo-primary), var(--odoo-secondary));
            color: white;
            border: none;
            padding: 1rem;
        }
        
        .table-odoo td {
            padding: 1rem;
            border-color: #f8f9fa;
            vertical-align: middle;
        }
        
        .btn-odoo {
            background: linear-gradient(45deg, var(--odoo-primary), var(--odoo-secondary));
            border: none;
            color: white;
            padding: 0.5rem 1.5rem;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        
        .btn-odoo:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(113, 75, 103, 0.3);
            color: white;
        }
        
        .breadcrumb-odoo {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        
        .search-box {
            background: white;
            border-radius: 25px;
            border: 1px solid #dee2e6;
            padding: 0.5rem 1rem;
            transition: all 0.3s ease;
        }
        
        .search-box:focus {
            border-color: var(--odoo-primary);
            box-shadow: 0 0 0 0.2rem rgba(113, 75, 103, 0.25);
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 4px solid;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-odoo">
        <div class="container-fluid">
            <a class="navbar-brand text-white" href="../dashboard.php">
                <i class="fas fa-cube me-2"></i>
                نظام ERP - إدارة الشركات
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="../dashboard.php">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- مسار التنقل -->
        <nav class="breadcrumb-odoo">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="../dashboard.php">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="#">الوحدة الأساسية</a></li>
                <li class="breadcrumb-item active">الشركات</li>
            </ol>
        </nav>

        <!-- العنوان الرئيسي -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2><i class="fas fa-building me-2"></i>إدارة الشركات</h2>
                    <p class="mb-0">إدارة وتنظيم الشركات في النظام</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-light" data-bs-toggle="modal" data-bs-target="#addCompanyModal">
                        <i class="fas fa-plus me-2"></i>إضافة شركة جديدة
                    </button>
                </div>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card border-success">
                    <h3 class="text-success"><?php echo count(array_filter($companies, function($c) { return $c['active']; })); ?></h3>
                    <p class="mb-0">شركات نشطة</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card border-primary">
                    <h3 class="text-primary"><?php echo count($companies); ?></h3>
                    <p class="mb-0">إجمالي الشركات</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card border-info">
                    <h3 class="text-info">SAR</h3>
                    <p class="mb-0">العملة الأساسية</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card border-warning">
                    <h3 class="text-warning">3</h3>
                    <p class="mb-0">مدن</p>
                </div>
            </div>
        </div>

        <!-- أدوات التحكم والعرض -->
        <div class="view-controls">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="btn-group" role="group">
                        <a href="?view=cards" class="view-btn <?php echo $view_mode === 'cards' ? 'active' : ''; ?>">
                            <i class="fas fa-th-large me-2"></i>عرض البطاقات
                        </a>
                        <a href="?view=table" class="view-btn <?php echo $view_mode === 'table' ? 'active' : ''; ?>">
                            <i class="fas fa-table me-2"></i>عرض الجدول
                        </a>
                        <a href="?view=list" class="view-btn <?php echo $view_mode === 'list' ? 'active' : ''; ?>">
                            <i class="fas fa-list me-2"></i>عرض القائمة
                        </a>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="d-flex justify-content-end">
                        <input type="text" class="form-control search-box me-2" placeholder="البحث في الشركات..." style="max-width: 300px;">
                        <button class="btn btn-outline-secondary">
                            <i class="fas fa-filter"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- محتوى العرض -->
        <?php if ($view_mode === 'cards'): ?>
            <!-- عرض البطاقات -->
            <div class="row">
                <?php foreach ($companies as $company): ?>
                    <div class="col-md-6 col-lg-4">
                        <div class="company-card">
                            <div class="d-flex align-items-center mb-3">
                                <div class="company-logo me-3">
                                    <?php echo strtoupper(substr($company['name'], 0, 2)); ?>
                                </div>
                                <div class="flex-grow-1">
                                    <h5 class="mb-1"><?php echo $company['name']; ?></h5>
                                    <p class="text-muted mb-0"><?php echo $company['code'] ?? 'N/A'; ?></p>
                                </div>
                                <div>
                                    <?php if ($company['active']): ?>
                                        <span class="badge bg-success">نشطة</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">غير نشطة</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <small class="text-muted">
                                    <i class="fas fa-envelope me-1"></i><?php echo $company['email'] ?? 'غير محدد'; ?><br>
                                    <i class="fas fa-phone me-1"></i><?php echo $company['phone'] ?? 'غير محدد'; ?><br>
                                    <i class="fas fa-map-marker-alt me-1"></i><?php echo $company['city'] ?? 'غير محدد'; ?>
                                </small>
                            </div>
                            
                            <div class="d-flex justify-content-between">
                                <button class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-edit me-1"></i>تعديل
                                </button>
                                <button class="btn btn-outline-info btn-sm">
                                    <i class="fas fa-eye me-1"></i>عرض
                                </button>
                                <button class="btn btn-outline-danger btn-sm">
                                    <i class="fas fa-trash me-1"></i>حذف
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

        <?php elseif ($view_mode === 'table'): ?>
            <!-- عرض الجدول -->
            <div class="table-odoo">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>الشركة</th>
                            <th>الكود</th>
                            <th>البريد الإلكتروني</th>
                            <th>الهاتف</th>
                            <th>المدينة</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($companies as $company): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="company-logo me-3" style="width: 40px; height: 40px; font-size: 1rem;">
                                            <?php echo strtoupper(substr($company['name'], 0, 2)); ?>
                                        </div>
                                        <div>
                                            <strong><?php echo $company['name']; ?></strong>
                                        </div>
                                    </div>
                                </td>
                                <td><?php echo $company['code'] ?? 'N/A'; ?></td>
                                <td><?php echo $company['email'] ?? 'غير محدد'; ?></td>
                                <td><?php echo $company['phone'] ?? 'غير محدد'; ?></td>
                                <td><?php echo $company['city'] ?? 'غير محدد'; ?></td>
                                <td>
                                    <?php if ($company['active']): ?>
                                        <span class="badge bg-success">نشطة</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">غير نشطة</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-info">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-danger">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

        <?php else: ?>
            <!-- عرض القائمة -->
            <div class="list-group">
                <?php foreach ($companies as $company): ?>
                    <div class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <div class="company-logo me-3" style="width: 50px; height: 50px; font-size: 1.2rem;">
                                    <?php echo strtoupper(substr($company['name'], 0, 2)); ?>
                                </div>
                                <div>
                                    <h5 class="mb-1"><?php echo $company['name']; ?></h5>
                                    <p class="mb-1"><?php echo $company['email'] ?? 'غير محدد'; ?></p>
                                    <small class="text-muted"><?php echo $company['city'] ?? 'غير محدد'; ?></small>
                                </div>
                            </div>
                            <div class="d-flex align-items-center">
                                <?php if ($company['active']): ?>
                                    <span class="badge bg-success me-3">نشطة</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary me-3">غير نشطة</span>
                                <?php endif; ?>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-info">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-danger">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.company-card, .list-group-item');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
        
        // البحث المباشر
        document.querySelector('.search-box').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const items = document.querySelectorAll('.company-card, .list-group-item, tbody tr');
            
            items.forEach(item => {
                const text = item.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    item.style.display = '';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
