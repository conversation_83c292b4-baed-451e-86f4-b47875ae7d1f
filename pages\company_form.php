<?php
/**
 * نموذج إضافة شركة - بأسلوب Odoo
 * Company Form - Odoo Style
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// تضمين الملفات المطلوبة
require_once '../config/database.php';
require_once '../models/ResCompany.php';

// إنشاء اتصال قاعدة البيانات
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    $pdo = null;
}

// إنشاء نموذج الشركة مع البيانات الوهمية
class ResCompanyDemo {
    private $demo_data;

    public function __construct() {
        $this->demo_data = array(
            array(
                'id' => 1,
                'name' => 'شركة التقنية المتقدمة',
                'legal_name' => 'شركة التقنية المتقدمة للحلول الرقمية',
                'email' => '<EMAIL>',
                'phone' => '+966 11 234 5678',
                'website' => 'https://advanced-tech.com',
                'vat' => '123456789012345',
                'street' => 'شارع الملك فهد',
                'street2' => 'مجمع الأعمال، الطابق 15',
                'city' => 'الرياض',
                'zip' => '12345',
                'country' => 'المملكة العربية السعودية',
                'currency' => 'SAR',
                'logo' => '',
                'active' => 1,
                'create_date' => date('Y-m-d H:i:s')
            )
        );
    }

    public function create($data) {
        $new_id = max(array_column($this->demo_data, 'id')) + 1;
        $data['id'] = $new_id;
        $data['create_date'] = date('Y-m-d H:i:s');
        $this->demo_data[] = $data;
        return $new_id;
    }

    public function get_demo_data() {
        return $this->demo_data;
    }
}

$company_model = new ResCompanyDemo();

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = isset($_POST['action']) ? $_POST['action'] : '';

    if ($action === 'create') {
        $data = array(
            'name' => $_POST['name'],
            'legal_name' => isset($_POST['legal_name']) ? $_POST['legal_name'] : '',
            'email' => isset($_POST['email']) ? $_POST['email'] : '',
            'phone' => isset($_POST['phone']) ? $_POST['phone'] : '',
            'website' => isset($_POST['website']) ? $_POST['website'] : '',
            'vat' => isset($_POST['vat']) ? $_POST['vat'] : '',
            'street' => isset($_POST['street']) ? $_POST['street'] : '',
            'street2' => isset($_POST['street2']) ? $_POST['street2'] : '',
            'city' => isset($_POST['city']) ? $_POST['city'] : '',
            'zip' => isset($_POST['zip']) ? $_POST['zip'] : '',
            'country' => isset($_POST['country']) ? $_POST['country'] : '',
            'currency' => isset($_POST['currency']) ? $_POST['currency'] : 'SAR',
            'active' => 1
        );

        try {
            $company_model->create($data);
            $success_message = "تم إنشاء الشركة بنجاح";
        } catch (Exception $e) {
            $error_message = "خطأ في إنشاء الشركة: " . $e->getMessage();
        }
    }
}

// قائمة العملات
$currencies = array(
    'SAR' => 'ريال سعودي (SAR)',
    'USD' => 'دولار أمريكي (USD)',
    'EUR' => 'يورو (EUR)',
    'GBP' => 'جنيه إسترليني (GBP)',
    'AED' => 'درهم إماراتي (AED)',
    'KWD' => 'دينار كويتي (KWD)',
    'QAR' => 'ريال قطري (QAR)',
    'BHD' => 'دينار بحريني (BHD)',
    'OMR' => 'ريال عماني (OMR)',
    'JOD' => 'دينار أردني (JOD)'
);

// قائمة الدول
$countries = array(
    'المملكة العربية السعودية',
    'الإمارات العربية المتحدة',
    'الكويت',
    'قطر',
    'البحرين',
    'عمان',
    'الأردن',
    'لبنان',
    'مصر',
    'المغرب',
    'تونس',
    'الجزائر',
    'العراق',
    'سوريا',
    'فلسطين',
    'اليمن',
    'ليبيا',
    'السودان'
);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة شركة - نظام ERP</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --odoo-primary: #714B67;
            --odoo-secondary: #875A7B;
            --odoo-accent: #D4AF37;
            --odoo-success: #28a745;
            --odoo-info: #17a2b8;
            --odoo-warning: #ffc107;
            --odoo-danger: #dc3545;
            --odoo-light: #f8f9fa;
            --odoo-dark: #2F3349;
        }

        * {
            font-family: 'Cairo', sans-serif;
        }

        body {
            background: var(--odoo-light);
            margin: 0;
            padding: 0;
        }

        .main-container {
            display: flex;
            min-height: 100vh;
        }

        .content-area {
            flex: 1;
            padding: 0;
            overflow-x: hidden;
        }

        /* رأس الصفحة */
        .page-header {
            background: white;
            border-bottom: 1px solid #e9ecef;
            padding: 20px 30px;
            margin-bottom: 0;
        }

        .breadcrumb-odoo {
            background: none;
            padding: 0;
            margin: 0;
            font-size: 0.9rem;
        }

        .breadcrumb-odoo .breadcrumb-item {
            color: #6c757d;
        }

        .breadcrumb-odoo .breadcrumb-item.active {
            color: var(--odoo-primary);
            font-weight: 600;
        }

        .page-title {
            color: var(--odoo-dark);
            font-size: 1.8rem;
            font-weight: 700;
            margin: 10px 0 0 0;
        }

        /* أزرار الإجراءات */
        .action-buttons {
            background: white;
            border-bottom: 1px solid #e9ecef;
            padding: 15px 30px;
        }

        .btn-odoo {
            background: var(--odoo-primary);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 4px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 0.95rem;
        }

        .btn-odoo:hover {
            background: var(--odoo-secondary);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(113, 75, 103, 0.3);
        }

        .btn-outline-odoo {
            background: transparent;
            border: 1px solid var(--odoo-primary);
            color: var(--odoo-primary);
        }

        .btn-outline-odoo:hover {
            background: var(--odoo-primary);
            color: white;
        }

        .btn-secondary-odoo {
            background: #6c757d;
            border: none;
            color: white;
        }

        .btn-secondary-odoo:hover {
            background: #5a6268;
            color: white;
        }

        /* منطقة النموذج */
        .form-container {
            padding: 30px;
        }

        .form-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
            overflow: hidden;
        }

        .form-header {
            background: linear-gradient(135deg, var(--odoo-primary), var(--odoo-secondary));
            color: white;
            padding: 20px 30px;
            border-bottom: 1px solid #e9ecef;
        }

        .form-header h4 {
            margin: 0;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-body {
            padding: 30px;
        }

        .form-section {
            margin-bottom: 30px;
        }

        .section-title {
            color: var(--odoo-dark);
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid var(--odoo-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-label {
            font-weight: 500;
            color: var(--odoo-dark);
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .required-field::after {
            content: " *";
            color: var(--odoo-danger);
            font-weight: bold;
        }

        .form-control, .form-select {
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px 12px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--odoo-primary);
            box-shadow: 0 0 0 0.2rem rgba(113, 75, 103, 0.25);
        }

        .form-text {
            font-size: 0.8rem;
            color: #6c757d;
            margin-top: 5px;
        }

        /* رسائل التنبيه */
        .alert {
            border: none;
            border-radius: 6px;
            padding: 15px 20px;
            margin-bottom: 20px;
            font-size: 0.9rem;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border-left: 4px solid #28a745;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border-left: 4px solid #dc3545;
        }

        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 768px) {
            .page-header, .action-buttons, .form-container {
                padding: 15px 20px;
            }

            .form-body {
                padding: 20px;
            }

            .form-header {
                padding: 15px 20px;
            }
        }

        /* تحسينات إضافية */
        .input-group-text {
            background: var(--odoo-light);
            border-color: #dee2e6;
            color: var(--odoo-dark);
        }

        .file-upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 30px;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .file-upload-area:hover {
            border-color: var(--odoo-primary);
            background: rgba(113, 75, 103, 0.05);
        }

        .file-upload-icon {
            font-size: 3rem;
            color: var(--odoo-primary);
            margin-bottom: 15px;
        }

        .btn-group-actions {
            gap: 10px;
            justify-content: flex-end;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- الشريط الجانبي -->
        <?php include '../includes/sidebar.php'; ?>

        <!-- منطقة المحتوى -->
        <div class="content-area">
            <!-- رأس الصفحة -->
            <div class="page-header">
                <nav class="breadcrumb-odoo">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="../dashboard.php">
                                <i class="fas fa-home"></i> الرئيسية
                            </a>
                        </li>
                        <li class="breadcrumb-item">الإعدادات</li>
                        <li class="breadcrumb-item active">إضافة شركة</li>
                    </ol>
                </nav>
                <h1 class="page-title">
                    <i class="fas fa-building me-2"></i>إضافة شركة جديدة
                </h1>
            </div>

            <!-- أزرار الإجراءات -->
            <div class="action-buttons">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="d-flex gap-2">
                        <button type="submit" form="companyForm" class="btn-odoo">
                            <i class="fas fa-save"></i>
                            حفظ الشركة
                        </button>
                        <button type="button" class="btn-outline-odoo" onclick="resetForm()">
                            <i class="fas fa-undo"></i>
                            إعادة تعيين
                        </button>
                    </div>

                    <div class="d-flex gap-2">
                        <a href="companies_list.php" class="btn-secondary-odoo">
                            <i class="fas fa-list"></i>
                            قائمة الشركات
                        </a>
                        <a href="../dashboard.php" class="btn-secondary-odoo">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </a>
                    </div>
                </div>
            </div>

            <!-- رسائل التنبيه -->
            <?php if (isset($success_message)): ?>
                <div class="alert alert-success mx-4">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo $success_message; ?>
                </div>
            <?php endif; ?>

            <?php if (isset($error_message)): ?>
                <div class="alert alert-danger mx-4">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <?php echo $error_message; ?>
                </div>
            <?php endif; ?>

            <!-- نموذج إضافة الشركة -->
            <div class="form-container">
                <div class="form-card">
                    <div class="form-header">
                        <h4>
                            <i class="fas fa-building"></i>
                            معلومات الشركة
                        </h4>
                    </div>

                    <form id="companyForm" method="POST" enctype="multipart/form-data">
                        <input type="hidden" name="action" value="create">

                        <div class="form-body">
                            <!-- القسم الأول: المعلومات الأساسية -->
                            <div class="form-section">
                                <h5 class="section-title">
                                    <i class="fas fa-info-circle"></i>
                                    المعلومات الأساسية
                                </h5>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label required-field">اسم الشركة</label>
                                            <input type="text" class="form-control" name="name" id="companyName" required>
                                            <div class="form-text">الاسم التجاري للشركة</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الاسم القانوني</label>
                                            <input type="text" class="form-control" name="legal_name" id="legalName">
                                            <div class="form-text">الاسم القانوني الرسمي للشركة</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">البريد الإلكتروني</label>
                                            <div class="input-group">
                                                <span class="input-group-text">
                                                    <i class="fas fa-envelope"></i>
                                                </span>
                                                <input type="email" class="form-control" name="email" id="companyEmail">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">رقم الهاتف</label>
                                            <div class="input-group">
                                                <span class="input-group-text">
                                                    <i class="fas fa-phone"></i>
                                                </span>
                                                <input type="tel" class="form-control" name="phone" id="companyPhone">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الموقع الإلكتروني</label>
                                            <div class="input-group">
                                                <span class="input-group-text">
                                                    <i class="fas fa-globe"></i>
                                                </span>
                                                <input type="url" class="form-control" name="website" id="companyWebsite">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الرقم الضريبي</label>
                                            <div class="input-group">
                                                <span class="input-group-text">
                                                    <i class="fas fa-receipt"></i>
                                                </span>
                                                <input type="text" class="form-control" name="vat" id="companyVat">
                                            </div>
                                            <div class="form-text">رقم التسجيل الضريبي (15 رقم)</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- القسم الثاني: العنوان -->
                            <div class="form-section">
                                <h5 class="section-title">
                                    <i class="fas fa-map-marker-alt"></i>
                                    عنوان الشركة
                                </h5>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الشارع</label>
                                            <input type="text" class="form-control" name="street" id="companyStreet">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">تفاصيل إضافية</label>
                                            <input type="text" class="form-control" name="street2" id="companyStreet2" placeholder="رقم المبنى، الطابق، المكتب">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">المدينة</label>
                                            <input type="text" class="form-control" name="city" id="companyCity">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">الرمز البريدي</label>
                                            <input type="text" class="form-control" name="zip" id="companyZip">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">الدولة</label>
                                            <select class="form-select" name="country" id="companyCountry">
                                                <option value="">اختر الدولة</option>
                                                <?php foreach ($countries as $country): ?>
                                                    <option value="<?php echo htmlspecialchars($country); ?>"
                                                            <?php echo $country === 'المملكة العربية السعودية' ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($country); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- القسم الثالث: الإعدادات المالية -->
                            <div class="form-section">
                                <h5 class="section-title">
                                    <i class="fas fa-coins"></i>
                                    الإعدادات المالية
                                </h5>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label required-field">العملة الأساسية</label>
                                            <select class="form-select" name="currency" id="companyCurrency" required>
                                                <?php foreach ($currencies as $code => $name): ?>
                                                    <option value="<?php echo $code; ?>"
                                                            <?php echo $code === 'SAR' ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($name); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                            <div class="form-text">العملة المستخدمة في جميع المعاملات</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- القسم الرابع: شعار الشركة -->
                            <div class="form-section">
                                <h5 class="section-title">
                                    <i class="fas fa-image"></i>
                                    شعار الشركة
                                </h5>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="file-upload-area" onclick="document.getElementById('logoFile').click()">
                                            <div class="file-upload-icon">
                                                <i class="fas fa-cloud-upload-alt"></i>
                                            </div>
                                            <h6>انقر لرفع شعار الشركة</h6>
                                            <p class="text-muted mb-0">PNG, JPG, GIF حتى 2MB</p>
                                            <input type="file" id="logoFile" name="logo" accept="image/*" style="display: none;" onchange="previewLogo(this)">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div id="logoPreview" style="display: none;">
                                            <img id="logoImage" src="" alt="معاينة الشعار" class="img-fluid rounded" style="max-height: 200px;">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- أزرار الإجراءات -->
                            <div class="d-flex btn-group-actions">
                                <a href="../dashboard.php" class="btn btn-secondary-odoo">
                                    <i class="fas fa-times me-1"></i>إلغاء
                                </a>
                                <button type="button" class="btn btn-outline-odoo" onclick="resetForm()">
                                    <i class="fas fa-undo me-1"></i>إعادة تعيين
                                </button>
                                <button type="submit" class="btn-odoo">
                                    <i class="fas fa-save me-1"></i>حفظ الشركة
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // معاينة الشعار
        function previewLogo(input) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();

                reader.onload = function(e) {
                    document.getElementById('logoImage').src = e.target.result;
                    document.getElementById('logoPreview').style.display = 'block';
                };

                reader.readAsDataURL(input.files[0]);
            }
        }

        // إعادة تعيين النموذج
        function resetForm() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟')) {
                document.getElementById('companyForm').reset();
                document.getElementById('logoPreview').style.display = 'none';

                // إعادة تعيين القيم الافتراضية
                document.getElementById('companyCountry').value = 'المملكة العربية السعودية';
                document.getElementById('companyCurrency').value = 'SAR';
            }
        }

        // التحقق من صحة النموذج
        document.getElementById('companyForm').addEventListener('submit', function(e) {
            const name = document.getElementById('companyName').value.trim();
            const currency = document.getElementById('companyCurrency').value;

            if (!name) {
                e.preventDefault();
                alert('يرجى إدخال اسم الشركة');
                document.getElementById('companyName').focus();
                return false;
            }

            if (!currency) {
                e.preventDefault();
                alert('يرجى اختيار العملة الأساسية');
                document.getElementById('companyCurrency').focus();
                return false;
            }

            // تأكيد الحفظ
            if (!confirm('هل أنت متأكد من حفظ بيانات الشركة؟')) {
                e.preventDefault();
                return false;
            }
        });

        // تحسينات UX
        document.addEventListener('DOMContentLoaded', function() {
            // تنسيق رقم الهاتف
            const phoneInput = document.getElementById('companyPhone');
            if (phoneInput) {
                phoneInput.addEventListener('input', function(e) {
                    let value = e.target.value.replace(/\D/g, '');
                    if (value.startsWith('966')) {
                        value = '+' + value;
                    } else if (value.startsWith('0')) {
                        value = '+966' + value.substring(1);
                    }
                    e.target.value = value;
                });
            }

            // تنسيق الرقم الضريبي
            const vatInput = document.getElementById('companyVat');
            if (vatInput) {
                vatInput.addEventListener('input', function(e) {
                    let value = e.target.value.replace(/\D/g, '');
                    if (value.length > 15) {
                        value = value.substring(0, 15);
                    }
                    e.target.value = value;
                });
            }

            // تحديث الاسم القانوني تلقائياً
            const nameInput = document.getElementById('companyName');
            const legalNameInput = document.getElementById('legalName');
            if (nameInput && legalNameInput) {
                nameInput.addEventListener('input', function(e) {
                    if (!legalNameInput.value) {
                        legalNameInput.value = e.target.value;
                    }
                });
            }
        });
    </script>
</body>
</html>