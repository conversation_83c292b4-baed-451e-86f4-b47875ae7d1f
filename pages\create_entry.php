<?php
/**
 * صفحة إنشاء قيد محاسبي جديد بأسلوب Odoo
 * Create Journal Entry Page - Odoo Style
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// تحميل نظام Odoo
require_once '../config/odoo_config.php';
require_once '../config/odoo_database.php';

$odoo_db = OdooDatabase::getInstance();

// تحميل النماذج
require_once '../models/BaseModel.php';
require_once '../models/AccountMove.php';
require_once '../models/AccountMoveLine.php';
require_once '../models/AccountJournal.php';
require_once '../models/AccountAccount.php';
require_once '../models/ResCurrency.php';

$move_model = new AccountMove($odoo_db);
$move_line_model = new AccountMoveLine($odoo_db);
$journal_model = new AccountJournal($odoo_db);
$account_model = new AccountAccount($odoo_db);
$currency_model = new ResCurrency($odoo_db);

$message = '';
$message_type = '';

// معالجة إنشاء القيد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'create_entry') {
    try {
        // التحقق من البيانات الأساسية
        if (empty($_POST['journal_id']) || empty($_POST['date'])) {
            throw new Exception('يرجى ملء جميع الحقول المطلوبة');
        }
        
        // التحقق من وجود بنود
        if (!isset($_POST['lines']) || !is_array($_POST['lines']) || count($_POST['lines']) < 2) {
            throw new Exception('يجب إدخال بندين على الأقل');
        }
        
        // التحقق من توازن القيد
        $total_debit = 0;
        $total_credit = 0;
        $valid_lines = 0;
        
        foreach ($_POST['lines'] as $line) {
            if (!empty($line['account_id']) && (!empty($line['debit']) || !empty($line['credit']))) {
                $debit = floatval($line['debit'] ?? 0);
                $credit = floatval($line['credit'] ?? 0);
                
                if ($debit > 0 && $credit > 0) {
                    throw new Exception('لا يمكن إدخال قيمة في المدين والدائن معاً في نفس البند');
                }
                
                $total_debit += $debit;
                $total_credit += $credit;
                $valid_lines++;
            }
        }
        
        if ($valid_lines < 2) {
            throw new Exception('يجب إدخال بندين صحيحين على الأقل');
        }
        
        if (abs($total_debit - $total_credit) > 0.01) {
            throw new Exception('القيد غير متوازن. المدين: ' . number_format($total_debit, 2) . ' - الدائن: ' . number_format($total_credit, 2));
        }
        
        // إنشاء القيد
        $move_data = array(
            'journal_id' => $_POST['journal_id'],
            'date' => $_POST['date'],
            'ref' => $_POST['ref'],
            'narration' => $_POST['narration']
        );
        
        $move_id = $move_model->create_move($move_data);
        
        // إضافة البنود
        foreach ($_POST['lines'] as $line) {
            if (!empty($line['account_id']) && (!empty($line['debit']) || !empty($line['credit']))) {
                $line_data = array(
                    'account_id' => $line['account_id'],
                    'name' => $line['name'] ?: $_POST['narration'],
                    'debit' => floatval($line['debit'] ?? 0),
                    'credit' => floatval($line['credit'] ?? 0),
                    'partner_id' => !empty($line['partner_id']) ? $line['partner_id'] : null
                );
                
                $move_model->add_move_line($move_id, $line_data);
            }
        }
        
        // حساب إجمالي القيد
        $move_model->compute_amount_total($move_id);
        
        $message = "تم إنشاء القيد بنجاح";
        $message_type = 'success';
        
        // إعادة توجيه إلى صفحة القيود
        header('Location: journal_entries.php?success=1');
        exit();
        
    } catch (Exception $e) {
        $message = 'خطأ: ' . $e->getMessage();
        $message_type = 'error';
    }
}

// جلب البيانات
$journals = $journal_model->get_demo_data();
$accounts = $account_model->get_demo_data();

// اليومية المحددة مسبقاً
$selected_journal = isset($_GET['journal_id']) ? intval($_GET['journal_id']) : null;
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء قيد محاسبي - نظام ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --odoo-primary: #714B67;
            --odoo-secondary: #8B5A8C;
        }
        
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 0.85rem;
        }
        
        .navbar-odoo {
            background: linear-gradient(45deg, var(--odoo-primary), var(--odoo-secondary));
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .page-header {
            background: linear-gradient(45deg, #16A085, #1ABC9C);
            color: white;
            padding: 1.5rem;
            border-radius: 15px;
            margin-bottom: 1.5rem;
        }
        
        .entry-form {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
            margin-bottom: 1rem;
        }
        
        .lines-table {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
        }
        
        .lines-table th {
            background: linear-gradient(45deg, #16A085, #1ABC9C);
            color: white;
            border: none;
            padding: 0.8rem;
            font-size: 0.8rem;
        }
        
        .lines-table td {
            padding: 0.5rem;
            border-color: #f8f9fa;
            vertical-align: middle;
        }
        
        .breadcrumb-odoo {
            background: white;
            border-radius: 8px;
            padding: 0.8rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            font-size: 0.8rem;
        }
        
        .balance-indicator {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            border-left: 4px solid;
        }
        
        .balance-indicator.balanced {
            border-left-color: #28a745;
        }
        
        .balance-indicator.unbalanced {
            border-left-color: #dc3545;
        }
        
        .line-row {
            transition: all 0.3s ease;
        }
        
        .line-row:hover {
            background: #f8f9fa;
        }
        
        .amount-input {
            text-align: left;
            direction: ltr;
        }
        
        .btn-add-line {
            background: #16A085;
            border-color: #16A085;
            color: white;
        }
        
        .btn-add-line:hover {
            background: #1ABC9C;
            border-color: #1ABC9C;
            color: white;
        }
        
        .btn-remove-line {
            background: #e74c3c;
            border-color: #e74c3c;
            color: white;
        }
        
        .btn-remove-line:hover {
            background: #c0392b;
            border-color: #c0392b;
            color: white;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-odoo">
        <div class="container-fluid">
            <a class="navbar-brand text-white" href="../dashboard.php">
                <i class="fas fa-cube me-2"></i>
                نظام ERP - إنشاء قيد محاسبي
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="../dashboard.php">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
                <a class="nav-link text-white" href="journal_entries.php">
                    <i class="fas fa-book me-1"></i>القيود اليومية
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-3">
        <!-- مسار التنقل -->
        <nav class="breadcrumb-odoo">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="../dashboard.php">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="#">المحاسبة</a></li>
                <li class="breadcrumb-item"><a href="journal_entries.php">القيود اليومية</a></li>
                <li class="breadcrumb-item active">إنشاء قيد جديد</li>
            </ol>
        </nav>

        <!-- العنوان الرئيسي -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h3><i class="fas fa-plus-circle me-2"></i>إنشاء قيد محاسبي جديد</h3>
                    <p class="mb-0 small">إدخال قيد محاسبي جديد مع التحقق من التوازن</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="journal_entries.php" class="btn btn-light btn-sm">
                        <i class="fas fa-arrow-right me-2"></i>العودة للقيود
                    </a>
                </div>
            </div>
        </div>

        <!-- رسائل النظام -->
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $message_type === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
                <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- مؤشر التوازن -->
        <div class="balance-indicator balanced" id="balanceIndicator">
            <div class="row align-items-center">
                <div class="col-md-3">
                    <div class="text-center">
                        <strong>إجمالي المدين</strong><br>
                        <span class="text-success" id="totalDebit">0.00 ر.س</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <strong>إجمالي الدائن</strong><br>
                        <span class="text-danger" id="totalCredit">0.00 ر.س</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <strong>الفرق</strong><br>
                        <span id="difference">0.00 ر.س</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <strong>الحالة</strong><br>
                        <span class="badge bg-success" id="balanceStatus">متوازن</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- نموذج إنشاء القيد -->
        <form method="POST" id="entryForm">
            <input type="hidden" name="action" value="create_entry">

            <!-- معلومات القيد الأساسية -->
            <div class="entry-form">
                <h5><i class="fas fa-info-circle me-2"></i>معلومات القيد الأساسية</h5>
                <div class="row">
                    <div class="col-md-3">
                        <label class="form-label">اليومية <span class="text-danger">*</span></label>
                        <select name="journal_id" class="form-select" required>
                            <option value="">اختر اليومية</option>
                            <?php foreach ($journals as $journal): ?>
                                <option value="<?php echo $journal['id']; ?>" <?php echo $selected_journal == $journal['id'] ? 'selected' : ''; ?>>
                                    <?php echo $journal['name']; ?> (<?php echo $journal['code']; ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">التاريخ <span class="text-danger">*</span></label>
                        <input type="date" name="date" class="form-control" value="<?php echo date('Y-m-d'); ?>" required>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">المرجع</label>
                        <input type="text" name="ref" class="form-control" placeholder="رقم المرجع">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">البيان <span class="text-danger">*</span></label>
                        <input type="text" name="narration" class="form-control" placeholder="بيان القيد" required>
                    </div>
                </div>
            </div>

            <!-- بنود القيد -->
            <div class="lines-table">
                <table class="table mb-0" id="linesTable">
                    <thead>
                        <tr>
                            <th style="width: 30%;">الحساب</th>
                            <th style="width: 25%;">البيان</th>
                            <th style="width: 15%;">مدين</th>
                            <th style="width: 15%;">دائن</th>
                            <th style="width: 10%;">الشريك</th>
                            <th style="width: 5%;">إجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="linesBody">
                        <!-- سيتم إضافة البنود هنا بواسطة JavaScript -->
                    </tbody>
                </table>
                <div class="p-3">
                    <button type="button" class="btn btn-add-line btn-sm" onclick="addLine()">
                        <i class="fas fa-plus me-1"></i>إضافة بند
                    </button>
                </div>
            </div>

            <!-- أزرار الحفظ -->
            <div class="entry-form mt-3">
                <div class="row">
                    <div class="col-md-6">
                        <button type="submit" class="btn btn-success me-2" id="saveBtn" disabled>
                            <i class="fas fa-save me-2"></i>حفظ القيد
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="resetForm()">
                            <i class="fas fa-undo me-2"></i>إعادة تعيين
                        </button>
                    </div>
                    <div class="col-md-6 text-end">
                        <a href="journal_entries.php" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/odoo-buttons-manager.js"></script>
    <script src="../assets/js/odoo-export-manager.js"></script>
    <script>
        let lineCounter = 0;
        const accounts = <?php echo json_encode($accounts); ?>;

        // إضافة بند جديد
        function addLine() {
            lineCounter++;
            const tbody = document.getElementById('linesBody');
            const row = document.createElement('tr');
            row.className = 'line-row';
            row.id = 'line_' + lineCounter;

            row.innerHTML = `
                <td>
                    <select name="lines[${lineCounter}][account_id]" class="form-select form-select-sm" onchange="updateBalance()">
                        <option value="">اختر الحساب</option>
                        ${accounts.map(account =>
                            `<option value="${account.id}">${account.code} - ${account.name}</option>`
                        ).join('')}
                    </select>
                </td>
                <td>
                    <input type="text" name="lines[${lineCounter}][name]" class="form-control form-control-sm" placeholder="بيان البند">
                </td>
                <td>
                    <input type="number" name="lines[${lineCounter}][debit]" class="form-control form-control-sm amount-input"
                           step="0.01" min="0" placeholder="0.00" onchange="updateBalance()" oninput="clearCredit(${lineCounter})">
                </td>
                <td>
                    <input type="number" name="lines[${lineCounter}][credit]" class="form-control form-control-sm amount-input"
                           step="0.01" min="0" placeholder="0.00" onchange="updateBalance()" oninput="clearDebit(${lineCounter})">
                </td>
                <td>
                    <select name="lines[${lineCounter}][partner_id]" class="form-select form-select-sm">
                        <option value="">اختياري</option>
                    </select>
                </td>
                <td>
                    <button type="button" class="btn btn-remove-line btn-sm" onclick="removeLine(${lineCounter})">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;

            tbody.appendChild(row);
            updateBalance();
        }

        // حذف بند
        function removeLine(lineId) {
            const row = document.getElementById('line_' + lineId);
            if (row) {
                row.remove();
                updateBalance();
            }
        }

        // مسح الدائن عند إدخال المدين
        function clearCredit(lineId) {
            const debitInput = document.querySelector(`input[name="lines[${lineId}][debit]"]`);
            const creditInput = document.querySelector(`input[name="lines[${lineId}][credit]"]`);

            if (debitInput.value && parseFloat(debitInput.value) > 0) {
                creditInput.value = '';
            }
        }

        // مسح المدين عند إدخال الدائن
        function clearDebit(lineId) {
            const debitInput = document.querySelector(`input[name="lines[${lineId}][debit]"]`);
            const creditInput = document.querySelector(`input[name="lines[${lineId}][credit]"]`);

            if (creditInput.value && parseFloat(creditInput.value) > 0) {
                debitInput.value = '';
            }
        }

        // تحديث حساب التوازن
        function updateBalance() {
            const debitInputs = document.querySelectorAll('input[name*="[debit]"]');
            const creditInputs = document.querySelectorAll('input[name*="[credit]"]');

            let totalDebit = 0;
            let totalCredit = 0;

            debitInputs.forEach(input => {
                if (input.value) {
                    totalDebit += parseFloat(input.value);
                }
            });

            creditInputs.forEach(input => {
                if (input.value) {
                    totalCredit += parseFloat(input.value);
                }
            });

            const difference = Math.abs(totalDebit - totalCredit);
            const isBalanced = difference < 0.01;

            // تحديث العرض
            document.getElementById('totalDebit').textContent = totalDebit.toFixed(2) + ' ر.س';
            document.getElementById('totalCredit').textContent = totalCredit.toFixed(2) + ' ر.س';
            document.getElementById('difference').textContent = difference.toFixed(2) + ' ر.س';

            const balanceIndicator = document.getElementById('balanceIndicator');
            const balanceStatus = document.getElementById('balanceStatus');
            const saveBtn = document.getElementById('saveBtn');

            if (isBalanced && totalDebit > 0) {
                balanceIndicator.className = 'balance-indicator balanced';
                balanceStatus.className = 'badge bg-success';
                balanceStatus.textContent = 'متوازن';
                saveBtn.disabled = false;
            } else {
                balanceIndicator.className = 'balance-indicator unbalanced';
                balanceStatus.className = 'badge bg-danger';
                balanceStatus.textContent = 'غير متوازن';
                saveBtn.disabled = true;
            }
        }

        // إعادة تعيين النموذج
        function resetForm() {
            if (confirm('هل أنت متأكد من إعادة تعيين النموذج؟')) {
                document.getElementById('entryForm').reset();
                document.getElementById('linesBody').innerHTML = '';
                lineCounter = 0;
                updateBalance();
            }
        }

        // إضافة بندين افتراضيين عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addLine();
            addLine();
        });

        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const elements = document.querySelectorAll('.entry-form, .lines-table, .balance-indicator');
            elements.forEach((element, index) => {
                element.style.opacity = '0';
                element.style.transform = 'translateY(15px)';

                setTimeout(() => {
                    element.style.transition = 'all 0.5s ease';
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
