<?php
/**
 * صفحة إنشاء قيد محاسبي محسنة بأسلوب Odoo
 * Enhanced Journal Entry Creation Page - Odoo Style
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// تحميل النماذج
require_once '../models/AccountMove.php';
require_once '../models/AccountJournal.php';
require_once '../models/AccountAccount.php';
require_once '../models/ResPartner.php';

// تهيئة النماذج
$move_model = new AccountMove();
$journal_model = new AccountJournal();
$account_model = new AccountAccount();
$partner_model = new ResPartner();

$message = '';
$message_type = '';

// معالجة الطلبات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'create_move':
            try {
                // إنشاء القيد
                $move_data = array(
                    'journal_id' => $_POST['journal_id'],
                    'date' => $_POST['date'],
                    'ref' => $_POST['ref'],
                    'narration' => $_POST['narration'],
                    'company_id' => 1,
                    'currency_id' => 1,
                    'state' => 'draft'
                );
                
                $move_id = $move_model->create($move_data);
                
                // إضافة بنود القيد
                $total_debit = 0;
                $total_credit = 0;
                
                if (isset($_POST['lines']) && is_array($_POST['lines'])) {
                    foreach ($_POST['lines'] as $line) {
                        if (!empty($line['account_id']) && (!empty($line['debit']) || !empty($line['credit']))) {
                            $debit = floatval($line['debit'] ?? 0);
                            $credit = floatval($line['credit'] ?? 0);
                            
                            $line_data = array(
                                'move_id' => $move_id,
                                'account_id' => $line['account_id'],
                                'name' => $line['name'] ?: 'بند قيد',
                                'debit' => $debit,
                                'credit' => $credit,
                                'partner_id' => !empty($line['partner_id']) ? $line['partner_id'] : null,
                                'date' => $_POST['date'],
                                'company_id' => 1,
                                'currency_id' => 1
                            );
                            
                            $move_model->add_move_line($move_id, $line_data);
                            $total_debit += $debit;
                            $total_credit += $credit;
                        }
                    }
                }
                
                // التحقق من توازن القيد
                if (abs($total_debit - $total_credit) < 0.01) {
                    // تحديث إجمالي القيد
                    $move_model->update($move_id, array('amount_total' => $total_debit));
                    
                    $message = "تم إنشاء القيد بنجاح - رقم: " . $move_id;
                    $message_type = 'success';
                    
                    // إعادة توجيه إلى صفحة القيود
                    header('Location: journal_entries.php?success=1');
                    exit();
                } else {
                    $message = "خطأ: القيد غير متوازن. إجمالي المدين: " . number_format($total_debit, 2) . " - إجمالي الدائن: " . number_format($total_credit, 2);
                    $message_type = 'error';
                }
                
            } catch (Exception $e) {
                $message = "خطأ في إنشاء القيد: " . $e->getMessage();
                $message_type = 'error';
            }
            break;
    }
}

// جلب البيانات
$journals = $journal_model->get_demo_data();
$accounts = $account_model->get_demo_data();
$partners = $partner_model->get_demo_data();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء قيد محاسبي - نظام ERP</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Odoo Style -->
    <link href="../assets/css/odoo-style.css" rel="stylesheet">
    <link href="../assets/css/odoo-advanced.css" rel="stylesheet">
    <link href="../assets/css/journal-entries-enhanced.css" rel="stylesheet">
    
    <style>
        .entry-form {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            overflow: hidden;
        }
        
        .form-header {
            background: linear-gradient(135deg, var(--odoo-primary), var(--odoo-secondary));
            color: white;
            padding: 16px 20px;
            border-bottom: 1px solid var(--odoo-border);
        }
        
        .form-body {
            padding: 20px;
        }
        
        .lines-table {
            background: white;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--box-shadow);
        }
        
        .lines-table th {
            background: #f8f9fa;
            font-weight: 600;
            font-size: 12px;
            padding: 12px 8px;
            border-bottom: 2px solid var(--odoo-border);
        }
        
        .lines-table td {
            padding: 8px;
            border-bottom: 1px solid #f0f0f0;
            vertical-align: middle;
        }
        
        .line-input {
            border: none;
            background: transparent;
            font-size: 13px;
            padding: 4px 8px;
            width: 100%;
        }
        
        .line-input:focus {
            outline: 2px solid var(--odoo-primary);
            outline-offset: -2px;
            background: #f8f9fa;
        }
        
        .balance-indicator {
            position: sticky;
            bottom: 0;
            background: white;
            border-top: 2px solid var(--odoo-border);
            padding: 12px 20px;
            font-weight: 600;
        }
        
        .balance-indicator.balanced {
            background: #d4edda;
            color: #155724;
            border-top-color: var(--odoo-success);
        }
        
        .balance-indicator.unbalanced {
            background: #f8d7da;
            color: #721c24;
            border-top-color: var(--odoo-danger);
        }
        
        .add-line-btn {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            color: var(--odoo-text-muted);
            padding: 12px;
            text-align: center;
            cursor: pointer;
            transition: var(--transition);
        }
        
        .add-line-btn:hover {
            background: #e9ecef;
            border-color: var(--odoo-primary);
            color: var(--odoo-primary);
        }
        
        .remove-line-btn {
            color: var(--odoo-danger);
            cursor: pointer;
            padding: 4px;
            border-radius: 50%;
            transition: var(--transition);
        }
        
        .remove-line-btn:hover {
            background: var(--odoo-danger);
            color: white;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <?php include '../includes/navbar.php'; ?>
    
    <div class="o_main_content">
        <div class="o_content">
            <!-- الشريط الجانبي -->
            <?php include '../includes/sidebar.php'; ?>
            
            <!-- المحتوى الرئيسي -->
            <div class="o_action_manager">
                <!-- شريط التحكم -->
                <div class="o_control_panel">
                    <div class="o_cp_left">
                        <!-- مسار التنقل -->
                        <div class="o_breadcrumb">
                            <a href="../dashboard.php" class="o_breadcrumb_item">الرئيسية</a>
                            <span>/</span>
                            <a href="journal_entries.php" class="o_breadcrumb_item">القيود اليومية</a>
                            <span>/</span>
                            <span class="o_breadcrumb_item active">إنشاء قيد جديد</span>
                        </div>
                    </div>
                    <div class="o_cp_right">
                        <!-- أزرار الإجراءات -->
                        <div class="btn-group">
                            <button type="button" class="btn btn-primary" onclick="saveEntry()">
                                <i class="fas fa-save me-1"></i>حفظ
                            </button>
                            <button type="button" class="btn btn-success" onclick="saveAndPost()">
                                <i class="fas fa-check me-1"></i>حفظ وترحيل
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="discardEntry()">
                                <i class="fas fa-times me-1"></i>إلغاء
                            </button>
                        </div>
                    </div>
                </div>

                <!-- رسائل النظام -->
                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $message_type === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show m-3">
                        <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- نموذج إنشاء القيد -->
                <div class="p-3">
                    <form id="entryForm" method="POST" action="">
                        <input type="hidden" name="action" value="create_move">
                        
                        <div class="entry-form">
                            <!-- رأس النموذج -->
                            <div class="form-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-plus-circle me-2"></i>
                                    إنشاء قيد محاسبي جديد
                                </h5>
                            </div>
                            
                            <!-- جسم النموذج -->
                            <div class="form-body">
                                <div class="row">
                                    <!-- اليومية -->
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">اليومية <span class="text-danger">*</span></label>
                                        <select class="form-select" name="journal_id" required>
                                            <option value="">اختر اليومية</option>
                                            <?php foreach ($journals as $journal): ?>
                                                <option value="<?php echo $journal['id']; ?>">
                                                    <?php echo $journal['name']; ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    
                                    <!-- التاريخ -->
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">التاريخ <span class="text-danger">*</span></label>
                                        <input type="date" class="form-control" name="date" value="<?php echo date('Y-m-d'); ?>" required>
                                    </div>
                                    
                                    <!-- المرجع -->
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">المرجع</label>
                                        <input type="text" class="form-control" name="ref" placeholder="رقم المرجع">
                                    </div>
                                    
                                    <!-- حالة القيد -->
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">الحالة</label>
                                        <input type="text" class="form-control" value="مسودة" readonly>
                                    </div>
                                </div>
                                
                                <!-- الوصف -->
                                <div class="mb-3">
                                    <label class="form-label">الوصف</label>
                                    <textarea class="form-control" name="narration" rows="2" placeholder="وصف القيد المحاسبي"></textarea>
                                </div>
                            </div>
                        </div>
                        
                        <!-- جدول بنود القيد -->
                        <div class="mt-3">
                            <h6 class="mb-3">
                                <i class="fas fa-list me-2"></i>
                                بنود القيد
                            </h6>
                            
                            <div class="lines-table">
                                <table class="table table-sm mb-0" id="linesTable">
                                    <thead>
                                        <tr>
                                            <th style="width: 30%">الحساب</th>
                                            <th style="width: 20%">الشريك</th>
                                            <th style="width: 25%">الوصف</th>
                                            <th style="width: 10%">مدين</th>
                                            <th style="width: 10%">دائن</th>
                                            <th style="width: 5%"></th>
                                        </tr>
                                    </thead>
                                    <tbody id="linesTableBody">
                                        <!-- سيتم إضافة البنود هنا بواسطة JavaScript -->
                                    </tbody>
                                </table>
                                
                                <!-- زر إضافة بند جديد -->
                                <div class="add-line-btn" onclick="addNewLine()">
                                    <i class="fas fa-plus me-2"></i>
                                    إضافة بند جديد
                                </div>
                                
                                <!-- مؤشر التوازن -->
                                <div class="balance-indicator" id="balanceIndicator">
                                    <div class="row">
                                        <div class="col-md-4">
                                            إجمالي المدين: <span id="totalDebit">0.00</span> ر.س
                                        </div>
                                        <div class="col-md-4">
                                            إجمالي الدائن: <span id="totalCredit">0.00</span> ر.س
                                        </div>
                                        <div class="col-md-4">
                                            الفرق: <span id="difference">0.00</span> ر.س
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let lineCounter = 0;
        
        // بيانات الحسابات والشركاء
        const accounts = <?php echo json_encode($accounts); ?>;
        const partners = <?php echo json_encode($partners); ?>;
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة بندين افتراضيين
            addNewLine();
            addNewLine();
            
            // تحديث التوازن
            updateBalance();
        });
        
        // إضافة بند جديد
        function addNewLine() {
            lineCounter++;
            const tbody = document.getElementById('linesTableBody');
            
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <select class="line-input" name="lines[${lineCounter}][account_id]" required>
                        <option value="">اختر الحساب</option>
                        ${accounts.map(account => `<option value="${account.id}">${account.code} - ${account.name}</option>`).join('')}
                    </select>
                </td>
                <td>
                    <select class="line-input" name="lines[${lineCounter}][partner_id]">
                        <option value="">اختر الشريك</option>
                        ${partners.map(partner => `<option value="${partner.id}">${partner.name}</option>`).join('')}
                    </select>
                </td>
                <td>
                    <input type="text" class="line-input" name="lines[${lineCounter}][name]" placeholder="وصف البند">
                </td>
                <td>
                    <input type="number" class="line-input text-end" name="lines[${lineCounter}][debit]" 
                           step="0.01" min="0" placeholder="0.00" onchange="updateBalance()">
                </td>
                <td>
                    <input type="number" class="line-input text-end" name="lines[${lineCounter}][credit]" 
                           step="0.01" min="0" placeholder="0.00" onchange="updateBalance()">
                </td>
                <td>
                    <i class="fas fa-trash remove-line-btn" onclick="removeLine(this)" title="حذف البند"></i>
                </td>
            `;
            
            tbody.appendChild(row);
        }
        
        // حذف بند
        function removeLine(button) {
            const row = button.closest('tr');
            row.remove();
            updateBalance();
        }
        
        // تحديث التوازن
        function updateBalance() {
            let totalDebit = 0;
            let totalCredit = 0;
            
            // حساب إجمالي المدين
            document.querySelectorAll('input[name*="[debit]"]').forEach(input => {
                totalDebit += parseFloat(input.value) || 0;
            });
            
            // حساب إجمالي الدائن
            document.querySelectorAll('input[name*="[credit]"]').forEach(input => {
                totalCredit += parseFloat(input.value) || 0;
            });
            
            const difference = totalDebit - totalCredit;
            
            // تحديث العرض
            document.getElementById('totalDebit').textContent = totalDebit.toFixed(2);
            document.getElementById('totalCredit').textContent = totalCredit.toFixed(2);
            document.getElementById('difference').textContent = Math.abs(difference).toFixed(2);
            
            // تحديث مؤشر التوازن
            const indicator = document.getElementById('balanceIndicator');
            if (Math.abs(difference) < 0.01 && totalDebit > 0) {
                indicator.className = 'balance-indicator balanced';
            } else {
                indicator.className = 'balance-indicator unbalanced';
            }
        }
        
        // حفظ القيد
        function saveEntry() {
            if (validateEntry()) {
                document.getElementById('entryForm').submit();
            }
        }
        
        // حفظ وترحيل
        function saveAndPost() {
            if (validateEntry()) {
                // إضافة حقل للترحيل
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'post_after_save';
                input.value = '1';
                document.getElementById('entryForm').appendChild(input);
                
                document.getElementById('entryForm').submit();
            }
        }
        
        // إلغاء القيد
        function discardEntry() {
            if (confirm('هل أنت متأكد من إلغاء القيد؟ سيتم فقدان جميع البيانات المدخلة.')) {
                window.location.href = 'journal_entries.php';
            }
        }
        
        // التحقق من صحة القيد
        function validateEntry() {
            // التحقق من اليومية والتاريخ
            const journal = document.querySelector('select[name="journal_id"]').value;
            const date = document.querySelector('input[name="date"]').value;
            
            if (!journal) {
                alert('يرجى اختيار اليومية');
                return false;
            }
            
            if (!date) {
                alert('يرجى إدخال التاريخ');
                return false;
            }
            
            // التحقق من وجود بنود
            const lines = document.querySelectorAll('#linesTableBody tr');
            if (lines.length < 2) {
                alert('يجب إضافة بندين على الأقل');
                return false;
            }
            
            // التحقق من التوازن
            let totalDebit = 0;
            let totalCredit = 0;
            let validLines = 0;
            
            document.querySelectorAll('#linesTableBody tr').forEach(row => {
                const account = row.querySelector('select[name*="[account_id]"]').value;
                const debit = parseFloat(row.querySelector('input[name*="[debit]"]').value) || 0;
                const credit = parseFloat(row.querySelector('input[name*="[credit]"]').value) || 0;
                
                if (account && (debit > 0 || credit > 0)) {
                    totalDebit += debit;
                    totalCredit += credit;
                    validLines++;
                }
            });
            
            if (validLines < 2) {
                alert('يجب إدخال بندين صحيحين على الأقل');
                return false;
            }
            
            if (Math.abs(totalDebit - totalCredit) >= 0.01) {
                alert('القيد غير متوازن. يجب أن يكون إجمالي المدين مساوياً لإجمالي الدائن');
                return false;
            }
            
            return true;
        }
        
        // وظيفة عرض الرسائل
        function showMessage(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(alertDiv);
            
            setTimeout(() => {
                alertDiv.remove();
            }, 4000);
        }
    </script>
</body>
</html>
