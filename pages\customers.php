<?php
session_start();
require_once '../config/database_simple.php';

// بيانات تجريبية للعملاء
$customers = array(
    array(
        'id' => 1,
        'code' => 'CUST-001',
        'name' => 'شركة الأمل للتجارة',
        'email' => '<EMAIL>',
        'phone' => '+966501234567',
        'tax_number' => '123456789012345',
        'city' => 'الرياض',
        'balance' => 15000,
        'credit_limit' => 50000,
        'is_active' => true,
        'last_transaction' => '2024-01-15'
    ),
    array(
        'id' => 2,
        'code' => 'CUST-002',
        'name' => 'مؤسسة النور للمقاولات',
        'email' => '<EMAIL>',
        'phone' => '+966507654321',
        'tax_number' => '987654321098765',
        'city' => 'جدة',
        'balance' => 8500,
        'credit_limit' => 30000,
        'is_active' => true,
        'last_transaction' => '2024-01-12'
    ),
    array(
        'id' => 3,
        'name' => 'شركة المستقبل للتكنولوجيا',
        'code' => 'CUST-003',
        'email' => '<EMAIL>',
        'phone' => '+966509876543',
        'tax_number' => '***************',
        'city' => 'الدمام',
        'balance' => -2500,
        'credit_limit' => 25000,
        'is_active' => false,
        'last_transaction' => '2024-01-08'
    ),
    array(
        'id' => 4,
        'code' => 'CUST-004',
        'name' => 'مجموعة الخليج التجارية',
        'email' => '<EMAIL>',
        'phone' => '+966512345678',
        'tax_number' => '789123456789123',
        'city' => 'الخبر',
        'balance' => 22000,
        'credit_limit' => 75000,
        'is_active' => true,
        'last_transaction' => '2024-01-14'
    )
);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العملاء - نظام ERP المحاسبي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
        }
        
        .customer-table {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .balance-positive {
            color: #28a745;
            font-weight: bold;
        }
        
        .balance-negative {
            color: #dc3545;
            font-weight: bold;
        }
        
        .search-box {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .customer-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .status-active {
            background: #d4edda;
            color: #155724;
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 0.8rem;
        }
        
        .status-inactive {
            background: #f8d7da;
            color: #721c24;
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="../demo.php">
                <i class="fas fa-chart-line me-2"></i>
                نظام ERP المحاسبي
            </a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="../demo.php">لوحة التحكم</a>
                <a class="nav-link" href="companies.php">الشركات</a>
                <a class="nav-link active" href="customers.php">العملاء</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- رأس الصفحة -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2><i class="fas fa-users me-2"></i>إدارة العملاء</h2>
                <p class="text-muted">إدارة قاعدة بيانات العملاء والحسابات</p>
            </div>
            <div>
                <button class="btn btn-success me-2" onclick="exportCustomers()">
                    <i class="fas fa-download me-2"></i>
                    تصدير Excel
                </button>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCustomerModal">
                    <i class="fas fa-user-plus me-2"></i>
                    عميل جديد
                </button>
            </div>
        </div>

        <!-- إحصائيات العملاء -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-users fa-2x mb-2"></i>
                        <h4><?php echo count($customers); ?></h4>
                        <small>إجمالي العملاء</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-user-check fa-2x mb-2"></i>
                        <h4><?php echo count(array_filter($customers, function($c) { return $c['is_active']; })); ?></h4>
                        <small>العملاء النشطين</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                        <h4><?php echo formatCurrency(array_sum(array_filter(array_column($customers, 'balance'), function($b) { return $b > 0; }))); ?></h4>
                        <small>إجمالي المدينين</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                        <h4><?php echo count(array_filter($customers, function($c) { return $c['balance'] < 0; })); ?></h4>
                        <small>حسابات متأخرة</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- مربع البحث والفلترة -->
        <div class="search-box">
            <div class="row">
                <div class="col-md-4">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                        <input type="text" class="form-control" placeholder="البحث في العملاء..." id="searchInput">
                    </div>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="statusFilter">
                        <option value="">جميع الحالات</option>
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="cityFilter">
                        <option value="">جميع المدن</option>
                        <option value="الرياض">الرياض</option>
                        <option value="جدة">جدة</option>
                        <option value="الدمام">الدمام</option>
                        <option value="الخبر">الخبر</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="balanceFilter">
                        <option value="">جميع الأرصدة</option>
                        <option value="positive">رصيد موجب</option>
                        <option value="negative">رصيد سالب</option>
                        <option value="zero">رصيد صفر</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                        <i class="fas fa-times me-1"></i>
                        مسح الفلاتر
                    </button>
                </div>
            </div>
        </div>

        <!-- جدول العملاء -->
        <div class="customer-table">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>العميل</th>
                            <th>كود العميل</th>
                            <th>الاتصال</th>
                            <th>المدينة</th>
                            <th>الرصيد</th>
                            <th>الحد الائتماني</th>
                            <th>آخر معاملة</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="customersTableBody">
                        <?php foreach ($customers as $customer): ?>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="customer-avatar me-3">
                                        <?php echo substr($customer['name'], 0, 1); ?>
                                    </div>
                                    <div>
                                        <div class="fw-bold"><?php echo $customer['name']; ?></div>
                                        <small class="text-muted"><?php echo $customer['tax_number']; ?></small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-secondary"><?php echo $customer['code']; ?></span>
                            </td>
                            <td>
                                <div>
                                    <i class="fas fa-phone text-muted me-1"></i>
                                    <?php echo $customer['phone']; ?>
                                </div>
                                <div>
                                    <i class="fas fa-envelope text-muted me-1"></i>
                                    <small><?php echo $customer['email']; ?></small>
                                </div>
                            </td>
                            <td><?php echo $customer['city']; ?></td>
                            <td>
                                <span class="<?php echo $customer['balance'] >= 0 ? 'balance-positive' : 'balance-negative'; ?>">
                                    <?php echo formatCurrency($customer['balance']); ?>
                                </span>
                            </td>
                            <td><?php echo formatCurrency($customer['credit_limit']); ?></td>
                            <td><?php echo $customer['last_transaction']; ?></td>
                            <td>
                                <span class="<?php echo $customer['is_active'] ? 'status-active' : 'status-inactive'; ?>">
                                    <?php echo $customer['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                </span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-sm btn-outline-primary" onclick="viewCustomer(<?php echo $customer['id']; ?>)" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-success" onclick="editCustomer(<?php echo $customer['id']; ?>)" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-info" onclick="customerStatement(<?php echo $customer['id']; ?>)" title="كشف حساب">
                                        <i class="fas fa-file-alt"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-warning" onclick="newInvoice(<?php echo $customer['id']; ?>)" title="فاتورة جديدة">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- ترقيم الصفحات -->
        <nav class="mt-4">
            <ul class="pagination justify-content-center">
                <li class="page-item disabled">
                    <a class="page-link" href="#" tabindex="-1">السابق</a>
                </li>
                <li class="page-item active"><a class="page-link" href="#">1</a></li>
                <li class="page-item"><a class="page-link" href="#">2</a></li>
                <li class="page-item"><a class="page-link" href="#">3</a></li>
                <li class="page-item">
                    <a class="page-link" href="#">التالي</a>
                </li>
            </ul>
        </nav>
    </div>

    <!-- نافذة إضافة عميل جديد -->
    <div class="modal fade" id="addCustomerModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-user-plus me-2"></i>
                        إضافة عميل جديد
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addCustomerForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم العميل *</label>
                                    <input type="text" class="form-control" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">كود العميل</label>
                                    <input type="text" class="form-control" name="code" placeholder="سيتم إنشاؤه تلقائياً">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">رقم الهاتف</label>
                                    <input type="tel" class="form-control" name="phone">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" name="email">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الرقم الضريبي</label>
                                    <input type="text" class="form-control" name="tax_number" maxlength="15">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">المدينة</label>
                                    <input type="text" class="form-control" name="city">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الحد الائتماني</label>
                                    <input type="number" class="form-control" name="credit_limit" value="0" min="0">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">مدة السداد (بالأيام)</label>
                                    <input type="number" class="form-control" name="payment_terms" value="30" min="0">
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">العنوان</label>
                            <textarea class="form-control" name="address" rows="3"></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" name="notes" rows="2"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveCustomer()">
                        <i class="fas fa-save me-2"></i>
                        حفظ العميل
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function viewCustomer(id) {
            alert('عرض تفاصيل العميل رقم: ' + id);
        }
        
        function editCustomer(id) {
            alert('تعديل العميل رقم: ' + id);
        }
        
        function customerStatement(id) {
            alert('كشف حساب العميل رقم: ' + id);
        }
        
        function newInvoice(id) {
            alert('إنشاء فاتورة جديدة للعميل رقم: ' + id);
        }
        
        function exportCustomers() {
            alert('تصدير قائمة العملاء إلى Excel');
        }
        
        function saveCustomer() {
            const form = document.getElementById('addCustomerForm');
            const formData = new FormData(form);
            
            if (!formData.get('name')) {
                alert('يرجى إدخال اسم العميل');
                return;
            }
            
            alert('تم حفظ العميل بنجاح!');
            
            const modal = bootstrap.Modal.getInstance(document.getElementById('addCustomerModal'));
            modal.hide();
            form.reset();
        }
        
        function clearFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('cityFilter').value = '';
            document.getElementById('balanceFilter').value = '';
            filterCustomers();
        }
        
        function filterCustomers() {
            // هنا يمكن إضافة منطق الفلترة
            console.log('تطبيق الفلاتر...');
        }
        
        // إضافة مستمعي الأحداث للفلاتر
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('searchInput').addEventListener('input', filterCustomers);
            document.getElementById('statusFilter').addEventListener('change', filterCustomers);
            document.getElementById('cityFilter').addEventListener('change', filterCustomers);
            document.getElementById('balanceFilter').addEventListener('change', filterCustomers);
        });
    </script>
</body>
</html>
