<?php
/**
 * صفحة العملاء المحسنة - بأسلوب Odoo
 * Enhanced Customers Page - Odoo Style
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// تضمين الملفات المطلوبة
require_once '../config/database.php';
require_once '../models/ResPartner.php';

// إنشاء اتصال قاعدة البيانات
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    $pdo = null; // سيتم استخدام البيانات الوهمية
}

$partner_model = new ResPartner($pdo);

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'create':
            $data = [
                'name' => $_POST['name'],
                'email' => $_POST['email'] ?? '',
                'phone' => $_POST['phone'] ?? '',
                'mobile' => $_POST['mobile'] ?? '',
                'street' => $_POST['street'] ?? '',
                'city' => $_POST['city'] ?? '',
                'is_company' => isset($_POST['is_company']) ? 1 : 0,
                'customer_rank' => 1, // دائماً عميل
                'supplier_rank' => 0,
                'vat' => $_POST['vat'] ?? '',
                'website' => $_POST['website'] ?? '',
                'active' => 1
            ];
            
            try {
                $partner_model->create($data);
                $success_message = "تم إنشاء العميل بنجاح";
            } catch (Exception $e) {
                $error_message = "خطأ في إنشاء العميل: " . $e->getMessage();
            }
            break;
            
        case 'update':
            $id = $_POST['id'];
            $data = [
                'name' => $_POST['name'],
                'email' => $_POST['email'] ?? '',
                'phone' => $_POST['phone'] ?? '',
                'mobile' => $_POST['mobile'] ?? '',
                'street' => $_POST['street'] ?? '',
                'city' => $_POST['city'] ?? '',
                'is_company' => isset($_POST['is_company']) ? 1 : 0,
                'vat' => $_POST['vat'] ?? '',
                'website' => $_POST['website'] ?? ''
            ];
            
            try {
                $partner_model->update($id, $data);
                $success_message = "تم تحديث العميل بنجاح";
            } catch (Exception $e) {
                $error_message = "خطأ في تحديث العميل: " . $e->getMessage();
            }
            break;
            
        case 'delete':
            $id = $_POST['id'];
            try {
                $partner_model->delete($id);
                $success_message = "تم حذف العميل بنجاح";
            } catch (Exception $e) {
                $error_message = "خطأ في حذف العميل: " . $e->getMessage();
            }
            break;
    }
}

// جلب العملاء
try {
    $all_partners = $partner_model->search([], ['name' => 'ASC']);
    
    // فلترة العملاء فقط
    $customers = [];
    foreach ($all_partners as $partner) {
        if ($partner['customer_rank'] > 0) {
            $customers[] = $partner;
        }
    }
    
    // إذا لم توجد بيانات، استخدم البيانات الوهمية
    if (empty($customers)) {
        $demo_data = $partner_model->get_demo_data();
        foreach ($demo_data as $partner) {
            if ($partner['customer_rank'] > 0) {
                $customers[] = $partner;
            }
        }
    }
} catch (Exception $e) {
    // استخدام البيانات الوهمية في حالة الخطأ
    $demo_data = $partner_model->get_demo_data();
    $customers = [];
    foreach ($demo_data as $partner) {
        if ($partner['customer_rank'] > 0) {
            $customers[] = $partner;
        }
    }
}

// معاملات البحث والفلترة
$search_term = $_GET['search'] ?? '';
$filter_type = $_GET['filter'] ?? 'all';
$view_mode = $_GET['view'] ?? 'kanban';

// تطبيق البحث
if (!empty($search_term)) {
    $customers = array_filter($customers, function($customer) use ($search_term) {
        return stripos($customer['name'], $search_term) !== false ||
               stripos($customer['email'], $search_term) !== false ||
               stripos($customer['phone'], $search_term) !== false;
    });
}

// تطبيق الفلاتر
if ($filter_type === 'companies') {
    $customers = array_filter($customers, function($customer) {
        return $customer['is_company'] == 1;
    });
} elseif ($filter_type === 'individuals') {
    $customers = array_filter($customers, function($customer) {
        return $customer['is_company'] == 0;
    });
}

// إحصائيات
$total_customers = count($customers);
$companies = count(array_filter($customers, function($c) { return $c['is_company'] == 1; }));
$individuals = count(array_filter($customers, function($c) { return $c['is_company'] == 0; }));
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>العملاء - نظام ERP</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --odoo-primary: #714B67;
            --odoo-secondary: #875A7B;
            --odoo-accent: #D4AF37;
            --odoo-success: #28a745;
            --odoo-info: #17a2b8;
            --odoo-warning: #ffc107;
            --odoo-danger: #dc3545;
            --odoo-light: #f8f9fa;
            --odoo-dark: #2F3349;
        }
        
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background: var(--odoo-light);
            margin: 0;
            padding: 0;
        }
        
        .main-container {
            display: flex;
            min-height: 100vh;
        }
        
        .content-area {
            flex: 1;
            padding: 0;
            overflow-x: hidden;
        }
        
        /* رأس الصفحة */
        .page-header {
            background: white;
            border-bottom: 1px solid #e9ecef;
            padding: 20px 30px;
            margin-bottom: 0;
        }
        
        .breadcrumb-odoo {
            background: none;
            padding: 0;
            margin: 0;
            font-size: 0.9rem;
        }
        
        .breadcrumb-odoo .breadcrumb-item {
            color: #6c757d;
        }
        
        .breadcrumb-odoo .breadcrumb-item.active {
            color: var(--odoo-primary);
            font-weight: 600;
        }
        
        .page-title {
            color: var(--odoo-dark);
            font-size: 1.8rem;
            font-weight: 700;
            margin: 10px 0 0 0;
        }
        
        /* أزرار الإجراءات */
        .action-buttons {
            background: white;
            border-bottom: 1px solid #e9ecef;
            padding: 15px 30px;
        }
        
        .btn-odoo {
            background: var(--odoo-primary);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-odoo:hover {
            background: var(--odoo-secondary);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(113, 75, 103, 0.3);
        }
        
        .btn-outline-odoo {
            background: transparent;
            border: 1px solid var(--odoo-primary);
            color: var(--odoo-primary);
        }
        
        .btn-outline-odoo:hover {
            background: var(--odoo-primary);
            color: white;
        }
        
        /* بطاقات الإحصائيات */
        .stats-container {
            padding: 20px 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid var(--odoo-primary);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--odoo-primary);
            margin: 0;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
            margin: 5px 0 0 0;
        }
        
        .stat-icon {
            font-size: 2.5rem;
            color: var(--odoo-primary);
            opacity: 0.7;
        }
        
        /* منطقة المحتوى */
        .content-section {
            padding: 20px 30px;
        }
        
        /* فلاتر البحث */
        .filter-tabs {
            display: flex;
            gap: 5px;
            margin-bottom: 20px;
        }
        
        .filter-tab {
            background: white;
            border: 1px solid #dee2e6;
            color: #6c757d;
            padding: 8px 16px;
            border-radius: 4px;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 6px;
            cursor: pointer;
        }
        
        .filter-tab:hover {
            background: #f8f9fa;
            color: var(--odoo-primary);
            text-decoration: none;
        }
        
        .filter-tab.active {
            background: var(--odoo-primary);
            color: white;
            border-color: var(--odoo-primary);
        }
        
        /* مربع البحث */
        .search-box {
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 8px 12px;
            font-size: 0.9rem;
        }
        
        .search-box:focus {
            border-color: var(--odoo-primary);
            box-shadow: 0 0 0 0.2rem rgba(113, 75, 103, 0.25);
        }
        
        /* مفتاح العرض */
        .view-switcher {
            display: flex;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .view-btn {
            background: white;
            border: none;
            padding: 8px 12px;
            color: #6c757d;
            transition: all 0.3s ease;
            border-right: 1px solid #dee2e6;
        }
        
        .view-btn:last-child {
            border-right: none;
        }
        
        .view-btn:hover {
            background: #f8f9fa;
            color: var(--odoo-primary);
        }
        
        .view-btn.active {
            background: var(--odoo-primary);
            color: white;
        }
        
        /* عرض البطاقات */
        .customer-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
            overflow: hidden;
        }
        
        .customer-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .customer-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 1.2rem;
        }
        
        .customer-type-badge {
            font-size: 0.75rem;
            padding: 2px 8px;
            border-radius: 12px;
        }
        
        .contact-details {
            font-size: 0.85rem;
        }
        
        /* أزرار الإجراءات */
        .action-btn {
            background: none;
            border: 1px solid #dee2e6;
            color: #6c757d;
            padding: 6px 10px;
            border-radius: 4px;
            transition: all 0.3s ease;
            font-size: 0.8rem;
        }
        
        .btn-edit:hover {
            background: var(--odoo-info);
            color: white;
            border-color: var(--odoo-info);
        }
        
        .btn-delete:hover {
            background: var(--odoo-danger);
            color: white;
            border-color: var(--odoo-danger);
        }
        
        /* عرض القائمة */
        .table-responsive {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .table {
            margin: 0;
        }
        
        .table th {
            background: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
            font-weight: 600;
            color: var(--odoo-dark);
            padding: 12px;
            font-size: 0.9rem;
        }
        
        .table td {
            padding: 12px;
            vertical-align: middle;
            border-bottom: 1px solid #e9ecef;
        }
        
        .table tbody tr:hover {
            background: #f8f9fa;
        }
        
        /* النوافذ المنبثقة */
        .modal-content {
            border: none;
            border-radius: 8px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .modal-header {
            background: var(--odoo-primary);
            color: white;
            border-radius: 8px 8px 0 0;
            padding: 15px 20px;
        }
        
        .modal-title {
            font-weight: 600;
        }
        
        .btn-close {
            filter: invert(1);
        }
        
        .form-label {
            font-weight: 500;
            color: var(--odoo-dark);
            margin-bottom: 5px;
        }
        
        .form-control:focus {
            border-color: var(--odoo-primary);
            box-shadow: 0 0 0 0.2rem rgba(113, 75, 103, 0.25);
        }
        
        /* رسائل التنبيه */
        .alert {
            border: none;
            border-radius: 6px;
            padding: 12px 16px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border-left: 4px solid #28a745;
        }
        
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border-left: 4px solid #dc3545;
        }
        
        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 768px) {
            .page-header, .action-buttons, .content-section {
                padding: 15px 20px;
            }
            
            .filter-tabs {
                flex-wrap: wrap;
            }
            
            .stats-container {
                padding: 15px 20px;
            }
            
            .customer-card {
                margin-bottom: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- الشريط الجانبي -->
        <?php include '../includes/sidebar.php'; ?>
        
        <!-- منطقة المحتوى -->
        <div class="content-area">
            <!-- رأس الصفحة -->
            <div class="page-header">
                <nav class="breadcrumb-odoo">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="../dashboard.php">
                                <i class="fas fa-home"></i> الرئيسية
                            </a>
                        </li>
                        <li class="breadcrumb-item">الشركاء</li>
                        <li class="breadcrumb-item active">العملاء</li>
                    </ol>
                </nav>
                <h1 class="page-title">
                    <i class="fas fa-user-tie me-2"></i>العملاء
                </h1>
            </div>
            
            <!-- أزرار الإجراءات -->
            <div class="action-buttons">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="d-flex gap-2">
                        <button class="btn-odoo" data-bs-toggle="modal" data-bs-target="#customerModal" onclick="openCreateModal()">
                            <i class="fas fa-plus"></i>
                            إنشاء عميل جديد
                        </button>
                        <button class="btn-outline-odoo" onclick="importCustomers()">
                            <i class="fas fa-upload"></i>
                            استيراد
                        </button>
                        <button class="btn-outline-odoo" onclick="exportCustomers()">
                            <i class="fas fa-download"></i>
                            تصدير
                        </button>
                    </div>
                    
                    <div class="d-flex align-items-center gap-3">
                        <span class="text-muted">
                            <i class="fas fa-users me-1"></i>
                            <?php echo $total_customers; ?> عميل
                        </span>
                    </div>
                </div>
            </div>
            
            <!-- رسائل التنبيه -->
            <?php if (isset($success_message)): ?>
                <div class="alert alert-success mx-4">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo $success_message; ?>
                </div>
            <?php endif; ?>
            
            <?php if (isset($error_message)): ?>
                <div class="alert alert-danger mx-4">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <?php echo $error_message; ?>
                </div>
            <?php endif; ?>
            
            <!-- بطاقات الإحصائيات -->
            <div class="stats-container">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="stat-number"><?php echo $total_customers; ?></h3>
                                    <p class="stat-label">إجمالي العملاء</p>
                                </div>
                                <i class="fas fa-users stat-icon"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="stat-number"><?php echo $companies; ?></h3>
                                    <p class="stat-label">الشركات</p>
                                </div>
                                <i class="fas fa-building stat-icon"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="stat-number"><?php echo $individuals; ?></h3>
                                    <p class="stat-label">الأفراد</p>
                                </div>
                                <i class="fas fa-user stat-icon"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="stat-number"><?php echo count(array_filter($customers, function($c) { return $c['active'] ?? 1; })); ?></h3>
                                    <p class="stat-label">النشطين</p>
                                </div>
                                <i class="fas fa-check-circle stat-icon"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- منطقة المحتوى الرئيسي -->
            <div class="content-section">
                <div class="container-fluid">
                    <!-- شريط البحث والفلاتر -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <!-- تبويبات الفلاتر -->
                            <div class="filter-tabs">
                                <button class="filter-tab <?php echo $filter_type === 'all' ? 'active' : ''; ?>"
                                        onclick="applyFilter('all')">
                                    <i class="fas fa-users me-1"></i>الكل
                                </button>
                                <button class="filter-tab <?php echo $filter_type === 'companies' ? 'active' : ''; ?>"
                                        onclick="applyFilter('companies')">
                                    <i class="fas fa-building me-1"></i>الشركات
                                </button>
                                <button class="filter-tab <?php echo $filter_type === 'individuals' ? 'active' : ''; ?>"
                                        onclick="applyFilter('individuals')">
                                    <i class="fas fa-user me-1"></i>الأفراد
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <!-- مربع البحث -->
                            <div class="input-group">
                                <input type="text" class="form-control search-box"
                                       placeholder="البحث في العملاء..."
                                       value="<?php echo htmlspecialchars($search_term); ?>"
                                       onkeyup="searchCustomers(this.value)">
                                <button class="btn btn-outline-secondary" type="button" onclick="clearSearch()">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- شريط أدوات العرض -->
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="d-flex align-items-center">
                            <!-- مفتاح العرض -->
                            <div class="view-switcher me-3">
                                <button class="view-btn <?php echo $view_mode === 'kanban' ? 'active' : ''; ?>"
                                        onclick="switchView('kanban')" title="عرض البطاقات">
                                    <i class="fas fa-th-large"></i>
                                </button>
                                <button class="view-btn <?php echo $view_mode === 'list' ? 'active' : ''; ?>"
                                        onclick="switchView('list')" title="عرض القائمة">
                                    <i class="fas fa-list"></i>
                                </button>
                            </div>

                            <!-- عدد النتائج -->
                            <span class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                <?php echo count($customers); ?> عميل
                            </span>
                        </div>

                        <div class="d-flex align-items-center">
                            <!-- ترتيب -->
                            <select class="form-select form-select-sm" style="width: auto;" onchange="sortCustomers(this.value)">
                                <option value="name_asc">الاسم (أ-ي)</option>
                                <option value="name_desc">الاسم (ي-أ)</option>
                                <option value="date_desc">الأحدث</option>
                                <option value="date_asc">الأقدم</option>
                            </select>
                        </div>
                    </div>

                    <!-- عرض العملاء -->
                    <div id="customersContainer" class="<?php echo $view_mode; ?>-view">
                        <?php if (empty($customers)): ?>
                            <!-- رسالة عدم وجود بيانات -->
                            <div class="text-center py-5">
                                <div class="mb-4">
                                    <i class="fas fa-user-tie fa-4x text-muted"></i>
                                </div>
                                <h4 class="text-muted">لا توجد عملاء</h4>
                                <p class="text-muted">ابدأ بإضافة عميل جديد لرؤية البيانات هنا</p>
                                <button class="btn-odoo" data-bs-toggle="modal" data-bs-target="#customerModal" onclick="openCreateModal()">
                                    <i class="fas fa-plus me-1"></i>إضافة عميل جديد
                                </button>
                            </div>
                        <?php else: ?>
                            <?php if ($view_mode === 'kanban'): ?>
                                <!-- عرض البطاقات -->
                                <div class="row">
                                    <?php foreach ($customers as $customer): ?>
                                        <div class="col-lg-4 col-md-6 mb-3">
                                            <div class="customer-card">
                                                <div class="card-body">
                                                    <div class="d-flex align-items-start mb-3">
                                                        <!-- صورة العميل -->
                                                        <div class="customer-avatar me-3"
                                                             style="background: <?php echo $customer['is_company'] ? '#714B67' : '#28a745'; ?>">
                                                            <?php if ($customer['is_company']): ?>
                                                                <i class="fas fa-building"></i>
                                                            <?php else: ?>
                                                                <?php echo strtoupper(substr($customer['name'], 0, 1)); ?>
                                                            <?php endif; ?>
                                                        </div>

                                                        <!-- معلومات العميل -->
                                                        <div class="flex-grow-1">
                                                            <h6 class="card-title mb-1">
                                                                <?php echo htmlspecialchars($customer['name']); ?>
                                                            </h6>

                                                            <!-- شارات النوع -->
                                                            <div class="mb-2">
                                                                <span class="badge bg-success customer-type-badge me-1">عميل</span>
                                                                <?php if ($customer['is_company']): ?>
                                                                    <span class="badge bg-primary customer-type-badge">شركة</span>
                                                                <?php else: ?>
                                                                    <span class="badge bg-secondary customer-type-badge">فرد</span>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>

                                                        <!-- قائمة الإجراءات -->
                                                        <div class="dropdown">
                                                            <button class="btn btn-sm btn-outline-secondary"
                                                                    data-bs-toggle="dropdown">
                                                                <i class="fas fa-ellipsis-v"></i>
                                                            </button>
                                                            <ul class="dropdown-menu">
                                                                <li>
                                                                    <a class="dropdown-item" href="#"
                                                                       onclick="editCustomer(<?php echo $customer['id']; ?>)">
                                                                        <i class="fas fa-edit me-2"></i>تعديل
                                                                    </a>
                                                                </li>
                                                                <li>
                                                                    <a class="dropdown-item" href="#"
                                                                       onclick="viewCustomer(<?php echo $customer['id']; ?>)">
                                                                        <i class="fas fa-eye me-2"></i>عرض
                                                                    </a>
                                                                </li>
                                                                <li><hr class="dropdown-divider"></li>
                                                                <li>
                                                                    <a class="dropdown-item text-danger" href="#"
                                                                       onclick="deleteCustomer(<?php echo $customer['id']; ?>)">
                                                                        <i class="fas fa-trash me-2"></i>حذف
                                                                    </a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </div>

                                                    <!-- تفاصيل الاتصال -->
                                                    <div class="contact-details">
                                                        <?php if (!empty($customer['email'])): ?>
                                                            <div class="mb-1">
                                                                <i class="fas fa-envelope text-muted me-2"></i>
                                                                <small><?php echo htmlspecialchars($customer['email']); ?></small>
                                                            </div>
                                                        <?php endif; ?>

                                                        <?php if (!empty($customer['phone'])): ?>
                                                            <div class="mb-1">
                                                                <i class="fas fa-phone text-muted me-2"></i>
                                                                <small><?php echo htmlspecialchars($customer['phone']); ?></small>
                                                            </div>
                                                        <?php endif; ?>

                                                        <?php if (!empty($customer['city'])): ?>
                                                            <div class="mb-1">
                                                                <i class="fas fa-map-marker-alt text-muted me-2"></i>
                                                                <small><?php echo htmlspecialchars($customer['city']); ?></small>
                                                            </div>
                                                        <?php endif; ?>

                                                        <?php if (!empty($customer['website'])): ?>
                                                            <div class="mb-1">
                                                                <i class="fas fa-globe text-muted me-2"></i>
                                                                <small>
                                                                    <a href="<?php echo htmlspecialchars($customer['website']); ?>"
                                                                       target="_blank" class="text-decoration-none">
                                                                        موقع إلكتروني
                                                                    </a>
                                                                </small>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php else: ?>
                                <!-- عرض القائمة -->
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>الاسم</th>
                                                <th>النوع</th>
                                                <th>البريد الإلكتروني</th>
                                                <th>الهاتف</th>
                                                <th>المدينة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($customers as $customer): ?>
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div class="customer-avatar me-2"
                                                                 style="background: <?php echo $customer['is_company'] ? '#714B67' : '#28a745'; ?>; width: 35px; height: 35px; font-size: 0.9rem;">
                                                                <?php if ($customer['is_company']): ?>
                                                                    <i class="fas fa-building"></i>
                                                                <?php else: ?>
                                                                    <?php echo strtoupper(substr($customer['name'], 0, 1)); ?>
                                                                <?php endif; ?>
                                                            </div>
                                                            <div>
                                                                <div class="fw-bold"><?php echo htmlspecialchars($customer['name']); ?></div>
                                                                <?php if (!empty($customer['vat'])): ?>
                                                                    <small class="text-muted">ضريبي: <?php echo htmlspecialchars($customer['vat']); ?></small>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-success me-1">عميل</span>
                                                        <span class="badge bg-<?php echo $customer['is_company'] ? 'primary' : 'secondary'; ?>">
                                                            <?php echo $customer['is_company'] ? 'شركة' : 'فرد'; ?>
                                                        </span>
                                                    </td>
                                                    <td><?php echo htmlspecialchars($customer['email'] ?? ''); ?></td>
                                                    <td><?php echo htmlspecialchars($customer['phone'] ?? ''); ?></td>
                                                    <td><?php echo htmlspecialchars($customer['city'] ?? ''); ?></td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            <button class="action-btn btn-edit"
                                                                    onclick="editCustomer(<?php echo $customer['id']; ?>)"
                                                                    title="تعديل">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            <button class="action-btn btn-delete"
                                                                    onclick="deleteCustomer(<?php echo $customer['id']; ?>)"
                                                                    title="حذف">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة حوار العميل -->
    <div class="modal fade" id="customerModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="customerModalTitle">إنشاء عميل جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="customerForm" method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" id="formAction" value="create">
                        <input type="hidden" name="id" id="customerId" value="">

                        <div class="row">
                            <!-- المعلومات الأساسية -->
                            <div class="col-md-6">
                                <h6 class="mb-3">المعلومات الأساسية</h6>

                                <div class="mb-3">
                                    <label class="form-label">الاسم *</label>
                                    <input type="text" class="form-control" name="name" id="customerName" required>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" name="email" id="customerEmail">
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الهاتف</label>
                                            <input type="text" class="form-control" name="phone" id="customerPhone">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الجوال</label>
                                            <input type="text" class="form-control" name="mobile" id="customerMobile">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">الموقع الإلكتروني</label>
                                    <input type="url" class="form-control" name="website" id="customerWebsite">
                                </div>
                            </div>

                            <!-- العنوان والتصنيف -->
                            <div class="col-md-6">
                                <h6 class="mb-3">العنوان والتصنيف</h6>

                                <div class="mb-3">
                                    <label class="form-label">العنوان</label>
                                    <input type="text" class="form-control" name="street" id="customerStreet">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">المدينة</label>
                                    <input type="text" class="form-control" name="city" id="customerCity">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">الرقم الضريبي</label>
                                    <input type="text" class="form-control" name="vat" id="customerVat">
                                </div>

                                <!-- خيارات التصنيف -->
                                <div class="mb-3">
                                    <label class="form-label">التصنيف</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="is_company" id="isCompany">
                                        <label class="form-check-label" for="isCompany">
                                            شركة
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn-odoo">حفظ</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // متغيرات عامة
        let currentFilter = '<?php echo $filter_type; ?>';
        let currentView = '<?php echo $view_mode; ?>';
        let currentSearch = '<?php echo $search_term; ?>';

        // تطبيق الفلتر
        function applyFilter(filter) {
            const url = new URL(window.location);
            url.searchParams.set('filter', filter);
            window.location.href = url.toString();
        }

        // تبديل طريقة العرض
        function switchView(view) {
            const url = new URL(window.location);
            url.searchParams.set('view', view);
            window.location.href = url.toString();
        }

        // البحث في العملاء
        function searchCustomers(term) {
            const url = new URL(window.location);
            if (term.trim()) {
                url.searchParams.set('search', term);
            } else {
                url.searchParams.delete('search');
            }

            // تأخير البحث لتحسين الأداء
            clearTimeout(window.searchTimeout);
            window.searchTimeout = setTimeout(() => {
                window.location.href = url.toString();
            }, 500);
        }

        // مسح البحث
        function clearSearch() {
            const url = new URL(window.location);
            url.searchParams.delete('search');
            window.location.href = url.toString();
        }

        // ترتيب العملاء
        function sortCustomers(sortBy) {
            console.log('Sorting by:', sortBy);
        }

        // فتح نافذة إنشاء عميل جديد
        function openCreateModal() {
            document.getElementById('customerModalTitle').textContent = 'إنشاء عميل جديد';
            document.getElementById('formAction').value = 'create';
            document.getElementById('customerId').value = '';
            document.getElementById('customerForm').reset();
        }

        // تعديل عميل
        function editCustomer(id) {
            document.getElementById('customerModalTitle').textContent = 'تعديل العميل';
            document.getElementById('formAction').value = 'update';
            document.getElementById('customerId').value = id;

            // فتح النافذة
            const modal = new bootstrap.Modal(document.getElementById('customerModal'));
            modal.show();
        }

        // عرض تفاصيل العميل
        function viewCustomer(id) {
            window.location.href = `customer_details.php?id=${id}`;
        }

        // حذف عميل
        function deleteCustomer(id) {
            if (confirm('هل أنت متأكد من حذف هذا العميل؟ لا يمكن التراجع عن هذا الإجراء.')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="id" value="${id}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        // استيراد العملاء
        function importCustomers() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.csv,.xlsx,.xls';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    alert('سيتم تطوير ميزة الاستيراد قريباً');
                }
            };
            input.click();
        }

        // تصدير العملاء
        function exportCustomers() {
            alert('سيتم تطوير ميزة التصدير قريباً');
        }

        // تحسينات UX
        document.addEventListener('DOMContentLoaded', function() {
            // تأثيرات hover للبطاقات
            document.querySelectorAll('.customer-card').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // تحسين البحث
            const searchInput = document.querySelector('.search-box');
            if (searchInput) {
                searchInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        searchCustomers(this.value);
                    }
                });
            }

            // تحسين النماذج
            const customerForm = document.getElementById('customerForm');
            if (customerForm) {
                customerForm.addEventListener('submit', function(e) {
                    const name = document.getElementById('customerName').value.trim();
                    if (!name) {
                        e.preventDefault();
                        alert('يرجى إدخال اسم العميل');
                        document.getElementById('customerName').focus();
                        return false;
                    }
                });
            }
        });
    </script>
</body>
</html>
