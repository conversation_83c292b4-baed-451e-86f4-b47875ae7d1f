<?php
/**
 * لوحة التحكم الرئيسية بأسلوب Odoo الكامل
 * Main Dashboard - Complete Odoo Style
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// تحميل النماذج
require_once '../models/AccountMove.php';
require_once '../models/ResPartner.php';
require_once '../models/ProductTemplate.php';

// تهيئة النماذج
$move_model = new AccountMove();
$partner_model = new ResPartner();
$product_model = new ProductTemplate();

// جلب البيانات للإحصائيات
$moves = $move_model->get_demo_data();
$partners = $partner_model->get_demo_data();
$products = $product_model->get_demo_data();

// حساب الإحصائيات
$stats = array(
    'total_moves' => count($moves),
    'draft_moves' => count(array_filter($moves, function($m) { return $m['state'] === 'draft'; })),
    'posted_moves' => count(array_filter($moves, function($m) { return $m['state'] === 'posted'; })),
    'total_partners' => count($partners),
    'customers' => count(array_filter($partners, function($p) { return $p['is_customer']; })),
    'suppliers' => count(array_filter($partners, function($p) { return $p['is_supplier']; })),
    'total_products' => count($products),
    'active_products' => count(array_filter($products, function($p) { return $p['active']; }))
);

// حساب المؤشرات المالية
$financial_data = array(
    'total_revenue' => 250000.00,
    'total_expenses' => 180000.00,
    'net_profit' => 70000.00,
    'cash_balance' => 95000.00,
    'receivables' => 65000.00,
    'payables' => 45000.00
);

// الوحدات الرئيسية
$main_modules = array(
    'accounting' => array(
        'name' => 'المحاسبة',
        'icon' => 'fas fa-calculator',
        'color' => '#875A7B',
        'description' => 'إدارة القيود والحسابات المالية',
        'url' => 'journal_entries_odoo.php',
        'count' => $stats['total_moves']
    ),
    'partners' => array(
        'name' => 'الشركاء',
        'icon' => 'fas fa-users',
        'color' => '#00A09D',
        'description' => 'إدارة العملاء والموردين',
        'url' => 'partners_odoo.php',
        'count' => $stats['total_partners']
    ),
    'products' => array(
        'name' => 'المنتجات',
        'icon' => 'fas fa-box',
        'color' => '#F0AD4E',
        'description' => 'إدارة المنتجات والخدمات',
        'url' => 'products_odoo.php',
        'count' => $stats['total_products']
    ),
    'invoices' => array(
        'name' => 'الفواتير',
        'icon' => 'fas fa-file-invoice',
        'color' => '#D73502',
        'description' => 'إدارة فواتير البيع والشراء',
        'url' => 'invoices_odoo.php',
        'count' => 15
    ),
    'reports' => array(
        'name' => 'التقارير',
        'icon' => 'fas fa-chart-line',
        'color' => '#00A7E1',
        'description' => 'التقارير المالية والإحصائية',
        'url' => 'reports_odoo.php',
        'count' => 8
    ),
    'settings' => array(
        'name' => 'الإعدادات',
        'icon' => 'fas fa-cog',
        'color' => '#6C757D',
        'description' => 'إعدادات النظام والمستخدمين',
        'url' => 'settings_odoo.php',
        'count' => 12
    )
);

// النشاطات الأخيرة
$recent_activities = array(
    array(
        'type' => 'move',
        'icon' => 'fas fa-plus-circle',
        'color' => 'success',
        'title' => 'قيد محاسبي جديد',
        'description' => 'تم إنشاء قيد رقم JE/2024/001',
        'time' => '5 دقائق',
        'user' => 'أحمد محمد'
    ),
    array(
        'type' => 'payment',
        'icon' => 'fas fa-money-bill',
        'color' => 'primary',
        'title' => 'مدفوعة جديدة',
        'description' => 'تم استلام دفعة بقيمة 15,000 ر.س',
        'time' => '15 دقيقة',
        'user' => 'سارة أحمد'
    ),
    array(
        'type' => 'partner',
        'icon' => 'fas fa-user-plus',
        'color' => 'info',
        'title' => 'عميل جديد',
        'description' => 'تم إضافة عميل: شركة التطوير المتقدم',
        'time' => '30 دقيقة',
        'user' => 'محمد علي'
    ),
    array(
        'type' => 'report',
        'icon' => 'fas fa-file-alt',
        'color' => 'warning',
        'title' => 'تقرير مالي',
        'description' => 'تم إنشاء تقرير الميزانية العمومية',
        'time' => '1 ساعة',
        'user' => 'فاطمة خالد'
    )
);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام ERP</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Odoo Style -->
    <link href="../assets/css/odoo-style.css" rel="stylesheet">
    <link href="../assets/css/odoo-advanced.css" rel="stylesheet">
    
    <style>
        .dashboard-welcome {
            background: linear-gradient(135deg, #714B67 0%, #875A7B 100%);
            color: white;
            padding: 2rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            box-shadow: 0 4px 20px rgba(113, 75, 103, 0.3);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
            position: relative;
            overflow: hidden;
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, transparent 0%, rgba(255,255,255,0.1) 50%, transparent 100%);
            transform: translateX(-100%);
            transition: transform 0.6s ease;
        }
        
        .stat-card:hover::before {
            transform: translateX(100%);
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .stat-card.revenue {
            border-left-color: #28a745;
        }
        
        .stat-card.expenses {
            border-left-color: #dc3545;
        }
        
        .stat-card.profit {
            border-left-color: #007bff;
        }
        
        .stat-card.cash {
            border-left-color: #17a2b8;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
            background: linear-gradient(45deg, #714B67, #875A7B);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }
        
        .stat-change {
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .modules-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .module-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        
        .module-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: var(--module-color);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }
        
        .module-card:hover::before {
            transform: scaleX(1);
        }
        
        .module-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .module-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-bottom: 1rem;
        }
        
        .module-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #2c3e50;
        }
        
        .module-description {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }
        
        .module-count {
            background: #f8f9fa;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            color: #495057;
            display: inline-block;
        }
        
        .chart-container {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .activity-feed {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .activity-item {
            display: flex;
            align-items: flex-start;
            padding: 1rem 0;
            border-bottom: 1px solid #f0f0f0;
            transition: all 0.3s ease;
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-item:hover {
            background: #f8f9fa;
            margin: 0 -1rem;
            padding: 1rem;
            border-radius: 8px;
        }
        
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 1rem;
            font-size: 0.9rem;
        }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-title {
            font-weight: 600;
            margin-bottom: 0.25rem;
            color: #2c3e50;
        }
        
        .activity-description {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 0.25rem;
        }
        
        .activity-meta {
            font-size: 0.8rem;
            color: #adb5bd;
        }
        
        .quick-actions {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .quick-action-btn {
            background: linear-gradient(45deg, #714B67, #875A7B);
            border: none;
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: block;
            text-align: center;
            margin-bottom: 0.5rem;
        }
        
        .quick-action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(113, 75, 103, 0.3);
            color: white;
        }
        
        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .modules-grid {
                grid-template-columns: 1fr;
            }
            
            .dashboard-welcome {
                padding: 1.5rem;
            }
            
            .stat-number {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <?php include '../includes/navbar.php'; ?>
    
    <div class="o_main_content">
        <div class="o_content">
            <!-- الشريط الجانبي -->
            <?php include '../includes/sidebar.php'; ?>
            
            <!-- المحتوى الرئيسي -->
            <div class="o_action_manager">
                <!-- شريط التحكم -->
                <div class="o_control_panel">
                    <div class="o_cp_left">
                        <div class="o_breadcrumb">
                            <span class="o_breadcrumb_item active">لوحة التحكم</span>
                        </div>
                    </div>
                    <div class="o_cp_right">
                        <div class="btn-group">
                            <button class="btn btn-outline-secondary btn-sm" onclick="refreshDashboard()">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                            <button class="btn btn-outline-secondary btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="fas fa-cog"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="customizeDashboard()">
                                    <i class="fas fa-edit me-2"></i>تخصيص لوحة التحكم
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="exportDashboard()">
                                    <i class="fas fa-download me-2"></i>تصدير البيانات
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- محتوى لوحة التحكم -->
                <div class="p-3">
                    <!-- ترحيب -->
                    <div class="dashboard-welcome">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h2 class="mb-2">مرحباً بك في نظام ERP</h2>
                                <p class="mb-0 opacity-75">إدارة شاملة لموارد المؤسسة مع واجهة Odoo الاحترافية</p>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="d-flex align-items-center justify-content-end">
                                    <div class="me-3">
                                        <div class="text-white-50 small">آخر تحديث</div>
                                        <div class="fw-bold"><?php echo date('Y/m/d H:i'); ?></div>
                                    </div>
                                    <i class="fas fa-chart-line fa-3x opacity-50"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- المؤشرات المالية -->
                    <div class="stats-grid">
                        <div class="stat-card revenue">
                            <div class="stat-number"><?php echo number_format($financial_data['total_revenue']); ?></div>
                            <div class="stat-label">إجمالي الإيرادات</div>
                            <div class="stat-change text-success">
                                <i class="fas fa-arrow-up me-1"></i>+12.5%
                            </div>
                        </div>
                        <div class="stat-card expenses">
                            <div class="stat-number"><?php echo number_format($financial_data['total_expenses']); ?></div>
                            <div class="stat-label">إجمالي المصروفات</div>
                            <div class="stat-change text-danger">
                                <i class="fas fa-arrow-up me-1"></i>+8.3%
                            </div>
                        </div>
                        <div class="stat-card profit">
                            <div class="stat-number"><?php echo number_format($financial_data['net_profit']); ?></div>
                            <div class="stat-label">صافي الربح</div>
                            <div class="stat-change text-success">
                                <i class="fas fa-arrow-up me-1"></i>+18.7%
                            </div>
                        </div>
                        <div class="stat-card cash">
                            <div class="stat-number"><?php echo number_format($financial_data['cash_balance']); ?></div>
                            <div class="stat-label">رصيد النقدية</div>
                            <div class="stat-change text-warning">
                                <i class="fas fa-minus me-1"></i>-2.1%
                            </div>
                        </div>
                    </div>

                    <!-- الوحدات الرئيسية -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h4 class="mb-3">
                                <i class="fas fa-th-large me-2 text-primary"></i>
                                الوحدات الرئيسية
                            </h4>
                        </div>
                    </div>

                    <div class="modules-grid">
                        <?php foreach ($main_modules as $key => $module): ?>
                            <div class="module-card" onclick="window.location.href='<?php echo $module['url']; ?>'" style="--module-color: <?php echo $module['color']; ?>">
                                <div class="module-icon" style="background: <?php echo $module['color']; ?>">
                                    <i class="<?php echo $module['icon']; ?>"></i>
                                </div>
                                <div class="module-title"><?php echo $module['name']; ?></div>
                                <div class="module-description"><?php echo $module['description']; ?></div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="module-count"><?php echo $module['count']; ?> عنصر</span>
                                    <i class="fas fa-arrow-left text-muted"></i>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- الرسوم البيانية والنشاطات -->
                    <div class="row">
                        <div class="col-lg-8">
                            <!-- الرسم البياني -->
                            <div class="chart-container">
                                <h5 class="mb-3">
                                    <i class="fas fa-chart-area me-2 text-primary"></i>
                                    تحليل الأداء المالي
                                </h5>
                                <canvas id="financialChart" height="300"></canvas>
                            </div>

                            <!-- الإجراءات السريعة -->
                            <div class="quick-actions">
                                <h5 class="mb-3">
                                    <i class="fas fa-bolt me-2 text-warning"></i>
                                    الإجراءات السريعة
                                </h5>
                                <div class="row">
                                    <div class="col-md-3 col-6 mb-2">
                                        <a href="create_entry_enhanced.php" class="quick-action-btn">
                                            <i class="fas fa-plus me-2"></i>قيد جديد
                                        </a>
                                    </div>
                                    <div class="col-md-3 col-6 mb-2">
                                        <a href="partners_odoo.php?action=create" class="quick-action-btn">
                                            <i class="fas fa-user-plus me-2"></i>شريك جديد
                                        </a>
                                    </div>
                                    <div class="col-md-3 col-6 mb-2">
                                        <a href="products_odoo.php?action=create" class="quick-action-btn">
                                            <i class="fas fa-box me-2"></i>منتج جديد
                                        </a>
                                    </div>
                                    <div class="col-md-3 col-6 mb-2">
                                        <a href="reports_odoo.php" class="quick-action-btn">
                                            <i class="fas fa-chart-bar me-2"></i>تقرير
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4">
                            <!-- النشاطات الأخيرة -->
                            <div class="activity-feed">
                                <h5 class="mb-3">
                                    <i class="fas fa-history me-2 text-info"></i>
                                    النشاطات الأخيرة
                                </h5>
                                <?php foreach ($recent_activities as $activity): ?>
                                    <div class="activity-item">
                                        <div class="activity-icon bg-<?php echo $activity['color']; ?> text-white">
                                            <i class="<?php echo $activity['icon']; ?>"></i>
                                        </div>
                                        <div class="activity-content">
                                            <div class="activity-title"><?php echo $activity['title']; ?></div>
                                            <div class="activity-description"><?php echo $activity['description']; ?></div>
                                            <div class="activity-meta">
                                                <?php echo $activity['user']; ?> • منذ <?php echo $activity['time']; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>

                                <div class="text-center mt-3">
                                    <a href="#" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye me-1"></i>عرض جميع النشاطات
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializeChart();
            animateCounters();
            startRealTimeUpdates();
        });

        // تهيئة الرسم البياني
        function initializeChart() {
            const ctx = document.getElementById('financialChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                    datasets: [{
                        label: 'الإيرادات',
                        data: [42000, 48000, 45000, 52000, 49000, 55000],
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: 'المصروفات',
                        data: [30000, 35000, 32000, 38000, 36000, 40000],
                        borderColor: '#dc3545',
                        backgroundColor: 'rgba(220, 53, 69, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                        }
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: 'الشهر'
                            }
                        },
                        y: {
                            display: true,
                            title: {
                                display: true,
                                text: 'المبلغ (ر.س)'
                            },
                            beginAtZero: true
                        }
                    },
                    interaction: {
                        mode: 'nearest',
                        axis: 'x',
                        intersect: false
                    }
                }
            });
        }

        // تحريك العدادات
        function animateCounters() {
            const counters = document.querySelectorAll('.stat-number');
            counters.forEach(counter => {
                const target = parseInt(counter.textContent.replace(/,/g, ''));
                let current = 0;
                const increment = target / 100;

                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    counter.textContent = Math.floor(current).toLocaleString();
                }, 20);
            });
        }

        // تحديثات الوقت الفعلي
        function startRealTimeUpdates() {
            setInterval(() => {
                updateLastRefresh();
            }, 60000); // كل دقيقة
        }

        function updateLastRefresh() {
            const now = new Date();
            const timeString = now.toLocaleString('ar-SA', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });

            const refreshElement = document.querySelector('.dashboard-welcome .fw-bold');
            if (refreshElement) {
                refreshElement.textContent = timeString;
            }
        }

        // وظائف الإجراءات
        function refreshDashboard() {
            showMessage('جاري تحديث لوحة التحكم...', 'info');
            setTimeout(() => {
                location.reload();
            }, 1000);
        }

        function customizeDashboard() {
            showMessage('ميزة التخصيص قيد التطوير', 'info');
        }

        function exportDashboard() {
            showMessage('جاري تصدير البيانات...', 'info');
        }

        // وظيفة عرض الرسائل
        function showMessage(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';

            const iconClass = type === 'success' ? 'check-circle' :
                             type === 'danger' ? 'exclamation-triangle' :
                             type === 'warning' ? 'exclamation-triangle' : 'info-circle';

            alertDiv.innerHTML = `
                <i class="fas fa-${iconClass} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(alertDiv);

            setTimeout(() => {
                alertDiv.remove();
            }, 4000);
        }

        // تأثيرات بصرية إضافية
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير hover للبطاقات
            document.querySelectorAll('.module-card').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // تأثير النقر على الإحصائيات
            document.querySelectorAll('.stat-card').forEach(card => {
                card.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });
        });
    </script>
</body>
</html>
