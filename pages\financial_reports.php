<?php
/**
 * صفحة التقارير المالية بأسلوب Odoo
 * Financial Reports Page - Odoo Style
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// تحميل نظام Odoo
require_once '../config/odoo_config.php';
require_once '../config/odoo_database.php';

$odoo_db = OdooDatabase::getInstance();

// تحميل النماذج
require_once '../models/BaseModel.php';
require_once '../models/AccountAccount.php';
require_once '../models/AccountMove.php';
require_once '../models/AccountMoveLine.php';
require_once '../models/ResCurrency.php';

$account_model = new AccountAccount($odoo_db);
$move_model = new AccountMove($odoo_db);
$move_line_model = new AccountMoveLine($odoo_db);
$currency_model = new ResCurrency($odoo_db);

// معالجة المعاملات
$report_type = isset($_GET['report']) ? $_GET['report'] : 'balance_sheet';
$date_from = isset($_GET['date_from']) ? $_GET['date_from'] : date('Y-01-01');
$date_to = isset($_GET['date_to']) ? $_GET['date_to'] : date('Y-m-d');
$company_id = isset($_GET['company_id']) ? $_GET['company_id'] : 1;

// دالة حساب الأرصدة حسب النوع
function calculate_balance_by_type($account_type, $date_from, $date_to, $account_model) {
    $accounts = $account_model->get_accounts_by_type($account_type);
    $total_balance = 0;
    $account_balances = array();
    
    foreach ($accounts as $account) {
        $balance = $account_model->compute_balance($account['id'], $date_from, $date_to);
        $account_balances[] = array(
            'account' => $account,
            'balance' => $balance['balance'],
            'debit' => $balance['debit'],
            'credit' => $balance['credit']
        );
        $total_balance += $balance['balance'];
    }
    
    return array(
        'total' => $total_balance,
        'accounts' => $account_balances
    );
}

// حساب البيانات للتقارير
$assets = calculate_balance_by_type('asset', $date_from, $date_to, $account_model);
$liabilities = calculate_balance_by_type('liability', $date_from, $date_to, $account_model);
$equity = calculate_balance_by_type('equity', $date_from, $date_to, $account_model);
$income = calculate_balance_by_type('income', $date_from, $date_to, $account_model);
$expenses = calculate_balance_by_type('expense', $date_from, $date_to, $account_model);

// حساب صافي الربح
$net_income = abs($income['total']) - $expenses['total'];

// أنواع التقارير
$report_types = array(
    'balance_sheet' => array('name' => 'الميزانية العمومية', 'icon' => 'fas fa-balance-scale', 'color' => 'primary'),
    'income_statement' => array('name' => 'قائمة الدخل', 'icon' => 'fas fa-chart-line', 'color' => 'success'),
    'trial_balance' => array('name' => 'ميزان المراجعة', 'icon' => 'fas fa-calculator', 'color' => 'info'),
    'cash_flow' => array('name' => 'قائمة التدفقات النقدية', 'icon' => 'fas fa-money-bill-wave', 'color' => 'warning')
);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير المالية - نظام ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --odoo-primary: #714B67;
            --odoo-secondary: #8B5A8C;
        }
        
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 0.85rem;
        }
        
        .navbar-odoo {
            background: linear-gradient(45deg, var(--odoo-primary), var(--odoo-secondary));
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .page-header {
            background: linear-gradient(45deg, #E74C3C, #C0392B);
            color: white;
            padding: 1.5rem;
            border-radius: 15px;
            margin-bottom: 1.5rem;
        }
        
        .report-controls {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        
        .report-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
            transition: all 0.3s ease;
        }
        
        .report-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.12);
        }
        
        .report-btn {
            border: 1px solid #dee2e6;
            background: white;
            color: #6c757d;
            padding: 0.6rem 1rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            text-decoration: none;
            font-size: 0.85rem;
            margin: 0.2rem;
        }
        
        .report-btn.active {
            background: #E74C3C;
            color: white;
            border-color: #E74C3C;
        }
        
        .report-btn:hover {
            background: #C0392B;
            color: white;
            border-color: #C0392B;
            text-decoration: none;
        }
        
        .financial-table {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
        }
        
        .financial-table th {
            background: linear-gradient(45deg, #E74C3C, #C0392B);
            color: white;
            border: none;
            padding: 1rem;
            font-size: 0.85rem;
        }
        
        .financial-table td {
            padding: 0.8rem 1rem;
            border-color: #f8f9fa;
            vertical-align: middle;
            font-size: 0.8rem;
        }
        
        .breadcrumb-odoo {
            background: white;
            border-radius: 8px;
            padding: 0.8rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            font-size: 0.8rem;
        }
        
        .summary-card {
            background: white;
            border-radius: 12px;
            padding: 1.2rem;
            text-align: center;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
            border-left: 4px solid;
            margin-bottom: 1rem;
        }
        
        .amount-positive { color: #28a745; font-weight: bold; }
        .amount-negative { color: #dc3545; font-weight: bold; }
        .amount-zero { color: #6c757d; }
        
        .section-header {
            background: #f8f9fa;
            padding: 0.8rem 1rem;
            font-weight: bold;
            border-bottom: 2px solid #dee2e6;
        }
        
        .account-row {
            padding-right: 2rem;
        }
        
        .total-row {
            background: #f8f9fa;
            font-weight: bold;
            border-top: 2px solid #dee2e6;
        }
        
        .chart-container {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-odoo">
        <div class="container-fluid">
            <a class="navbar-brand text-white" href="../dashboard.php">
                <i class="fas fa-cube me-2"></i>
                نظام ERP - التقارير المالية
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="../dashboard.php">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-3">
        <!-- مسار التنقل -->
        <nav class="breadcrumb-odoo">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="../dashboard.php">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="#">المحاسبة</a></li>
                <li class="breadcrumb-item active">التقارير المالية</li>
            </ol>
        </nav>

        <!-- العنوان الرئيسي -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h3><i class="fas fa-chart-bar me-2"></i>التقارير المالية</h3>
                    <p class="mb-0 small">تقارير مالية شاملة ومفصلة</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-light btn-sm" onclick="window.print()">
                        <i class="fas fa-print me-2"></i>طباعة
                    </button>
                    <button class="btn btn-outline-light btn-sm" onclick="exportReport()">
                        <i class="fas fa-download me-2"></i>تصدير
                    </button>
                </div>
            </div>
        </div>

        <!-- أدوات التحكم -->
        <div class="report-controls">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="d-flex flex-wrap">
                        <?php foreach ($report_types as $type => $info): ?>
                            <a href="?report=<?php echo $type; ?>&date_from=<?php echo $date_from; ?>&date_to=<?php echo $date_to; ?>"
                               class="report-btn <?php echo $report_type === $type ? 'active' : ''; ?>">
                                <i class="<?php echo $info['icon']; ?> me-1"></i><?php echo $info['name']; ?>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
                <div class="col-md-6">
                    <form method="GET" class="d-flex justify-content-end align-items-center">
                        <input type="hidden" name="report" value="<?php echo $report_type; ?>">
                        <label class="me-2 small">من:</label>
                        <input type="date" name="date_from" value="<?php echo $date_from; ?>" class="form-control form-control-sm me-2" style="width: 140px;">
                        <label class="me-2 small">إلى:</label>
                        <input type="date" name="date_to" value="<?php echo $date_to; ?>" class="form-control form-control-sm me-2" style="width: 140px;">
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="fas fa-search me-1"></i>تحديث
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- ملخص سريع -->
        <div class="row mb-3">
            <div class="col-md-3">
                <div class="summary-card border-success">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <i class="fas fa-coins text-success" style="font-size: 1.5rem;"></i>
                    </div>
                    <h4 class="text-success"><?php echo number_format($assets['total'], 2); ?> ر.س</h4>
                    <p class="mb-0 small">إجمالي الأصول</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="summary-card border-danger">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <i class="fas fa-credit-card text-danger" style="font-size: 1.5rem;"></i>
                    </div>
                    <h4 class="text-danger"><?php echo number_format(abs($liabilities['total']), 2); ?> ر.س</h4>
                    <p class="mb-0 small">إجمالي الخصوم</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="summary-card border-primary">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <i class="fas fa-balance-scale text-primary" style="font-size: 1.5rem;"></i>
                    </div>
                    <h4 class="text-primary"><?php echo number_format(abs($equity['total']), 2); ?> ر.س</h4>
                    <p class="mb-0 small">حقوق الملكية</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="summary-card border-<?php echo $net_income >= 0 ? 'success' : 'warning'; ?>">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <i class="fas fa-chart-line text-<?php echo $net_income >= 0 ? 'success' : 'warning'; ?>" style="font-size: 1.5rem;"></i>
                    </div>
                    <h4 class="text-<?php echo $net_income >= 0 ? 'success' : 'warning'; ?>"><?php echo number_format($net_income, 2); ?> ر.س</h4>
                    <p class="mb-0 small">صافي <?php echo $net_income >= 0 ? 'الربح' : 'الخسارة'; ?></p>
                </div>
            </div>
        </div>

        <!-- محتوى التقرير -->
        <?php if ($report_type === 'balance_sheet'): ?>
            <!-- الميزانية العمومية -->
            <div class="row">
                <div class="col-md-6">
                    <div class="financial-table">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th colspan="2">الأصول</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($assets['accounts'] as $account_data): ?>
                                    <tr class="account-row">
                                        <td><?php echo $account_data['account']['name']; ?></td>
                                        <td class="text-end">
                                            <span class="<?php echo $account_data['balance'] > 0 ? 'amount-positive' : ($account_data['balance'] < 0 ? 'amount-negative' : 'amount-zero'); ?>">
                                                <?php echo number_format($account_data['balance'], 2); ?> ر.س
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                                <tr class="total-row">
                                    <td><strong>إجمالي الأصول</strong></td>
                                    <td class="text-end">
                                        <strong class="amount-positive"><?php echo number_format($assets['total'], 2); ?> ر.س</strong>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="financial-table mb-3">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th colspan="2">الخصوم</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($liabilities['accounts'] as $account_data): ?>
                                    <tr class="account-row">
                                        <td><?php echo $account_data['account']['name']; ?></td>
                                        <td class="text-end">
                                            <span class="<?php echo $account_data['balance'] < 0 ? 'amount-positive' : ($account_data['balance'] > 0 ? 'amount-negative' : 'amount-zero'); ?>">
                                                <?php echo number_format(abs($account_data['balance']), 2); ?> ر.س
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                                <tr class="total-row">
                                    <td><strong>إجمالي الخصوم</strong></td>
                                    <td class="text-end">
                                        <strong class="amount-positive"><?php echo number_format(abs($liabilities['total']), 2); ?> ر.س</strong>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="financial-table">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th colspan="2">حقوق الملكية</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($equity['accounts'] as $account_data): ?>
                                    <tr class="account-row">
                                        <td><?php echo $account_data['account']['name']; ?></td>
                                        <td class="text-end">
                                            <span class="<?php echo $account_data['balance'] < 0 ? 'amount-positive' : ($account_data['balance'] > 0 ? 'amount-negative' : 'amount-zero'); ?>">
                                                <?php echo number_format(abs($account_data['balance']), 2); ?> ر.س
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                                <tr class="account-row">
                                    <td>صافي الربح للفترة</td>
                                    <td class="text-end">
                                        <span class="<?php echo $net_income >= 0 ? 'amount-positive' : 'amount-negative'; ?>">
                                            <?php echo number_format($net_income, 2); ?> ر.س
                                        </span>
                                    </td>
                                </tr>
                                <tr class="total-row">
                                    <td><strong>إجمالي حقوق الملكية</strong></td>
                                    <td class="text-end">
                                        <strong class="amount-positive"><?php echo number_format(abs($equity['total']) + $net_income, 2); ?> ر.س</strong>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

        <?php elseif ($report_type === 'income_statement'): ?>
            <!-- قائمة الدخل -->
            <div class="row">
                <div class="col-md-8 mx-auto">
                    <div class="financial-table">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th colspan="2">قائمة الدخل للفترة من <?php echo $date_from; ?> إلى <?php echo $date_to; ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="section-header">
                                    <td colspan="2">الإيرادات</td>
                                </tr>
                                <?php foreach ($income['accounts'] as $account_data): ?>
                                    <tr class="account-row">
                                        <td><?php echo $account_data['account']['name']; ?></td>
                                        <td class="text-end">
                                            <span class="amount-positive"><?php echo number_format(abs($account_data['balance']), 2); ?> ر.س</span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                                <tr class="total-row">
                                    <td><strong>إجمالي الإيرادات</strong></td>
                                    <td class="text-end">
                                        <strong class="amount-positive"><?php echo number_format(abs($income['total']), 2); ?> ر.س</strong>
                                    </td>
                                </tr>

                                <tr class="section-header">
                                    <td colspan="2">المصروفات</td>
                                </tr>
                                <?php foreach ($expenses['accounts'] as $account_data): ?>
                                    <tr class="account-row">
                                        <td><?php echo $account_data['account']['name']; ?></td>
                                        <td class="text-end">
                                            <span class="amount-negative"><?php echo number_format($account_data['balance'], 2); ?> ر.س</span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                                <tr class="total-row">
                                    <td><strong>إجمالي المصروفات</strong></td>
                                    <td class="text-end">
                                        <strong class="amount-negative"><?php echo number_format($expenses['total'], 2); ?> ر.س</strong>
                                    </td>
                                </tr>

                                <tr class="total-row" style="background: #e8f5e8;">
                                    <td><strong>صافي الربح (الخسارة)</strong></td>
                                    <td class="text-end">
                                        <strong class="<?php echo $net_income >= 0 ? 'amount-positive' : 'amount-negative'; ?>">
                                            <?php echo number_format($net_income, 2); ?> ر.س
                                        </strong>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

        <?php elseif ($report_type === 'trial_balance'): ?>
            <!-- ميزان المراجعة -->
            <div class="report-card">
                <div class="text-center">
                    <i class="fas fa-calculator text-info" style="font-size: 3rem;"></i>
                    <h5 class="mt-3">ميزان المراجعة</h5>
                    <p class="text-muted">عرض تفصيلي لجميع أرصدة الحسابات</p>
                    <a href="trial_balance.php?date_from=<?php echo $date_from; ?>&date_to=<?php echo $date_to; ?>" class="btn btn-info">
                        <i class="fas fa-eye me-2"></i>عرض ميزان المراجعة
                    </a>
                </div>
            </div>

        <?php else: ?>
            <!-- تقارير أخرى -->
            <div class="report-card">
                <div class="text-center">
                    <i class="fas fa-tools text-muted" style="font-size: 3rem;"></i>
                    <h5 class="mt-3">قيد التطوير</h5>
                    <p class="text-muted">سيتم إضافة هذا التقرير قريباً</p>
                </div>
            </div>
        <?php endif; ?>

        <!-- الرسوم البيانية -->
        <?php if ($report_type === 'balance_sheet' || $report_type === 'income_statement'): ?>
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="chart-container">
                        <h6><i class="fas fa-chart-pie me-2"></i>توزيع الأصول والخصوم</h6>
                        <canvas id="balanceChart" height="200"></canvas>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="chart-container">
                        <h6><i class="fas fa-chart-bar me-2"></i>الإيرادات والمصروفات</h6>
                        <canvas id="incomeChart" height="200"></canvas>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // رسم بياني للميزانية
        const balanceCtx = document.getElementById('balanceChart');
        if (balanceCtx) {
            new Chart(balanceCtx, {
                type: 'doughnut',
                data: {
                    labels: ['الأصول', 'الخصوم', 'حقوق الملكية'],
                    datasets: [{
                        data: [
                            <?php echo $assets['total']; ?>,
                            <?php echo abs($liabilities['total']); ?>,
                            <?php echo abs($equity['total']) + $net_income; ?>
                        ],
                        backgroundColor: ['#28a745', '#dc3545', '#007bff'],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // رسم بياني للإيرادات والمصروفات
        const incomeCtx = document.getElementById('incomeChart');
        if (incomeCtx) {
            new Chart(incomeCtx, {
                type: 'bar',
                data: {
                    labels: ['الإيرادات', 'المصروفات', 'صافي الربح'],
                    datasets: [{
                        data: [
                            <?php echo abs($income['total']); ?>,
                            <?php echo $expenses['total']; ?>,
                            <?php echo $net_income; ?>
                        ],
                        backgroundColor: ['#28a745', '#dc3545', '<?php echo $net_income >= 0 ? "#17a2b8" : "#ffc107"; ?>'],
                        borderWidth: 1,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        function exportReport() {
            alert('سيتم تطوير ميزة التصدير قريباً');
        }

        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.summary-card, .financial-table, .chart-container');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(15px)';

                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
