<?php
/**
 * صفحة التقارير المالية المتقدمة بأسلوب Odoo
 * Advanced Financial Reports Page - Odoo Style
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// تحميل نظام Odoo
require_once '../config/odoo_config.php';
require_once '../config/odoo_database.php';

$odoo_db = OdooDatabase::getInstance();

// تحميل النماذج
require_once '../models/BaseModel.php';
require_once '../models/AccountAccount.php';
require_once '../models/AccountMove.php';
require_once '../models/AccountMoveLine.php';
require_once '../models/AccountFinancialReport.php';
require_once '../models/ResCurrency.php';

$account_model = new AccountAccount($odoo_db);
$move_model = new AccountMove($odoo_db);
$move_line_model = new AccountMoveLine($odoo_db);
$report_model = new AccountFinancialReport($odoo_db);
$currency_model = new ResCurrency($odoo_db);

// معالجة المرشحات
$date_from = isset($_GET['date_from']) ? $_GET['date_from'] : date('Y-01-01');
$date_to = isset($_GET['date_to']) ? $_GET['date_to'] : date('Y-m-d');
$report_type = isset($_GET['report_type']) ? $_GET['report_type'] : 'balance_sheet';
$comparison = isset($_GET['comparison']) ? $_GET['comparison'] : 'none';
$currency = isset($_GET['currency']) ? $_GET['currency'] : 'SAR';

// جلب البيانات
$accounts = $account_model->get_demo_data();
$currencies = $currency_model->get_demo_data();

// إعداد التقارير
$reports = array(
    'balance_sheet' => array(
        'name' => 'الميزانية العمومية',
        'icon' => 'fas fa-balance-scale',
        'color' => 'primary'
    ),
    'income_statement' => array(
        'name' => 'قائمة الدخل',
        'icon' => 'fas fa-chart-line',
        'color' => 'success'
    ),
    'trial_balance' => array(
        'name' => 'ميزان المراجعة',
        'icon' => 'fas fa-calculator',
        'color' => 'info'
    ),
    'cash_flow' => array(
        'name' => 'قائمة التدفقات النقدية',
        'icon' => 'fas fa-money-bill-wave',
        'color' => 'warning'
    ),
    'general_ledger' => array(
        'name' => 'دفتر الأستاذ العام',
        'icon' => 'fas fa-book',
        'color' => 'secondary'
    ),
    'aged_receivables' => array(
        'name' => 'أعمار الذمم المدينة',
        'icon' => 'fas fa-clock',
        'color' => 'danger'
    )
);

// حساب البيانات للتقرير المحدد
$report_data = array();
switch ($report_type) {
    case 'balance_sheet':
        $report_data = $report_model->generate_balance_sheet($date_from, $date_to);
        break;
    case 'income_statement':
        $report_data = $report_model->generate_income_statement($date_from, $date_to);
        break;
    case 'trial_balance':
        $report_data = $report_model->generate_trial_balance($date_from, $date_to);
        break;
    default:
        $report_data = array();
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير المالية المتقدمة - نظام ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/odoo-advanced.css" rel="stylesheet">
    <style>
        .report-header {
            background: linear-gradient(135deg, #2C3E50, #34495E);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
        }
        
        .report-filters {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
            border-left: 4px solid #2C3E50;
        }
        
        .report-card {
            background: white;
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
            transition: all 0.3s ease;
            cursor: pointer;
            border-left: 4px solid;
        }
        
        .report-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.12);
        }
        
        .report-card.active {
            border-left-color: #2C3E50;
            background: #f8f9fa;
        }
        
        .report-content {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
            margin-bottom: 2rem;
        }
        
        .financial-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 2rem;
        }
        
        .financial-table th,
        .financial-table td {
            padding: 0.75rem;
            text-align: right;
            border-bottom: 1px solid #dee2e6;
        }
        
        .financial-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2C3E50;
            border-bottom: 2px solid #2C3E50;
        }
        
        .financial-table .level-0 {
            font-weight: bold;
            background: #f8f9fa;
            color: #2C3E50;
        }
        
        .financial-table .level-1 {
            padding-right: 2rem;
            font-weight: 600;
        }
        
        .financial-table .level-2 {
            padding-right: 3rem;
        }
        
        .financial-table .level-3 {
            padding-right: 4rem;
            color: #6c757d;
        }
        
        .amount-positive {
            color: #28a745;
        }
        
        .amount-negative {
            color: #dc3545;
        }
        
        .amount-zero {
            color: #6c757d;
        }
        
        .report-summary {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 1.5rem;
            border-radius: 12px;
            margin-bottom: 2rem;
        }
        
        .comparison-column {
            background: #e3f2fd;
        }
        
        .variance-positive {
            background: #e8f5e8;
            color: #28a745;
        }
        
        .variance-negative {
            background: #ffeaea;
            color: #dc3545;
        }
        
        .print-controls {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .export-dropdown {
            position: relative;
            display: inline-block;
        }
        
        .export-dropdown-content {
            display: none;
            position: absolute;
            background: white;
            min-width: 200px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.2);
            border-radius: 8px;
            z-index: 1000;
            top: 100%;
            right: 0;
            border: 1px solid #dee2e6;
        }
        
        .export-dropdown.active .export-dropdown-content {
            display: block;
        }
        
        .export-option {
            padding: 0.75rem 1rem;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .export-option:hover {
            background: #f8f9fa;
        }
        
        .export-option:last-child {
            border-bottom: none;
        }
        
        /* أنماط الطباعة المتقدمة */
        @media print {
            body {
                font-size: 11px;
                color: black;
                background: white;
                margin: 0;
                padding: 0;
            }
            
            .no-print {
                display: none !important;
            }
            
            .report-content {
                box-shadow: none;
                border: none;
                padding: 0;
                margin: 0;
            }
            
            .financial-table {
                font-size: 10px;
                page-break-inside: avoid;
            }
            
            .financial-table th,
            .financial-table td {
                border: 1px solid #000;
                padding: 0.4rem;
            }
            
            .financial-table th {
                background: #f0f0f0 !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            
            .print-header {
                text-align: center;
                margin-bottom: 1rem;
                border-bottom: 2px solid #000;
                padding-bottom: 0.5rem;
            }
            
            .print-footer {
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                text-align: center;
                font-size: 9px;
                border-top: 1px solid #000;
                padding-top: 0.25rem;
                background: white;
            }
            
            .page-break {
                page-break-before: always;
            }
            
            .report-summary {
                background: #f0f0f0 !important;
                color: black !important;
                border: 2px solid #000;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
        }
        
        /* تحسينات الاستجابة */
        @media (max-width: 768px) {
            .report-filters {
                padding: 1rem;
            }
            
            .financial-table {
                font-size: 11px;
            }
            
            .financial-table th,
            .financial-table td {
                padding: 0.5rem 0.25rem;
            }
            
            .level-1 { padding-right: 1rem; }
            .level-2 { padding-right: 1.5rem; }
            .level-3 { padding-right: 2rem; }
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-odoo no-print">
        <div class="container-fluid">
            <a class="navbar-brand text-white" href="../dashboard.php">
                <i class="fas fa-cube me-2"></i>
                نظام ERP - التقارير المالية المتقدمة
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="../dashboard.php">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-3">
        <!-- مسار التنقل -->
        <nav class="breadcrumb-odoo no-print">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="../dashboard.php">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="#">المحاسبة</a></li>
                <li class="breadcrumb-item active">التقارير المالية المتقدمة</li>
            </ol>
        </nav>

        <!-- العنوان الرئيسي -->
        <div class="report-header no-print">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h3><i class="fas fa-chart-bar me-2"></i>التقارير المالية المتقدمة</h3>
                    <p class="mb-0 small">تقارير مالية شاملة مع إمكانيات طباعة وتصدير متقدمة</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="d-flex gap-2">
                        <button class="btn btn-light btn-sm" onclick="refreshReport()">
                            <i class="fas fa-sync-alt me-2"></i>تحديث
                        </button>
                        <button class="btn btn-success btn-sm" onclick="scheduleReport()">
                            <i class="fas fa-clock me-2"></i>جدولة
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- قائمة التقارير -->
            <div class="col-md-3 no-print">
                <div class="report-filters">
                    <h6 class="mb-3"><i class="fas fa-filter me-2"></i>أنواع التقارير</h6>
                    <?php foreach ($reports as $key => $report): ?>
                        <div class="report-card <?php echo $report_type === $key ? 'active' : ''; ?>" 
                             onclick="selectReport('<?php echo $key; ?>')"
                             style="border-left-color: var(--bs-<?php echo $report['color']; ?>);">
                            <div class="d-flex align-items-center">
                                <i class="<?php echo $report['icon']; ?> text-<?php echo $report['color']; ?> me-2"></i>
                                <span class="small"><?php echo $report['name']; ?></span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- مرشحات التقرير -->
                <div class="report-filters">
                    <h6 class="mb-3"><i class="fas fa-sliders-h me-2"></i>مرشحات التقرير</h6>
                    <form method="GET" id="reportFilters">
                        <input type="hidden" name="report_type" value="<?php echo $report_type; ?>">
                        
                        <div class="mb-3">
                            <label class="form-label small">من تاريخ</label>
                            <input type="date" name="date_from" class="form-control form-control-sm" 
                                   value="<?php echo $date_from; ?>">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label small">إلى تاريخ</label>
                            <input type="date" name="date_to" class="form-control form-control-sm" 
                                   value="<?php echo $date_to; ?>">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label small">العملة</label>
                            <select name="currency" class="form-select form-select-sm">
                                <?php foreach ($currencies as $curr): ?>
                                    <option value="<?php echo $curr['name']; ?>" 
                                            <?php echo $currency === $curr['name'] ? 'selected' : ''; ?>>
                                        <?php echo $curr['name']; ?> - <?php echo $curr['symbol']; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label small">المقارنة</label>
                            <select name="comparison" class="form-select form-select-sm">
                                <option value="none" <?php echo $comparison === 'none' ? 'selected' : ''; ?>>بدون مقارنة</option>
                                <option value="previous_period" <?php echo $comparison === 'previous_period' ? 'selected' : ''; ?>>الفترة السابقة</option>
                                <option value="previous_year" <?php echo $comparison === 'previous_year' ? 'selected' : ''; ?>>السنة السابقة</option>
                                <option value="budget" <?php echo $comparison === 'budget' ? 'selected' : ''; ?>>الميزانية</option>
                            </select>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-sm w-100">
                            <i class="fas fa-search me-2"></i>تطبيق المرشحات
                        </button>
                    </form>
                </div>
            </div>

            <!-- محتوى التقرير -->
            <div class="col-md-9">
                <!-- أدوات التحكم في الطباعة -->
                <div class="print-controls no-print">
                    <div class="d-flex align-items-center gap-2">
                        <span class="small text-muted">أدوات التحكم:</span>
                        <button class="btn btn-outline-secondary btn-sm" onclick="toggleTableManager()">
                            <i class="fas fa-cogs me-1"></i>إعدادات الجدول
                        </button>
                    </div>
                    
                    <div class="d-flex align-items-center gap-2">
                        <div class="export-dropdown">
                            <button class="btn btn-success btn-sm" onclick="toggleExportDropdown()">
                                <i class="fas fa-download me-2"></i>تصدير
                                <i class="fas fa-chevron-down ms-1"></i>
                            </button>
                            <div class="export-dropdown-content">
                                <div class="export-option" onclick="exportToPDF()">
                                    <i class="fas fa-file-pdf text-danger"></i>
                                    <span>PDF</span>
                                </div>
                                <div class="export-option" onclick="exportToExcel()">
                                    <i class="fas fa-file-excel text-success"></i>
                                    <span>Excel</span>
                                </div>
                                <div class="export-option" onclick="exportToCSV()">
                                    <i class="fas fa-file-csv text-info"></i>
                                    <span>CSV</span>
                                </div>
                                <div class="export-option" onclick="exportToWord()">
                                    <i class="fas fa-file-word text-primary"></i>
                                    <span>Word</span>
                                </div>
                            </div>
                        </div>
                        
                        <button class="btn btn-info btn-sm" onclick="printReport()">
                            <i class="fas fa-print me-2"></i>طباعة
                        </button>
                        
                        <button class="btn btn-warning btn-sm" onclick="emailReport()">
                            <i class="fas fa-envelope me-2"></i>إرسال
                        </button>
                    </div>
                </div>

                <!-- محتوى التقرير -->
                <div class="report-content" id="reportContent">
                    <!-- عنوان التقرير للطباعة -->
                    <div class="print-header" style="display: none;">
                        <h2><?php echo $reports[$report_type]['name']; ?></h2>
                        <p>من <?php echo $date_from; ?> إلى <?php echo $date_to; ?></p>
                        <p>شركة النظام المتقدم</p>
                        <small>تاريخ الطباعة: <?php echo date('Y-m-d H:i:s'); ?></small>
                    </div>

                    <!-- ملخص التقرير -->
                    <?php if (!empty($report_data)): ?>
                        <div class="report-summary">
                            <div class="row">
                                <div class="col-md-3 text-center">
                                    <h5 class="mb-1"><?php echo count($report_data); ?></h5>
                                    <small>عدد البنود</small>
                                </div>
                                <div class="col-md-3 text-center">
                                    <h5 class="mb-1"><?php echo number_format(array_sum(array_column($report_data, 'balance')), 2); ?></h5>
                                    <small>إجمالي الأرصدة</small>
                                </div>
                                <div class="col-md-3 text-center">
                                    <h5 class="mb-1"><?php echo $currency; ?></h5>
                                    <small>العملة</small>
                                </div>
                                <div class="col-md-3 text-center">
                                    <h5 class="mb-1"><?php echo date('Y-m-d'); ?></h5>
                                    <small>تاريخ التقرير</small>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- جدول التقرير -->
                    <div class="odoo-table-container">
                        <table id="financial-report-table" class="financial-table odoo-table-auto" data-selectable="false">
                            <thead>
                                <tr>
                                    <th class="sortable">اسم الحساب</th>
                                    <th class="sortable">الكود</th>
                                    <th class="sortable">الرصيد الحالي</th>
                                    <?php if ($comparison !== 'none'): ?>
                                        <th class="comparison-column sortable">رصيد المقارنة</th>
                                        <th class="sortable">الفرق</th>
                                        <th class="sortable">النسبة %</th>
                                    <?php endif; ?>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($report_data)): ?>
                                    <?php foreach ($report_data as $item): ?>
                                        <tr class="level-<?php echo $item['level'] ?? 0; ?>">
                                            <td><?php echo str_repeat('&nbsp;&nbsp;', ($item['level'] ?? 0)) . $item['name']; ?></td>
                                            <td><?php echo $item['code'] ?? ''; ?></td>
                                            <td class="<?php echo $item['balance'] > 0 ? 'amount-positive' : ($item['balance'] < 0 ? 'amount-negative' : 'amount-zero'); ?>">
                                                <?php echo number_format($item['balance'], 2); ?>
                                            </td>
                                            <?php if ($comparison !== 'none'): ?>
                                                <td class="comparison-column">
                                                    <?php echo number_format($item['comparison_balance'] ?? 0, 2); ?>
                                                </td>
                                                <td class="<?php echo ($item['variance'] ?? 0) > 0 ? 'variance-positive' : 'variance-negative'; ?>">
                                                    <?php echo number_format($item['variance'] ?? 0, 2); ?>
                                                </td>
                                                <td>
                                                    <?php echo number_format($item['variance_percent'] ?? 0, 1); ?>%
                                                </td>
                                            <?php endif; ?>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="<?php echo $comparison !== 'none' ? '6' : '3'; ?>" class="text-center text-muted">
                                            لا توجد بيانات للعرض
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- تذييل الطباعة -->
                    <div class="print-footer" style="display: none;">
                        <p>تم إنشاء هذا التقرير بواسطة نظام ERP - جميع الحقوق محفوظة</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="../assets/js/odoo-table-manager.js"></script>
    <script src="../assets/js/odoo-buttons-manager.js"></script>
    <script src="../assets/js/odoo-export-manager.js"></script>
    <script>
        // إدارة التقارير المالية المتقدمة
        let tableManager;

        // تهيئة مديري الأزرار والتصدير
        document.addEventListener('DOMContentLoaded', function() {
            // تهيئة مدير الأزرار للجداول
            const reportTables = document.querySelectorAll('table[id*="report"]');
            reportTables.forEach(table => {
                if (table.id) {
                    odooButtons.addTableButtons(table.id, {
                        enableAdd: false,
                        enableEdit: false,
                        enableDelete: false,
                        enablePrint: true,
                        enableExport: true,
                        enableRefresh: true
                    });
                }
            });

            // تفعيل دوال التصدير المتقدمة
            setupReportsExportFunctions();
        });

        function setupReportsExportFunctions() {
            // دوال التصدير للتقارير المالية
            window.exportReportToExcel = function(reportType = 'current') {
                const tableId = getCurrentReportTableId();
                if (tableId) {
                    odooExport.exportToExcel(tableId, `تقرير_${reportType}.xlsx`)
                        .then(filename => showMessage('تم تصدير التقرير إلى Excel بنجاح', 'success'))
                        .catch(error => showMessage('خطأ في التصدير: ' + error.message, 'error'));
                }
            };

            window.exportReportToXLS = function(reportType = 'current') {
                const tableId = getCurrentReportTableId();
                if (tableId) {
                    odooExport.exportToXLS(tableId, `تقرير_${reportType}.xls`)
                        .then(filename => showMessage('تم تصدير التقرير إلى XLS بنجاح', 'success'))
                        .catch(error => showMessage('خطأ في التصدير: ' + error.message, 'error'));
                }
            };

            window.exportReportToPDF = function(reportType = 'current') {
                const tableId = getCurrentReportTableId();
                if (tableId) {
                    odooExport.exportToPDF(tableId, `تقرير_${reportType}.pdf`, {
                        title: getReportTitle(),
                        orientation: 'portrait'
                    })
                        .then(filename => showMessage('تم تصدير التقرير إلى PDF بنجاح', 'success'))
                        .catch(error => showMessage('خطأ في التصدير: ' + error.message, 'error'));
                }
            };

            window.exportReportToCSV = function(reportType = 'current') {
                const tableId = getCurrentReportTableId();
                if (tableId) {
                    odooExport.exportToCSV(tableId, `تقرير_${reportType}.csv`)
                        .then(filename => showMessage('تم تصدير التقرير إلى CSV بنجاح', 'success'))
                        .catch(error => showMessage('خطأ في التصدير: ' + error.message, 'error'));
                }
            };

            window.exportReportToWord = function(reportType = 'current') {
                const tableId = getCurrentReportTableId();
                if (tableId) {
                    odooExport.exportToWord(tableId, `تقرير_${reportType}.doc`)
                        .then(filename => showMessage('تم تصدير التقرير إلى Word بنجاح', 'success'))
                        .catch(error => showMessage('خطأ في التصدير: ' + error.message, 'error'));
                }
            };
        }

        function getCurrentReportTableId() {
            // البحث عن الجدول المرئي حالياً
            const visibleTables = document.querySelectorAll('table[id*="report"]:not([style*="display: none"])');
            return visibleTables.length > 0 ? visibleTables[0].id : null;
        }

        function getReportTitle() {
            const reportType = document.getElementById('reportType').value;
            const reportTitles = {
                'balance_sheet': 'الميزانية العمومية',
                'income_statement': 'قائمة الدخل',
                'trial_balance': 'ميزان المراجعة',
                'cash_flow': 'قائمة التدفقات النقدية',
                'partner_ledger': 'كشف حساب الشركاء',
                'general_ledger': 'دفتر الأستاذ العام'
            };
            return reportTitles[reportType] || 'التقرير المالي';
        }

        document.addEventListener('DOMContentLoaded', function() {
            // تهيئة مدير الجدول
            tableManager = new OdooTableManager('financial-report-table', {
                sortable: true,
                filterable: true,
                searchable: true,
                exportable: true,
                printable: true,
                columnControl: true,
                fontControl: true,
                selectable: false
            });

            // تطبيق تأثيرات بصرية
            animateElements();
        });

        function selectReport(reportType) {
            const form = document.getElementById('reportFilters');
            form.querySelector('input[name="report_type"]').value = reportType;
            form.submit();
        }

        function refreshReport() {
            showLoadingOverlay();
            setTimeout(() => {
                location.reload();
            }, 1000);
        }

        function toggleExportDropdown() {
            const dropdown = document.querySelector('.export-dropdown');
            dropdown.classList.toggle('active');

            // إغلاق عند النقر خارج القائمة
            document.addEventListener('click', function(e) {
                if (!dropdown.contains(e.target)) {
                    dropdown.classList.remove('active');
                }
            }, { once: true });
        }

        function exportToPDF() {
            showMessage('جاري تصدير التقرير إلى PDF...', 'info');

            // إعداد محتوى الطباعة
            const printContent = generatePrintContent();

            // هنا يمكن إضافة مكتبة PDF مثل jsPDF
            setTimeout(() => {
                showMessage('تم تصدير التقرير بنجاح', 'success');
                downloadFile('financial-report.pdf', 'application/pdf');
            }, 2000);
        }

        function exportToExcel() {
            exportReportToExcel();
        }

        function exportToCSV() {
            showMessage('جاري تصدير التقرير إلى CSV...', 'info');

            const tableData = tableManager.getTableData();
            let csvContent = '';

            // إضافة العناوين
            csvContent += tableData.headers.join(',') + '\\n';

            // إضافة البيانات
            tableData.rows.forEach(row => {
                csvContent += row.join(',') + '\\n';
            });

            // تحميل الملف
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = 'financial-report.csv';
            link.click();

            showMessage('تم تصدير التقرير بنجاح', 'success');
        }

        function exportToWord() {
            showMessage('جاري تصدير التقرير إلى Word...', 'info');

            // هنا يمكن إضافة مكتبة Word
            setTimeout(() => {
                showMessage('تم تصدير التقرير بنجاح', 'success');
                downloadFile('financial-report.docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document');
            }, 2000);
        }

        function printReport() {
            // إظهار عناصر الطباعة
            document.querySelectorAll('.print-header, .print-footer').forEach(el => {
                el.style.display = 'block';
            });

            // طباعة النافذة
            window.print();

            // إخفاء عناصر الطباعة بعد الطباعة
            setTimeout(() => {
                document.querySelectorAll('.print-header, .print-footer').forEach(el => {
                    el.style.display = 'none';
                });
            }, 1000);
        }

        function emailReport() {
            showMessage('جاري إعداد إرسال التقرير بالبريد الإلكتروني...', 'info');

            // هنا يمكن فتح نافذة لإدخال بيانات الإرسال
            const email = prompt('أدخل البريد الإلكتروني:');
            if (email) {
                setTimeout(() => {
                    showMessage('تم إرسال التقرير بنجاح إلى ' + email, 'success');
                }, 2000);
            }
        }

        function scheduleReport() {
            showMessage('جاري فتح نافذة جدولة التقارير...', 'info');

            // هنا يمكن فتح نافذة لجدولة التقارير
            setTimeout(() => {
                showMessage('تم حفظ جدولة التقرير', 'success');
            }, 1500);
        }

        function toggleTableManager() {
            if (tableManager) {
                tableManager.showColumnControls();
            }
        }

        function generatePrintContent() {
            const reportContent = document.getElementById('reportContent').cloneNode(true);

            // إزالة العناصر غير المطلوبة للطباعة
            reportContent.querySelectorAll('.no-print').forEach(el => el.remove());

            // إظهار عناصر الطباعة
            reportContent.querySelectorAll('.print-header, .print-footer').forEach(el => {
                el.style.display = 'block';
            });

            return reportContent.outerHTML;
        }

        function downloadFile(filename, mimeType) {
            // محاكاة تحميل الملف
            const link = document.createElement('a');
            link.href = '#';
            link.download = filename;
            link.click();
        }

        function showLoadingOverlay() {
            const overlay = document.createElement('div');
            overlay.className = 'loading-overlay';
            overlay.innerHTML = '<div class="loading-spinner"></div>';
            document.body.appendChild(overlay);

            setTimeout(() => {
                overlay.remove();
            }, 3000);
        }

        function showMessage(message, type = 'info') {
            const alert = document.createElement('div');
            alert.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alert.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(alert);

            // إزالة الرسالة تلقائياً
            setTimeout(() => {
                alert.remove();
            }, 4000);
        }

        function animateElements() {
            const elements = document.querySelectorAll('.report-card, .report-content, .report-summary');
            elements.forEach((element, index) => {
                element.style.opacity = '0';
                element.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    element.style.transition = 'all 0.6s ease';
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, index * 100);
            });
        }

        // تحديث تلقائي للتقرير كل 5 دقائق
        setInterval(() => {
            if (document.visibilityState === 'visible') {
                console.log('تحديث تلقائي للبيانات...');
                // يمكن إضافة تحديث AJAX هنا
            }
        }, 300000); // 5 دقائق

        // حفظ إعدادات المرشحات في localStorage
        document.getElementById('reportFilters').addEventListener('submit', function() {
            const formData = new FormData(this);
            const settings = {};
            for (let [key, value] of formData.entries()) {
                settings[key] = value;
            }
            localStorage.setItem('financial-report-settings', JSON.stringify(settings));
        });

        // تحميل إعدادات المرشحات المحفوظة
        window.addEventListener('load', function() {
            const saved = localStorage.getItem('financial-report-settings');
            if (saved) {
                try {
                    const settings = JSON.parse(saved);
                    Object.keys(settings).forEach(key => {
                        const input = document.querySelector(`[name="${key}"]`);
                        if (input) {
                            input.value = settings[key];
                        }
                    });
                } catch (e) {
                    console.error('Error loading saved settings:', e);
                }
            }
        });
    </script>
