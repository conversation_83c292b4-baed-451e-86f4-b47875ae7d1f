<?php
/**
 * صفحة الفهرس لجميع واجهات Odoo
 * Index Page for All Odoo Interfaces
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// قائمة الواجهات المتاحة
$interfaces = array(
    'dashboard' => array(
        'name' => 'لوحة التحكم الرئيسية',
        'description' => 'نظرة شاملة على النظام مع إحصائيات تفاعلية ورسوم بيانية',
        'url' => 'dashboard_odoo.php',
        'icon' => 'fas fa-tachometer-alt',
        'color' => '#007bff',
        'status' => 'مكتمل',
        'features' => array(
            'إحصائيات مالية متحركة',
            'رسوم بيانية تفاعلية',
            'نشاطات أخيرة',
            'إجراءات سريعة',
            'وحدات النظام'
        )
    ),
    'journal_entries' => array(
        'name' => 'القيود اليومية',
        'description' => 'إدارة القيود المحاسبية مع بحث وفلترة متقدمة',
        'url' => 'journal_entries_odoo.php',
        'icon' => 'fas fa-calculator',
        'color' => '#875A7B',
        'status' => 'مكتمل',
        'features' => array(
            'إنشاء وتعديل القيود',
            'بحث وفلترة متقدمة',
            'عرض مزدوج (قائمة/بطاقات)',
            'إجراءات مجمعة',
            'ترحيل القيود'
        )
    ),
    'partners' => array(
        'name' => 'إدارة الشركاء',
        'description' => 'إدارة العملاء والموردين مع معلومات الاتصال',
        'url' => 'partners_odoo.php',
        'icon' => 'fas fa-users',
        'color' => '#00A09D',
        'status' => 'مكتمل',
        'features' => array(
            'إدارة العملاء والموردين',
            'معلومات اتصال شاملة',
            'تصنيف الشركاء',
            'بحث متقدم',
            'حالة النشاط'
        )
    ),
    'products' => array(
        'name' => 'إدارة المنتجات',
        'description' => 'كتالوج المنتجات والخدمات مع إدارة المخزون',
        'url' => 'products_odoo.php',
        'icon' => 'fas fa-box',
        'color' => '#F0AD4E',
        'status' => 'مكتمل',
        'features' => array(
            'كتالوج منتجات متطور',
            'أنواع مختلفة (مخزني/خدمة/مستهلك)',
            'إدارة الأسعار والتكلفة',
            'مؤشرات المخزون',
            'فلترة حسب الفئات'
        )
    ),
    'invoices' => array(
        'name' => 'إدارة الفواتير',
        'description' => 'فواتير البيع والشراء مع متابعة المدفوعات',
        'url' => 'invoices_odoo.php',
        'icon' => 'fas fa-file-invoice',
        'color' => '#D73502',
        'status' => 'قيد التطوير',
        'features' => array(
            'فواتير البيع والشراء',
            'متابعة المدفوعات',
            'تقارير الفواتير',
            'ربط مع الشركاء',
            'حالات الفواتير'
        )
    ),
    'reports' => array(
        'name' => 'التقارير المالية',
        'description' => 'تقارير شاملة للأداء المالي والإحصائيات',
        'url' => 'reports_odoo.php',
        'icon' => 'fas fa-chart-line',
        'color' => '#00A7E1',
        'status' => 'قيد التطوير',
        'features' => array(
            'تقارير مالية شاملة',
            'رسوم بيانية تفاعلية',
            'تصدير متعدد الصيغ',
            'فلترة حسب التاريخ',
            'مقارنات دورية'
        )
    ),
    'settings' => array(
        'name' => 'إعدادات النظام',
        'description' => 'إعدادات النظام والمستخدمين والصلاحيات',
        'url' => 'settings_odoo.php',
        'icon' => 'fas fa-cog',
        'color' => '#6C757D',
        'status' => 'قيد التطوير',
        'features' => array(
            'إدارة المستخدمين',
            'صلاحيات النظام',
            'إعدادات الشركة',
            'تخصيص الواجهة',
            'نسخ احتياطية'
        )
    )
);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فهرس واجهات Odoo - نظام ERP</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Odoo Style -->
    <link href="../assets/css/odoo-style.css" rel="stylesheet">
    <link href="../assets/css/odoo-advanced.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            min-height: 100vh;
        }
        
        .hero-section {
            background: linear-gradient(135deg, #714B67 0%, #875A7B 100%);
            color: white;
            padding: 4rem 0;
            margin-bottom: 3rem;
            position: relative;
            overflow: hidden;
        }
        
        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }
        
        .hero-content {
            position: relative;
            z-index: 2;
        }
        
        .interface-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            padding: 0 1rem;
        }
        
        .interface-card {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            border-left: 4px solid transparent;
        }
        
        .interface-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, transparent 0%, rgba(255,255,255,0.1) 50%, transparent 100%);
            transform: translateX(-100%);
            transition: transform 0.6s ease;
        }
        
        .interface-card:hover::before {
            transform: translateX(100%);
        }
        
        .interface-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
        }
        
        .interface-header {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        
        .interface-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-left: 1rem;
        }
        
        .interface-title {
            flex: 1;
        }
        
        .interface-name {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #2c3e50;
        }
        
        .interface-status {
            font-size: 0.8rem;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-weight: 500;
        }
        
        .status-complete {
            background: #d4edda;
            color: #155724;
        }
        
        .status-development {
            background: #fff3cd;
            color: #856404;
        }
        
        .interface-description {
            color: #6c757d;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }
        
        .interface-features {
            margin-bottom: 2rem;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
            color: #495057;
        }
        
        .feature-item i {
            color: #28a745;
            margin-left: 0.5rem;
            width: 16px;
        }
        
        .interface-actions {
            display: flex;
            gap: 1rem;
        }
        
        .btn-interface {
            flex: 1;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 500;
            text-decoration: none;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .btn-primary-interface {
            background: linear-gradient(45deg, #714B67, #875A7B);
            color: white;
            border: none;
        }
        
        .btn-primary-interface:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(113, 75, 103, 0.3);
            color: white;
        }
        
        .btn-secondary-interface {
            background: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;
        }
        
        .btn-secondary-interface:hover {
            background: #e9ecef;
            color: #495057;
        }
        
        .stats-section {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 3rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
        }
        
        .stat-item {
            text-align: center;
            padding: 1.5rem;
            border-radius: 12px;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }
        
        .stat-item:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #714B67;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        @media (max-width: 768px) {
            .interface-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }
            
            .hero-section {
                padding: 2rem 0;
            }
            
            .interface-card {
                padding: 1.5rem;
            }
            
            .interface-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- القسم الرئيسي -->
    <div class="hero-section">
        <div class="container">
            <div class="hero-content text-center">
                <h1 class="display-4 mb-3">
                    <i class="fas fa-rocket me-3"></i>
                    واجهات Odoo الاحترافية
                </h1>
                <p class="lead mb-4">
                    نظام ERP متكامل بتصميم Odoo الأصلي مع واجهات تفاعلية متطورة
                </p>
                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <p class="mb-0">
                            تم تطوير جميع الواجهات بأسلوب Odoo الاحترافي مع تفاعل متقدم وتصميم عصري
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- إحصائيات سريعة -->
        <div class="stats-section">
            <h3 class="text-center mb-4">
                <i class="fas fa-chart-bar me-2 text-primary"></i>
                إحصائيات المشروع
            </h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number"><?php echo count($interfaces); ?></div>
                    <div class="stat-label">إجمالي الواجهات</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?php echo count(array_filter($interfaces, function($i) { return $i['status'] === 'مكتمل'; })); ?></div>
                    <div class="stat-label">واجهات مكتملة</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?php echo count(array_filter($interfaces, function($i) { return $i['status'] === 'قيد التطوير'; })); ?></div>
                    <div class="stat-label">قيد التطوير</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">100%</div>
                    <div class="stat-label">تصميم Odoo</div>
                </div>
            </div>
        </div>

        <!-- شبكة الواجهات -->
        <div class="interface-grid">
            <?php foreach ($interfaces as $key => $interface): ?>
                <div class="interface-card" style="border-left-color: <?php echo $interface['color']; ?>">
                    <div class="interface-header">
                        <div class="interface-icon" style="background: <?php echo $interface['color']; ?>">
                            <i class="<?php echo $interface['icon']; ?>"></i>
                        </div>
                        <div class="interface-title">
                            <div class="interface-name"><?php echo $interface['name']; ?></div>
                            <span class="interface-status <?php echo $interface['status'] === 'مكتمل' ? 'status-complete' : 'status-development'; ?>">
                                <?php echo $interface['status']; ?>
                            </span>
                        </div>
                    </div>
                    
                    <div class="interface-description">
                        <?php echo $interface['description']; ?>
                    </div>
                    
                    <div class="interface-features">
                        <h6 class="mb-2">الميزات الرئيسية:</h6>
                        <?php foreach ($interface['features'] as $feature): ?>
                            <div class="feature-item">
                                <i class="fas fa-check"></i>
                                <?php echo $feature; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <div class="interface-actions">
                        <?php if ($interface['status'] === 'مكتمل'): ?>
                            <a href="<?php echo $interface['url']; ?>" class="btn-interface btn-primary-interface">
                                <i class="fas fa-external-link-alt me-2"></i>
                                فتح الواجهة
                            </a>
                            <a href="#" class="btn-interface btn-secondary-interface" onclick="showDemo('<?php echo $key; ?>')">
                                <i class="fas fa-play me-2"></i>
                                عرض توضيحي
                            </a>
                        <?php else: ?>
                            <button class="btn-interface btn-secondary-interface" disabled>
                                <i class="fas fa-clock me-2"></i>
                                قيد التطوير
                            </button>
                            <a href="#" class="btn-interface btn-secondary-interface" onclick="showRoadmap('<?php echo $key; ?>')">
                                <i class="fas fa-road me-2"></i>
                                خارطة الطريق
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            // تحريك البطاقات عند التحميل
            const cards = document.querySelectorAll('.interface-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
            
            // تحريك الإحصائيات
            animateStats();
        });
        
        function animateStats() {
            const statNumbers = document.querySelectorAll('.stat-number');
            statNumbers.forEach(stat => {
                const finalValue = stat.textContent;
                if (!isNaN(finalValue)) {
                    let currentValue = 0;
                    const increment = finalValue / 50;
                    
                    const timer = setInterval(() => {
                        currentValue += increment;
                        if (currentValue >= finalValue) {
                            currentValue = finalValue;
                            clearInterval(timer);
                        }
                        stat.textContent = Math.floor(currentValue);
                    }, 30);
                }
            });
        }
        
        function showDemo(interfaceKey) {
            alert('عرض توضيحي للواجهة: ' + interfaceKey);
        }
        
        function showRoadmap(interfaceKey) {
            alert('خارطة طريق التطوير للواجهة: ' + interfaceKey);
        }
        
        // تأثيرات hover إضافية
        document.querySelectorAll('.interface-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.borderLeftWidth = '6px';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.borderLeftWidth = '4px';
            });
        });
    </script>
</body>
</html>
