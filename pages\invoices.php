<?php
session_start();
require_once '../config/database_simple.php';

// بيانات تجريبية للفواتير
$invoices = array(
    array(
        'id' => 1,
        'number' => 'INV-2024-001',
        'customer' => 'شركة الأمل للتجارة',
        'date' => '2024-01-15',
        'due_date' => '2024-02-14',
        'subtotal' => 5000,
        'tax' => 750,
        'total' => 5750,
        'paid' => 5750,
        'status' => 'paid',
        'items_count' => 3
    ),
    array(
        'id' => 2,
        'number' => 'INV-2024-002',
        'customer' => 'مؤسسة النور للمقاولات',
        'date' => '2024-01-14',
        'due_date' => '2024-02-13',
        'subtotal' => 8500,
        'tax' => 1275,
        'total' => 9775,
        'paid' => 5000,
        'status' => 'partial',
        'items_count' => 5
    ),
    array(
        'id' => 3,
        'number' => 'INV-2024-003',
        'customer' => 'شركة المستقبل للتكنولوجيا',
        'date' => '2024-01-13',
        'due_date' => '2024-02-12',
        'subtotal' => 12000,
        'tax' => 1800,
        'total' => 13800,
        'paid' => 0,
        'status' => 'pending',
        'items_count' => 7
    ),
    array(
        'id' => 4,
        'number' => 'INV-2024-004',
        'customer' => 'مجموعة الخليج التجارية',
        'date' => '2024-01-12',
        'due_date' => '2024-02-11',
        'subtotal' => 3200,
        'tax' => 480,
        'total' => 3680,
        'paid' => 0,
        'status' => 'draft',
        'items_count' => 2
    ),
    array(
        'id' => 5,
        'number' => 'INV-2024-005',
        'customer' => 'شركة الأمل للتجارة',
        'date' => '2024-01-10',
        'due_date' => '2024-01-25',
        'subtotal' => 7500,
        'tax' => 1125,
        'total' => 8625,
        'paid' => 0,
        'status' => 'overdue',
        'items_count' => 4
    )
);

// دالة لتحديد لون الحالة
function getStatusColor($status) {
    switch($status) {
        case 'paid': return 'success';
        case 'partial': return 'warning';
        case 'pending': return 'info';
        case 'overdue': return 'danger';
        case 'draft': return 'secondary';
        default: return 'secondary';
    }
}

// دالة لتحديد نص الحالة
function getStatusText($status) {
    switch($status) {
        case 'paid': return 'مدفوعة';
        case 'partial': return 'مدفوعة جزئياً';
        case 'pending': return 'في الانتظار';
        case 'overdue': return 'متأخرة';
        case 'draft': return 'مسودة';
        default: return 'غير محدد';
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الفواتير - نظام ERP المحاسبي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
        }
        
        .invoice-card {
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .invoice-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }
        
        .invoice-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 10px 10px 0 0;
        }
        
        .invoice-number {
            font-size: 1.2rem;
            font-weight: bold;
        }
        
        .invoice-amount {
            font-size: 1.5rem;
            font-weight: bold;
        }
        
        .progress-payment {
            height: 8px;
            border-radius: 10px;
        }
        
        .quick-stats {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .stat-item {
            text-align: center;
            padding: 15px;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .filter-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="../demo.php">
                <i class="fas fa-chart-line me-2"></i>
                نظام ERP المحاسبي
            </a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="../demo.php">لوحة التحكم</a>
                <a class="nav-link" href="companies.php">الشركات</a>
                <a class="nav-link" href="customers.php">العملاء</a>
                <a class="nav-link active" href="invoices.php">الفواتير</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- رأس الصفحة -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2><i class="fas fa-file-invoice me-2"></i>إدارة الفواتير</h2>
                <p class="text-muted">إنشاء ومتابعة فواتير المبيعات</p>
            </div>
            <div>
                <button class="btn btn-success me-2" onclick="exportInvoices()">
                    <i class="fas fa-download me-2"></i>
                    تصدير
                </button>
                <button class="btn btn-primary" onclick="createInvoice()">
                    <i class="fas fa-plus me-2"></i>
                    فاتورة جديدة
                </button>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="quick-stats">
            <div class="row">
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number text-primary"><?php echo count($invoices); ?></div>
                        <div class="stat-label">إجمالي الفواتير</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number text-success">
                            <?php echo formatCurrency(array_sum(array_column(array_filter($invoices, function($i) { return $i['status'] == 'paid'; }), 'total'))); ?>
                        </div>
                        <div class="stat-label">المبيعات المحصلة</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number text-warning">
                            <?php echo formatCurrency(array_sum(array_column(array_filter($invoices, function($i) { return $i['status'] == 'pending' || $i['status'] == 'partial'; }), 'total')) - array_sum(array_column(array_filter($invoices, function($i) { return $i['status'] == 'partial'; }), 'paid'))); ?>
                        </div>
                        <div class="stat-label">المبالغ المستحقة</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number text-danger">
                            <?php echo count(array_filter($invoices, function($i) { return $i['status'] == 'overdue'; })); ?>
                        </div>
                        <div class="stat-label">فواتير متأخرة</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم الفلترة -->
        <div class="filter-section">
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">البحث</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                        <input type="text" class="form-control" placeholder="رقم الفاتورة أو العميل..." id="searchInput">
                    </div>
                </div>
                <div class="col-md-2">
                    <label class="form-label">الحالة</label>
                    <select class="form-select" id="statusFilter">
                        <option value="">جميع الحالات</option>
                        <option value="paid">مدفوعة</option>
                        <option value="partial">مدفوعة جزئياً</option>
                        <option value="pending">في الانتظار</option>
                        <option value="overdue">متأخرة</option>
                        <option value="draft">مسودة</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="dateFrom">
                </div>
                <div class="col-md-2">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="dateTo">
                </div>
                <div class="col-md-2">
                    <label class="form-label">العميل</label>
                    <select class="form-select" id="customerFilter">
                        <option value="">جميع العملاء</option>
                        <option value="شركة الأمل للتجارة">شركة الأمل للتجارة</option>
                        <option value="مؤسسة النور للمقاولات">مؤسسة النور للمقاولات</option>
                        <option value="شركة المستقبل للتكنولوجيا">شركة المستقبل للتكنولوجيا</option>
                    </select>
                </div>
                <div class="col-md-1">
                    <label class="form-label">&nbsp;</label>
                    <button class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- قائمة الفواتير -->
        <div class="row" id="invoicesContainer">
            <?php foreach ($invoices as $invoice): ?>
            <div class="col-lg-6 col-xl-4">
                <div class="card invoice-card">
                    <div class="invoice-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="invoice-number"><?php echo $invoice['number']; ?></div>
                                <small><?php echo $invoice['customer']; ?></small>
                            </div>
                            <div class="text-end">
                                <div class="invoice-amount"><?php echo formatCurrency($invoice['total']); ?></div>
                                <small><?php echo $invoice['items_count']; ?> عنصر</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card-body">
                        <!-- معلومات التواريخ -->
                        <div class="row mb-3">
                            <div class="col-6">
                                <small class="text-muted">تاريخ الإصدار</small>
                                <div><?php echo $invoice['date']; ?></div>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">تاريخ الاستحقاق</small>
                                <div><?php echo $invoice['due_date']; ?></div>
                            </div>
                        </div>
                        
                        <!-- تفاصيل المبالغ -->
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>المبلغ الأساسي:</span>
                                <span><?php echo formatCurrency($invoice['subtotal']); ?></span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>الضريبة:</span>
                                <span><?php echo formatCurrency($invoice['tax']); ?></span>
                            </div>
                            <div class="d-flex justify-content-between fw-bold">
                                <span>الإجمالي:</span>
                                <span><?php echo formatCurrency($invoice['total']); ?></span>
                            </div>
                            <div class="d-flex justify-content-between text-success">
                                <span>المدفوع:</span>
                                <span><?php echo formatCurrency($invoice['paid']); ?></span>
                            </div>
                            <div class="d-flex justify-content-between text-danger">
                                <span>المتبقي:</span>
                                <span><?php echo formatCurrency($invoice['total'] - $invoice['paid']); ?></span>
                            </div>
                        </div>
                        
                        <!-- شريط التقدم -->
                        <?php if ($invoice['status'] != 'draft'): ?>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between mb-1">
                                <small>نسبة السداد</small>
                                <small><?php echo round(($invoice['paid'] / $invoice['total']) * 100); ?>%</small>
                            </div>
                            <div class="progress progress-payment">
                                <div class="progress-bar bg-<?php echo getStatusColor($invoice['status']); ?>" 
                                     style="width: <?php echo ($invoice['paid'] / $invoice['total']) * 100; ?>%"></div>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <!-- الحالة -->
                        <div class="mb-3">
                            <span class="badge bg-<?php echo getStatusColor($invoice['status']); ?> w-100 py-2">
                                <?php echo getStatusText($invoice['status']); ?>
                            </span>
                        </div>
                        
                        <!-- الإجراءات -->
                        <div class="d-grid gap-2">
                            <div class="btn-group" role="group">
                                <button class="btn btn-outline-primary btn-sm" onclick="viewInvoice(<?php echo $invoice['id']; ?>)">
                                    <i class="fas fa-eye me-1"></i>
                                    عرض
                                </button>
                                <button class="btn btn-outline-success btn-sm" onclick="printInvoice(<?php echo $invoice['id']; ?>)">
                                    <i class="fas fa-print me-1"></i>
                                    طباعة
                                </button>
                                <?php if ($invoice['status'] == 'draft'): ?>
                                <button class="btn btn-outline-warning btn-sm" onclick="editInvoice(<?php echo $invoice['id']; ?>)">
                                    <i class="fas fa-edit me-1"></i>
                                    تعديل
                                </button>
                                <?php endif; ?>
                            </div>
                            
                            <?php if ($invoice['status'] != 'paid' && $invoice['status'] != 'draft'): ?>
                            <button class="btn btn-success btn-sm" onclick="recordPayment(<?php echo $invoice['id']; ?>)">
                                <i class="fas fa-money-bill-wave me-1"></i>
                                تسجيل دفعة
                            </button>
                            <?php endif; ?>
                            
                            <?php if ($invoice['status'] == 'draft'): ?>
                            <button class="btn btn-primary btn-sm" onclick="confirmInvoice(<?php echo $invoice['id']; ?>)">
                                <i class="fas fa-check me-1"></i>
                                تأكيد الفاتورة
                            </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <!-- ترقيم الصفحات -->
        <nav class="mt-4">
            <ul class="pagination justify-content-center">
                <li class="page-item disabled">
                    <a class="page-link" href="#" tabindex="-1">السابق</a>
                </li>
                <li class="page-item active"><a class="page-link" href="#">1</a></li>
                <li class="page-item"><a class="page-link" href="#">2</a></li>
                <li class="page-item"><a class="page-link" href="#">3</a></li>
                <li class="page-item">
                    <a class="page-link" href="#">التالي</a>
                </li>
            </ul>
        </nav>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function createInvoice() {
            alert('إنشاء فاتورة جديدة');
        }
        
        function viewInvoice(id) {
            alert('عرض الفاتورة رقم: ' + id);
        }
        
        function editInvoice(id) {
            alert('تعديل الفاتورة رقم: ' + id);
        }
        
        function printInvoice(id) {
            alert('طباعة الفاتورة رقم: ' + id);
        }
        
        function recordPayment(id) {
            const amount = prompt('أدخل مبلغ الدفعة:');
            if (amount && !isNaN(amount) && parseFloat(amount) > 0) {
                alert('تم تسجيل دفعة بمبلغ ' + amount + ' للفاتورة رقم: ' + id);
            }
        }
        
        function confirmInvoice(id) {
            if (confirm('هل أنت متأكد من تأكيد هذه الفاتورة؟')) {
                alert('تم تأكيد الفاتورة رقم: ' + id);
            }
        }
        
        function exportInvoices() {
            alert('تصدير الفواتير');
        }
        
        function clearFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('dateFrom').value = '';
            document.getElementById('dateTo').value = '';
            document.getElementById('customerFilter').value = '';
            filterInvoices();
        }
        
        function filterInvoices() {
            // منطق الفلترة
            console.log('تطبيق الفلاتر...');
        }
        
        // إضافة مستمعي الأحداث
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('searchInput').addEventListener('input', filterInvoices);
            document.getElementById('statusFilter').addEventListener('change', filterInvoices);
            document.getElementById('dateFrom').addEventListener('change', filterInvoices);
            document.getElementById('dateTo').addEventListener('change', filterInvoices);
            document.getElementById('customerFilter').addEventListener('change', filterInvoices);
        });
    </script>
</body>
</html>
