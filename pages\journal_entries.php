<?php
/**
 * صفحة القيود اليومية بأسلوب Odoo
 * Journal Entries Page - Odoo Style
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// تحميل نظام Odoo
require_once '../config/odoo_config.php';
require_once '../config/odoo_database.php';

$odoo_db = OdooDatabase::getInstance();

// تحميل النماذج
require_once '../models/BaseModel.php';
require_once '../models/AccountMove.php';
require_once '../models/AccountMoveLine.php';
require_once '../models/AccountJournal.php';
require_once '../models/AccountAccount.php';
require_once '../models/ResCurrency.php';

$move_model = new AccountMove($odoo_db);
$move_line_model = new AccountMoveLine($odoo_db);
$journal_model = new AccountJournal($odoo_db);
$account_model = new AccountAccount($odoo_db);
$currency_model = new ResCurrency($odoo_db);

$message = '';
$message_type = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = isset($_POST['action']) ? $_POST['action'] : '';
    
    try {
        switch ($action) {
            case 'create_move':
                $move_data = array(
                    'journal_id' => $_POST['journal_id'],
                    'date' => $_POST['date'],
                    'ref' => $_POST['ref'],
                    'narration' => $_POST['narration']
                );
                
                $move_id = $move_model->create_move($move_data);
                
                // إضافة بنود القيد
                if (isset($_POST['lines']) && is_array($_POST['lines'])) {
                    foreach ($_POST['lines'] as $line) {
                        if (!empty($line['account_id']) && (!empty($line['debit']) || !empty($line['credit']))) {
                            $line_data = array(
                                'account_id' => $line['account_id'],
                                'name' => $line['name'],
                                'debit' => floatval(isset($line['debit']) ? $line['debit'] : 0),
                                'credit' => floatval(isset($line['credit']) ? $line['credit'] : 0),
                                'partner_id' => !empty($line['partner_id']) ? $line['partner_id'] : null
                            );
                            
                            $move_model->add_move_line($move_id, $line_data);
                        }
                    }
                }
                
                // حساب إجمالي القيد
                $move_model->compute_amount_total($move_id);
                
                $message = "تم إنشاء القيد بنجاح";
                $message_type = 'success';
                break;
                
            case 'post_move':
                $move_id = $_POST['move_id'];
                $move_model->post_move($move_id);
                $message = "تم اعتماد القيد بنجاح";
                $message_type = 'success';
                break;
                
            case 'cancel_move':
                $move_id = $_POST['move_id'];
                $move_model->cancel_move($move_id);
                $message = "تم إلغاء القيد بنجاح";
                $message_type = 'success';
                break;
                
            default:
                throw new Exception('إجراء غير صحيح');
        }
    } catch (Exception $e) {
        $message = 'خطأ: ' . $e->getMessage();
        $message_type = 'error';
    }
}

// جلب البيانات
$moves = $move_model->get_demo_data();
$journals = $journal_model->get_demo_data();
$accounts = $account_model->get_demo_data();

// فلترة حسب اليومية
$filter_journal = isset($_GET['journal']) ? $_GET['journal'] : 'all';
if ($filter_journal !== 'all') {
    $filtered_moves = array();
    foreach ($moves as $move) {
        if ($move['journal_id'] == $filter_journal) {
            $filtered_moves[] = $move;
        }
    }
    $moves = $filtered_moves;
}

// فلترة حسب الحالة
$filter_state = isset($_GET['state']) ? $_GET['state'] : 'all';
if ($filter_state !== 'all') {
    $filtered_moves = array();
    foreach ($moves as $move) {
        if ($move['state'] === $filter_state) {
            $filtered_moves[] = $move;
        }
    }
    $moves = $filtered_moves;
}

// طريقة العرض المحددة
$view_mode = isset($_GET['view']) ? $_GET['view'] : 'list';

// حالات القيود
$move_states = array(
    'draft' => array('name' => 'مسودة', 'color' => 'secondary', 'icon' => 'fas fa-edit'),
    'posted' => array('name' => 'معتمد', 'color' => 'success', 'icon' => 'fas fa-check'),
    'cancelled' => array('name' => 'ملغي', 'color' => 'danger', 'icon' => 'fas fa-times')
);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>القيود اليومية - نظام ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/odoo-advanced.css" rel="stylesheet">
    <style>
        :root {
            --odoo-primary: #714B67;
            --odoo-secondary: #8B5A8C;
        }
        
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 0.85rem;
        }
        
        .navbar-odoo {
            background: linear-gradient(45deg, var(--odoo-primary), var(--odoo-secondary));
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .page-header {
            background: linear-gradient(45deg, #3498DB, #2980B9);
            color: white;
            padding: 1.5rem;
            border-radius: 15px;
            margin-bottom: 1.5rem;
        }
        
        .view-controls {
            background: white;
            border-radius: 10px;
            padding: 0.8rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        
        .view-btn, .filter-btn {
            border: 1px solid #dee2e6;
            background: white;
            color: #6c757d;
            padding: 0.4rem 0.8rem;
            border-radius: 5px;
            transition: all 0.3s ease;
            text-decoration: none;
            font-size: 0.8rem;
        }
        
        .view-btn.active, .filter-btn.active {
            background: #3498DB;
            color: white;
            border-color: #3498DB;
        }
        
        .view-btn:hover, .filter-btn:hover {
            background: #2980B9;
            color: white;
            border-color: #2980B9;
            text-decoration: none;
        }
        
        .move-card {
            background: white;
            border-radius: 12px;
            padding: 1.2rem;
            margin-bottom: 0.8rem;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
            transition: all 0.3s ease;
            border-left: 3px solid;
        }
        
        .move-card.draft { border-left-color: #6c757d; }
        .move-card.posted { border-left-color: #28a745; }
        .move-card.cancelled { border-left-color: #dc3545; }
        
        .move-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.12);
        }
        
        .table-odoo {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
        }
        
        .table-odoo th {
            background: linear-gradient(45deg, #3498DB, #2980B9);
            color: white;
            border: none;
            padding: 0.8rem;
            font-size: 0.8rem;
        }
        
        .table-odoo td {
            padding: 0.8rem;
            border-color: #f8f9fa;
            vertical-align: middle;
            font-size: 0.8rem;
        }
        
        .breadcrumb-odoo {
            background: white;
            border-radius: 8px;
            padding: 0.8rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            font-size: 0.8rem;
        }
        
        .stats-card {
            background: white;
            border-radius: 12px;
            padding: 1.2rem;
            text-align: center;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
            border-left: 3px solid;
            margin-bottom: 1rem;
        }
        
        .move-lines {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 0.8rem;
            margin-top: 0.5rem;
        }
        
        .line-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.3rem 0;
            border-bottom: 1px solid #dee2e6;
        }
        
        .line-item:last-child {
            border-bottom: none;
        }
        
        .amount-debit { color: #28a745; }
        .amount-credit { color: #dc3545; }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-odoo">
        <div class="container-fluid">
            <a class="navbar-brand text-white" href="../dashboard.php">
                <i class="fas fa-cube me-2"></i>
                نظام ERP - القيود اليومية
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="../dashboard.php">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-3">
        <!-- مسار التنقل -->
        <nav class="breadcrumb-odoo">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="../dashboard.php">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="#">المحاسبة</a></li>
                <li class="breadcrumb-item active">القيود اليومية</li>
            </ol>
        </nav>

        <!-- العنوان الرئيسي -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h3><i class="fas fa-book me-2"></i>القيود اليومية</h3>
                    <p class="mb-0 small">إدارة وتسجيل القيود المحاسبية</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#addMoveModal">
                        <i class="fas fa-plus me-2"></i>إضافة قيد جديد
                    </button>
                </div>
            </div>
        </div>

        <!-- رسائل النظام -->
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $message_type === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
                <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- إحصائيات القيود -->
        <div class="row mb-3">
            <?php foreach ($move_states as $state => $info): ?>
                <div class="col-md-3">
                    <div class="stats-card border-<?php echo $info['color']; ?>">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <i class="<?php echo $info['icon']; ?> text-<?php echo $info['color']; ?>" style="font-size: 1.5rem;"></i>
                        </div>
                        <h4 class="text-<?php echo $info['color']; ?>"><?php
                            $count = 0;
                            foreach($move_model->get_demo_data() as $move) {
                                if($move['state'] === $state) $count++;
                            }
                            echo $count;
                        ?></h4>
                        <p class="mb-0 small"><?php echo $info['name']; ?></p>
                    </div>
                </div>
            <?php endforeach; ?>
            <div class="col-md-3">
                <div class="stats-card border-primary">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <i class="fas fa-calculator text-primary" style="font-size: 1.5rem;"></i>
                    </div>
                    <h4 class="text-primary"><?php
                        $total_amount = 0;
                        foreach($move_model->get_demo_data() as $move) {
                            if($move['state'] === 'posted') {
                                $total_amount += $move['amount_total'];
                            }
                        }
                        echo number_format($total_amount, 0);
                    ?> ر.س</h4>
                    <p class="mb-0 small">إجمالي القيود المعتمدة</p>
                </div>
            </div>
        </div>

        <!-- أدوات التحكم والعرض -->
        <div class="view-controls">
            <div class="row align-items-center">
                <div class="col-md-3">
                    <div class="btn-group" role="group">
                        <a href="?view=list&journal=<?php echo $filter_journal; ?>&state=<?php echo $filter_state; ?>" class="view-btn <?php echo $view_mode === 'list' ? 'active' : ''; ?>">
                            <i class="fas fa-list me-1"></i>قائمة
                        </a>
                        <a href="?view=cards&journal=<?php echo $filter_journal; ?>&state=<?php echo $filter_state; ?>" class="view-btn <?php echo $view_mode === 'cards' ? 'active' : ''; ?>">
                            <i class="fas fa-th-large me-1"></i>بطاقات
                        </a>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="btn-group" role="group">
                        <a href="?view=<?php echo $view_mode; ?>&journal=all&state=<?php echo $filter_state; ?>" class="filter-btn <?php echo $filter_journal === 'all' ? 'active' : ''; ?>">
                            كل اليوميات
                        </a>
                        <?php foreach (array_slice($journals, 0, 3) as $journal): ?>
                            <a href="?view=<?php echo $view_mode; ?>&journal=<?php echo $journal['id']; ?>&state=<?php echo $filter_state; ?>" class="filter-btn <?php echo $filter_journal == $journal['id'] ? 'active' : ''; ?>">
                                <?php echo $journal['code']; ?>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="btn-group" role="group">
                        <a href="?view=<?php echo $view_mode; ?>&journal=<?php echo $filter_journal; ?>&state=all" class="filter-btn <?php echo $filter_state === 'all' ? 'active' : ''; ?>">
                            الكل
                        </a>
                        <a href="?view=<?php echo $view_mode; ?>&journal=<?php echo $filter_journal; ?>&state=draft" class="filter-btn <?php echo $filter_state === 'draft' ? 'active' : ''; ?>">
                            مسودة
                        </a>
                        <a href="?view=<?php echo $view_mode; ?>&journal=<?php echo $filter_journal; ?>&state=posted" class="filter-btn <?php echo $filter_state === 'posted' ? 'active' : ''; ?>">
                            معتمد
                        </a>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="d-flex justify-content-end">
                        <input type="text" class="form-control search-box" placeholder="البحث في القيود..." style="max-width: 200px; font-size: 0.8rem;">
                    </div>
                </div>
            </div>
        </div>

        <!-- محتوى العرض -->
        <?php if ($view_mode === 'cards'): ?>
            <!-- عرض البطاقات -->
            <div class="row">
                <?php foreach ($moves as $move):
                    $state_info = $move_states[$move['state']];
                    $journal_name = '';
                    foreach ($journals as $journal) {
                        if ($journal['id'] == $move['journal_id']) {
                            $journal_name = $journal['name'];
                            break;
                        }
                    }

                    // جلب بنود القيد
                    $move_lines = $move_line_model->get_move_lines($move['id']);
                ?>
                    <div class="col-md-6 col-lg-4">
                        <div class="move-card <?php echo $move['state']; ?>">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <div>
                                    <h6 class="mb-1"><?php echo $move['name']; ?></h6>
                                    <p class="text-muted mb-0 small"><?php echo $journal_name; ?></p>
                                </div>
                                <span class="badge bg-<?php echo $state_info['color']; ?>"><?php echo $state_info['name']; ?></span>
                            </div>

                            <div class="mb-2">
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i><?php echo date('Y-m-d', strtotime($move['date'])); ?><br>
                                    <i class="fas fa-tag me-1"></i><?php echo isset($move['ref']) ? $move['ref'] : 'بدون مرجع'; ?><br>
                                    <i class="fas fa-calculator me-1"></i><?php echo number_format($move['amount_total'], 2); ?> ر.س
                                </small>
                            </div>

                            <?php if (!empty($move['narration'])): ?>
                                <div class="mb-2">
                                    <small class="text-muted"><?php echo $move['narration']; ?></small>
                                </div>
                            <?php endif; ?>

                            <!-- بنود القيد -->
                            <div class="move-lines">
                                <small class="text-muted fw-bold">بنود القيد:</small>
                                <?php foreach (array_slice($move_lines, 0, 3) as $line):
                                    $account_name = '';
                                    foreach ($accounts as $account) {
                                        if ($account['id'] == $line['account_id']) {
                                            $account_name = $account['name'];
                                            break;
                                        }
                                    }
                                ?>
                                    <div class="line-item">
                                        <small><?php echo $account_name; ?></small>
                                        <small>
                                            <?php if ($line['debit'] > 0): ?>
                                                <span class="amount-debit"><?php echo number_format($line['debit'], 2); ?> مدين</span>
                                            <?php else: ?>
                                                <span class="amount-credit"><?php echo number_format($line['credit'], 2); ?> دائن</span>
                                            <?php endif; ?>
                                        </small>
                                    </div>
                                <?php endforeach; ?>
                                <?php if (count($move_lines) > 3): ?>
                                    <small class="text-muted">... و <?php echo count($move_lines) - 3; ?> بنود أخرى</small>
                                <?php endif; ?>
                            </div>

                            <div class="d-flex justify-content-between mt-2">
                                <button class="btn btn-outline-primary btn-sm" onclick="editMove(<?php echo $move['id']; ?>)">
                                    <i class="fas fa-edit me-1"></i>تعديل
                                </button>
                                <button class="btn btn-outline-info btn-sm" onclick="viewMove(<?php echo $move['id']; ?>)">
                                    <i class="fas fa-eye me-1"></i>عرض
                                </button>
                                <?php if ($move['state'] === 'draft'): ?>
                                    <button class="btn btn-outline-success btn-sm" onclick="postMove(<?php echo $move['id']; ?>)">
                                        <i class="fas fa-check me-1"></i>اعتماد
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

        <?php else: ?>
            <!-- عرض القائمة -->
            <div class="table-odoo">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>رقم القيد</th>
                            <th>التاريخ</th>
                            <th>اليومية</th>
                            <th>المرجع</th>
                            <th>البيان</th>
                            <th>المبلغ</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($moves as $move):
                            $state_info = $move_states[$move['state']];
                            $journal_name = '';
                            foreach ($journals as $journal) {
                                if ($journal['id'] == $move['journal_id']) {
                                    $journal_name = $journal['name'];
                                    break;
                                }
                            }
                        ?>
                            <tr>
                                <td><strong><?php echo $move['name']; ?></strong></td>
                                <td><?php echo date('Y-m-d', strtotime($move['date'])); ?></td>
                                <td><?php echo $journal_name; ?></td>
                                <td><?php echo $move['ref'] ?? '-'; ?></td>
                                <td>
                                    <small><?php echo $move['narration'] ? (strlen($move['narration']) > 50 ? substr($move['narration'], 0, 50) . '...' : $move['narration']) : '-'; ?></small>
                                </td>
                                <td><strong><?php echo number_format($move['amount_total'], 2); ?> ر.س</strong></td>
                                <td>
                                    <span class="badge bg-<?php echo $state_info['color']; ?>">
                                        <i class="<?php echo $state_info['icon']; ?> me-1"></i><?php echo $state_info['name']; ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary btn-sm" onclick="editMove(<?php echo $move['id']; ?>)">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-info btn-sm" onclick="viewMove(<?php echo $move['id']; ?>)">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <?php if ($move['state'] === 'draft'): ?>
                                            <button class="btn btn-outline-success btn-sm" onclick="postMove(<?php echo $move['id']; ?>)">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        <?php endif; ?>
                                        <?php if ($move['state'] === 'posted'): ?>
                                            <button class="btn btn-outline-warning btn-sm" onclick="reverseMove(<?php echo $move['id']; ?>)">
                                                <i class="fas fa-undo"></i>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editMove(moveId) {
            alert('سيتم تطوير نافذة تعديل القيد قريباً');
        }

        function viewMove(moveId) {
            window.location.href = 'move_details.php?move_id=' + moveId;
        }

        function postMove(moveId) {
            if (confirm('هل أنت متأكد من اعتماد هذا القيد؟')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="post_move">
                    <input type="hidden" name="move_id" value="${moveId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        function reverseMove(moveId) {
            alert('سيتم تطوير ميزة عكس القيد قريباً');
        }

        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const items = document.querySelectorAll('.move-card, tbody tr');
            items.forEach((item, index) => {
                item.style.opacity = '0';
                item.style.transform = 'translateY(15px)';

                setTimeout(() => {
                    item.style.transition = 'all 0.5s ease';
                    item.style.opacity = '1';
                    item.style.transform = 'translateY(0)';
                }, index * 80);
            });
        });

        // البحث المباشر
        document.querySelector('.search-box').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const items = document.querySelectorAll('.move-card, tbody tr');

            items.forEach(item => {
                const text = item.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    item.style.display = '';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
