<?php
/**
 * صفحة القيود اليومية بأسلوب Odoo
 * Journal Entries Page - Odoo Style
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// تحميل نظام Odoo
require_once '../config/odoo_config.php';
require_once '../config/odoo_database.php';

$odoo_db = OdooDatabase::getInstance();

// تحميل النماذج
require_once '../models/BaseModel.php';
require_once '../models/AccountMove.php';
require_once '../models/AccountMoveLine.php';
require_once '../models/AccountJournal.php';
require_once '../models/AccountAccount.php';
require_once '../models/ResCurrency.php';

$move_model = new AccountMove($odoo_db);
$move_line_model = new AccountMoveLine($odoo_db);
$journal_model = new AccountJournal($odoo_db);
$account_model = new AccountAccount($odoo_db);
$currency_model = new ResCurrency($odoo_db);

$message = '';
$message_type = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = isset($_POST['action']) ? $_POST['action'] : '';
    
    try {
        switch ($action) {
            case 'create_move':
                $move_data = array(
                    'journal_id' => $_POST['journal_id'],
                    'date' => $_POST['date'],
                    'ref' => $_POST['ref'],
                    'narration' => $_POST['narration']
                );
                
                $move_id = $move_model->create_move($move_data);
                
                // إضافة بنود القيد
                if (isset($_POST['lines']) && is_array($_POST['lines'])) {
                    foreach ($_POST['lines'] as $line) {
                        if (!empty($line['account_id']) && (!empty($line['debit']) || !empty($line['credit']))) {
                            $line_data = array(
                                'account_id' => $line['account_id'],
                                'name' => $line['name'],
                                'debit' => floatval(isset($line['debit']) ? $line['debit'] : 0),
                                'credit' => floatval(isset($line['credit']) ? $line['credit'] : 0),
                                'partner_id' => !empty($line['partner_id']) ? $line['partner_id'] : null
                            );
                            
                            $move_model->add_move_line($move_id, $line_data);
                        }
                    }
                }
                
                // حساب إجمالي القيد
                $move_model->compute_amount_total($move_id);
                
                $message = "تم إنشاء القيد بنجاح";
                $message_type = 'success';
                break;
                
            case 'post_move':
                $move_id = $_POST['move_id'];
                $move_model->post_move($move_id);
                $message = "تم اعتماد القيد بنجاح";
                $message_type = 'success';
                break;
                
            case 'cancel_move':
                $move_id = $_POST['move_id'];
                $move_model->cancel_move($move_id);
                $message = "تم إلغاء القيد بنجاح";
                $message_type = 'success';
                break;
                
            default:
                throw new Exception('إجراء غير صحيح');
        }
    } catch (Exception $e) {
        $message = 'خطأ: ' . $e->getMessage();
        $message_type = 'error';
    }
}

// جلب البيانات
$moves = $move_model->get_demo_data();
$journals = $journal_model->get_demo_data();
$accounts = $account_model->get_demo_data();

// فلترة حسب اليومية
$filter_journal = isset($_GET['journal']) ? $_GET['journal'] : 'all';
if ($filter_journal !== 'all') {
    $filtered_moves = array();
    foreach ($moves as $move) {
        if ($move['journal_id'] == $filter_journal) {
            $filtered_moves[] = $move;
        }
    }
    $moves = $filtered_moves;
}

// فلترة حسب الحالة
$filter_state = isset($_GET['state']) ? $_GET['state'] : 'all';
if ($filter_state !== 'all') {
    $filtered_moves = array();
    foreach ($moves as $move) {
        if ($move['state'] === $filter_state) {
            $filtered_moves[] = $move;
        }
    }
    $moves = $filtered_moves;
}

// طريقة العرض المحددة
$view_mode = isset($_GET['view']) ? $_GET['view'] : 'list';

// حالات القيود
$move_states = array(
    'draft' => array('name' => 'مسودة', 'color' => 'secondary', 'icon' => 'fas fa-edit'),
    'posted' => array('name' => 'معتمد', 'color' => 'success', 'icon' => 'fas fa-check'),
    'cancelled' => array('name' => 'ملغي', 'color' => 'danger', 'icon' => 'fas fa-times')
);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>القيود اليومية - نظام ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/odoo-style.css" rel="stylesheet">
    <link href="../assets/css/odoo-advanced.css" rel="stylesheet">
    <link href="../assets/css/journal-entries-enhanced.css" rel="stylesheet">
    <style>
        :root {
            --odoo-primary: #714B67;
            --odoo-secondary: #8B5A8C;
        }
        
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 0.85rem;
        }
        
        .navbar-odoo {
            background: linear-gradient(45deg, var(--odoo-primary), var(--odoo-secondary));
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .page-header {
            background: linear-gradient(45deg, #3498DB, #2980B9);
            color: white;
            padding: 1.5rem;
            border-radius: 15px;
            margin-bottom: 1.5rem;
        }
        
        .view-controls {
            background: white;
            border-radius: 10px;
            padding: 0.8rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        
        .view-btn, .filter-btn {
            border: 1px solid #dee2e6;
            background: white;
            color: #6c757d;
            padding: 0.4rem 0.8rem;
            border-radius: 5px;
            transition: all 0.3s ease;
            text-decoration: none;
            font-size: 0.8rem;
        }
        
        .view-btn.active, .filter-btn.active {
            background: #3498DB;
            color: white;
            border-color: #3498DB;
        }
        
        .view-btn:hover, .filter-btn:hover {
            background: #2980B9;
            color: white;
            border-color: #2980B9;
            text-decoration: none;
        }
        
        .move-card {
            background: white;
            border-radius: 12px;
            padding: 1.2rem;
            margin-bottom: 0.8rem;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
            transition: all 0.3s ease;
            border-left: 3px solid;
        }
        
        .move-card.draft { border-left-color: #6c757d; }
        .move-card.posted { border-left-color: #28a745; }
        .move-card.cancelled { border-left-color: #dc3545; }
        
        .move-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.12);
        }
        
        .table-odoo {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
        }
        
        .table-odoo th {
            background: linear-gradient(45deg, #3498DB, #2980B9);
            color: white;
            border: none;
            padding: 0.8rem;
            font-size: 0.8rem;
        }
        
        .table-odoo td {
            padding: 0.8rem;
            border-color: #f8f9fa;
            vertical-align: middle;
            font-size: 0.8rem;
        }
        
        .breadcrumb-odoo {
            background: white;
            border-radius: 8px;
            padding: 0.8rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            font-size: 0.8rem;
        }
        
        .stats-card {
            background: white;
            border-radius: 12px;
            padding: 1.2rem;
            text-align: center;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
            border-left: 3px solid;
            margin-bottom: 1rem;
        }
        
        .move-lines {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 0.8rem;
            margin-top: 0.5rem;
        }
        
        .line-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.3rem 0;
            border-bottom: 1px solid #dee2e6;
        }
        
        .line-item:last-child {
            border-bottom: none;
        }
        
        .amount-debit { color: #28a745; }
        .amount-credit { color: #dc3545; }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <?php include '../includes/navbar.php'; ?>

    <div class="o_main_content">
        <div class="o_content">
            <!-- الشريط الجانبي -->
            <?php include '../includes/sidebar.php'; ?>

            <!-- المحتوى الرئيسي -->
            <div class="o_action_manager">
                <!-- شريط التحكم -->
                <div class="o_control_panel">
                    <div class="o_cp_left">
                        <!-- مسار التنقل -->
                        <div class="o_breadcrumb">
                            <a href="../dashboard.php" class="o_breadcrumb_item">الرئيسية</a>
                            <span>/</span>
                            <a href="#" class="o_breadcrumb_item">المحاسبة</a>
                            <span>/</span>
                            <span class="o_breadcrumb_item active">القيود اليومية</span>
                        </div>
                    </div>
                    <div class="o_cp_right">
                        <!-- أزرار الإجراءات -->
                        <div class="btn-group me-2">
                            <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addMoveModal">
                                <i class="fas fa-plus me-1"></i>إنشاء
                            </button>
                            <button class="btn btn-outline-primary btn-sm" onclick="importEntries()">
                                <i class="fas fa-upload me-1"></i>استيراد
                            </button>
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-outline-secondary btn-sm" onclick="refreshData()">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                            <button class="btn btn-outline-secondary btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="fas fa-cog"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#"><i class="fas fa-print me-2"></i>طباعة</a></li>
                                <li><a class="dropdown-item" href="#"><i class="fas fa-download me-2"></i>تصدير</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>إعدادات</a></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- رسائل النظام -->
                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $message_type === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show m-3">
                        <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- شريط الفلترة والبحث -->
                <div class="o_search_panel">
                    <div class="d-flex align-items-center gap-2 p-3 border-bottom">
                        <!-- البحث -->
                        <div class="flex-grow-1">
                            <div class="input-group input-group-sm">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" class="form-control" placeholder="البحث في القيود..." id="searchInput">
                            </div>
                        </div>

                        <!-- فلترة حسب اليومية -->
                        <select class="form-select form-select-sm" style="width: auto;" onchange="filterByJournal(this.value)">
                            <option value="all">جميع اليوميات</option>
                            <?php foreach ($journals as $journal): ?>
                                <option value="<?php echo $journal['id']; ?>" <?php echo $filter_journal == $journal['id'] ? 'selected' : ''; ?>>
                                    <?php echo $journal['name']; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>

                        <!-- فلترة حسب الحالة -->
                        <select class="form-select form-select-sm" style="width: auto;" onchange="filterByState(this.value)">
                            <option value="all">جميع الحالات</option>
                            <?php foreach ($move_states as $state => $info): ?>
                                <option value="<?php echo $state; ?>" <?php echo $filter_state == $state ? 'selected' : ''; ?>>
                                    <?php echo $info['name']; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>

                        <!-- أزرار العرض -->
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-secondary <?php echo $view_mode === 'list' ? 'active' : ''; ?>"
                                    onclick="changeView('list')" title="عرض قائمة">
                                <i class="fas fa-list"></i>
                            </button>
                            <button class="btn btn-outline-secondary <?php echo $view_mode === 'kanban' ? 'active' : ''; ?>"
                                    onclick="changeView('kanban')" title="عرض بطاقات">
                                <i class="fas fa-th-large"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <!-- عرض القيود -->
                <div class="o_list_view">
                    <?php if ($view_mode === 'list'): ?>
                        <!-- عرض الجدول -->
                        <table class="o_list_table">
                            <thead>
                                <tr>
                                    <th class="o_list_record_selector">
                                        <input type="checkbox" id="selectAll">
                                    </th>
                                    <th class="o_column_sortable" data-sort="name">
                                        رقم القيد
                                    </th>
                                    <th class="o_column_sortable" data-sort="date">
                                        التاريخ
                                    </th>
                                    <th class="o_column_sortable" data-sort="journal">
                                        اليومية
                                    </th>
                                    <th class="o_column_sortable" data-sort="ref">
                                        المرجع
                                    </th>
                                    <th class="o_column_sortable o_list_monetary" data-sort="amount">
                                        المبلغ
                                    </th>
                                    <th class="o_column_sortable" data-sort="state">
                                        الحالة
                                    </th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($moves as $move): ?>
                                    <tr class="o_data_row" data-id="<?php echo $move['id']; ?>">
                                        <td class="o_list_record_selector">
                                            <input type="checkbox" class="record-checkbox" value="<?php echo $move['id']; ?>">
                                        </td>
                                        <td>
                                            <strong><?php echo $move['name']; ?></strong>
                                        </td>
                                        <td class="o_list_date">
                                            <?php echo date('Y/m/d', strtotime($move['date'])); ?>
                                        </td>
                                        <td>
                                            <?php
                                            $journal_name = 'غير محدد';
                                            foreach ($journals as $journal) {
                                                if ($journal['id'] == $move['journal_id']) {
                                                    $journal_name = $journal['name'];
                                                    break;
                                                }
                                            }
                                            echo $journal_name;
                                            ?>
                                        </td>
                                        <td><?php echo !empty($move['ref']) ? $move['ref'] : '-'; ?></td>
                                        <td class="o_list_monetary">
                                            <?php echo number_format($move['amount_total'], 2); ?> ر.س
                                        </td>
                                        <td>
                                            <span class="o_status_badge <?php echo $move['state']; ?>">
                                                <?php echo $move_states[$move['state']]['name']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary btn-sm" onclick="viewMove(<?php echo $move['id']; ?>)" title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <?php if ($move['state'] === 'draft'): ?>
                                                    <button class="btn btn-outline-success btn-sm" onclick="postMove(<?php echo $move['id']; ?>)" title="ترحيل">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                    <button class="btn btn-outline-warning btn-sm" onclick="editMove(<?php echo $move['id']; ?>)" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                <?php endif; ?>
                                                <button class="btn btn-outline-secondary btn-sm dropdown-toggle" data-bs-toggle="dropdown" title="المزيد">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="#" onclick="duplicateMove(<?php echo $move['id']; ?>)">
                                                        <i class="fas fa-copy me-2"></i>نسخ
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="#" onclick="printMove(<?php echo $move['id']; ?>)">
                                                        <i class="fas fa-print me-2"></i>طباعة
                                                    </a></li>
                                                    <?php if ($move['state'] === 'draft'): ?>
                                                        <li><hr class="dropdown-divider"></li>
                                                        <li><a class="dropdown-item text-danger" href="#" onclick="deleteMove(<?php echo $move['id']; ?>)">
                                                            <i class="fas fa-trash me-2"></i>حذف
                                                        </a></li>
                                                    <?php endif; ?>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php else: ?>
                        <!-- عرض البطاقات -->
                        <div class="row">
                            <?php foreach ($moves as $move): ?>
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card h-100 move-card <?php echo $move['state']; ?>">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0"><?php echo $move['name']; ?></h6>
                                            <span class="o_status_badge <?php echo $move['state']; ?>">
                                                <?php echo $move_states[$move['state']]['name']; ?>
                                            </span>
                                        </div>
                                        <div class="card-body">
                                            <p class="card-text">
                                                <small class="text-muted">التاريخ:</small> <?php echo date('Y/m/d', strtotime($move['date'])); ?><br>
                                                <small class="text-muted">اليومية:</small>
                                                <?php
                                                $journal_name = 'غير محدد';
                                                foreach ($journals as $journal) {
                                                    if ($journal['id'] == $move['journal_id']) {
                                                        $journal_name = $journal['name'];
                                                        break;
                                                    }
                                                }
                                                echo $journal_name;
                                                ?><br>
                                                <small class="text-muted">المبلغ:</small> <strong><?php echo number_format($move['amount_total'], 2); ?> ر.س</strong>
                                            </p>
                                        </div>
                                        <div class="card-footer">
                                            <div class="btn-group btn-group-sm w-100">
                                                <button class="btn btn-outline-primary" onclick="viewMove(<?php echo $move['id']; ?>)">
                                                    <i class="fas fa-eye me-1"></i>عرض
                                                </button>
                                                <?php if ($move['state'] === 'draft'): ?>
                                                    <button class="btn btn-outline-success" onclick="postMove(<?php echo $move['id']; ?>)">
                                                        <i class="fas fa-check me-1"></i>ترحيل
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
                </div>
            </div>
        </div>

        <!-- محتوى العرض -->
        <?php if ($view_mode === 'cards'): ?>
            <!-- عرض البطاقات -->
            <div class="row">
                <?php foreach ($moves as $move):
                    $state_info = $move_states[$move['state']];
                    $journal_name = '';
                    foreach ($journals as $journal) {
                        if ($journal['id'] == $move['journal_id']) {
                            $journal_name = $journal['name'];
                            break;
                        }
                    }

                    // جلب بنود القيد
                    $move_lines = $move_line_model->get_move_lines($move['id']);
                ?>
                    <div class="col-md-6 col-lg-4">
                        <div class="move-card <?php echo $move['state']; ?>">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <div>
                                    <h6 class="mb-1"><?php echo $move['name']; ?></h6>
                                    <p class="text-muted mb-0 small"><?php echo $journal_name; ?></p>
                                </div>
                                <span class="badge bg-<?php echo $state_info['color']; ?>"><?php echo $state_info['name']; ?></span>
                            </div>

                            <div class="mb-2">
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i><?php echo date('Y-m-d', strtotime($move['date'])); ?><br>
                                    <i class="fas fa-tag me-1"></i><?php echo isset($move['ref']) ? $move['ref'] : 'بدون مرجع'; ?><br>
                                    <i class="fas fa-calculator me-1"></i><?php echo number_format($move['amount_total'], 2); ?> ر.س
                                </small>
                            </div>

                            <?php if (!empty($move['narration'])): ?>
                                <div class="mb-2">
                                    <small class="text-muted"><?php echo $move['narration']; ?></small>
                                </div>
                            <?php endif; ?>

                            <!-- بنود القيد -->
                            <div class="move-lines">
                                <small class="text-muted fw-bold">بنود القيد:</small>
                                <?php foreach (array_slice($move_lines, 0, 3) as $line):
                                    $account_name = '';
                                    foreach ($accounts as $account) {
                                        if ($account['id'] == $line['account_id']) {
                                            $account_name = $account['name'];
                                            break;
                                        }
                                    }
                                ?>
                                    <div class="line-item">
                                        <small><?php echo $account_name; ?></small>
                                        <small>
                                            <?php if ($line['debit'] > 0): ?>
                                                <span class="amount-debit"><?php echo number_format($line['debit'], 2); ?> مدين</span>
                                            <?php else: ?>
                                                <span class="amount-credit"><?php echo number_format($line['credit'], 2); ?> دائن</span>
                                            <?php endif; ?>
                                        </small>
                                    </div>
                                <?php endforeach; ?>
                                <?php if (count($move_lines) > 3): ?>
                                    <small class="text-muted">... و <?php echo count($move_lines) - 3; ?> بنود أخرى</small>
                                <?php endif; ?>
                            </div>

                            <div class="d-flex justify-content-between mt-2">
                                <button class="btn btn-outline-primary btn-sm" onclick="editMove(<?php echo $move['id']; ?>)">
                                    <i class="fas fa-edit me-1"></i>تعديل
                                </button>
                                <button class="btn btn-outline-info btn-sm" onclick="viewMove(<?php echo $move['id']; ?>)">
                                    <i class="fas fa-eye me-1"></i>عرض
                                </button>
                                <?php if ($move['state'] === 'draft'): ?>
                                    <button class="btn btn-outline-success btn-sm" onclick="postMove(<?php echo $move['id']; ?>)">
                                        <i class="fas fa-check me-1"></i>اعتماد
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

        <?php else: ?>
            <!-- عرض القائمة المتقدم -->
            <div class="odoo-table-container">
                <table id="journal-entries-table" class="odoo-table odoo-table-auto" data-selectable="true">
                    <thead>
                        <tr>
                            <th class="sortable" data-column="0">رقم القيد</th>
                            <th class="sortable" data-column="1">التاريخ</th>
                            <th class="sortable" data-column="2">اليومية</th>
                            <th class="sortable" data-column="3">المرجع</th>
                            <th class="sortable" data-column="4">البيان</th>
                            <th class="sortable" data-column="5">المبلغ</th>
                            <th class="sortable" data-column="6">الحالة</th>
                            <th class="no-sort">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($moves as $move):
                            $state_info = $move_states[$move['state']];
                            $journal_name = '';
                            foreach ($journals as $journal) {
                                if ($journal['id'] == $move['journal_id']) {
                                    $journal_name = $journal['name'];
                                    break;
                                }
                            }
                        ?>
                            <tr>
                                <td><strong><?php echo $move['name']; ?></strong></td>
                                <td><?php echo date('Y-m-d', strtotime($move['date'])); ?></td>
                                <td><?php echo $journal_name; ?></td>
                                <td><?php echo isset($move['ref']) ? $move['ref'] : '-'; ?></td>
                                <td>
                                    <small><?php echo $move['narration'] ? (strlen($move['narration']) > 50 ? substr($move['narration'], 0, 50) . '...' : $move['narration']) : '-'; ?></small>
                                </td>
                                <td><strong><?php echo number_format($move['amount_total'], 2); ?> ر.س</strong></td>
                                <td>
                                    <span class="badge bg-<?php echo $state_info['color']; ?>">
                                        <i class="<?php echo $state_info['icon']; ?> me-1"></i><?php echo $state_info['name']; ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary btn-sm" onclick="editMove(<?php echo $move['id']; ?>)">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-info btn-sm" onclick="viewMove(<?php echo $move['id']; ?>)">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <?php if ($move['state'] === 'draft'): ?>
                                            <button class="btn btn-outline-success btn-sm" onclick="postMove(<?php echo $move['id']; ?>)">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        <?php endif; ?>
                                        <?php if ($move['state'] === 'posted'): ?>
                                            <button class="btn btn-outline-warning btn-sm" onclick="reverseMove(<?php echo $move['id']; ?>)">
                                                <i class="fas fa-undo"></i>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/odoo-table-manager.js"></script>
    <script src="../assets/js/odoo-buttons-manager.js"></script>
    <script src="../assets/js/odoo-export-manager.js"></script>
    <script>
        // تهيئة مديري الجداول والأزرار
        document.addEventListener('DOMContentLoaded', function() {
            // تهيئة مدير الأزرار للجدول
            odooButtons.addTableButtons('journal-entries-table', {
                enableAdd: true,
                enableEdit: true,
                enableDelete: true,
                enablePrint: true,
                enableExport: true,
                enableRefresh: true
            });

            // تفعيل دوال التصدير
            setupJournalExportFunctions();

            // تطبيق تأثيرات بصرية
            animateElements();
        });

        function setupJournalExportFunctions() {
            window.exportJournalToExcel = function() {
                odooExport.exportToExcel('journal-entries-table', 'القيود_اليومية.xlsx')
                    .then(filename => showMessage('تم تصدير القيود اليومية إلى Excel بنجاح', 'success'))
                    .catch(error => showMessage('خطأ في التصدير: ' + error.message, 'error'));
            };

            window.exportJournalToXLS = function() {
                odooExport.exportToXLS('journal-entries-table', 'القيود_اليومية.xls')
                    .then(filename => showMessage('تم تصدير القيود اليومية إلى XLS بنجاح', 'success'))
                    .catch(error => showMessage('خطأ في التصدير: ' + error.message, 'error'));
            };

            window.exportJournalToPDF = function() {
                odooExport.exportToPDF('journal-entries-table', 'القيود_اليومية.pdf', {
                    title: 'القيود اليومية',
                    orientation: 'landscape'
                })
                    .then(filename => showMessage('تم تصدير القيود اليومية إلى PDF بنجاح', 'success'))
                    .catch(error => showMessage('خطأ في التصدير: ' + error.message, 'error'));
            };

            window.exportJournalToCSV = function() {
                odooExport.exportToCSV('journal-entries-table', 'القيود_اليومية.csv')
                    .then(filename => showMessage('تم تصدير القيود اليومية إلى CSV بنجاح', 'success'))
                    .catch(error => showMessage('خطأ في التصدير: ' + error.message, 'error'));
            };
        }

        function addNewJournalEntry() {
            // فتح نافذة إضافة قيد جديد
            window.location.href = 'create_entry.php';
        }

        function editSelectedEntry() {
            const selectedRows = document.querySelectorAll('#journal-entries-table .selected-row');
            if (selectedRows.length === 0) {
                showMessage('يرجى تحديد قيد للتعديل', 'warning');
                return;
            }
            if (selectedRows.length > 1) {
                showMessage('يرجى تحديد قيد واحد فقط للتعديل', 'warning');
                return;
            }

            const entryId = selectedRows[0].getAttribute('data-id');
            window.location.href = `create_entry.php?edit=${entryId}`;
        }

        function deleteSelectedEntries() {
            const selectedRows = document.querySelectorAll('#journal-entries-table .selected-row');
            if (selectedRows.length === 0) {
                showMessage('يرجى تحديد قيد أو أكثر للحذف', 'warning');
                return;
            }

            if (confirm(`هل أنت متأكد من حذف ${selectedRows.length} قيد؟`)) {
                selectedRows.forEach(row => {
                    const entryId = row.getAttribute('data-id');
                    row.remove();
                });

                showMessage(`تم حذف ${selectedRows.length} قيد بنجاح`, 'success');
            }
        }

        function postSelectedEntries() {
            const selectedRows = document.querySelectorAll('#journal-entries-table .selected-row');
            if (selectedRows.length === 0) {
                showMessage('يرجى تحديد قيد أو أكثر للترحيل', 'warning');
                return;
            }

            if (confirm(`هل أنت متأكد من ترحيل ${selectedRows.length} قيد؟`)) {
                selectedRows.forEach(row => {
                    const statusCell = row.querySelector('.status-badge');
                    if (statusCell) {
                        statusCell.className = 'badge bg-success status-badge';
                        statusCell.innerHTML = '<i class="fas fa-check me-1"></i>مرحل';
                    }
                });

                showMessage(`تم ترحيل ${selectedRows.length} قيد بنجاح`, 'success');
            }
        }

        function showMessage(message, type = 'info') {
            const alert = document.createElement('div');
            alert.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alert.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(alert);

            setTimeout(() => {
                alert.remove();
            }, 4000);
        }

        function animateElements() {
            const elements = document.querySelectorAll('.odoo-table-container, .view-controls, .stats-card');
            elements.forEach((element, index) => {
                element.style.opacity = '0';
                element.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    element.style.transition = 'all 0.6s ease';
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, index * 100);
            });
        }
    </script>
    <script>
        function editMove(moveId) {
            alert('سيتم تطوير نافذة تعديل القيد قريباً');
        }

        function viewMove(moveId) {
            window.location.href = 'move_details.php?move_id=' + moveId;
        }

        function postMove(moveId) {
            if (confirm('هل أنت متأكد من اعتماد هذا القيد؟')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="post_move">
                    <input type="hidden" name="move_id" value="${moveId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        function reverseMove(moveId) {
            alert('سيتم تطوير ميزة عكس القيد قريباً');
        }

        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const items = document.querySelectorAll('.move-card, tbody tr');
            items.forEach((item, index) => {
                item.style.opacity = '0';
                item.style.transform = 'translateY(15px)';

                setTimeout(() => {
                    item.style.transition = 'all 0.5s ease';
                    item.style.opacity = '1';
                    item.style.transform = 'translateY(0)';
                }, index * 80);
            });
        });

        // وظائف Odoo المحسنة الإضافية

        // تحديد/إلغاء تحديد جميع الصفوف
        document.getElementById('selectAll')?.addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.record-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
                toggleRowSelection(checkbox, false);
            });
        });

        // تحديد صف واحد
        function toggleRowSelection(checkbox, updateSelectAll = true) {
            const row = checkbox.closest('tr');
            if (checkbox.checked) {
                row.classList.add('o_selected_row');
            } else {
                row.classList.remove('o_selected_row');
            }

            if (updateSelectAll) {
                updateSelectAllState();
            }
        }

        // تحديث حالة "تحديد الكل"
        function updateSelectAllState() {
            const allCheckboxes = document.querySelectorAll('.record-checkbox');
            const checkedCheckboxes = document.querySelectorAll('.record-checkbox:checked');
            const selectAllCheckbox = document.getElementById('selectAll');

            if (!selectAllCheckbox) return;

            if (checkedCheckboxes.length === allCheckboxes.length && allCheckboxes.length > 0) {
                selectAllCheckbox.checked = true;
                selectAllCheckbox.indeterminate = false;
            } else if (checkedCheckboxes.length > 0) {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = true;
            } else {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = false;
            }
        }

        // إضافة معالجات الأحداث للصفوف
        document.querySelectorAll('.record-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                toggleRowSelection(this);
            });
        });

        // معالج البحث
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', function() {
                filterTable(this.value);
            });
        }

        // معالج ترتيب الأعمدة
        document.querySelectorAll('.o_column_sortable').forEach(header => {
            header.addEventListener('click', function() {
                sortTable(this.dataset.sort);
            });
        });

        // وظائف الإجراءات
        function viewMove(moveId) {
            window.open(`view_move.php?id=${moveId}`, '_blank', 'width=800,height=600');
        }

        function editMove(moveId) {
            window.location.href = `create_entry.php?edit=${moveId}`;
        }

        function postMove(moveId) {
            if (confirm('هل أنت متأكد من ترحيل هذا القيد؟')) {
                showMessage('تم ترحيل القيد بنجاح', 'success');
                setTimeout(() => location.reload(), 1000);
            }
        }

        function duplicateMove(moveId) {
            window.location.href = `create_entry.php?duplicate=${moveId}`;
        }

        function printMove(moveId) {
            window.open(`print_move.php?id=${moveId}`, '_blank');
        }

        function deleteMove(moveId) {
            if (confirm('هل أنت متأكد من حذف هذا القيد؟')) {
                showMessage('تم حذف القيد بنجاح', 'success');
                setTimeout(() => location.reload(), 1000);
            }
        }

        // وظائف الفلترة والبحث
        function filterByJournal(journalId) {
            const url = new URL(window.location);
            url.searchParams.set('journal', journalId);
            window.location.href = url.toString();
        }

        function filterByState(state) {
            const url = new URL(window.location);
            url.searchParams.set('state', state);
            window.location.href = url.toString();
        }

        function changeView(viewMode) {
            const url = new URL(window.location);
            url.searchParams.set('view', viewMode);
            window.location.href = url.toString();
        }

        function filterTable(searchTerm) {
            const rows = document.querySelectorAll('.o_data_row');
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm.toLowerCase())) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        function sortTable(column) {
            console.log('Sorting by:', column);
        }

        function refreshData() {
            location.reload();
        }

        function importEntries() {
            showMessage('ميزة الاستيراد قيد التطوير', 'info');
        }

        // وظيفة عرض الرسائل
        function showMessage(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(alertDiv);

            setTimeout(() => {
                alertDiv.remove();
            }, 4000);
        }
    </script>
</body>
</html>
