<?php
/**
 * صفحة القيود اليومية المحسنة بأسلوب Odoo الكامل
 * Enhanced Journal Entries Page - Complete Odoo Style
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// تحميل النماذج
require_once '../models/AccountMove.php';
require_once '../models/AccountJournal.php';
require_once '../models/AccountAccount.php';

// تهيئة النماذج
$move_model = new AccountMove();
$journal_model = new AccountJournal();
$account_model = new AccountAccount();

$message = '';
$message_type = '';

// معالجة الطلبات AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    $action = isset($_POST['action']) ? $_POST['action'] : '';

    switch ($action) {
        case 'delete_move':
            $move_id = isset($_POST['move_id']) ? $_POST['move_id'] : 0;
            if ($move_id) {
                try {
                    // محاكاة حذف القيد
                    echo json_encode(array('success' => true, 'message' => 'تم حذف القيد بنجاح'));
                    exit();
                } catch (Exception $e) {
                    echo json_encode(array('success' => false, 'message' => 'خطأ في حذف القيد: ' . $e->getMessage()));
                    exit();
                }
            }
            break;

        case 'post_move':
            $move_id = isset($_POST['move_id']) ? $_POST['move_id'] : 0;
            if ($move_id) {
                try {
                    // محاكاة ترحيل القيد
                    echo json_encode(array('success' => true, 'message' => 'تم ترحيل القيد بنجاح'));
                    exit();
                } catch (Exception $e) {
                    echo json_encode(array('success' => false, 'message' => 'خطأ في ترحيل القيد: ' . $e->getMessage()));
                    exit();
                }
            }
            break;

        case 'bulk_action':
            $selected_ids = isset($_POST['selected_ids']) ? $_POST['selected_ids'] : array();
            $bulk_action = isset($_POST['bulk_action']) ? $_POST['bulk_action'] : '';

            if (!empty($selected_ids) && $bulk_action) {
                $success_count = count($selected_ids);
                echo json_encode(array(
                    'success' => true,
                    'message' => "تم تنفيذ العملية على {$success_count} قيد بنجاح"
                ));
                exit();
            }
            break;
    }
}

// معاملات الفلترة والعرض
$view_mode = isset($_GET['view']) ? $_GET['view'] : 'list';
$filter_journal = isset($_GET['journal']) ? $_GET['journal'] : 'all';
$filter_state = isset($_GET['state']) ? $_GET['state'] : 'all';
$search_term = isset($_GET['search']) ? $_GET['search'] : '';

// جلب البيانات
$moves = $move_model->get_demo_data();
$journals = $journal_model->get_demo_data();

// تطبيق الفلاتر
if ($filter_journal !== 'all') {
    $filtered_moves = array();
    foreach ($moves as $move) {
        if ($move['journal_id'] == $filter_journal) {
            $filtered_moves[] = $move;
        }
    }
    $moves = $filtered_moves;
}

if ($filter_state !== 'all') {
    $filtered_moves = array();
    foreach ($moves as $move) {
        if ($move['state'] === $filter_state) {
            $filtered_moves[] = $move;
        }
    }
    $moves = $filtered_moves;
}

// تطبيق البحث
if (!empty($search_term)) {
    $filtered_moves = array();
    foreach ($moves as $move) {
        $ref = isset($move['ref']) ? $move['ref'] : '';
        if (stripos($move['name'], $search_term) !== false ||
            stripos($ref, $search_term) !== false) {
            $filtered_moves[] = $move;
        }
    }
    $moves = $filtered_moves;
}

// حالات القيود
$move_states = array(
    'draft' => array('name' => 'مسودة', 'color' => 'secondary', 'icon' => 'fas fa-edit'),
    'posted' => array('name' => 'مرحل', 'color' => 'success', 'icon' => 'fas fa-check-circle'),
    'cancelled' => array('name' => 'ملغي', 'color' => 'danger', 'icon' => 'fas fa-times-circle')
);

// إحصائيات سريعة
$all_moves = $move_model->get_demo_data();
$draft_count = 0;
$posted_count = 0;
$cancelled_count = 0;

foreach ($all_moves as $move) {
    switch ($move['state']) {
        case 'draft':
            $draft_count++;
            break;
        case 'posted':
            $posted_count++;
            break;
        case 'cancelled':
            $cancelled_count++;
            break;
    }
}

$stats = array(
    'total' => count($all_moves),
    'draft' => $draft_count,
    'posted' => $posted_count,
    'cancelled' => $cancelled_count
);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>القيود اليومية - نظام ERP</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Odoo Style -->
    <link href="../assets/css/odoo-style.css" rel="stylesheet">
    <link href="../assets/css/odoo-advanced.css" rel="stylesheet">
    <link href="../assets/css/journal-entries-enhanced.css" rel="stylesheet">

    <style>
        /* أنماط إضافية محسنة */
        .o_kanban_view {
            padding: 16px;
            background: #f5f5f5;
        }

        .o_kanban_record {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 16px;
            padding: 16px;
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }

        .o_kanban_record:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }

        .o_kanban_record.draft {
            border-left-color: #6c757d;
        }

        .o_kanban_record.posted {
            border-left-color: #28a745;
        }

        .o_kanban_record.cancelled {
            border-left-color: #dc3545;
        }

        .bulk-actions {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 12px 16px;
            margin: 16px 0;
            display: none;
        }

        .bulk-actions.show {
            display: block;
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .stats-card {
            background: white;
            border-radius: 8px;
            padding: 16px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }

        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }

        .stats-card.total {
            border-left-color: #007bff;
        }

        .stats-card.draft {
            border-left-color: #6c757d;
        }

        .stats-card.posted {
            border-left-color: #28a745;
        }

        .stats-card.cancelled {
            border-left-color: #dc3545;
        }

        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .stats-label {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .loading-spinner {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }

        .o_searchview {
            background: white;
            border-bottom: 1px solid #dee2e6;
            padding: 12px 16px;
        }

        .o_searchview_input {
            border: 1px solid #ced4da;
            border-radius: 20px;
            padding: 8px 16px;
            font-size: 14px;
            width: 300px;
        }

        .o_searchview_input:focus {
            outline: none;
            border-color: #714B67;
            box-shadow: 0 0 0 0.2rem rgba(113, 75, 103, 0.25);
        }

        .o_facet {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 16px;
            padding: 4px 12px;
            margin: 0 4px;
            font-size: 12px;
            display: inline-flex;
            align-items: center;
        }

        .o_facet_remove {
            margin-right: 8px;
            cursor: pointer;
            color: #666;
        }

        .o_facet_remove:hover {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <?php include '../includes/navbar.php'; ?>

    <div class="o_main_content">
        <div class="o_content">
            <!-- الشريط الجانبي -->
            <?php include '../includes/sidebar.php'; ?>

            <!-- المحتوى الرئيسي -->
            <div class="o_action_manager">
                <!-- شريط التحكم -->
                <div class="o_control_panel">
                    <div class="o_cp_left">
                        <!-- مسار التنقل -->
                        <div class="o_breadcrumb">
                            <a href="../dashboard.php" class="o_breadcrumb_item">الرئيسية</a>
                            <span>/</span>
                            <a href="#" class="o_breadcrumb_item">المحاسبة</a>
                            <span>/</span>
                            <span class="o_breadcrumb_item active">القيود اليومية</span>
                        </div>
                    </div>
                    <div class="o_cp_right">
                        <!-- أزرار الإجراءات -->
                        <div class="btn-group me-2">
                            <button class="btn btn-primary btn-sm" onclick="window.location.href='create_entry_enhanced.php'">
                                <i class="fas fa-plus me-1"></i>إنشاء
                            </button>
                            <button class="btn btn-outline-primary btn-sm" onclick="importEntries()">
                                <i class="fas fa-upload me-1"></i>استيراد
                            </button>
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-outline-secondary btn-sm" onclick="refreshData()">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                            <button class="btn btn-outline-secondary btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="fas fa-cog"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="exportData('excel')">
                                    <i class="fas fa-file-excel me-2"></i>تصدير Excel
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="exportData('pdf')">
                                    <i class="fas fa-file-pdf me-2"></i>تصدير PDF
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" onclick="printData()">
                                    <i class="fas fa-print me-2"></i>طباعة
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- الإحصائيات السريعة -->
                <div class="row mb-3 px-3">
                    <div class="col-lg-3 col-md-6 mb-2">
                        <div class="stats-card total">
                            <div class="stats-number text-primary"><?php echo $stats['total']; ?></div>
                            <div class="stats-label">إجمالي القيود</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-2">
                        <div class="stats-card draft">
                            <div class="stats-number text-secondary"><?php echo $stats['draft']; ?></div>
                            <div class="stats-label">مسودات</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-2">
                        <div class="stats-card posted">
                            <div class="stats-number text-success"><?php echo $stats['posted']; ?></div>
                            <div class="stats-label">مرحلة</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-2">
                        <div class="stats-card cancelled">
                            <div class="stats-number text-danger"><?php echo $stats['cancelled']; ?></div>
                            <div class="stats-label">ملغية</div>
                        </div>
                    </div>
                </div>

                <!-- شريط البحث والفلترة -->
                <div class="o_searchview">
                    <div class="d-flex align-items-center gap-3">
                        <!-- البحث -->
                        <div class="flex-grow-1">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" class="o_searchview_input form-control"
                                       placeholder="البحث في القيود..."
                                       id="searchInput"
                                       value="<?php echo htmlspecialchars($search_term); ?>">
                            </div>
                        </div>

                        <!-- الفلاتر النشطة -->
                        <div class="d-flex align-items-center gap-2">
                            <?php if ($filter_journal !== 'all'): ?>
                                <span class="o_facet">
                                    <i class="fas fa-times o_facet_remove" onclick="removeFilter('journal')"></i>
                                    اليومية: <?php
                                        foreach ($journals as $journal) {
                                            if ($journal['id'] == $filter_journal) {
                                                echo $journal['name'];
                                                break;
                                            }
                                        }
                                    ?>
                                </span>
                            <?php endif; ?>

                            <?php if ($filter_state !== 'all'): ?>
                                <span class="o_facet">
                                    <i class="fas fa-times o_facet_remove" onclick="removeFilter('state')"></i>
                                    الحالة: <?php echo $move_states[$filter_state]['name']; ?>
                                </span>
                            <?php endif; ?>
                        </div>

                        <!-- فلترة حسب اليومية -->
                        <select class="form-select form-select-sm" style="width: auto;" onchange="filterByJournal(this.value)">
                            <option value="all">جميع اليوميات</option>
                            <?php foreach ($journals as $journal): ?>
                                <option value="<?php echo $journal['id']; ?>" <?php echo $filter_journal == $journal['id'] ? 'selected' : ''; ?>>
                                    <?php echo $journal['name']; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>

                        <!-- فلترة حسب الحالة -->
                        <select class="form-select form-select-sm" style="width: auto;" onchange="filterByState(this.value)">
                            <option value="all">جميع الحالات</option>
                            <?php foreach ($move_states as $state => $info): ?>
                                <option value="<?php echo $state; ?>" <?php echo $filter_state == $state ? 'selected' : ''; ?>>
                                    <?php echo $info['name']; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>

                        <!-- أزرار العرض -->
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-secondary <?php echo $view_mode === 'list' ? 'active' : ''; ?>"
                                    onclick="changeView('list')" title="عرض قائمة">
                                <i class="fas fa-list"></i>
                            </button>
                            <button class="btn btn-outline-secondary <?php echo $view_mode === 'kanban' ? 'active' : ''; ?>"
                                    onclick="changeView('kanban')" title="عرض بطاقات">
                                <i class="fas fa-th-large"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- شريط الإجراءات المجمعة -->
                <div class="bulk-actions" id="bulkActions">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <span id="selectedCount">0</span> قيد محدد
                        </div>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-success" onclick="bulkAction('post')">
                                <i class="fas fa-check me-1"></i>ترحيل المحدد
                            </button>
                            <button class="btn btn-danger" onclick="bulkAction('delete')">
                                <i class="fas fa-trash me-1"></i>حذف المحدد
                            </button>
                            <button class="btn btn-secondary" onclick="clearSelection()">
                                <i class="fas fa-times me-1"></i>إلغاء التحديد
                            </button>
                        </div>
                    </div>
                </div>

                <!-- عرض القيود -->
                <div class="flex-grow-1" style="overflow: auto;">
                    <?php if ($view_mode === 'list'): ?>
                        <!-- عرض الجدول -->
                        <div class="o_list_view">
                            <table class="o_list_table">
                                <thead>
                                    <tr>
                                        <th class="o_list_record_selector">
                                            <input type="checkbox" id="selectAll">
                                        </th>
                                        <th class="o_column_sortable" data-sort="name">
                                            رقم القيد
                                        </th>
                                        <th class="o_column_sortable" data-sort="date">
                                            التاريخ
                                        </th>
                                        <th class="o_column_sortable" data-sort="journal">
                                            اليومية
                                        </th>
                                        <th class="o_column_sortable" data-sort="ref">
                                            المرجع
                                        </th>
                                        <th class="o_column_sortable o_list_monetary" data-sort="amount">
                                            المبلغ
                                        </th>
                                        <th class="o_column_sortable" data-sort="state">
                                            الحالة
                                        </th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($moves as $move): ?>
                                        <tr class="o_data_row" data-id="<?php echo $move['id']; ?>">
                                            <td class="o_list_record_selector">
                                                <input type="checkbox" class="record-checkbox" value="<?php echo $move['id']; ?>">
                                            </td>
                                            <td>
                                                <strong><?php echo $move['name']; ?></strong>
                                            </td>
                                            <td class="o_list_date">
                                                <?php echo date('Y/m/d', strtotime($move['date'])); ?>
                                            </td>
                                            <td>
                                                <?php
                                                $journal_name = 'غير محدد';
                                                foreach ($journals as $journal) {
                                                    if ($journal['id'] == $move['journal_id']) {
                                                        $journal_name = $journal['name'];
                                                        break;
                                                    }
                                                }
                                                echo $journal_name;
                                                ?>
                                            </td>
                                            <td><?php echo !empty($move['ref']) ? $move['ref'] : '-'; ?></td>
                                            <td class="o_list_monetary">
                                                <?php echo number_format($move['amount_total'], 2); ?> ر.س
                                            </td>
                                            <td>
                                                <span class="o_status_badge <?php echo $move['state']; ?>">
                                                    <?php echo $move_states[$move['state']]['name']; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary btn-sm" onclick="viewMove(<?php echo $move['id']; ?>)" title="عرض">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <?php if ($move['state'] === 'draft'): ?>
                                                        <button class="btn btn-outline-success btn-sm" onclick="postMove(<?php echo $move['id']; ?>)" title="ترحيل">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                        <button class="btn btn-outline-warning btn-sm" onclick="editMove(<?php echo $move['id']; ?>)" title="تعديل">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                    <button class="btn btn-outline-secondary btn-sm dropdown-toggle" data-bs-toggle="dropdown" title="المزيد">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li><a class="dropdown-item" href="#" onclick="duplicateMove(<?php echo $move['id']; ?>)">
                                                            <i class="fas fa-copy me-2"></i>نسخ
                                                        </a></li>
                                                        <li><a class="dropdown-item" href="#" onclick="printMove(<?php echo $move['id']; ?>)">
                                                            <i class="fas fa-print me-2"></i>طباعة
                                                        </a></li>
                                                        <?php if ($move['state'] === 'draft'): ?>
                                                            <li><hr class="dropdown-divider"></li>
                                                            <li><a class="dropdown-item text-danger" href="#" onclick="deleteMove(<?php echo $move['id']; ?>)">
                                                                <i class="fas fa-trash me-2"></i>حذف
                                                            </a></li>
                                                        <?php endif; ?>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <!-- عرض البطاقات -->
                        <div class="o_kanban_view">
                            <div class="row">
                                <?php foreach ($moves as $move): ?>
                                    <div class="col-md-6 col-lg-4 mb-3">
                                        <div class="o_kanban_record <?php echo $move['state']; ?>" data-id="<?php echo $move['id']; ?>">
                                            <div class="d-flex justify-content-between align-items-start mb-3">
                                                <h6 class="mb-0"><?php echo $move['name']; ?></h6>
                                                <span class="o_status_badge <?php echo $move['state']; ?>">
                                                    <?php echo $move_states[$move['state']]['name']; ?>
                                                </span>
                                            </div>
                                            <div class="mb-3">
                                                <div class="row">
                                                    <div class="col-6">
                                                        <small class="text-muted">التاريخ:</small><br>
                                                        <strong><?php echo date('Y/m/d', strtotime($move['date'])); ?></strong>
                                                    </div>
                                                    <div class="col-6">
                                                        <small class="text-muted">المبلغ:</small><br>
                                                        <strong><?php echo number_format($move['amount_total'], 2); ?> ر.س</strong>
                                                    </div>
                                                </div>
                                                <div class="mt-2">
                                                    <small class="text-muted">اليومية:</small><br>
                                                    <?php
                                                    $journal_name = 'غير محدد';
                                                    foreach ($journals as $journal) {
                                                        if ($journal['id'] == $move['journal_id']) {
                                                            $journal_name = $journal['name'];
                                                            break;
                                                        }
                                                    }
                                                    echo $journal_name;
                                                    ?>
                                                </div>
                                            </div>
                                            <div class="d-flex gap-2">
                                                <button class="btn btn-outline-primary btn-sm flex-fill" onclick="viewMove(<?php echo $move['id']; ?>)">
                                                    <i class="fas fa-eye me-1"></i>عرض
                                                </button>
                                                <?php if ($move['state'] === 'draft'): ?>
                                                    <button class="btn btn-outline-success btn-sm flex-fill" onclick="postMove(<?php echo $move['id']; ?>)">
                                                        <i class="fas fa-check me-1"></i>ترحيل
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- تراكب التحميل -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <div class="mt-2">جاري المعالجة...</div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // متغيرات عامة
        let selectedRecords = new Set();

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializeEventListeners();
            updateSelectedCount();
        });

        // تهيئة معالجات الأحداث
        function initializeEventListeners() {
            // معالج تحديد الكل
            const selectAllCheckbox = document.getElementById('selectAll');
            if (selectAllCheckbox) {
                selectAllCheckbox.addEventListener('change', function() {
                    const checkboxes = document.querySelectorAll('.record-checkbox');
                    checkboxes.forEach(checkbox => {
                        checkbox.checked = this.checked;
                        toggleRowSelection(checkbox, false);
                    });
                    updateSelectedCount();
                });
            }

            // معالج تحديد الصفوف الفردية
            document.querySelectorAll('.record-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    toggleRowSelection(this);
                    updateSelectAllState();
                    updateSelectedCount();
                });
            });

            // معالج البحث
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                let searchTimeout;
                searchInput.addEventListener('input', function() {
                    clearTimeout(searchTimeout);
                    searchTimeout = setTimeout(() => {
                        performSearch(this.value);
                    }, 300);
                });
            }
        }

        // تحديد/إلغاء تحديد صف
        function toggleRowSelection(checkbox, updateSelectAll = true) {
            const row = checkbox.closest('tr') || checkbox.closest('.o_kanban_record');
            const recordId = checkbox.value;

            if (checkbox.checked) {
                row.classList.add('o_selected_row');
                selectedRecords.add(recordId);
            } else {
                row.classList.remove('o_selected_row');
                selectedRecords.delete(recordId);
            }

            if (updateSelectAll) {
                updateSelectAllState();
            }
        }

        // تحديث حالة "تحديد الكل"
        function updateSelectAllState() {
            const allCheckboxes = document.querySelectorAll('.record-checkbox');
            const checkedCheckboxes = document.querySelectorAll('.record-checkbox:checked');
            const selectAllCheckbox = document.getElementById('selectAll');

            if (!selectAllCheckbox) return;

            if (checkedCheckboxes.length === allCheckboxes.length && allCheckboxes.length > 0) {
                selectAllCheckbox.checked = true;
                selectAllCheckbox.indeterminate = false;
            } else if (checkedCheckboxes.length > 0) {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = true;
            } else {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = false;
            }
        }

        // تحديث عدد المحدد
        function updateSelectedCount() {
            const count = selectedRecords.size;
            const selectedCountElement = document.getElementById('selectedCount');
            const bulkActionsElement = document.getElementById('bulkActions');

            if (selectedCountElement) {
                selectedCountElement.textContent = count;
            }

            if (bulkActionsElement) {
                if (count > 0) {
                    bulkActionsElement.classList.add('show');
                } else {
                    bulkActionsElement.classList.remove('show');
                }
            }
        }

        // إلغاء التحديد
        function clearSelection() {
            selectedRecords.clear();
            document.querySelectorAll('.record-checkbox').forEach(checkbox => {
                checkbox.checked = false;
                toggleRowSelection(checkbox, false);
            });
            updateSelectAllState();
            updateSelectedCount();
        }

        // وظائف الإجراءات
        function viewMove(moveId) {
            window.open('view_move.php?id=' + moveId, '_blank', 'width=800,height=600');
        }

        function editMove(moveId) {
            window.location.href = 'create_entry_enhanced.php?edit=' + moveId;
        }

        function postMove(moveId) {
            if (confirm('هل أنت متأكد من ترحيل هذا القيد؟')) {
                showLoading();

                fetch(window.location.href, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=post_move&move_id=' + moveId
                })
                .then(response => response.json())
                .then(data => {
                    hideLoading();
                    if (data.success) {
                        showMessage(data.message, 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showMessage(data.message || 'حدث خطأ أثناء الترحيل', 'danger');
                    }
                })
                .catch(error => {
                    hideLoading();
                    showMessage('حدث خطأ في الاتصال', 'danger');
                });
            }
        }

        function duplicateMove(moveId) {
            window.location.href = 'create_entry_enhanced.php?duplicate=' + moveId;
        }

        function printMove(moveId) {
            window.open('print_move.php?id=' + moveId, '_blank');
        }

        function deleteMove(moveId) {
            if (confirm('هل أنت متأكد من حذف هذا القيد؟')) {
                showLoading();

                fetch(window.location.href, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=delete_move&move_id=' + moveId
                })
                .then(response => response.json())
                .then(data => {
                    hideLoading();
                    if (data.success) {
                        showMessage(data.message, 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showMessage(data.message || 'حدث خطأ أثناء الحذف', 'danger');
                    }
                })
                .catch(error => {
                    hideLoading();
                    showMessage('حدث خطأ في الاتصال', 'danger');
                });
            }
        }

        // الإجراءات المجمعة
        function bulkAction(action) {
            if (selectedRecords.size === 0) {
                showMessage('يرجى تحديد قيد واحد على الأقل', 'warning');
                return;
            }

            const actionText = action === 'post' ? 'ترحيل' : 'حذف';
            if (confirm(`هل أنت متأكد من ${actionText} ${selectedRecords.size} قيد؟`)) {
                showLoading();

                const formData = new FormData();
                formData.append('action', 'bulk_action');
                formData.append('bulk_action', action);
                selectedRecords.forEach(id => {
                    formData.append('selected_ids[]', id);
                });

                fetch(window.location.href, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    hideLoading();
                    if (data.success) {
                        showMessage(data.message, 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showMessage(data.message || 'حدث خطأ أثناء العملية', 'danger');
                    }
                })
                .catch(error => {
                    hideLoading();
                    showMessage('حدث خطأ في الاتصال', 'danger');
                });
            }
        }

        // وظائف الفلترة والبحث
        function filterByJournal(journalId) {
            const url = new URL(window.location);
            url.searchParams.set('journal', journalId);
            window.location.href = url.toString();
        }

        function filterByState(state) {
            const url = new URL(window.location);
            url.searchParams.set('state', state);
            window.location.href = url.toString();
        }

        function changeView(viewMode) {
            const url = new URL(window.location);
            url.searchParams.set('view', viewMode);
            window.location.href = url.toString();
        }

        function performSearch(searchTerm) {
            const url = new URL(window.location);
            if (searchTerm.trim()) {
                url.searchParams.set('search', searchTerm);
            } else {
                url.searchParams.delete('search');
            }
            window.location.href = url.toString();
        }

        function removeFilter(filterType) {
            const url = new URL(window.location);
            url.searchParams.delete(filterType);
            window.location.href = url.toString();
        }

        function sortTable(column) {
            console.log('Sorting by:', column);
            // يمكن تنفيذ الترتيب هنا
        }

        function refreshData() {
            location.reload();
        }

        function importEntries() {
            showMessage('ميزة الاستيراد قيد التطوير', 'info');
        }

        function exportData(format) {
            showMessage(`جاري تصدير البيانات بصيغة ${format}...`, 'info');
            // يمكن تنفيذ التصدير هنا
        }

        function printData() {
            window.print();
        }

        // وظائف المساعدة
        function showLoading() {
            const overlay = document.getElementById('loadingOverlay');
            if (overlay) {
                overlay.style.display = 'flex';
            }
        }

        function hideLoading() {
            const overlay = document.getElementById('loadingOverlay');
            if (overlay) {
                overlay.style.display = 'none';
            }
        }

        function showMessage(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';

            const iconClass = type === 'success' ? 'check-circle' :
                             type === 'danger' ? 'exclamation-triangle' :
                             type === 'warning' ? 'exclamation-triangle' : 'info-circle';

            alertDiv.innerHTML = `
                <i class="fas fa-${iconClass} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(alertDiv);

            setTimeout(() => {
                alertDiv.remove();
            }, 4000);
        }

        // تأثيرات بصرية إضافية
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير hover للبطاقات
            document.querySelectorAll('.o_kanban_record').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // تأثير النقر على الصفوف
            document.querySelectorAll('.o_data_row').forEach(row => {
                row.addEventListener('click', function(e) {
                    if (!e.target.closest('input') && !e.target.closest('button') && !e.target.closest('.dropdown')) {
                        const checkbox = this.querySelector('.record-checkbox');
                        if (checkbox) {
                            checkbox.checked = !checkbox.checked;
                            checkbox.dispatchEvent(new Event('change'));
                        }
                    }
                });
            });
        });
    </script>
</body>
</html>