<?php
/**
 * صفحة إدارة اليوميات المحاسبية بأسلوب Odoo
 * Journals Management Page - Odoo Style
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// تحميل نظام Odoo
require_once '../config/odoo_config.php';
require_once '../config/odoo_database.php';

$odoo_db = OdooDatabase::getInstance();

// تحميل النماذج
require_once '../models/BaseModel.php';
require_once '../models/AccountJournal.php';
require_once '../models/AccountAccount.php';
require_once '../models/AccountMove.php';
require_once '../models/ResCurrency.php';

$journal_model = new AccountJournal($odoo_db);
$account_model = new AccountAccount($odoo_db);
$move_model = new AccountMove($odoo_db);
$currency_model = new ResCurrency($odoo_db);

$message = '';
$message_type = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = isset($_POST['action']) ? $_POST['action'] : '';
    
    try {
        switch ($action) {
            case 'create_journal':
                $data = array(
                    'name' => $_POST['name'],
                    'code' => $_POST['code'],
                    'type' => $_POST['type'],
                    'default_account_id' => !empty($_POST['default_account_id']) ? $_POST['default_account_id'] : null,
                    'active' => isset($_POST['active'])
                );
                
                $journal_model->create_journal($data);
                $message = "تم إنشاء اليومية بنجاح";
                $message_type = 'success';
                break;
                
            case 'update_journal':
                $journal_id = $_POST['journal_id'];
                $data = array(
                    'name' => $_POST['name'],
                    'type' => $_POST['type'],
                    'default_account_id' => !empty($_POST['default_account_id']) ? $_POST['default_account_id'] : null,
                    'active' => isset($_POST['active'])
                );
                
                $journal_model->update($journal_id, $data);
                $message = "تم تحديث اليومية بنجاح";
                $message_type = 'success';
                break;
                
            default:
                throw new Exception('إجراء غير صحيح');
        }
    } catch (Exception $e) {
        $message = 'خطأ: ' . $e->getMessage();
        $message_type = 'error';
    }
}

// جلب البيانات
$journals = $journal_model->get_demo_data();
$accounts = $account_model->get_demo_data();

// فلترة حسب النوع
$filter_type = isset($_GET['filter']) ? $_GET['filter'] : 'all';
if ($filter_type !== 'all') {
    $filtered_journals = array();
    foreach ($journals as $journal) {
        if ($journal['type'] === $filter_type) {
            $filtered_journals[] = $journal;
        }
    }
    $journals = $filtered_journals;
}

// طريقة العرض المحددة
$view_mode = isset($_GET['view']) ? $_GET['view'] : 'cards';

// أنواع اليوميات
$journal_types = array(
    'sale' => array('name' => 'مبيعات', 'color' => 'success', 'icon' => 'fas fa-shopping-cart'),
    'purchase' => array('name' => 'مشتريات', 'color' => 'primary', 'icon' => 'fas fa-shopping-bag'),
    'cash' => array('name' => 'نقدية', 'color' => 'warning', 'icon' => 'fas fa-money-bill'),
    'bank' => array('name' => 'بنكية', 'color' => 'info', 'icon' => 'fas fa-university'),
    'general' => array('name' => 'عامة', 'color' => 'secondary', 'icon' => 'fas fa-cogs')
);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة اليوميات - نظام ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --odoo-primary: #714B67;
            --odoo-secondary: #8B5A8C;
        }
        
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 0.85rem;
        }
        
        .navbar-odoo {
            background: linear-gradient(45deg, var(--odoo-primary), var(--odoo-secondary));
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .page-header {
            background: linear-gradient(45deg, #F39C12, #E67E22);
            color: white;
            padding: 1.5rem;
            border-radius: 15px;
            margin-bottom: 1.5rem;
        }
        
        .view-controls {
            background: white;
            border-radius: 10px;
            padding: 0.8rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        
        .view-btn, .filter-btn {
            border: 1px solid #dee2e6;
            background: white;
            color: #6c757d;
            padding: 0.4rem 0.8rem;
            border-radius: 5px;
            transition: all 0.3s ease;
            text-decoration: none;
            font-size: 0.8rem;
        }
        
        .view-btn.active, .filter-btn.active {
            background: #F39C12;
            color: white;
            border-color: #F39C12;
        }
        
        .view-btn:hover, .filter-btn:hover {
            background: #E67E22;
            color: white;
            border-color: #E67E22;
            text-decoration: none;
        }
        
        .journal-card {
            background: white;
            border-radius: 12px;
            padding: 1.2rem;
            margin-bottom: 0.8rem;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
            transition: all 0.3s ease;
            border-left: 3px solid;
        }
        
        .journal-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.12);
        }
        
        .journal-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }
        
        .table-odoo {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
        }
        
        .table-odoo th {
            background: linear-gradient(45deg, #F39C12, #E67E22);
            color: white;
            border: none;
            padding: 0.8rem;
            font-size: 0.8rem;
        }
        
        .table-odoo td {
            padding: 0.8rem;
            border-color: #f8f9fa;
            vertical-align: middle;
            font-size: 0.8rem;
        }
        
        .breadcrumb-odoo {
            background: white;
            border-radius: 8px;
            padding: 0.8rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            font-size: 0.8rem;
        }
        
        .stats-card {
            background: white;
            border-radius: 12px;
            padding: 1.2rem;
            text-align: center;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
            border-left: 3px solid;
            margin-bottom: 1rem;
        }
        
        .journal-stats {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 0.8rem;
            margin-top: 0.5rem;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.2rem 0;
            font-size: 0.75rem;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-odoo">
        <div class="container-fluid">
            <a class="navbar-brand text-white" href="../dashboard.php">
                <i class="fas fa-cube me-2"></i>
                نظام ERP - إدارة اليوميات
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="../dashboard.php">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-3">
        <!-- مسار التنقل -->
        <nav class="breadcrumb-odoo">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="../dashboard.php">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="#">المحاسبة</a></li>
                <li class="breadcrumb-item active">إدارة اليوميات</li>
            </ol>
        </nav>

        <!-- العنوان الرئيسي -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h3><i class="fas fa-book-open me-2"></i>إدارة اليوميات المحاسبية</h3>
                    <p class="mb-0 small">إدارة وتنظيم اليوميات المحاسبية</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#addJournalModal">
                        <i class="fas fa-plus me-2"></i>إضافة يومية جديدة
                    </button>
                </div>
            </div>
        </div>

        <!-- رسائل النظام -->
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $message_type === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
                <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- إحصائيات اليوميات -->
        <div class="row mb-3">
            <?php foreach ($journal_types as $type => $info): ?>
                <div class="col-md-2">
                    <div class="stats-card border-<?php echo $info['color']; ?>">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <i class="<?php echo $info['icon']; ?> text-<?php echo $info['color']; ?>" style="font-size: 1.5rem;"></i>
                        </div>
                        <h4 class="text-<?php echo $info['color']; ?>"><?php
                            $count = 0;
                            foreach($journal_model->get_demo_data() as $journal) {
                                if($journal['type'] === $type) $count++;
                            }
                            echo $count;
                        ?></h4>
                        <p class="mb-0 small"><?php echo $info['name']; ?></p>
                    </div>
                </div>
            <?php endforeach; ?>
            <div class="col-md-2">
                <div class="stats-card border-dark">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <i class="fas fa-list text-dark" style="font-size: 1.5rem;"></i>
                    </div>
                    <h4 class="text-dark"><?php echo count($journal_model->get_demo_data()); ?></h4>
                    <p class="mb-0 small">إجمالي اليوميات</p>
                </div>
            </div>
        </div>

        <!-- أدوات التحكم والعرض -->
        <div class="view-controls">
            <div class="row align-items-center">
                <div class="col-md-3">
                    <div class="btn-group" role="group">
                        <a href="?view=cards&filter=<?php echo $filter_type; ?>" class="view-btn <?php echo $view_mode === 'cards' ? 'active' : ''; ?>">
                            <i class="fas fa-th-large me-1"></i>بطاقات
                        </a>
                        <a href="?view=table&filter=<?php echo $filter_type; ?>" class="view-btn <?php echo $view_mode === 'table' ? 'active' : ''; ?>">
                            <i class="fas fa-table me-1"></i>جدول
                        </a>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="btn-group" role="group">
                        <a href="?view=<?php echo $view_mode; ?>&filter=all" class="filter-btn <?php echo $filter_type === 'all' ? 'active' : ''; ?>">
                            الكل
                        </a>
                        <?php foreach ($journal_types as $type => $info): ?>
                            <a href="?view=<?php echo $view_mode; ?>&filter=<?php echo $type; ?>" class="filter-btn <?php echo $filter_type === $type ? 'active' : ''; ?>">
                                <?php echo $info['name']; ?>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="d-flex justify-content-end">
                        <input type="text" class="form-control search-box" placeholder="البحث في اليوميات..." style="max-width: 200px; font-size: 0.8rem;">
                    </div>
                </div>
            </div>
        </div>

        <!-- محتوى العرض -->
        <?php if ($view_mode === 'cards'): ?>
            <!-- عرض البطاقات -->
            <div class="row">
                <?php foreach ($journals as $journal):
                    $type_info = $journal_types[$journal['type']];
                    $journal_stats = $journal_model->get_journal_statistics($journal['id']);

                    // البحث عن الحساب الافتراضي
                    $default_account_name = 'غير محدد';
                    if ($journal['default_account_id']) {
                        foreach ($accounts as $account) {
                            if ($account['id'] == $journal['default_account_id']) {
                                $default_account_name = $account['name'];
                                break;
                            }
                        }
                    }
                ?>
                    <div class="col-md-6 col-lg-4">
                        <div class="journal-card border-<?php echo $type_info['color']; ?>">
                            <div class="d-flex align-items-center mb-2">
                                <div class="journal-icon me-3 bg-<?php echo $type_info['color']; ?>">
                                    <i class="<?php echo $type_info['icon']; ?>"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1"><?php echo $journal['name']; ?></h6>
                                    <p class="text-muted mb-0 small"><?php echo $journal['code']; ?></p>
                                </div>
                                <div>
                                    <?php if ($journal['active']): ?>
                                        <span class="badge bg-success">نشط</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">غير نشط</span>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="mb-2">
                                <span class="badge bg-<?php echo $type_info['color']; ?>"><?php echo $type_info['name']; ?></span>
                            </div>

                            <div class="mb-2">
                                <small class="text-muted">الحساب الافتراضي:</small><br>
                                <small><?php echo $default_account_name; ?></small>
                            </div>

                            <!-- إحصائيات اليومية -->
                            <div class="journal-stats">
                                <div class="stat-item">
                                    <span>إجمالي القيود:</span>
                                    <strong><?php echo $journal_stats['total_moves']; ?></strong>
                                </div>
                                <div class="stat-item">
                                    <span>قيود معتمدة:</span>
                                    <strong class="text-success"><?php echo $journal_stats['posted_moves']; ?></strong>
                                </div>
                                <div class="stat-item">
                                    <span>مسودات:</span>
                                    <strong class="text-warning"><?php echo $journal_stats['draft_moves']; ?></strong>
                                </div>
                                <div class="stat-item">
                                    <span>إجمالي المبلغ:</span>
                                    <strong class="text-primary"><?php echo number_format($journal_stats['total_amount'], 0); ?> ر.س</strong>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between mt-2">
                                <button class="btn btn-outline-primary btn-sm" onclick="editJournal(<?php echo $journal['id']; ?>)">
                                    <i class="fas fa-edit me-1"></i>تعديل
                                </button>
                                <button class="btn btn-outline-info btn-sm" onclick="viewJournal(<?php echo $journal['id']; ?>)">
                                    <i class="fas fa-eye me-1"></i>عرض
                                </button>
                                <button class="btn btn-outline-success btn-sm" onclick="createEntry(<?php echo $journal['id']; ?>)">
                                    <i class="fas fa-plus me-1"></i>قيد جديد
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

        <?php else: ?>
            <!-- عرض الجدول -->
            <div class="table-odoo">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>الكود</th>
                            <th>اسم اليومية</th>
                            <th>النوع</th>
                            <th>الحساب الافتراضي</th>
                            <th>إجمالي القيود</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($journals as $journal):
                            $type_info = $journal_types[$journal['type']];
                            $journal_stats = $journal_model->get_journal_statistics($journal['id']);

                            // البحث عن الحساب الافتراضي
                            $default_account_name = 'غير محدد';
                            if ($journal['default_account_id']) {
                                foreach ($accounts as $account) {
                                    if ($account['id'] == $journal['default_account_id']) {
                                        $default_account_name = $account['name'];
                                        break;
                                    }
                                }
                            }
                        ?>
                            <tr>
                                <td><strong><?php echo $journal['code']; ?></strong></td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="<?php echo $type_info['icon']; ?> text-<?php echo $type_info['color']; ?> me-2"></i>
                                        <?php echo $journal['name']; ?>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo $type_info['color']; ?>"><?php echo $type_info['name']; ?></span>
                                </td>
                                <td><?php echo $default_account_name; ?></td>
                                <td>
                                    <div class="small">
                                        <div>المعتمدة: <span class="text-success"><?php echo $journal_stats['posted_moves']; ?></span></div>
                                        <div>المسودات: <span class="text-warning"><?php echo $journal_stats['draft_moves']; ?></span></div>
                                    </div>
                                </td>
                                <td>
                                    <?php if ($journal['active']): ?>
                                        <span class="badge bg-success">نشط</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">غير نشط</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary btn-sm" onclick="editJournal(<?php echo $journal['id']; ?>)">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-info btn-sm" onclick="viewJournal(<?php echo $journal['id']; ?>)">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-success btn-sm" onclick="createEntry(<?php echo $journal['id']; ?>)">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editJournal(journalId) {
            alert('سيتم تطوير نافذة تعديل اليومية قريباً');
        }

        function viewJournal(journalId) {
            window.location.href = 'journal_entries.php?journal=' + journalId;
        }

        function createEntry(journalId) {
            alert('سيتم تطوير نافذة إنشاء قيد جديد قريباً');
        }

        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const items = document.querySelectorAll('.journal-card, tbody tr');
            items.forEach((item, index) => {
                item.style.opacity = '0';
                item.style.transform = 'translateY(15px)';

                setTimeout(() => {
                    item.style.transition = 'all 0.5s ease';
                    item.style.opacity = '1';
                    item.style.transform = 'translateY(0)';
                }, index * 80);
            });
        });

        // البحث المباشر
        document.querySelector('.search-box').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const items = document.querySelectorAll('.journal-card, tbody tr');

            items.forEach(item => {
                const text = item.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    item.style.display = '';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
