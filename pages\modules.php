<?php
/**
 * صفحة إدارة الوحدات بأسلوب Odoo
 * Odoo-Style Module Management Page
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// التحقق من الصلاحيات
if (!in_array('admin', $_SESSION['groups'] ?? array())) {
    header('Location: ../dashboard.php?error=access_denied');
    exit();
}

// تحميل نظام الوحدات
require_once '../includes/odoo_modules.php';

$module_manager = OdooModuleManager::getInstance();
$message = '';
$message_type = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $module_name = $_POST['module_name'] ?? '';
    
    try {
        switch ($action) {
            case 'install':
                $module_manager->installModule($module_name);
                $message = "تم تثبيت الوحدة '{$module_name}' بنجاح";
                $message_type = 'success';
                break;
                
            case 'uninstall':
                $module_manager->uninstallModule($module_name);
                $message = "تم إلغاء تثبيت الوحدة '{$module_name}' بنجاح";
                $message_type = 'success';
                break;
                
            default:
                $message = 'إجراء غير صحيح';
                $message_type = 'error';
        }
    } catch (Exception $e) {
        $message = 'خطأ: ' . $e->getMessage();
        $message_type = 'error';
    }
}

$installed_modules = $module_manager->getInstalledModules();
$available_modules = $module_manager->getAvailableModules();
$all_modules = $module_manager->getAllModules();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الوحدات - نظام ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --odoo-primary: #714B67;
            --odoo-secondary: #8B5A8C;
        }
        
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar-odoo {
            background: linear-gradient(45deg, var(--odoo-primary), var(--odoo-secondary));
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .module-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            border-left: 4px solid #dee2e6;
        }
        
        .module-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .module-card.installed {
            border-left-color: #28a745;
            background: linear-gradient(135deg, #d4edda 0%, #ffffff 100%);
        }
        
        .module-card.available {
            border-left-color: #007bff;
        }
        
        .module-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-bottom: 1rem;
        }
        
        .module-header {
            background: linear-gradient(45deg, var(--odoo-primary), var(--odoo-secondary));
            color: white;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
        }
        
        .btn-odoo {
            background: linear-gradient(45deg, var(--odoo-primary), var(--odoo-secondary));
            border: none;
            color: white;
            padding: 0.5rem 1.5rem;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        
        .btn-odoo:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(113, 75, 103, 0.3);
            color: white;
        }
        
        .btn-uninstall {
            background: linear-gradient(45deg, #dc3545, #c82333);
            border: none;
            color: white;
            padding: 0.5rem 1.5rem;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        
        .btn-uninstall:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
            color: white;
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 4px solid;
        }
        
        .breadcrumb-odoo {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-odoo">
        <div class="container-fluid">
            <a class="navbar-brand text-white" href="../dashboard.php">
                <i class="fas fa-cube me-2"></i>
                نظام ERP - إدارة الوحدات
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="../dashboard.php">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- مسار التنقل -->
        <nav class="breadcrumb-odoo">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="../dashboard.php">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="#">إدارة النظام</a></li>
                <li class="breadcrumb-item active">إدارة الوحدات</li>
            </ol>
        </nav>

        <!-- العنوان الرئيسي -->
        <div class="module-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2><i class="fas fa-puzzle-piece me-2"></i>إدارة الوحدات</h2>
                    <p class="mb-0">إدارة وتثبيت وحدات نظام Odoo</p>
                </div>
                <div class="col-md-4 text-end">
                    <i class="fas fa-cogs fa-3x opacity-50"></i>
                </div>
            </div>
        </div>

        <!-- رسائل النظام -->
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $message_type === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
                <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- إحصائيات الوحدات -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card border-success">
                    <h3 class="text-success"><?php echo count($installed_modules); ?></h3>
                    <p class="mb-0">وحدات مثبتة</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card border-primary">
                    <h3 class="text-primary"><?php echo count($available_modules); ?></h3>
                    <p class="mb-0">وحدات متاحة</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card border-info">
                    <h3 class="text-info"><?php echo count($all_modules); ?></h3>
                    <p class="mb-0">إجمالي الوحدات</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card border-warning">
                    <h3 class="text-warning">8</h3>
                    <p class="mb-0">تطبيقات رئيسية</p>
                </div>
            </div>
        </div>

        <!-- تبويبات الوحدات -->
        <ul class="nav nav-tabs mb-4" id="modulesTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="installed-tab" data-bs-toggle="tab" data-bs-target="#installed" type="button" role="tab">
                    <i class="fas fa-check-circle me-2"></i>الوحدات المثبتة (<?php echo count($installed_modules); ?>)
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="available-tab" data-bs-toggle="tab" data-bs-target="#available" type="button" role="tab">
                    <i class="fas fa-download me-2"></i>الوحدات المتاحة (<?php echo count($available_modules); ?>)
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="all-tab" data-bs-toggle="tab" data-bs-target="#all" type="button" role="tab">
                    <i class="fas fa-list me-2"></i>جميع الوحدات (<?php echo count($all_modules); ?>)
                </button>
            </li>
        </ul>

        <!-- محتوى التبويبات -->
        <div class="tab-content" id="modulesTabContent">
            <!-- الوحدات المثبتة -->
            <div class="tab-pane fade show active" id="installed" role="tabpanel">
                <div class="row">
                    <?php foreach ($installed_modules as $name => $module): ?>
                        <div class="col-md-6 col-lg-4">
                            <div class="module-card installed">
                                <div class="module-icon" style="background-color: <?php echo $module['color']; ?>">
                                    <i class="<?php echo $module['icon']; ?>"></i>
                                </div>
                                <h5><?php echo $module['name']; ?></h5>
                                <p class="text-muted small"><?php echo $module['description']; ?></p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="badge bg-success">مثبتة</span>
                                    <div>
                                        <?php if ($name !== 'base'): ?>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="uninstall">
                                                <input type="hidden" name="module_name" value="<?php echo $name; ?>">
                                                <button type="submit" class="btn btn-uninstall btn-sm" 
                                                        onclick="return confirm('هل أنت متأكد من إلغاء تثبيت هذه الوحدة؟')">
                                                    <i class="fas fa-trash me-1"></i>إلغاء التثبيت
                                                </button>
                                            </form>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">وحدة أساسية</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <small class="text-muted">
                                        <i class="fas fa-tag me-1"></i>الإصدار: <?php echo $module['version']; ?>
                                        <br>
                                        <i class="fas fa-folder me-1"></i>الفئة: <?php echo $module['category']; ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- الوحدات المتاحة -->
            <div class="tab-pane fade" id="available" role="tabpanel">
                <div class="row">
                    <?php foreach ($available_modules as $name => $module): ?>
                        <div class="col-md-6 col-lg-4">
                            <div class="module-card available">
                                <div class="module-icon" style="background-color: <?php echo $module['color']; ?>">
                                    <i class="<?php echo $module['icon']; ?>"></i>
                                </div>
                                <h5><?php echo $module['name']; ?></h5>
                                <p class="text-muted small"><?php echo $module['description']; ?></p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="badge bg-primary">متاحة للتثبيت</span>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="action" value="install">
                                        <input type="hidden" name="module_name" value="<?php echo $name; ?>">
                                        <button type="submit" class="btn btn-odoo btn-sm">
                                            <i class="fas fa-download me-1"></i>تثبيت
                                        </button>
                                    </form>
                                </div>
                                <div class="mt-2">
                                    <small class="text-muted">
                                        <i class="fas fa-tag me-1"></i>الإصدار: <?php echo $module['version']; ?>
                                        <br>
                                        <i class="fas fa-folder me-1"></i>الفئة: <?php echo $module['category']; ?>
                                        <?php if (!empty($module['depends'])): ?>
                                            <br>
                                            <i class="fas fa-link me-1"></i>يعتمد على: <?php echo implode(', ', $module['depends']); ?>
                                        <?php endif; ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- جميع الوحدات -->
            <div class="tab-pane fade" id="all" role="tabpanel">
                <div class="row">
                    <?php foreach ($all_modules as $name => $module): ?>
                        <div class="col-md-6 col-lg-4">
                            <div class="module-card <?php echo in_array($name, array_keys($installed_modules)) ? 'installed' : 'available'; ?>">
                                <div class="module-icon" style="background-color: <?php echo $module['color']; ?>">
                                    <i class="<?php echo $module['icon']; ?>"></i>
                                </div>
                                <h5><?php echo $module['name']; ?></h5>
                                <p class="text-muted small"><?php echo $module['description']; ?></p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <?php if (in_array($name, array_keys($installed_modules))): ?>
                                        <span class="badge bg-success">مثبتة</span>
                                        <?php if ($name !== 'base'): ?>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="uninstall">
                                                <input type="hidden" name="module_name" value="<?php echo $name; ?>">
                                                <button type="submit" class="btn btn-uninstall btn-sm" 
                                                        onclick="return confirm('هل أنت متأكد من إلغاء تثبيت هذه الوحدة؟')">
                                                    <i class="fas fa-trash me-1"></i>إلغاء التثبيت
                                                </button>
                                            </form>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <span class="badge bg-primary">متاحة للتثبيت</span>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="install">
                                            <input type="hidden" name="module_name" value="<?php echo $name; ?>">
                                            <button type="submit" class="btn btn-odoo btn-sm">
                                                <i class="fas fa-download me-1"></i>تثبيت
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                </div>
                                <div class="mt-2">
                                    <small class="text-muted">
                                        <i class="fas fa-tag me-1"></i>الإصدار: <?php echo $module['version']; ?>
                                        <br>
                                        <i class="fas fa-folder me-1"></i>الفئة: <?php echo $module['category']; ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.module-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
