<?php
/**
 * نموذج إضافة شريك - بأسلوب Odoo
 * Partner Form - Odoo Style
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// تضمين الملفات المطلوبة
require_once '../config/database.php';
require_once '../models/ResPartner.php';

// إنشاء اتصال قاعدة البيانات
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    $pdo = null;
}

$partner_model = new ResPartner($pdo);

// تحديد نوع الشريك من الرابط
$partner_type = isset($_GET['type']) ? $_GET['type'] : '';
$is_customer = $partner_type === 'customer' ? true : false;
$is_supplier = $partner_type === 'supplier' ? true : false;

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = isset($_POST['action']) ? $_POST['action'] : '';

    if ($action === 'create') {
        $data = array(
            'name' => $_POST['name'],
            'email' => isset($_POST['email']) ? $_POST['email'] : '',
            'phone' => isset($_POST['phone']) ? $_POST['phone'] : '',
            'mobile' => isset($_POST['mobile']) ? $_POST['mobile'] : '',
            'website' => isset($_POST['website']) ? $_POST['website'] : '',
            'street' => isset($_POST['street']) ? $_POST['street'] : '',
            'street2' => isset($_POST['street2']) ? $_POST['street2'] : '',
            'city' => isset($_POST['city']) ? $_POST['city'] : '',
            'zip' => isset($_POST['zip']) ? $_POST['zip'] : '',
            'country' => isset($_POST['country']) ? $_POST['country'] : '',
            'is_company' => isset($_POST['is_company']) ? 1 : 0,
            'customer_rank' => isset($_POST['is_customer']) ? 1 : 0,
            'supplier_rank' => isset($_POST['is_supplier']) ? 1 : 0,
            'vat' => isset($_POST['vat']) ? $_POST['vat'] : '',
            'category' => isset($_POST['category']) ? $_POST['category'] : '',
            'active' => 1
        );
        
        try {
            $partner_model->create($data);
            $success_message = "تم إنشاء الشريك بنجاح";
        } catch (Exception $e) {
            $error_message = "خطأ في إنشاء الشريك: " . $e->getMessage();
        }
    }
}

// قائمة الدول
$countries = array(
    'المملكة العربية السعودية',
    'الإمارات العربية المتحدة',
    'الكويت',
    'قطر',
    'البحرين',
    'عمان',
    'الأردن',
    'لبنان',
    'مصر',
    'المغرب',
    'تونس',
    'الجزائر',
    'العراق',
    'سوريا',
    'فلسطين',
    'اليمن',
    'ليبيا',
    'السودان'
);

// قائمة فئات الشركاء
$categories = array(
    'عميل مميز',
    'مورد أساسي',
    'شريك استراتيجي',
    'عميل جديد',
    'مورد محلي',
    'مورد دولي',
    'موزع',
    'وكيل',
    'مقاول',
    'مستشار',
    'خدمات مساندة',
    'أخرى'
);

// قائمة الصناعات
$industries = array(
    'تقنية المعلومات',
    'التجارة والتوزيع',
    'الصناعة والتصنيع',
    'الخدمات المالية',
    'العقارات والإنشاءات',
    'الصحة والطب',
    'التعليم والتدريب',
    'النقل واللوجستيات',
    'الطاقة والمرافق',
    'الزراعة والغذاء',
    'السياحة والضيافة',
    'الإعلام والاتصالات',
    'الاستشارات',
    'أخرى'
);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة شريك - نظام ERP</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --odoo-primary: #714B67;
            --odoo-secondary: #875A7B;
            --odoo-accent: #D4AF37;
            --odoo-success: #28a745;
            --odoo-info: #17a2b8;
            --odoo-warning: #ffc107;
            --odoo-danger: #dc3545;
            --odoo-light: #f8f9fa;
            --odoo-dark: #2F3349;
        }
        
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background: var(--odoo-light);
            margin: 0;
            padding: 0;
        }
        
        .main-container {
            display: flex;
            min-height: 100vh;
        }
        
        .content-area {
            flex: 1;
            padding: 0;
            overflow-x: hidden;
        }
        
        /* رأس الصفحة */
        .page-header {
            background: white;
            border-bottom: 1px solid #e9ecef;
            padding: 20px 30px;
            margin-bottom: 0;
        }
        
        .breadcrumb-odoo {
            background: none;
            padding: 0;
            margin: 0;
            font-size: 0.9rem;
        }
        
        .breadcrumb-odoo .breadcrumb-item {
            color: #6c757d;
        }
        
        .breadcrumb-odoo .breadcrumb-item.active {
            color: var(--odoo-primary);
            font-weight: 600;
        }
        
        .page-title {
            color: var(--odoo-dark);
            font-size: 1.8rem;
            font-weight: 700;
            margin: 10px 0 0 0;
        }
        
        /* أزرار الإجراءات */
        .action-buttons {
            background: white;
            border-bottom: 1px solid #e9ecef;
            padding: 15px 30px;
        }
        
        .btn-odoo {
            background: var(--odoo-primary);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 4px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 0.95rem;
        }
        
        .btn-odoo:hover {
            background: var(--odoo-secondary);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(113, 75, 103, 0.3);
        }
        
        .btn-outline-odoo {
            background: transparent;
            border: 1px solid var(--odoo-primary);
            color: var(--odoo-primary);
        }
        
        .btn-outline-odoo:hover {
            background: var(--odoo-primary);
            color: white;
        }
        
        .btn-secondary-odoo {
            background: #6c757d;
            border: none;
            color: white;
        }
        
        .btn-secondary-odoo:hover {
            background: #5a6268;
            color: white;
        }
        
        /* منطقة النموذج */
        .form-container {
            padding: 30px;
        }
        
        .form-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
            overflow: hidden;
        }
        
        .form-header {
            background: linear-gradient(135deg, var(--odoo-success), #20c997);
            color: white;
            padding: 20px 30px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .form-header h4 {
            margin: 0;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .form-body {
            padding: 30px;
        }
        
        .form-section {
            margin-bottom: 30px;
        }
        
        .section-title {
            color: var(--odoo-dark);
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid var(--odoo-success);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .form-label {
            font-weight: 500;
            color: var(--odoo-dark);
            margin-bottom: 8px;
            font-size: 0.9rem;
        }
        
        .required-field::after {
            content: " *";
            color: var(--odoo-danger);
            font-weight: bold;
        }
        
        .form-control, .form-select {
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px 12px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--odoo-success);
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }
        
        .form-text {
            font-size: 0.8rem;
            color: #6c757d;
            margin-top: 5px;
        }
        
        /* رسائل التنبيه */
        .alert {
            border: none;
            border-radius: 6px;
            padding: 15px 20px;
            margin-bottom: 20px;
            font-size: 0.9rem;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border-left: 4px solid #28a745;
        }
        
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border-left: 4px solid #dc3545;
        }
        
        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 768px) {
            .page-header, .action-buttons, .form-container {
                padding: 15px 20px;
            }
            
            .form-body {
                padding: 20px;
            }
            
            .form-header {
                padding: 15px 20px;
            }
        }
        
        /* تحسينات إضافية */
        .input-group-text {
            background: var(--odoo-light);
            border-color: #dee2e6;
            color: var(--odoo-dark);
        }
        
        .partner-type-cards {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .type-card {
            flex: 1;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }
        
        .type-card:hover {
            border-color: var(--odoo-success);
            background: rgba(40, 167, 69, 0.05);
        }
        
        .type-card.active {
            border-color: var(--odoo-success);
            background: rgba(40, 167, 69, 0.1);
        }
        
        .type-card .icon {
            font-size: 2rem;
            color: var(--odoo-success);
            margin-bottom: 10px;
        }
        
        .type-card .title {
            font-weight: 600;
            color: var(--odoo-dark);
            margin-bottom: 5px;
        }
        
        .type-card .description {
            font-size: 0.8rem;
            color: #6c757d;
        }
        
        .btn-group-actions {
            gap: 10px;
            justify-content: flex-end;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            margin-top: 30px;
        }
        
        .form-check-input:checked {
            background-color: var(--odoo-success);
            border-color: var(--odoo-success);
        }
        
        .contact-person-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .contact-person-title {
            font-weight: 600;
            color: var(--odoo-dark);
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- الشريط الجانبي -->
        <?php include '../includes/sidebar.php'; ?>
        
        <!-- منطقة المحتوى -->
        <div class="content-area">
            <!-- رأس الصفحة -->
            <div class="page-header">
                <nav class="breadcrumb-odoo">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="../dashboard.php">
                                <i class="fas fa-home"></i> الرئيسية
                            </a>
                        </li>
                        <li class="breadcrumb-item">الشركاء</li>
                        <li class="breadcrumb-item active">إضافة شريك</li>
                    </ol>
                </nav>
                <h1 class="page-title">
                    <?php if ($partner_type === 'customer'): ?>
                        <i class="fas fa-user me-2"></i>إضافة عميل جديد
                    <?php elseif ($partner_type === 'supplier'): ?>
                        <i class="fas fa-truck me-2"></i>إضافة مورد جديد
                    <?php else: ?>
                        <i class="fas fa-handshake me-2"></i>إضافة شريك جديد
                    <?php endif; ?>
                </h1>
            </div>
            
            <!-- أزرار الإجراءات -->
            <div class="action-buttons">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="d-flex gap-2">
                        <button type="submit" form="partnerForm" class="btn-odoo">
                            <i class="fas fa-save"></i>
                            حفظ الشريك
                        </button>
                        <button type="button" class="btn-outline-odoo" onclick="resetForm()">
                            <i class="fas fa-undo"></i>
                            إعادة تعيين
                        </button>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <?php if ($partner_type === 'customer'): ?>
                            <a href="customers_odoo.php" class="btn-secondary-odoo">
                                <i class="fas fa-list"></i>
                                قائمة العملاء
                            </a>
                        <?php elseif ($partner_type === 'supplier'): ?>
                            <a href="suppliers_odoo.php" class="btn-secondary-odoo">
                                <i class="fas fa-list"></i>
                                قائمة الموردين
                            </a>
                        <?php else: ?>
                            <a href="partners_fixed.php" class="btn-secondary-odoo">
                                <i class="fas fa-list"></i>
                                قائمة الشركاء
                            </a>
                        <?php endif; ?>
                        <a href="../dashboard.php" class="btn-secondary-odoo">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- رسائل التنبيه -->
            <?php if (isset($success_message)): ?>
                <div class="alert alert-success mx-4">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo $success_message; ?>
                </div>
            <?php endif; ?>
            
            <?php if (isset($error_message)): ?>
                <div class="alert alert-danger mx-4">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <?php echo $error_message; ?>
                </div>
            <?php endif; ?>

            <!-- نموذج إضافة الشريك -->
            <div class="form-container">
                <div class="form-card">
                    <div class="form-header">
                        <h4>
                            <i class="fas fa-handshake"></i>
                            معلومات الشريك
                        </h4>
                    </div>

                    <form id="partnerForm" method="POST" enctype="multipart/form-data">
                        <input type="hidden" name="action" value="create">

                        <div class="form-body">
                            <!-- القسم الأول: نوع الشريك -->
                            <div class="form-section">
                                <h5 class="section-title">
                                    <i class="fas fa-tags"></i>
                                    نوع الشريك
                                </h5>

                                <div class="partner-type-cards">
                                    <div class="type-card" onclick="selectPartnerType('individual')">
                                        <div class="icon">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div class="title">فرد</div>
                                        <div class="description">شخص طبيعي</div>
                                    </div>
                                    <div class="type-card active" onclick="selectPartnerType('company')">
                                        <div class="icon">
                                            <i class="fas fa-building"></i>
                                        </div>
                                        <div class="title">شركة</div>
                                        <div class="description">شخص اعتباري</div>
                                    </div>
                                </div>
                                <input type="hidden" name="is_company" id="isCompany" value="1">

                                <!-- خيارات الشريك -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="is_customer" id="isCustomer" <?php echo $is_customer ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="isCustomer">
                                                <i class="fas fa-shopping-cart me-1"></i>
                                                <strong>عميل</strong> - يمكن البيع له
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="is_supplier" id="isSupplier" <?php echo $is_supplier ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="isSupplier">
                                                <i class="fas fa-truck me-1"></i>
                                                <strong>مورد</strong> - يمكن الشراء منه
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- القسم الثاني: المعلومات الأساسية -->
                            <div class="form-section">
                                <h5 class="section-title">
                                    <i class="fas fa-info-circle"></i>
                                    المعلومات الأساسية
                                </h5>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label required-field" id="nameLabel">اسم الشركة</label>
                                            <input type="text" class="form-control" name="name" id="partnerName" required>
                                            <div class="form-text" id="nameHelp">الاسم التجاري أو اسم الشركة</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الفئة</label>
                                            <select class="form-select" name="category" id="partnerCategory">
                                                <option value="">اختر الفئة</option>
                                                <?php foreach ($categories as $category): ?>
                                                    <option value="<?php echo htmlspecialchars($category); ?>">
                                                        <?php echo htmlspecialchars($category); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">البريد الإلكتروني</label>
                                            <div class="input-group">
                                                <span class="input-group-text">
                                                    <i class="fas fa-envelope"></i>
                                                </span>
                                                <input type="email" class="form-control" name="email" id="partnerEmail">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الموقع الإلكتروني</label>
                                            <div class="input-group">
                                                <span class="input-group-text">
                                                    <i class="fas fa-globe"></i>
                                                </span>
                                                <input type="url" class="form-control" name="website" id="partnerWebsite">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">رقم الهاتف</label>
                                            <div class="input-group">
                                                <span class="input-group-text">
                                                    <i class="fas fa-phone"></i>
                                                </span>
                                                <input type="tel" class="form-control" name="phone" id="partnerPhone">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">رقم الجوال</label>
                                            <div class="input-group">
                                                <span class="input-group-text">
                                                    <i class="fas fa-mobile-alt"></i>
                                                </span>
                                                <input type="tel" class="form-control" name="mobile" id="partnerMobile">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row" id="vatSection">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الرقم الضريبي</label>
                                            <div class="input-group">
                                                <span class="input-group-text">
                                                    <i class="fas fa-receipt"></i>
                                                </span>
                                                <input type="text" class="form-control" name="vat" id="partnerVat">
                                            </div>
                                            <div class="form-text">رقم التسجيل الضريبي (15 رقم)</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الصناعة</label>
                                            <select class="form-select" name="industry" id="partnerIndustry">
                                                <option value="">اختر الصناعة</option>
                                                <?php foreach ($industries as $industry): ?>
                                                    <option value="<?php echo htmlspecialchars($industry); ?>">
                                                        <?php echo htmlspecialchars($industry); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- القسم الثالث: العنوان -->
                            <div class="form-section">
                                <h5 class="section-title">
                                    <i class="fas fa-map-marker-alt"></i>
                                    عنوان الشريك
                                </h5>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الشارع</label>
                                            <input type="text" class="form-control" name="street" id="partnerStreet">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">تفاصيل إضافية</label>
                                            <input type="text" class="form-control" name="street2" id="partnerStreet2" placeholder="رقم المبنى، الطابق، المكتب">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">المدينة</label>
                                            <input type="text" class="form-control" name="city" id="partnerCity">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">الرمز البريدي</label>
                                            <input type="text" class="form-control" name="zip" id="partnerZip">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">الدولة</label>
                                            <select class="form-select" name="country" id="partnerCountry">
                                                <option value="">اختر الدولة</option>
                                                <?php foreach ($countries as $country): ?>
                                                    <option value="<?php echo htmlspecialchars($country); ?>"
                                                            <?php echo $country === 'المملكة العربية السعودية' ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($country); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- القسم الرابع: جهات الاتصال -->
                            <div class="form-section" id="contactPersonsSection">
                                <h5 class="section-title">
                                    <i class="fas fa-address-card"></i>
                                    جهات الاتصال
                                </h5>

                                <div class="contact-person-card">
                                    <div class="contact-person-title">
                                        <i class="fas fa-user-tie"></i>
                                        جهة اتصال رئيسية
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">الاسم</label>
                                                <input type="text" class="form-control" name="contact_name" id="contactName">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">المنصب</label>
                                                <input type="text" class="form-control" name="contact_position" id="contactPosition">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">البريد الإلكتروني</label>
                                                <div class="input-group">
                                                    <span class="input-group-text">
                                                        <i class="fas fa-envelope"></i>
                                                    </span>
                                                    <input type="email" class="form-control" name="contact_email" id="contactEmail">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">رقم الهاتف</label>
                                                <div class="input-group">
                                                    <span class="input-group-text">
                                                        <i class="fas fa-phone"></i>
                                                    </span>
                                                    <input type="tel" class="form-control" name="contact_phone" id="contactPhone">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="text-center mt-3">
                                    <button type="button" class="btn btn-outline-odoo" onclick="addContactPerson()">
                                        <i class="fas fa-plus me-1"></i>إضافة جهة اتصال أخرى
                                    </button>
                                </div>
                            </div>

                            <!-- القسم الخامس: ملاحظات -->
                            <div class="form-section">
                                <h5 class="section-title">
                                    <i class="fas fa-sticky-note"></i>
                                    ملاحظات
                                </h5>

                                <div class="mb-3">
                                    <textarea class="form-control" name="notes" id="partnerNotes" rows="4" placeholder="أي ملاحظات إضافية عن الشريك..."></textarea>
                                </div>
                            </div>

                            <!-- أزرار الإجراءات -->
                            <div class="d-flex btn-group-actions">
                                <?php if ($partner_type === 'customer'): ?>
                                    <a href="customers_odoo.php" class="btn btn-secondary-odoo">
                                        <i class="fas fa-times me-1"></i>إلغاء
                                    </a>
                                <?php elseif ($partner_type === 'supplier'): ?>
                                    <a href="suppliers_odoo.php" class="btn btn-secondary-odoo">
                                        <i class="fas fa-times me-1"></i>إلغاء
                                    </a>
                                <?php else: ?>
                                    <a href="partners_fixed.php" class="btn btn-secondary-odoo">
                                        <i class="fas fa-times me-1"></i>إلغاء
                                    </a>
                                <?php endif; ?>
                                <button type="button" class="btn btn-outline-odoo" onclick="resetForm()">
                                    <i class="fas fa-undo me-1"></i>إعادة تعيين
                                </button>
                                <button type="submit" class="btn-odoo">
                                    <i class="fas fa-save me-1"></i>
                                    <?php if ($partner_type === 'customer'): ?>
                                        حفظ العميل
                                    <?php elseif ($partner_type === 'supplier'): ?>
                                        حفظ المورد
                                    <?php else: ?>
                                        حفظ الشريك
                                    <?php endif; ?>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحديد نوع الشريك
        function selectPartnerType(type) {
            const companyCard = document.querySelectorAll('.type-card')[1];
            const individualCard = document.querySelectorAll('.type-card')[0];
            const isCompanyInput = document.getElementById('isCompany');
            const nameLabel = document.getElementById('nameLabel');
            const nameHelp = document.getElementById('nameHelp');
            const vatSection = document.getElementById('vatSection');
            const contactPersonsSection = document.getElementById('contactPersonsSection');

            if (type === 'company') {
                companyCard.classList.add('active');
                individualCard.classList.remove('active');
                isCompanyInput.value = '1';
                nameLabel.textContent = 'اسم الشركة';
                nameHelp.textContent = 'الاسم التجاري أو اسم الشركة';
                vatSection.style.display = 'flex';
                contactPersonsSection.style.display = 'block';
            } else {
                individualCard.classList.add('active');
                companyCard.classList.remove('active');
                isCompanyInput.value = '0';
                nameLabel.textContent = 'الاسم الكامل';
                nameHelp.textContent = 'الاسم الأول والأخير';
                vatSection.style.display = 'none';
                contactPersonsSection.style.display = 'none';
            }
        }

        // إضافة جهة اتصال جديدة
        function addContactPerson() {
            const contactsContainer = document.getElementById('contactPersonsSection');
            const contactCount = document.querySelectorAll('.contact-person-card').length + 1;

            const newContact = document.createElement('div');
            newContact.className = 'contact-person-card mt-3';
            newContact.innerHTML = `
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <div class="contact-person-title">
                        <i class="fas fa-user-tie"></i>
                        جهة اتصال إضافية #${contactCount}
                    </div>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="this.parentNode.parentNode.remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">الاسم</label>
                            <input type="text" class="form-control" name="contact_name_${contactCount}" id="contactName${contactCount}">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">المنصب</label>
                            <input type="text" class="form-control" name="contact_position_${contactCount}" id="contactPosition${contactCount}">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">البريد الإلكتروني</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-envelope"></i>
                                </span>
                                <input type="email" class="form-control" name="contact_email_${contactCount}" id="contactEmail${contactCount}">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">رقم الهاتف</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-phone"></i>
                                </span>
                                <input type="tel" class="form-control" name="contact_phone_${contactCount}" id="contactPhone${contactCount}">
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // إضافة قبل زر الإضافة
            const addButton = contactsContainer.querySelector('.text-center');
            contactsContainer.insertBefore(newContact, addButton);
        }

        // إعادة تعيين النموذج
        function resetForm() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟')) {
                document.getElementById('partnerForm').reset();

                // إعادة تعيين نوع الشريك
                selectPartnerType('company');

                // إعادة تعيين جهات الاتصال
                const contactsContainer = document.getElementById('contactPersonsSection');
                const contactCards = contactsContainer.querySelectorAll('.contact-person-card');

                // الاحتفاظ بالبطاقة الأولى فقط
                for (let i = 1; i < contactCards.length; i++) {
                    contactCards[i].remove();
                }

                // إعادة تعيين القيم الافتراضية
                document.getElementById('partnerCountry').value = 'المملكة العربية السعودية';
                document.getElementById('isCustomer').checked = true;
                document.getElementById('isSupplier').checked = false;
            }
        }

        // التحقق من صحة النموذج
        document.getElementById('partnerForm').addEventListener('submit', function(e) {
            const name = document.getElementById('partnerName').value.trim();

            if (!name) {
                e.preventDefault();
                alert('يرجى إدخال اسم الشريك');
                document.getElementById('partnerName').focus();
                return false;
            }

            const isCustomer = document.getElementById('isCustomer').checked;
            const isSupplier = document.getElementById('isSupplier').checked;

            if (!isCustomer && !isSupplier) {
                e.preventDefault();
                alert('يجب تحديد نوع الشريك (عميل أو مورد)');
                return false;
            }

            // تأكيد الحفظ
            if (!confirm('هل أنت متأكد من حفظ بيانات الشريك؟')) {
                e.preventDefault();
                return false;
            }
        });

        // تحسينات UX
        document.addEventListener('DOMContentLoaded', function() {
            // تنسيق رقم الهاتف
            const phoneInputs = document.querySelectorAll('input[type="tel"]');
            phoneInputs.forEach(input => {
                input.addEventListener('input', function(e) {
                    let value = e.target.value.replace(/\D/g, '');
                    if (value.startsWith('966')) {
                        value = '+' + value;
                    } else if (value.startsWith('0')) {
                        value = '+966' + value.substring(1);
                    }
                    e.target.value = value;
                });
            });

            // تنسيق الرقم الضريبي
            const vatInput = document.getElementById('partnerVat');
            if (vatInput) {
                vatInput.addEventListener('input', function(e) {
                    let value = e.target.value.replace(/\D/g, '');
                    if (value.length > 15) {
                        value = value.substring(0, 15);
                    }
                    e.target.value = value;
                });
            }
        });
    </script>
</body>
</html>
