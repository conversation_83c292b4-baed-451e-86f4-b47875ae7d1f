<?php
/**
 * صفحة إدارة الشركاء بأسلوب Odoo
 * Partners Management Page - Odoo Style
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// تحميل نظام Odoo
require_once '../config/odoo_config.php';
require_once '../config/odoo_database.php';

$odoo_db = OdooDatabase::getInstance();

// تحميل النماذج
require_once '../models/BaseModel.php';
require_once '../models/ResPartner.php';

$partner_model = new ResPartner($odoo_db);

// جلب الشركاء من قاعدة البيانات
try {
    $partners = $partner_model->search(array(), array('name' => 'ASC'));

    // إذا لم توجد بيانات، استخدم البيانات الوهمية
    if (empty($partners)) {
        $partners = $partner_model->get_demo_data();
    }
} catch (Exception $e) {
    $partners = $partner_model->get_demo_data();
}

// فلترة حسب النوع
$filter_type = isset($_GET['filter']) ? $_GET['filter'] : 'all';
if ($filter_type === 'customers') {
    $filtered_partners = array();
    foreach ($partners as $p) {
        if ($p['customer_rank'] > 0) {
            $filtered_partners[] = $p;
        }
    }
    $partners = $filtered_partners;
} elseif ($filter_type === 'suppliers') {
    $filtered_partners = array();
    foreach ($partners as $p) {
        if ($p['supplier_rank'] > 0) {
            $filtered_partners[] = $p;
        }
    }
    $partners = $filtered_partners;
} elseif ($filter_type === 'companies') {
    $filtered_partners = array();
    foreach ($partners as $p) {
        if ($p['is_company']) {
            $filtered_partners[] = $p;
        }
    }
    $partners = $filtered_partners;
}

// طريقة العرض المحددة
$view_mode = isset($_GET['view']) ? $_GET['view'] : 'cards';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الشركاء - نظام ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --odoo-primary: #714B67;
            --odoo-secondary: #8B5A8C;
        }
        
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar-odoo {
            background: linear-gradient(45deg, var(--odoo-primary), var(--odoo-secondary));
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .page-header {
            background: linear-gradient(45deg, var(--odoo-primary), var(--odoo-secondary));
            color: white;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
        }
        
        .view-controls {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        
        .view-btn, .filter-btn {
            border: 1px solid #dee2e6;
            background: white;
            color: #6c757d;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: all 0.3s ease;
            text-decoration: none;
        }
        
        .view-btn.active, .filter-btn.active {
            background: var(--odoo-primary);
            color: white;
            border-color: var(--odoo-primary);
        }
        
        .view-btn:hover, .filter-btn:hover {
            background: var(--odoo-secondary);
            color: white;
            border-color: var(--odoo-secondary);
            text-decoration: none;
        }
        
        .partner-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            border-left: 4px solid #dee2e6;
        }
        
        .partner-card.customer {
            border-left-color: #28a745;
        }
        
        .partner-card.supplier {
            border-left-color: #007bff;
        }
        
        .partner-card.both {
            border-left-color: #ffc107;
        }
        
        .partner-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }
        
        .partner-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(45deg, var(--odoo-primary), var(--odoo-secondary));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
        }
        
        .table-odoo {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .table-odoo th {
            background: linear-gradient(45deg, var(--odoo-primary), var(--odoo-secondary));
            color: white;
            border: none;
            padding: 1rem;
        }
        
        .table-odoo td {
            padding: 1rem;
            border-color: #f8f9fa;
            vertical-align: middle;
        }
        
        .breadcrumb-odoo {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 4px solid;
            margin-bottom: 1rem;
        }
        
        .partner-type-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-odoo">
        <div class="container-fluid">
            <a class="navbar-brand text-white" href="../dashboard.php">
                <i class="fas fa-cube me-2"></i>
                نظام ERP - إدارة الشركاء
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="../dashboard.php">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- مسار التنقل -->
        <nav class="breadcrumb-odoo">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="../dashboard.php">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="#">الوحدة الأساسية</a></li>
                <li class="breadcrumb-item active">الشركاء</li>
            </ol>
        </nav>

        <!-- العنوان الرئيسي -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2><i class="fas fa-users me-2"></i>إدارة الشركاء</h2>
                    <p class="mb-0">إدارة العملاء والموردين والشركاء</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-light">
                        <i class="fas fa-plus me-2"></i>إضافة شريك جديد
                    </button>
                </div>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card border-success">
                    <h3 class="text-success"><?php
                        $customers = 0;
                        foreach($partner_model->get_demo_data() as $p) {
                            if ($p['customer_rank'] > 0) $customers++;
                        }
                        echo $customers;
                    ?></h3>
                    <p class="mb-0">عملاء</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card border-primary">
                    <h3 class="text-primary"><?php
                        $suppliers = 0;
                        foreach($partner_model->get_demo_data() as $p) {
                            if ($p['supplier_rank'] > 0) $suppliers++;
                        }
                        echo $suppliers;
                    ?></h3>
                    <p class="mb-0">موردين</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card border-info">
                    <h3 class="text-info"><?php
                        $companies = 0;
                        foreach($partner_model->get_demo_data() as $p) {
                            if ($p['is_company']) $companies++;
                        }
                        echo $companies;
                    ?></h3>
                    <p class="mb-0">شركات</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card border-warning">
                    <h3 class="text-warning"><?php echo count($partner_model->get_demo_data()); ?></h3>
                    <p class="mb-0">إجمالي الشركاء</p>
                </div>
            </div>
        </div>

        <!-- أدوات التحكم والعرض -->
        <div class="view-controls">
            <div class="row align-items-center">
                <div class="col-md-4">
                    <div class="btn-group" role="group">
                        <a href="?view=cards&filter=<?php echo $filter_type; ?>" class="view-btn <?php echo $view_mode === 'cards' ? 'active' : ''; ?>">
                            <i class="fas fa-th-large me-2"></i>بطاقات
                        </a>
                        <a href="?view=table&filter=<?php echo $filter_type; ?>" class="view-btn <?php echo $view_mode === 'table' ? 'active' : ''; ?>">
                            <i class="fas fa-table me-2"></i>جدول
                        </a>
                        <a href="?view=list&filter=<?php echo $filter_type; ?>" class="view-btn <?php echo $view_mode === 'list' ? 'active' : ''; ?>">
                            <i class="fas fa-list me-2"></i>قائمة
                        </a>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="btn-group" role="group">
                        <a href="?view=<?php echo $view_mode; ?>&filter=all" class="filter-btn <?php echo $filter_type === 'all' ? 'active' : ''; ?>">
                            الكل
                        </a>
                        <a href="?view=<?php echo $view_mode; ?>&filter=customers" class="filter-btn <?php echo $filter_type === 'customers' ? 'active' : ''; ?>">
                            العملاء
                        </a>
                        <a href="?view=<?php echo $view_mode; ?>&filter=suppliers" class="filter-btn <?php echo $filter_type === 'suppliers' ? 'active' : ''; ?>">
                            الموردين
                        </a>
                        <a href="?view=<?php echo $view_mode; ?>&filter=companies" class="filter-btn <?php echo $filter_type === 'companies' ? 'active' : ''; ?>">
                            الشركات
                        </a>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="d-flex justify-content-end">
                        <input type="text" class="form-control search-box" placeholder="البحث في الشركاء..." style="max-width: 250px;">
                    </div>
                </div>
            </div>
        </div>

        <!-- محتوى العرض -->
        <?php if ($view_mode === 'cards'): ?>
            <!-- عرض البطاقات -->
            <div class="row">
                <?php foreach ($partners as $partner): 
                    $card_class = '';
                    if ($partner['customer_rank'] > 0 && $partner['supplier_rank'] > 0) {
                        $card_class = 'both';
                    } elseif ($partner['customer_rank'] > 0) {
                        $card_class = 'customer';
                    } elseif ($partner['supplier_rank'] > 0) {
                        $card_class = 'supplier';
                    }
                ?>
                    <div class="col-md-6 col-lg-4">
                        <div class="partner-card <?php echo $card_class; ?>">
                            <div class="d-flex align-items-center mb-3">
                                <div class="partner-avatar me-3">
                                    <?php echo strtoupper(substr($partner['name'], 0, 2)); ?>
                                </div>
                                <div class="flex-grow-1">
                                    <h5 class="mb-1"><?php echo $partner['name']; ?></h5>
                                    <p class="text-muted mb-0"><?php echo isset($partner['email']) ? $partner['email'] : 'غير محدد'; ?></p>
                                </div>
                                <div>
                                    <?php if ($partner['is_company']): ?>
                                        <span class="badge bg-info partner-type-badge">شركة</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary partner-type-badge">فرد</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <?php if ($partner['customer_rank'] > 0): ?>
                                    <span class="badge bg-success me-1">عميل</span>
                                <?php endif; ?>
                                <?php if ($partner['supplier_rank'] > 0): ?>
                                    <span class="badge bg-primary me-1">مورد</span>
                                <?php endif; ?>
                            </div>
                            
                            <div class="mb-3">
                                <small class="text-muted">
                                    <i class="fas fa-phone me-1"></i><?php echo isset($partner['phone']) ? $partner['phone'] : 'غير محدد'; ?><br>
                                    <i class="fas fa-map-marker-alt me-1"></i><?php echo isset($partner['city']) ? $partner['city'] : 'غير محدد'; ?>
                                </small>
                            </div>
                            
                            <div class="d-flex justify-content-between">
                                <button class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-edit me-1"></i>تعديل
                                </button>
                                <button class="btn btn-outline-info btn-sm">
                                    <i class="fas fa-eye me-1"></i>عرض
                                </button>
                                <button class="btn btn-outline-success btn-sm">
                                    <i class="fas fa-file-invoice me-1"></i>فاتورة
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

        <?php elseif ($view_mode === 'table'): ?>
            <!-- عرض الجدول -->
            <div class="table-odoo">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>الشريك</th>
                            <th>النوع</th>
                            <th>البريد الإلكتروني</th>
                            <th>الهاتف</th>
                            <th>المدينة</th>
                            <th>التصنيف</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($partners as $partner): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="partner-avatar me-3" style="width: 40px; height: 40px; font-size: 1rem;">
                                            <?php echo strtoupper(substr($partner['name'], 0, 2)); ?>
                                        </div>
                                        <div>
                                            <strong><?php echo $partner['name']; ?></strong>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <?php if ($partner['is_company']): ?>
                                        <span class="badge bg-info">شركة</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">فرد</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo isset($partner['email']) ? $partner['email'] : 'غير محدد'; ?></td>
                                <td><?php echo isset($partner['phone']) ? $partner['phone'] : 'غير محدد'; ?></td>
                                <td><?php echo isset($partner['city']) ? $partner['city'] : 'غير محدد'; ?></td>
                                <td>
                                    <?php if ($partner['customer_rank'] > 0): ?>
                                        <span class="badge bg-success me-1">عميل</span>
                                    <?php endif; ?>
                                    <?php if ($partner['supplier_rank'] > 0): ?>
                                        <span class="badge bg-primary me-1">مورد</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-info">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-success">
                                            <i class="fas fa-file-invoice"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

        <?php else: ?>
            <!-- عرض القائمة -->
            <div class="list-group">
                <?php foreach ($partners as $partner): ?>
                    <div class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <div class="partner-avatar me-3" style="width: 50px; height: 50px; font-size: 1.2rem;">
                                    <?php echo strtoupper(substr($partner['name'], 0, 2)); ?>
                                </div>
                                <div>
                                    <h5 class="mb-1"><?php echo $partner['name']; ?></h5>
                                    <p class="mb-1"><?php echo isset($partner['email']) ? $partner['email'] : 'غير محدد'; ?></p>
                                    <small class="text-muted"><?php echo isset($partner['phone']) ? $partner['phone'] : 'غير محدد'; ?> • <?php echo isset($partner['city']) ? $partner['city'] : 'غير محدد'; ?></small>
                                </div>
                            </div>
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <?php if ($partner['customer_rank'] > 0): ?>
                                        <span class="badge bg-success me-1">عميل</span>
                                    <?php endif; ?>
                                    <?php if ($partner['supplier_rank'] > 0): ?>
                                        <span class="badge bg-primary me-1">مورد</span>
                                    <?php endif; ?>
                                    <?php if ($partner['is_company']): ?>
                                        <span class="badge bg-info">شركة</span>
                                    <?php endif; ?>
                                </div>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-info">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-success">
                                        <i class="fas fa-file-invoice"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/odoo-table-manager.js"></script>
    <script src="../assets/js/odoo-buttons-manager.js"></script>
    <script src="../assets/js/odoo-export-manager.js"></script>
    <script>
        // تهيئة مديري الجداول والأزرار
        document.addEventListener('DOMContentLoaded', function() {
            // تهيئة مدير الأزرار للجدول
            odooButtons.addTableButtons('partners-table', {
                enableAdd: true,
                enableEdit: true,
                enableDelete: true,
                enablePrint: true,
                enableExport: true,
                enableRefresh: true
            });

            // تفعيل دوال التصدير
            setupPartnersExportFunctions();
        });

        function setupPartnersExportFunctions() {
            window.exportPartnersToExcel = function() {
                odooExport.exportToExcel('partners-table', 'الشركاء.xlsx')
                    .then(filename => showMessage('تم تصدير الشركاء إلى Excel بنجاح', 'success'))
                    .catch(error => showMessage('خطأ في التصدير: ' + error.message, 'error'));
            };

            window.exportPartnersToXLS = function() {
                odooExport.exportToXLS('partners-table', 'الشركاء.xls')
                    .then(filename => showMessage('تم تصدير الشركاء إلى XLS بنجاح', 'success'))
                    .catch(error => showMessage('خطأ في التصدير: ' + error.message, 'error'));
            };

            window.exportPartnersToPDF = function() {
                odooExport.exportToPDF('partners-table', 'الشركاء.pdf', {
                    title: 'قائمة الشركاء',
                    orientation: 'landscape'
                })
                    .then(filename => showMessage('تم تصدير الشركاء إلى PDF بنجاح', 'success'))
                    .catch(error => showMessage('خطأ في التصدير: ' + error.message, 'error'));
            };

            window.exportPartnersToCSV = function() {
                odooExport.exportToCSV('partners-table', 'الشركاء.csv')
                    .then(filename => showMessage('تم تصدير الشركاء إلى CSV بنجاح', 'success'))
                    .catch(error => showMessage('خطأ في التصدير: ' + error.message, 'error'));
            };
        }

        function showMessage(message, type = 'info') {
            const alert = document.createElement('div');
            alert.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alert.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(alert);

            setTimeout(() => {
                alert.remove();
            }, 4000);
        }
    </script>
    <script>
        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const items = document.querySelectorAll('.partner-card, .list-group-item');
            items.forEach((item, index) => {
                item.style.opacity = '0';
                item.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    item.style.transition = 'all 0.6s ease';
                    item.style.opacity = '1';
                    item.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
        
        // البحث المباشر
        document.querySelector('.search-box').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const items = document.querySelectorAll('.partner-card, .list-group-item, tbody tr');
            
            items.forEach(item => {
                const text = item.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    item.style.display = '';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
