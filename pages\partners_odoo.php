<?php
/**
 * صفحة الشركاء بأسلوب Odoo الكامل
 * Partners Page - Complete Odoo Style
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// تحميل النماذج
require_once '../models/ResPartner.php';

// تهيئة النماذج
$partner_model = new ResPartner();

$message = '';
$message_type = '';

// معالجة الطلبات AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    $action = isset($_POST['action']) ? $_POST['action'] : '';
    
    switch ($action) {
        case 'delete_partner':
            $partner_id = isset($_POST['partner_id']) ? $_POST['partner_id'] : 0;
            if ($partner_id) {
                try {
                    echo json_encode(array('success' => true, 'message' => 'تم حذف الشريك بنجاح'));
                    exit();
                } catch (Exception $e) {
                    echo json_encode(array('success' => false, 'message' => 'خطأ في حذف الشريك: ' . $e->getMessage()));
                    exit();
                }
            }
            break;
            
        case 'toggle_active':
            $partner_id = isset($_POST['partner_id']) ? $_POST['partner_id'] : 0;
            if ($partner_id) {
                try {
                    echo json_encode(array('success' => true, 'message' => 'تم تحديث حالة الشريك بنجاح'));
                    exit();
                } catch (Exception $e) {
                    echo json_encode(array('success' => false, 'message' => 'خطأ في تحديث الحالة: ' . $e->getMessage()));
                    exit();
                }
            }
            break;
            
        case 'bulk_action':
            $selected_ids = isset($_POST['selected_ids']) ? $_POST['selected_ids'] : array();
            $bulk_action = isset($_POST['bulk_action']) ? $_POST['bulk_action'] : '';
            
            if (!empty($selected_ids) && $bulk_action) {
                $success_count = count($selected_ids);
                echo json_encode(array(
                    'success' => true, 
                    'message' => "تم تنفيذ العملية على {$success_count} شريك بنجاح"
                ));
                exit();
            }
            break;
    }
}

// معاملات الفلترة والعرض
$view_mode = isset($_GET['view']) ? $_GET['view'] : 'list';
$filter_type = isset($_GET['type']) ? $_GET['type'] : 'all';
$filter_active = isset($_GET['active']) ? $_GET['active'] : 'all';
$search_term = isset($_GET['search']) ? $_GET['search'] : '';

// جلب البيانات
$partners = $partner_model->get_demo_data();

// تطبيق الفلاتر
if ($filter_type !== 'all') {
    $filtered_partners = array();
    foreach ($partners as $partner) {
        if ($filter_type === 'customer' && $partner['is_customer']) {
            $filtered_partners[] = $partner;
        } elseif ($filter_type === 'supplier' && $partner['is_supplier']) {
            $filtered_partners[] = $partner;
        } elseif ($filter_type === 'company' && $partner['is_company']) {
            $filtered_partners[] = $partner;
        }
    }
    $partners = $filtered_partners;
}

if ($filter_active !== 'all') {
    $filtered_partners = array();
    foreach ($partners as $partner) {
        if (($filter_active === 'active' && $partner['active']) || 
            ($filter_active === 'inactive' && !$partner['active'])) {
            $filtered_partners[] = $partner;
        }
    }
    $partners = $filtered_partners;
}

// تطبيق البحث
if (!empty($search_term)) {
    $filtered_partners = array();
    foreach ($partners as $partner) {
        $email = isset($partner['email']) ? $partner['email'] : '';
        $phone = isset($partner['phone']) ? $partner['phone'] : '';
        if (stripos($partner['name'], $search_term) !== false || 
            stripos($email, $search_term) !== false ||
            stripos($phone, $search_term) !== false) {
            $filtered_partners[] = $partner;
        }
    }
    $partners = $filtered_partners;
}

// إحصائيات سريعة
$all_partners = $partner_model->get_demo_data();
$customers_count = 0;
$suppliers_count = 0;
$companies_count = 0;
$active_count = 0;

foreach ($all_partners as $partner) {
    if ($partner['is_customer']) $customers_count++;
    if ($partner['is_supplier']) $suppliers_count++;
    if ($partner['is_company']) $companies_count++;
    if ($partner['active']) $active_count++;
}

$stats = array(
    'total' => count($all_partners),
    'customers' => $customers_count,
    'suppliers' => $suppliers_count,
    'companies' => $companies_count,
    'active' => $active_count
);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الشركاء - نظام ERP</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Odoo Style -->
    <link href="../assets/css/odoo-style.css" rel="stylesheet">
    <link href="../assets/css/odoo-advanced.css" rel="stylesheet">
    <link href="../assets/css/journal-entries-enhanced.css" rel="stylesheet">
    
    <style>
        .partner-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(45deg, #714B67, #875A7B);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 0.9rem;
        }
        
        .partner-type-badge {
            font-size: 0.7rem;
            padding: 2px 6px;
            border-radius: 10px;
            margin: 0 2px;
        }
        
        .partner-type-badge.customer {
            background: #e3f2fd;
            color: #1976d2;
        }
        
        .partner-type-badge.supplier {
            background: #f3e5f5;
            color: #7b1fa2;
        }
        
        .partner-type-badge.company {
            background: #e8f5e8;
            color: #388e3c;
        }
        
        .partner-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 16px;
            padding: 20px;
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
            position: relative;
            overflow: hidden;
        }
        
        .partner-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }
        
        .partner-card.customer {
            border-left-color: #2196f3;
        }
        
        .partner-card.supplier {
            border-left-color: #9c27b0;
        }
        
        .partner-card.company {
            border-left-color: #4caf50;
        }
        
        .partner-card.inactive {
            opacity: 0.6;
        }
        
        .partner-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .partner-info {
            flex: 1;
            margin-right: 15px;
        }
        
        .partner-name {
            font-weight: 600;
            font-size: 1.1rem;
            margin-bottom: 5px;
            color: #2c3e50;
        }
        
        .partner-details {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .partner-actions {
            display: flex;
            gap: 5px;
        }
        
        .contact-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            font-size: 0.9rem;
            color: #6c757d;
        }
        
        .contact-item i {
            width: 16px;
            margin-left: 8px;
            color: #875A7B;
        }
        
        .partner-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-top: 10px;
        }
        
        .stats-row {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .stat-item {
            text-align: center;
            padding: 15px;
            border-radius: 8px;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }
        
        .stat-item:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }
        
        .stat-number {
            font-size: 1.8rem;
            font-weight: bold;
            color: #714B67;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <?php include '../includes/navbar.php'; ?>
    
    <div class="o_main_content">
        <div class="o_content">
            <!-- الشريط الجانبي -->
            <?php include '../includes/sidebar.php'; ?>
            
            <!-- المحتوى الرئيسي -->
            <div class="o_action_manager">
                <!-- شريط التحكم -->
                <div class="o_control_panel">
                    <div class="o_cp_left">
                        <!-- مسار التنقل -->
                        <div class="o_breadcrumb">
                            <a href="dashboard_odoo.php" class="o_breadcrumb_item">الرئيسية</a>
                            <span>/</span>
                            <span class="o_breadcrumb_item active">الشركاء</span>
                        </div>
                    </div>
                    <div class="o_cp_right">
                        <!-- أزرار الإجراءات -->
                        <div class="btn-group me-2">
                            <button class="btn btn-primary btn-sm" onclick="createPartner()">
                                <i class="fas fa-plus me-1"></i>إنشاء
                            </button>
                            <button class="btn btn-outline-primary btn-sm" onclick="importPartners()">
                                <i class="fas fa-upload me-1"></i>استيراد
                            </button>
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-outline-secondary btn-sm" onclick="refreshData()">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                            <button class="btn btn-outline-secondary btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="fas fa-cog"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="exportData('excel')">
                                    <i class="fas fa-file-excel me-2"></i>تصدير Excel
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="exportData('pdf')">
                                    <i class="fas fa-file-pdf me-2"></i>تصدير PDF
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" onclick="printData()">
                                    <i class="fas fa-print me-2"></i>طباعة
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- الإحصائيات السريعة -->
                <div class="stats-row mx-3">
                    <div class="row">
                        <div class="col-lg-2 col-md-4 col-6 mb-2">
                            <div class="stat-item">
                                <div class="stat-number"><?php echo $stats['total']; ?></div>
                                <div class="stat-label">إجمالي الشركاء</div>
                            </div>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6 mb-2">
                            <div class="stat-item">
                                <div class="stat-number"><?php echo $stats['customers']; ?></div>
                                <div class="stat-label">العملاء</div>
                            </div>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6 mb-2">
                            <div class="stat-item">
                                <div class="stat-number"><?php echo $stats['suppliers']; ?></div>
                                <div class="stat-label">الموردين</div>
                            </div>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6 mb-2">
                            <div class="stat-item">
                                <div class="stat-number"><?php echo $stats['companies']; ?></div>
                                <div class="stat-label">الشركات</div>
                            </div>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6 mb-2">
                            <div class="stat-item">
                                <div class="stat-number"><?php echo $stats['active']; ?></div>
                                <div class="stat-label">النشطة</div>
                            </div>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6 mb-2">
                            <div class="stat-item">
                                <div class="stat-number"><?php echo $stats['total'] - $stats['active']; ?></div>
                                <div class="stat-label">غير النشطة</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- شريط البحث والفلترة -->
                <div class="o_searchview">
                    <div class="d-flex align-items-center gap-3">
                        <!-- البحث -->
                        <div class="flex-grow-1">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" class="o_searchview_input form-control"
                                       placeholder="البحث في الشركاء..."
                                       id="searchInput"
                                       value="<?php echo htmlspecialchars($search_term); ?>">
                            </div>
                        </div>

                        <!-- الفلاتر النشطة -->
                        <div class="d-flex align-items-center gap-2">
                            <?php if ($filter_type !== 'all'): ?>
                                <span class="o_facet">
                                    <i class="fas fa-times o_facet_remove" onclick="removeFilter('type')"></i>
                                    النوع: <?php
                                        $types = array('customer' => 'عميل', 'supplier' => 'مورد', 'company' => 'شركة');
                                        echo $types[$filter_type];
                                    ?>
                                </span>
                            <?php endif; ?>

                            <?php if ($filter_active !== 'all'): ?>
                                <span class="o_facet">
                                    <i class="fas fa-times o_facet_remove" onclick="removeFilter('active')"></i>
                                    الحالة: <?php echo $filter_active === 'active' ? 'نشط' : 'غير نشط'; ?>
                                </span>
                            <?php endif; ?>
                        </div>

                        <!-- فلترة حسب النوع -->
                        <select class="form-select form-select-sm" style="width: auto;" onchange="filterByType(this.value)">
                            <option value="all">جميع الأنواع</option>
                            <option value="customer" <?php echo $filter_type == 'customer' ? 'selected' : ''; ?>>العملاء</option>
                            <option value="supplier" <?php echo $filter_type == 'supplier' ? 'selected' : ''; ?>>الموردين</option>
                            <option value="company" <?php echo $filter_type == 'company' ? 'selected' : ''; ?>>الشركات</option>
                        </select>

                        <!-- فلترة حسب الحالة -->
                        <select class="form-select form-select-sm" style="width: auto;" onchange="filterByActive(this.value)">
                            <option value="all">جميع الحالات</option>
                            <option value="active" <?php echo $filter_active == 'active' ? 'selected' : ''; ?>>نشط</option>
                            <option value="inactive" <?php echo $filter_active == 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                        </select>

                        <!-- أزرار العرض -->
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-secondary <?php echo $view_mode === 'list' ? 'active' : ''; ?>"
                                    onclick="changeView('list')" title="عرض قائمة">
                                <i class="fas fa-list"></i>
                            </button>
                            <button class="btn btn-outline-secondary <?php echo $view_mode === 'kanban' ? 'active' : ''; ?>"
                                    onclick="changeView('kanban')" title="عرض بطاقات">
                                <i class="fas fa-th-large"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- شريط الإجراءات المجمعة -->
                <div class="bulk-actions" id="bulkActions">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <span id="selectedCount">0</span> شريك محدد
                        </div>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-success" onclick="bulkAction('activate')">
                                <i class="fas fa-check me-1"></i>تفعيل المحدد
                            </button>
                            <button class="btn btn-warning" onclick="bulkAction('deactivate')">
                                <i class="fas fa-pause me-1"></i>إلغاء تفعيل المحدد
                            </button>
                            <button class="btn btn-danger" onclick="bulkAction('delete')">
                                <i class="fas fa-trash me-1"></i>حذف المحدد
                            </button>
                            <button class="btn btn-secondary" onclick="clearSelection()">
                                <i class="fas fa-times me-1"></i>إلغاء التحديد
                            </button>
                        </div>
                    </div>
                </div>

                <!-- عرض الشركاء -->
                <div class="flex-grow-1" style="overflow: auto;">
                    <?php if ($view_mode === 'list'): ?>
                        <!-- عرض الجدول -->
                        <div class="o_list_view">
                            <table class="o_list_table">
                                <thead>
                                    <tr>
                                        <th class="o_list_record_selector">
                                            <input type="checkbox" id="selectAll">
                                        </th>
                                        <th class="o_column_sortable" data-sort="name">
                                            الاسم
                                        </th>
                                        <th class="o_column_sortable" data-sort="email">
                                            البريد الإلكتروني
                                        </th>
                                        <th class="o_column_sortable" data-sort="phone">
                                            الهاتف
                                        </th>
                                        <th>النوع</th>
                                        <th class="o_column_sortable" data-sort="active">
                                            الحالة
                                        </th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($partners as $partner): ?>
                                        <tr class="o_data_row <?php echo !$partner['active'] ? 'o_row_readonly' : ''; ?>" data-id="<?php echo $partner['id']; ?>">
                                            <td class="o_list_record_selector">
                                                <input type="checkbox" class="record-checkbox" value="<?php echo $partner['id']; ?>">
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="partner-avatar me-2">
                                                        <?php echo strtoupper(substr($partner['name'], 0, 1)); ?>
                                                    </div>
                                                    <div>
                                                        <strong><?php echo $partner['name']; ?></strong>
                                                        <?php if ($partner['is_company']): ?>
                                                            <br><small class="text-muted">شركة</small>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </td>
                                            <td><?php echo !empty($partner['email']) ? $partner['email'] : '-'; ?></td>
                                            <td><?php echo !empty($partner['phone']) ? $partner['phone'] : '-'; ?></td>
                                            <td>
                                                <div class="partner-tags">
                                                    <?php if ($partner['is_customer']): ?>
                                                        <span class="partner-type-badge customer">عميل</span>
                                                    <?php endif; ?>
                                                    <?php if ($partner['is_supplier']): ?>
                                                        <span class="partner-type-badge supplier">مورد</span>
                                                    <?php endif; ?>
                                                    <?php if ($partner['is_company']): ?>
                                                        <span class="partner-type-badge company">شركة</span>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="o_status_badge <?php echo $partner['active'] ? 'posted' : 'cancelled'; ?>">
                                                    <?php echo $partner['active'] ? 'نشط' : 'غير نشط'; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary btn-sm" onclick="viewPartner(<?php echo $partner['id']; ?>)" title="عرض">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-warning btn-sm" onclick="editPartner(<?php echo $partner['id']; ?>)" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-outline-<?php echo $partner['active'] ? 'secondary' : 'success'; ?> btn-sm"
                                                            onclick="toggleActive(<?php echo $partner['id']; ?>)"
                                                            title="<?php echo $partner['active'] ? 'إلغاء تفعيل' : 'تفعيل'; ?>">
                                                        <i class="fas fa-<?php echo $partner['active'] ? 'pause' : 'play'; ?>"></i>
                                                    </button>
                                                    <button class="btn btn-outline-secondary btn-sm dropdown-toggle" data-bs-toggle="dropdown" title="المزيد">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li><a class="dropdown-item" href="#" onclick="duplicatePartner(<?php echo $partner['id']; ?>)">
                                                            <i class="fas fa-copy me-2"></i>نسخ
                                                        </a></li>
                                                        <li><a class="dropdown-item" href="#" onclick="sendEmail(<?php echo $partner['id']; ?>)">
                                                            <i class="fas fa-envelope me-2"></i>إرسال بريد
                                                        </a></li>
                                                        <li><hr class="dropdown-divider"></li>
                                                        <li><a class="dropdown-item text-danger" href="#" onclick="deletePartner(<?php echo $partner['id']; ?>)">
                                                            <i class="fas fa-trash me-2"></i>حذف
                                                        </a></li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <!-- عرض البطاقات -->
                        <div class="o_kanban_view">
                            <div class="row">
                                <?php foreach ($partners as $partner): ?>
                                    <div class="col-md-6 col-lg-4 mb-3">
                                        <div class="partner-card <?php echo $partner['is_customer'] ? 'customer' : ($partner['is_supplier'] ? 'supplier' : 'company'); ?> <?php echo !$partner['active'] ? 'inactive' : ''; ?>"
                                             data-id="<?php echo $partner['id']; ?>">
                                            <div class="partner-header">
                                                <div class="partner-avatar">
                                                    <?php echo strtoupper(substr($partner['name'], 0, 1)); ?>
                                                </div>
                                                <div class="partner-info">
                                                    <div class="partner-name"><?php echo $partner['name']; ?></div>
                                                    <div class="partner-details">
                                                        <?php if ($partner['is_company']): ?>
                                                            <i class="fas fa-building me-1"></i>شركة
                                                        <?php else: ?>
                                                            <i class="fas fa-user me-1"></i>فرد
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                                <div class="partner-actions">
                                                    <button class="btn btn-outline-primary btn-sm" onclick="viewPartner(<?php echo $partner['id']; ?>)" title="عرض">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-warning btn-sm" onclick="editPartner(<?php echo $partner['id']; ?>)" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                </div>
                                            </div>

                                            <div class="contact-info">
                                                <?php if (!empty($partner['email'])): ?>
                                                    <div class="contact-item">
                                                        <i class="fas fa-envelope"></i>
                                                        <span><?php echo $partner['email']; ?></span>
                                                    </div>
                                                <?php endif; ?>
                                                <?php if (!empty($partner['phone'])): ?>
                                                    <div class="contact-item">
                                                        <i class="fas fa-phone"></i>
                                                        <span><?php echo $partner['phone']; ?></span>
                                                    </div>
                                                <?php endif; ?>
                                                <?php if (!empty($partner['website'])): ?>
                                                    <div class="contact-item">
                                                        <i class="fas fa-globe"></i>
                                                        <span><?php echo $partner['website']; ?></span>
                                                    </div>
                                                <?php endif; ?>
                                            </div>

                                            <div class="partner-tags">
                                                <?php if ($partner['is_customer']): ?>
                                                    <span class="partner-type-badge customer">عميل</span>
                                                <?php endif; ?>
                                                <?php if ($partner['is_supplier']): ?>
                                                    <span class="partner-type-badge supplier">مورد</span>
                                                <?php endif; ?>
                                                <?php if ($partner['is_company']): ?>
                                                    <span class="partner-type-badge company">شركة</span>
                                                <?php endif; ?>
                                                <span class="o_status_badge <?php echo $partner['active'] ? 'posted' : 'cancelled'; ?>">
                                                    <?php echo $partner['active'] ? 'نشط' : 'غير نشط'; ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- تراكب التحميل -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <div class="mt-2">جاري المعالجة...</div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // متغيرات عامة
        let selectedRecords = new Set();

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializeEventListeners();
            updateSelectedCount();
        });

        // تهيئة معالجات الأحداث
        function initializeEventListeners() {
            // معالج تحديد الكل
            const selectAllCheckbox = document.getElementById('selectAll');
            if (selectAllCheckbox) {
                selectAllCheckbox.addEventListener('change', function() {
                    const checkboxes = document.querySelectorAll('.record-checkbox');
                    checkboxes.forEach(checkbox => {
                        checkbox.checked = this.checked;
                        toggleRowSelection(checkbox, false);
                    });
                    updateSelectedCount();
                });
            }

            // معالج تحديد الصفوف الفردية
            document.querySelectorAll('.record-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    toggleRowSelection(this);
                    updateSelectAllState();
                    updateSelectedCount();
                });
            });

            // معالج البحث
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                let searchTimeout;
                searchInput.addEventListener('input', function() {
                    clearTimeout(searchTimeout);
                    searchTimeout = setTimeout(() => {
                        performSearch(this.value);
                    }, 300);
                });
            }
        }

        // تحديد/إلغاء تحديد صف
        function toggleRowSelection(checkbox, updateSelectAll = true) {
            const row = checkbox.closest('tr') || checkbox.closest('.partner-card');
            const recordId = checkbox.value;

            if (checkbox.checked) {
                row.classList.add('o_selected_row');
                selectedRecords.add(recordId);
            } else {
                row.classList.remove('o_selected_row');
                selectedRecords.delete(recordId);
            }

            if (updateSelectAll) {
                updateSelectAllState();
            }
        }

        // تحديث حالة "تحديد الكل"
        function updateSelectAllState() {
            const allCheckboxes = document.querySelectorAll('.record-checkbox');
            const checkedCheckboxes = document.querySelectorAll('.record-checkbox:checked');
            const selectAllCheckbox = document.getElementById('selectAll');

            if (!selectAllCheckbox) return;

            if (checkedCheckboxes.length === allCheckboxes.length && allCheckboxes.length > 0) {
                selectAllCheckbox.checked = true;
                selectAllCheckbox.indeterminate = false;
            } else if (checkedCheckboxes.length > 0) {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = true;
            } else {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = false;
            }
        }

        // تحديث عدد المحدد
        function updateSelectedCount() {
            const count = selectedRecords.size;
            const selectedCountElement = document.getElementById('selectedCount');
            const bulkActionsElement = document.getElementById('bulkActions');

            if (selectedCountElement) {
                selectedCountElement.textContent = count;
            }

            if (bulkActionsElement) {
                if (count > 0) {
                    bulkActionsElement.classList.add('show');
                } else {
                    bulkActionsElement.classList.remove('show');
                }
            }
        }

        // إلغاء التحديد
        function clearSelection() {
            selectedRecords.clear();
            document.querySelectorAll('.record-checkbox').forEach(checkbox => {
                checkbox.checked = false;
                toggleRowSelection(checkbox, false);
            });
            updateSelectAllState();
            updateSelectedCount();
        }

        // وظائف الإجراءات
        function createPartner() {
            window.location.href = 'create_partner.php';
        }

        function viewPartner(partnerId) {
            window.open('view_partner.php?id=' + partnerId, '_blank', 'width=800,height=600');
        }

        function editPartner(partnerId) {
            window.location.href = 'edit_partner.php?id=' + partnerId;
        }

        function toggleActive(partnerId) {
            if (confirm('هل أنت متأكد من تغيير حالة هذا الشريك؟')) {
                showLoading();

                fetch(window.location.href, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=toggle_active&partner_id=' + partnerId
                })
                .then(response => response.json())
                .then(data => {
                    hideLoading();
                    if (data.success) {
                        showMessage(data.message, 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showMessage(data.message || 'حدث خطأ أثناء التحديث', 'danger');
                    }
                })
                .catch(error => {
                    hideLoading();
                    showMessage('حدث خطأ في الاتصال', 'danger');
                });
            }
        }

        function duplicatePartner(partnerId) {
            window.location.href = 'create_partner.php?duplicate=' + partnerId;
        }

        function sendEmail(partnerId) {
            showMessage('ميزة إرسال البريد قيد التطوير', 'info');
        }

        function deletePartner(partnerId) {
            if (confirm('هل أنت متأكد من حذف هذا الشريك؟')) {
                showLoading();

                fetch(window.location.href, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=delete_partner&partner_id=' + partnerId
                })
                .then(response => response.json())
                .then(data => {
                    hideLoading();
                    if (data.success) {
                        showMessage(data.message, 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showMessage(data.message || 'حدث خطأ أثناء الحذف', 'danger');
                    }
                })
                .catch(error => {
                    hideLoading();
                    showMessage('حدث خطأ في الاتصال', 'danger');
                });
            }
        }

        // الإجراءات المجمعة
        function bulkAction(action) {
            if (selectedRecords.size === 0) {
                showMessage('يرجى تحديد شريك واحد على الأقل', 'warning');
                return;
            }

            const actionText = action === 'activate' ? 'تفعيل' :
                              action === 'deactivate' ? 'إلغاء تفعيل' : 'حذف';
            if (confirm(`هل أنت متأكد من ${actionText} ${selectedRecords.size} شريك؟`)) {
                showLoading();

                const formData = new FormData();
                formData.append('action', 'bulk_action');
                formData.append('bulk_action', action);
                selectedRecords.forEach(id => {
                    formData.append('selected_ids[]', id);
                });

                fetch(window.location.href, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    hideLoading();
                    if (data.success) {
                        showMessage(data.message, 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showMessage(data.message || 'حدث خطأ أثناء العملية', 'danger');
                    }
                })
                .catch(error => {
                    hideLoading();
                    showMessage('حدث خطأ في الاتصال', 'danger');
                });
            }
        }

        // وظائف الفلترة والبحث
        function filterByType(type) {
            const url = new URL(window.location);
            url.searchParams.set('type', type);
            window.location.href = url.toString();
        }

        function filterByActive(active) {
            const url = new URL(window.location);
            url.searchParams.set('active', active);
            window.location.href = url.toString();
        }

        function changeView(viewMode) {
            const url = new URL(window.location);
            url.searchParams.set('view', viewMode);
            window.location.href = url.toString();
        }

        function performSearch(searchTerm) {
            const url = new URL(window.location);
            if (searchTerm.trim()) {
                url.searchParams.set('search', searchTerm);
            } else {
                url.searchParams.delete('search');
            }
            window.location.href = url.toString();
        }

        function removeFilter(filterType) {
            const url = new URL(window.location);
            url.searchParams.delete(filterType);
            window.location.href = url.toString();
        }

        function refreshData() {
            location.reload();
        }

        function importPartners() {
            showMessage('ميزة الاستيراد قيد التطوير', 'info');
        }

        function exportData(format) {
            showMessage(`جاري تصدير البيانات بصيغة ${format}...`, 'info');
        }

        function printData() {
            window.print();
        }

        // وظائف المساعدة
        function showLoading() {
            const overlay = document.getElementById('loadingOverlay');
            if (overlay) {
                overlay.style.display = 'flex';
            }
        }

        function hideLoading() {
            const overlay = document.getElementById('loadingOverlay');
            if (overlay) {
                overlay.style.display = 'none';
            }
        }

        function showMessage(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';

            const iconClass = type === 'success' ? 'check-circle' :
                             type === 'danger' ? 'exclamation-triangle' :
                             type === 'warning' ? 'exclamation-triangle' : 'info-circle';

            alertDiv.innerHTML = `
                <i class="fas fa-${iconClass} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(alertDiv);

            setTimeout(() => {
                alertDiv.remove();
            }, 4000);
        }

        // تأثيرات بصرية إضافية
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير hover للبطاقات
            document.querySelectorAll('.partner-card').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // تأثير النقر على الصفوف
            document.querySelectorAll('.o_data_row').forEach(row => {
                row.addEventListener('click', function(e) {
                    if (!e.target.closest('input') && !e.target.closest('button') && !e.target.closest('.dropdown')) {
                        const checkbox = this.querySelector('.record-checkbox');
                        if (checkbox) {
                            checkbox.checked = !checkbox.checked;
                            checkbox.dispatchEvent(new Event('change'));
                        }
                    }
                });
            });

            // تأثير النقر على الإحصائيات
            document.querySelectorAll('.stat-item').forEach(item => {
                item.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });
        });
    </script>
</body>
</html>
