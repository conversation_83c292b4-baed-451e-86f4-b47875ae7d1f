<?php
/**
 * صفحة الشركاء المحسنة - بأسلوب Odoo المتقدم
 * Partners Page - Advanced Odoo Style
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// تحميل الملفات المطلوبة
require_once '../config/odoo_config.php';
require_once '../models/ResPartner.php';

// إنشاء كائن النموذج
$partner_model = new ResPartner();

// معالجة العمليات
$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = isset($_POST['action']) ? $_POST['action'] : '';

    switch ($action) {
        case 'create':
            $data = array(
                'name' => isset($_POST['name']) ? $_POST['name'] : '',
                'email' => isset($_POST['email']) ? $_POST['email'] : '',
                'phone' => isset($_POST['phone']) ? $_POST['phone'] : '',
                'mobile' => isset($_POST['mobile']) ? $_POST['mobile'] : '',
                'street' => isset($_POST['street']) ? $_POST['street'] : '',
                'street2' => isset($_POST['street2']) ? $_POST['street2'] : '',
                'city' => isset($_POST['city']) ? $_POST['city'] : '',
                'zip' => isset($_POST['zip']) ? $_POST['zip'] : '',
                'country_id' => isset($_POST['country_id']) ? $_POST['country_id'] : 1,
                'is_company' => isset($_POST['is_company']),
                'customer_rank' => isset($_POST['is_customer']) ? 1 : 0,
                'supplier_rank' => isset($_POST['is_supplier']) ? 1 : 0,
                'vat' => isset($_POST['vat']) ? $_POST['vat'] : '',
                'website' => isset($_POST['website']) ? $_POST['website'] : '',
                'title' => isset($_POST['title']) ? $_POST['title'] : '',
                'function' => isset($_POST['function']) ? $_POST['function'] : '',
                'comment' => isset($_POST['comment']) ? $_POST['comment'] : '',
                'credit_limit' => isset($_POST['credit_limit']) ? floatval($_POST['credit_limit']) : 0,
                'lang' => isset($_POST['lang']) ? $_POST['lang'] : 'ar_SA',
                'tz' => isset($_POST['tz']) ? $_POST['tz'] : 'Asia/Riyadh',
                'active' => 1
            );
            
            try {
                if ($partner_model->create($data)) {
                    $message = 'تم إنشاء الشريك بنجاح';
                    $message_type = 'success';
                } else {
                    $message = 'خطأ في إنشاء الشريك';
                    $message_type = 'danger';
                }
            } catch (Exception $e) {
                $message = 'خطأ في إنشاء الشريك: ' . $e->getMessage();
                $message_type = 'danger';
            }
            break;
            
        case 'update':
            $id = isset($_POST['id']) ? $_POST['id'] : 0;
            $data = array(
                'name' => isset($_POST['name']) ? $_POST['name'] : '',
                'email' => isset($_POST['email']) ? $_POST['email'] : '',
                'phone' => isset($_POST['phone']) ? $_POST['phone'] : '',
                'mobile' => isset($_POST['mobile']) ? $_POST['mobile'] : '',
                'street' => isset($_POST['street']) ? $_POST['street'] : '',
                'street2' => isset($_POST['street2']) ? $_POST['street2'] : '',
                'city' => isset($_POST['city']) ? $_POST['city'] : '',
                'zip' => isset($_POST['zip']) ? $_POST['zip'] : '',
                'country_id' => isset($_POST['country_id']) ? $_POST['country_id'] : 1,
                'is_company' => isset($_POST['is_company']),
                'customer_rank' => isset($_POST['is_customer']) ? 1 : 0,
                'supplier_rank' => isset($_POST['is_supplier']) ? 1 : 0,
                'vat' => isset($_POST['vat']) ? $_POST['vat'] : '',
                'website' => isset($_POST['website']) ? $_POST['website'] : '',
                'title' => isset($_POST['title']) ? $_POST['title'] : '',
                'function' => isset($_POST['function']) ? $_POST['function'] : '',
                'comment' => isset($_POST['comment']) ? $_POST['comment'] : '',
                'credit_limit' => isset($_POST['credit_limit']) ? floatval($_POST['credit_limit']) : 0,
                'lang' => isset($_POST['lang']) ? $_POST['lang'] : 'ar_SA',
                'tz' => isset($_POST['tz']) ? $_POST['tz'] : 'Asia/Riyadh'
            );
            
            try {
                if ($partner_model->update($id, $data)) {
                    $message = 'تم تحديث الشريك بنجاح';
                    $message_type = 'success';
                } else {
                    $message = 'خطأ في تحديث الشريك';
                    $message_type = 'danger';
                }
            } catch (Exception $e) {
                $message = 'خطأ في تحديث الشريك: ' . $e->getMessage();
                $message_type = 'danger';
            }
            break;
            
        case 'delete':
            $id = isset($_POST['id']) ? $_POST['id'] : 0;
            try {
                if ($partner_model->delete($id)) {
                    $message = 'تم حذف الشريك بنجاح';
                    $message_type = 'success';
                } else {
                    $message = 'خطأ في حذف الشريك';
                    $message_type = 'danger';
                }
            } catch (Exception $e) {
                $message = 'خطأ في حذف الشريك: ' . $e->getMessage();
                $message_type = 'danger';
            }
            break;

        case 'archive':
            $id = isset($_POST['id']) ? $_POST['id'] : 0;
            try {
                if ($partner_model->update($id, array('active' => 0))) {
                    $message = 'تم أرشفة الشريك بنجاح';
                    $message_type = 'success';
                } else {
                    $message = 'خطأ في أرشفة الشريك';
                    $message_type = 'danger';
                }
            } catch (Exception $e) {
                $message = 'خطأ في أرشفة الشريك: ' . $e->getMessage();
                $message_type = 'danger';
            }
            break;
    }
}

// معالجة الفلاتر والبحث
$search_term = isset($_GET['search']) ? $_GET['search'] : '';
$filter_type = isset($_GET['filter']) ? $_GET['filter'] : 'all';
$view_mode = isset($_GET['view']) ? $_GET['view'] : 'kanban';
$show_archived = isset($_GET['show_archived']);

// جلب البيانات مع معالجة الأخطاء
try {
    $partners = $partner_model->search(array(), array('name' => 'ASC'));
} catch (Exception $e) {
    // بيانات وهمية في حالة عدم وجود قاعدة بيانات
    $partners = array(
        array(
            'id' => 1,
            'name' => 'شركة التقنية المتقدمة',
            'email' => '<EMAIL>',
            'phone' => '+966 11 234 5678',
            'mobile' => '+966 50 123 4567',
            'street' => 'شارع الملك فهد',
            'city' => 'الرياض',
            'is_company' => 1,
            'customer_rank' => 1,
            'supplier_rank' => 0,
            'vat' => '123456789012345',
            'website' => 'https://advanced-tech.com',
            'active' => 1,
            'create_date' => date('Y-m-d H:i:s')
        ),
        array(
            'id' => 2,
            'name' => 'أحمد محمد العلي',
            'email' => '<EMAIL>',
            'phone' => '+966 11 987 6543',
            'mobile' => '+966 55 987 6543',
            'street' => 'حي النخيل',
            'city' => 'جدة',
            'is_company' => 0,
            'customer_rank' => 1,
            'supplier_rank' => 0,
            'vat' => '',
            'website' => '',
            'active' => 1,
            'create_date' => date('Y-m-d H:i:s')
        ),
        array(
            'id' => 3,
            'name' => 'مؤسسة الخدمات التجارية',
            'email' => '<EMAIL>',
            'phone' => '+966 13 456 7890',
            'mobile' => '+966 56 456 7890',
            'street' => 'طريق الدمام',
            'city' => 'الدمام',
            'is_company' => 1,
            'customer_rank' => 0,
            'supplier_rank' => 1,
            'vat' => '987654321098765',
            'website' => 'https://commercial-services.com',
            'active' => 1,
            'create_date' => date('Y-m-d H:i:s')
        ),
        array(
            'id' => 4,
            'name' => 'فاطمة أحمد السالم',
            'email' => '<EMAIL>',
            'phone' => '+966 12 345 6789',
            'mobile' => '+966 54 345 6789',
            'street' => 'شارع التحلية',
            'city' => 'جدة',
            'is_company' => 0,
            'customer_rank' => 1,
            'supplier_rank' => 0,
            'vat' => '',
            'website' => '',
            'active' => 1,
            'create_date' => date('Y-m-d H:i:s')
        ),
        array(
            'id' => 5,
            'name' => 'شركة الإنشاءات الحديثة',
            'email' => '<EMAIL>',
            'phone' => '+966 14 567 8901',
            'mobile' => '+966 57 567 8901',
            'street' => 'طريق الملك عبدالعزيز',
            'city' => 'الخبر',
            'is_company' => 1,
            'customer_rank' => 1,
            'supplier_rank' => 1,
            'vat' => '456789012345678',
            'website' => 'https://modern-construction.com',
            'active' => 1,
            'create_date' => date('Y-m-d H:i:s')
        )
    );
}

// تطبيق الفلاتر
$filtered_partners = array();
foreach ($partners as $partner) {
    // فلتر البحث
    if (!empty($search_term)) {
        $search_fields = strtolower($partner['name'] . ' ' . $partner['email'] . ' ' . $partner['phone']);
        if (strpos($search_fields, strtolower($search_term)) === false) {
            continue;
        }
    }
    
    // فلتر النوع
    switch ($filter_type) {
        case 'customers':
            if ($partner['customer_rank'] <= 0) continue 2;
            break;
        case 'suppliers':
            if ($partner['supplier_rank'] <= 0) continue 2;
            break;
        case 'companies':
            if (!$partner['is_company']) continue 2;
            break;
        case 'individuals':
            if ($partner['is_company']) continue 2;
            break;
    }
    
    // فلتر الأرشيف
    if (!$show_archived && isset($partner['active']) && !$partner['active']) {
        continue;
    }
    
    $filtered_partners[] = $partner;
}

$partners = $filtered_partners;

// حساب الإحصائيات
$total_partners = count($partners);
$customers_count = 0;
$suppliers_count = 0;
$companies_count = 0;
$individuals_count = 0;

foreach ($partners as $partner) {
    if ($partner['customer_rank'] > 0) $customers_count++;
    if ($partner['supplier_rank'] > 0) $suppliers_count++;
    if ($partner['is_company']) $companies_count++;
    else $individuals_count++;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الشركاء - نظام ERP</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Odoo Style -->
    <link href="../assets/css/odoo-style.css" rel="stylesheet">
    <link href="../assets/css/odoo-advanced.css" rel="stylesheet">
    
    <style>
        .partner-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
            margin-bottom: 1rem;
        }
        
        .partner-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);
        }
        
        .partner-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            font-size: 1.2rem;
        }
        
        .partner-type-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
        }
        
        .stats-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid;
            transition: all 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);
        }
        
        .stats-card h3 {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .filter-tabs {
            background: white;
            border-radius: 12px;
            padding: 0.5rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
        }
        
        .filter-tab {
            padding: 0.75rem 1.5rem;
            border: none;
            background: transparent;
            border-radius: 8px;
            transition: all 0.3s ease;
            color: #6c757d;
            font-weight: 500;
        }
        
        .filter-tab.active {
            background: linear-gradient(45deg, #714B67, #875A7B);
            color: white;
            box-shadow: 0 2px 8px rgba(113, 75, 103, 0.3);
        }
        
        .filter-tab:hover:not(.active) {
            background: #f8f9fa;
            color: #714B67;
        }
        
        .view-switcher {
            background: white;
            border-radius: 8px;
            padding: 0.25rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .view-btn {
            padding: 0.5rem 1rem;
            border: none;
            background: transparent;
            border-radius: 6px;
            transition: all 0.3s ease;
            color: #6c757d;
        }
        
        .view-btn.active {
            background: #714B67;
            color: white;
        }
        
        .search-box {
            background: white;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        
        .search-box:focus {
            border-color: #714B67;
            box-shadow: 0 0 0 0.2rem rgba(113, 75, 103, 0.25);
        }
        
        .action-btn {
            padding: 0.5rem;
            border: none;
            border-radius: 6px;
            transition: all 0.3s ease;
            margin: 0 0.25rem;
        }
        
        .action-btn:hover {
            transform: translateY(-1px);
        }
        
        .btn-edit {
            background: #17a2b8;
            color: white;
        }
        
        .btn-edit:hover {
            background: #138496;
            color: white;
        }
        
        .btn-delete {
            background: #dc3545;
            color: white;
        }
        
        .btn-delete:hover {
            background: #c82333;
            color: white;
        }
        
        .btn-archive {
            background: #6c757d;
            color: white;
        }
        
        .btn-archive:hover {
            background: #5a6268;
            color: white;
        }
        
        .kanban-view .partner-card {
            height: 100%;
        }
        
        .list-view .partner-card {
            margin-bottom: 0.5rem;
        }
        
        .list-view .partner-card .card-body {
            padding: 1rem;
        }
        
        @media (max-width: 768px) {
            .stats-card h3 {
                font-size: 1.5rem;
            }
            
            .filter-tab {
                padding: 0.5rem 1rem;
                font-size: 0.9rem;
            }
            
            .partner-card .card-body {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <?php include '../includes/navbar.php'; ?>
    
    <div class="o_main_content">
        <div class="o_content">
            <!-- الشريط الجانبي -->
            <?php include '../includes/sidebar.php'; ?>
            
            <!-- المحتوى الرئيسي -->
            <div class="o_action_manager">
                <!-- شريط التحكم -->
                <div class="o_control_panel">
                    <div class="o_cp_left">
                        <!-- مسار التنقل -->
                        <div class="o_breadcrumb">
                            <a href="dashboard_odoo.php" class="o_breadcrumb_item">الرئيسية</a>
                            <span>/</span>
                            <span class="o_breadcrumb_item active">الشركاء</span>
                        </div>
                    </div>
                    <div class="o_cp_right">
                        <!-- أزرار الإجراءات -->
                        <div class="btn-group">
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#partnerModal" onclick="openCreateModal()">
                                <i class="fas fa-plus me-1"></i>إنشاء شريك
                            </button>
                            <button class="btn btn-outline-secondary" onclick="importPartners()">
                                <i class="fas fa-upload me-1"></i>استيراد
                            </button>
                            <button class="btn btn-outline-secondary" onclick="exportPartners()">
                                <i class="fas fa-download me-1"></i>تصدير
                            </button>
                        </div>
                    </div>
                </div>

                <!-- محتوى الشركاء -->
                <div class="p-3">
                    <?php if ($message): ?>
                        <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show">
                            <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                            <?php echo $message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <!-- الإحصائيات -->
                    <div class="row mb-4">
                        <div class="col-md-3 mb-3">
                            <div class="stats-card border-primary">
                                <h3 class="text-primary"><?php echo $total_partners; ?></h3>
                                <p class="mb-0">إجمالي الشركاء</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="stats-card border-success">
                                <h3 class="text-success"><?php echo $customers_count; ?></h3>
                                <p class="mb-0">عملاء</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="stats-card border-warning">
                                <h3 class="text-warning"><?php echo $suppliers_count; ?></h3>
                                <p class="mb-0">موردين</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="stats-card border-info">
                                <h3 class="text-info"><?php echo $companies_count; ?></h3>
                                <p class="mb-0">شركات</p>
                            </div>
                        </div>
                    </div>

                    <!-- شريط البحث والفلاتر -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <!-- تبويبات الفلاتر -->
                            <div class="filter-tabs">
                                <button class="filter-tab <?php echo $filter_type === 'all' ? 'active' : ''; ?>"
                                        onclick="applyFilter('all')">
                                    <i class="fas fa-users me-1"></i>الكل
                                </button>
                                <button class="filter-tab <?php echo $filter_type === 'customers' ? 'active' : ''; ?>"
                                        onclick="applyFilter('customers')">
                                    <i class="fas fa-user-tie me-1"></i>العملاء
                                </button>
                                <button class="filter-tab <?php echo $filter_type === 'suppliers' ? 'active' : ''; ?>"
                                        onclick="applyFilter('suppliers')">
                                    <i class="fas fa-truck me-1"></i>الموردين
                                </button>
                                <button class="filter-tab <?php echo $filter_type === 'companies' ? 'active' : ''; ?>"
                                        onclick="applyFilter('companies')">
                                    <i class="fas fa-building me-1"></i>الشركات
                                </button>
                                <button class="filter-tab <?php echo $filter_type === 'individuals' ? 'active' : ''; ?>"
                                        onclick="applyFilter('individuals')">
                                    <i class="fas fa-user me-1"></i>الأفراد
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <!-- مربع البحث -->
                            <div class="input-group">
                                <input type="text" class="form-control search-box"
                                       placeholder="البحث في الشركاء..."
                                       value="<?php echo htmlspecialchars($search_term); ?>"
                                       onkeyup="searchPartners(this.value)">
                                <button class="btn btn-outline-secondary" type="button" onclick="clearSearch()">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- شريط أدوات العرض -->
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="d-flex align-items-center">
                            <!-- مفتاح العرض -->
                            <div class="view-switcher me-3">
                                <button class="view-btn <?php echo $view_mode === 'kanban' ? 'active' : ''; ?>"
                                        onclick="switchView('kanban')" title="عرض البطاقات">
                                    <i class="fas fa-th-large"></i>
                                </button>
                                <button class="view-btn <?php echo $view_mode === 'list' ? 'active' : ''; ?>"
                                        onclick="switchView('list')" title="عرض القائمة">
                                    <i class="fas fa-list"></i>
                                </button>
                            </div>

                            <!-- عدد النتائج -->
                            <span class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                <?php echo count($partners); ?> شريك
                            </span>
                        </div>

                        <div class="d-flex align-items-center">
                            <!-- خيارات إضافية -->
                            <div class="form-check me-3">
                                <input class="form-check-input" type="checkbox" id="showArchived"
                                       <?php echo $show_archived ? 'checked' : ''; ?>
                                       onchange="toggleArchived(this.checked)">
                                <label class="form-check-label" for="showArchived">
                                    عرض المؤرشف
                                </label>
                            </div>

                            <!-- ترتيب -->
                            <select class="form-select form-select-sm" style="width: auto;" onchange="sortPartners(this.value)">
                                <option value="name_asc">الاسم (أ-ي)</option>
                                <option value="name_desc">الاسم (ي-أ)</option>
                                <option value="date_desc">الأحدث</option>
                                <option value="date_asc">الأقدم</option>
                            </select>
                        </div>
                    </div>

                    <!-- عرض الشركاء -->
                    <div id="partnersContainer" class="<?php echo $view_mode; ?>-view">
                        <?php if (empty($partners)): ?>
                            <!-- رسالة عدم وجود بيانات -->
                            <div class="text-center py-5">
                                <div class="mb-4">
                                    <i class="fas fa-users fa-4x text-muted"></i>
                                </div>
                                <h4 class="text-muted">لا توجد شركاء</h4>
                                <p class="text-muted">ابدأ بإضافة شريك جديد لرؤية البيانات هنا</p>
                                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#partnerModal" onclick="openCreateModal()">
                                    <i class="fas fa-plus me-1"></i>إضافة شريك جديد
                                </button>
                            </div>
                        <?php else: ?>
                            <?php if ($view_mode === 'kanban'): ?>
                                <!-- عرض البطاقات -->
                                <div class="row">
                                    <?php foreach ($partners as $partner): ?>
                                        <div class="col-lg-4 col-md-6 mb-3">
                                            <div class="partner-card">
                                                <div class="card-body">
                                                    <div class="d-flex align-items-start mb-3">
                                                        <!-- صورة الشريك -->
                                                        <div class="partner-avatar me-3"
                                                             style="background: <?php echo $partner['is_company'] ? '#714B67' : '#28a745'; ?>">
                                                            <?php if ($partner['is_company']): ?>
                                                                <i class="fas fa-building"></i>
                                                            <?php else: ?>
                                                                <?php echo strtoupper(substr($partner['name'], 0, 1)); ?>
                                                            <?php endif; ?>
                                                        </div>

                                                        <!-- معلومات الشريك -->
                                                        <div class="flex-grow-1">
                                                            <h6 class="card-title mb-1">
                                                                <?php echo htmlspecialchars($partner['name']); ?>
                                                            </h6>

                                                            <!-- شارات النوع -->
                                                            <div class="mb-2">
                                                                <?php if ($partner['customer_rank'] > 0): ?>
                                                                    <span class="badge bg-success partner-type-badge me-1">عميل</span>
                                                                <?php endif; ?>
                                                                <?php if ($partner['supplier_rank'] > 0): ?>
                                                                    <span class="badge bg-warning partner-type-badge me-1">مورد</span>
                                                                <?php endif; ?>
                                                                <?php if ($partner['is_company']): ?>
                                                                    <span class="badge bg-primary partner-type-badge">شركة</span>
                                                                <?php else: ?>
                                                                    <span class="badge bg-secondary partner-type-badge">فرد</span>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>

                                                        <!-- قائمة الإجراءات -->
                                                        <div class="dropdown">
                                                            <button class="btn btn-sm btn-outline-secondary"
                                                                    data-bs-toggle="dropdown">
                                                                <i class="fas fa-ellipsis-v"></i>
                                                            </button>
                                                            <ul class="dropdown-menu">
                                                                <li>
                                                                    <a class="dropdown-item" href="#"
                                                                       onclick="editPartner(<?php echo $partner['id']; ?>)">
                                                                        <i class="fas fa-edit me-2"></i>تعديل
                                                                    </a>
                                                                </li>
                                                                <li>
                                                                    <a class="dropdown-item" href="#"
                                                                       onclick="viewPartner(<?php echo $partner['id']; ?>)">
                                                                        <i class="fas fa-eye me-2"></i>عرض
                                                                    </a>
                                                                </li>
                                                                <li><hr class="dropdown-divider"></li>
                                                                <li>
                                                                    <a class="dropdown-item text-warning" href="#"
                                                                       onclick="archivePartner(<?php echo $partner['id']; ?>)">
                                                                        <i class="fas fa-archive me-2"></i>أرشفة
                                                                    </a>
                                                                </li>
                                                                <li>
                                                                    <a class="dropdown-item text-danger" href="#"
                                                                       onclick="deletePartner(<?php echo $partner['id']; ?>)">
                                                                        <i class="fas fa-trash me-2"></i>حذف
                                                                    </a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </div>

                                                    <!-- تفاصيل الاتصال -->
                                                    <div class="contact-details">
                                                        <?php if (!empty($partner['email'])): ?>
                                                            <div class="mb-1">
                                                                <i class="fas fa-envelope text-muted me-2"></i>
                                                                <small><?php echo htmlspecialchars($partner['email']); ?></small>
                                                            </div>
                                                        <?php endif; ?>

                                                        <?php if (!empty($partner['phone'])): ?>
                                                            <div class="mb-1">
                                                                <i class="fas fa-phone text-muted me-2"></i>
                                                                <small><?php echo htmlspecialchars($partner['phone']); ?></small>
                                                            </div>
                                                        <?php endif; ?>

                                                        <?php if (!empty($partner['city'])): ?>
                                                            <div class="mb-1">
                                                                <i class="fas fa-map-marker-alt text-muted me-2"></i>
                                                                <small><?php echo htmlspecialchars($partner['city']); ?></small>
                                                            </div>
                                                        <?php endif; ?>

                                                        <?php if (!empty($partner['website'])): ?>
                                                            <div class="mb-1">
                                                                <i class="fas fa-globe text-muted me-2"></i>
                                                                <small>
                                                                    <a href="<?php echo htmlspecialchars($partner['website']); ?>"
                                                                       target="_blank" class="text-decoration-none">
                                                                        موقع إلكتروني
                                                                    </a>
                                                                </small>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php else: ?>
                                <!-- عرض القائمة -->
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>الاسم</th>
                                                <th>النوع</th>
                                                <th>البريد الإلكتروني</th>
                                                <th>الهاتف</th>
                                                <th>المدينة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($partners as $partner): ?>
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div class="partner-avatar me-2"
                                                                 style="background: <?php echo $partner['is_company'] ? '#714B67' : '#28a745'; ?>; width: 35px; height: 35px; font-size: 0.9rem;">
                                                                <?php if ($partner['is_company']): ?>
                                                                    <i class="fas fa-building"></i>
                                                                <?php else: ?>
                                                                    <?php echo strtoupper(substr($partner['name'], 0, 1)); ?>
                                                                <?php endif; ?>
                                                            </div>
                                                            <div>
                                                                <div class="fw-bold"><?php echo htmlspecialchars($partner['name']); ?></div>
                                                                <?php if (!empty($partner['vat'])): ?>
                                                                    <small class="text-muted">ضريبي: <?php echo htmlspecialchars($partner['vat']); ?></small>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <?php if ($partner['customer_rank'] > 0): ?>
                                                            <span class="badge bg-success me-1">عميل</span>
                                                        <?php endif; ?>
                                                        <?php if ($partner['supplier_rank'] > 0): ?>
                                                            <span class="badge bg-warning me-1">مورد</span>
                                                        <?php endif; ?>
                                                        <span class="badge bg-<?php echo $partner['is_company'] ? 'primary' : 'secondary'; ?>">
                                                            <?php echo $partner['is_company'] ? 'شركة' : 'فرد'; ?>
                                                        </span>
                                                    </td>
                                                    <td><?php echo htmlspecialchars(isset($partner['email']) ? $partner['email'] : ''); ?></td>
                                                    <td><?php echo htmlspecialchars(isset($partner['phone']) ? $partner['phone'] : ''); ?></td>
                                                    <td><?php echo htmlspecialchars(isset($partner['city']) ? $partner['city'] : ''); ?></td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            <button class="action-btn btn-edit"
                                                                    onclick="editPartner(<?php echo $partner['id']; ?>)"
                                                                    title="تعديل">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            <button class="action-btn btn-archive"
                                                                    onclick="archivePartner(<?php echo $partner['id']; ?>)"
                                                                    title="أرشفة">
                                                                <i class="fas fa-archive"></i>
                                                            </button>
                                                            <button class="action-btn btn-delete"
                                                                    onclick="deletePartner(<?php echo $partner['id']; ?>)"
                                                                    title="حذف">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة حوار الشريك -->
    <div class="modal fade" id="partnerModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="partnerModalTitle">إنشاء شريك جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="partnerForm" method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" id="formAction" value="create">
                        <input type="hidden" name="id" id="partnerId" value="">

                        <div class="row">
                            <!-- المعلومات الأساسية -->
                            <div class="col-md-6">
                                <h6 class="mb-3">المعلومات الأساسية</h6>

                                <div class="mb-3">
                                    <label class="form-label">الاسم *</label>
                                    <input type="text" class="form-control" name="name" id="partnerName" required>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" name="email" id="partnerEmail">
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الهاتف</label>
                                            <input type="text" class="form-control" name="phone" id="partnerPhone">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الجوال</label>
                                            <input type="text" class="form-control" name="mobile" id="partnerMobile">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">الموقع الإلكتروني</label>
                                    <input type="url" class="form-control" name="website" id="partnerWebsite">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">الرقم الضريبي</label>
                                    <input type="text" class="form-control" name="vat" id="partnerVat">
                                </div>
                            </div>

                            <!-- العنوان والتصنيف -->
                            <div class="col-md-6">
                                <h6 class="mb-3">العنوان والتصنيف</h6>

                                <div class="mb-3">
                                    <label class="form-label">العنوان</label>
                                    <input type="text" class="form-control" name="street" id="partnerStreet">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">العنوان 2</label>
                                    <input type="text" class="form-control" name="street2" id="partnerStreet2">
                                </div>

                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label class="form-label">المدينة</label>
                                            <input type="text" class="form-control" name="city" id="partnerCity">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">الرمز البريدي</label>
                                            <input type="text" class="form-control" name="zip" id="partnerZip">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">الدولة</label>
                                    <select class="form-select" name="country_id" id="partnerCountry">
                                        <option value="1">المملكة العربية السعودية</option>
                                        <option value="2">الإمارات العربية المتحدة</option>
                                        <option value="3">الكويت</option>
                                        <option value="4">قطر</option>
                                        <option value="5">البحرين</option>
                                        <option value="6">عمان</option>
                                    </select>
                                </div>

                                <!-- خيارات التصنيف -->
                                <div class="mb-3">
                                    <label class="form-label">التصنيف</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="is_company" id="isCompany">
                                        <label class="form-check-label" for="isCompany">
                                            شركة
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="is_customer" id="isCustomer">
                                        <label class="form-check-label" for="isCustomer">
                                            عميل
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="is_supplier" id="isSupplier">
                                        <label class="form-check-label" for="isSupplier">
                                            مورد
                                        </label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">حد الائتمان</label>
                                    <input type="number" class="form-control" name="credit_limit" id="partnerCreditLimit" step="0.01" min="0">
                                </div>
                            </div>
                        </div>

                        <!-- ملاحظات -->
                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" name="comment" id="partnerComment" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // متغيرات عامة
        let currentFilter = '<?php echo $filter_type; ?>';
        let currentView = '<?php echo $view_mode; ?>';
        let currentSearch = '<?php echo $search_term; ?>';

        // تطبيق الفلتر
        function applyFilter(filter) {
            const url = new URL(window.location);
            url.searchParams.set('filter', filter);
            window.location.href = url.toString();
        }

        // تبديل طريقة العرض
        function switchView(view) {
            const url = new URL(window.location);
            url.searchParams.set('view', view);
            window.location.href = url.toString();
        }

        // البحث في الشركاء
        function searchPartners(term) {
            const url = new URL(window.location);
            if (term.trim()) {
                url.searchParams.set('search', term);
            } else {
                url.searchParams.delete('search');
            }

            // تأخير البحث لتحسين الأداء
            clearTimeout(window.searchTimeout);
            window.searchTimeout = setTimeout(() => {
                window.location.href = url.toString();
            }, 500);
        }

        // مسح البحث
        function clearSearch() {
            const url = new URL(window.location);
            url.searchParams.delete('search');
            window.location.href = url.toString();
        }

        // تبديل عرض المؤرشف
        function toggleArchived(show) {
            const url = new URL(window.location);
            if (show) {
                url.searchParams.set('show_archived', '1');
            } else {
                url.searchParams.delete('show_archived');
            }
            window.location.href = url.toString();
        }

        // ترتيب الشركاء
        function sortPartners(sortBy) {
            // يمكن تطبيق الترتيب هنا
            console.log('Sorting by:', sortBy);
        }

        // فتح نافذة إنشاء شريك جديد
        function openCreateModal() {
            document.getElementById('partnerModalTitle').textContent = 'إنشاء شريك جديد';
            document.getElementById('formAction').value = 'create';
            document.getElementById('partnerId').value = '';
            document.getElementById('partnerForm').reset();
        }

        // تعديل شريك
        function editPartner(id) {
            // هنا يمكن جلب بيانات الشريك وملء النموذج
            document.getElementById('partnerModalTitle').textContent = 'تعديل الشريك';
            document.getElementById('formAction').value = 'update';
            document.getElementById('partnerId').value = id;

            // فتح النافذة
            const modal = new bootstrap.Modal(document.getElementById('partnerModal'));
            modal.show();
        }

        // عرض تفاصيل الشريك
        function viewPartner(id) {
            // يمكن فتح صفحة تفاصيل الشريك
            window.location.href = `partner_details.php?id=${id}`;
        }

        // أرشفة شريك
        function archivePartner(id) {
            if (confirm('هل أنت متأكد من أرشفة هذا الشريك؟')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="archive">
                    <input type="hidden" name="id" value="${id}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        // حذف شريك
        function deletePartner(id) {
            if (confirm('هل أنت متأكد من حذف هذا الشريك؟ لا يمكن التراجع عن هذا الإجراء.')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="id" value="${id}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        // استيراد الشركاء
        function importPartners() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.csv,.xlsx,.xls';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    // هنا يمكن إضافة كود رفع الملف
                    alert('سيتم تطوير ميزة الاستيراد قريباً');
                }
            };
            input.click();
        }

        // تصدير الشركاء
        function exportPartners() {
            // إنشاء رابط تحميل
            const url = new URL(window.location);
            url.pathname = url.pathname.replace('.php', '_export.php');
            url.searchParams.set('format', 'excel');

            const link = document.createElement('a');
            link.href = url.toString();
            link.download = 'partners_export.xlsx';
            link.click();
        }

        // تحسينات UX
        document.addEventListener('DOMContentLoaded', function() {
            // تأثيرات hover للبطاقات
            document.querySelectorAll('.partner-card').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // تحسين البحث
            const searchInput = document.querySelector('.search-box');
            if (searchInput) {
                searchInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        searchPartners(this.value);
                    }
                });
            }

            // تحسين النماذج
            const partnerForm = document.getElementById('partnerForm');
            if (partnerForm) {
                partnerForm.addEventListener('submit', function(e) {
                    const name = document.getElementById('partnerName').value.trim();
                    if (!name) {
                        e.preventDefault();
                        alert('يرجى إدخال اسم الشريك');
                        document.getElementById('partnerName').focus();
                        return false;
                    }
                });
            }
        });
    </script>
</body>
</html>
