<?php
/**
 * صفحة مراقبة الأداء بأسلوب Odoo
 * Performance Monitoring Page - Odoo Style
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// التحقق من الصلاحيات
$user_groups = isset($_SESSION['groups']) ? $_SESSION['groups'] : array();
if (!in_array('admin', $user_groups)) {
    header('Location: ../dashboard.php?error=access_denied');
    exit();
}

// بيانات الأداء التجريبية
$performance_data = array(
    'server' => array(
        'cpu_usage' => 35.2,
        'memory_usage' => 68.5,
        'disk_usage' => 42.1,
        'network_in' => 125.6,
        'network_out' => 89.3,
        'uptime' => '15 days, 8 hours',
        'load_average' => '0.85, 0.92, 1.05'
    ),
    'database' => array(
        'connections' => 12,
        'max_connections' => 100,
        'queries_per_second' => 45.2,
        'slow_queries' => 3,
        'cache_hit_ratio' => 94.8,
        'size' => '2.5 GB',
        'tables' => 156
    ),
    'application' => array(
        'response_time' => 245,
        'requests_per_minute' => 180,
        'active_sessions' => 8,
        'error_rate' => 0.2,
        'cache_size' => '128 MB',
        'php_memory' => '64 MB',
        'version' => PHP_VERSION
    )
);

// بيانات الرسوم البيانية (آخر 24 ساعة)
$chart_data = array(
    'cpu' => array(25, 30, 35, 40, 38, 42, 45, 48, 35, 32, 28, 30, 33, 36, 39, 42, 38, 35, 32, 30, 28, 25, 22, 20),
    'memory' => array(60, 62, 65, 68, 70, 72, 75, 78, 76, 74, 72, 70, 68, 66, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46),
    'requests' => array(120, 135, 150, 180, 200, 220, 250, 280, 260, 240, 220, 200, 180, 160, 140, 120, 100, 80, 60, 40, 30, 25, 20, 15)
);

// سجل الأحداث
$events_log = array(
    array(
        'time' => '2024-01-15 14:30:00',
        'type' => 'warning',
        'message' => 'استخدام الذاكرة مرتفع (75%)',
        'source' => 'System Monitor'
    ),
    array(
        'time' => '2024-01-15 13:45:00',
        'type' => 'info',
        'message' => 'تم تنظيف ذاكرة التخزين المؤقت',
        'source' => 'Cache Manager'
    ),
    array(
        'time' => '2024-01-15 12:20:00',
        'type' => 'success',
        'message' => 'تم تحسين قاعدة البيانات بنجاح',
        'source' => 'Database Optimizer'
    ),
    array(
        'time' => '2024-01-15 11:15:00',
        'type' => 'error',
        'message' => 'فشل في الاتصال بخادم البريد الإلكتروني',
        'source' => 'Mail Server'
    ),
    array(
        'time' => '2024-01-15 10:30:00',
        'type' => 'info',
        'message' => 'تم إنشاء نسخة احتياطية تلقائية',
        'source' => 'Backup Service'
    )
);

// طريقة العرض المحددة
$view_mode = isset($_GET['view']) ? $_GET['view'] : 'overview';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مراقبة الأداء - نظام ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --odoo-primary: #714B67;
            --odoo-secondary: #8B5A8C;
        }

        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 0.85rem;
        }

        .navbar-odoo {
            background: linear-gradient(45deg, var(--odoo-primary), var(--odoo-secondary));
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .page-header {
            background: linear-gradient(45deg, #2ECC71, #27AE60);
            color: white;
            padding: 1.5rem;
            border-radius: 15px;
            margin-bottom: 1.5rem;
        }

        .view-controls {
            background: white;
            border-radius: 10px;
            padding: 0.8rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }

        .view-btn {
            border: 1px solid #dee2e6;
            background: white;
            color: #6c757d;
            padding: 0.4rem 0.8rem;
            border-radius: 5px;
            transition: all 0.3s ease;
            text-decoration: none;
            font-size: 0.8rem;
        }

        .view-btn.active {
            background: #2ECC71;
            color: white;
            border-color: #2ECC71;
        }

        .view-btn:hover {
            background: #27AE60;
            color: white;
            border-color: #27AE60;
            text-decoration: none;
        }

        .metric-card {
            background: white;
            border-radius: 12px;
            padding: 1.2rem;
            margin-bottom: 0.8rem;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
            transition: all 0.3s ease;
            border-left: 3px solid;
        }

        .metric-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.12);
        }

        .metric-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }

        .progress-circle {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .chart-card {
            background: white;
            border-radius: 12px;
            padding: 1.2rem;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
            margin-bottom: 1rem;
        }

        .breadcrumb-odoo {
            background: white;
            border-radius: 8px;
            padding: 0.8rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            font-size: 0.8rem;
        }

        .event-item {
            background: white;
            border-radius: 8px;
            padding: 0.8rem;
            margin-bottom: 0.5rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            border-left: 3px solid;
        }

        .event-item.success { border-left-color: #28a745; }
        .event-item.warning { border-left-color: #ffc107; }
        .event-item.error { border-left-color: #dc3545; }
        .event-item.info { border-left-color: #17a2b8; }

        .real-time-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            background: #28a745;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-odoo">
        <div class="container-fluid">
            <a class="navbar-brand text-white" href="../dashboard.php">
                <i class="fas fa-cube me-2"></i>
                نظام ERP - مراقبة الأداء
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="../dashboard.php">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
                <a class="nav-link text-white" href="system_admin.php">
                    <i class="fas fa-tools me-1"></i>إدارة النظام
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-3">
        <!-- مسار التنقل -->
        <nav class="breadcrumb-odoo">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="../dashboard.php">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="system_admin.php">إدارة النظام</a></li>
                <li class="breadcrumb-item active">مراقبة الأداء</li>
            </ol>
        </nav>

        <!-- العنوان الرئيسي -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h3><i class="fas fa-chart-line me-2"></i>مراقبة الأداء</h3>
                    <p class="mb-0 small">
                        <span class="real-time-indicator me-2"></span>
                        مراقبة أداء النظام والخادم في الوقت الفعلي
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-light btn-sm me-2">
                        <i class="fas fa-sync me-2"></i>تحديث
                    </button>
                    <button class="btn btn-outline-light btn-sm">
                        <i class="fas fa-download me-2"></i>تقرير
                    </button>
                </div>
            </div>
        </div>

        <!-- أدوات التحكم والعرض -->
        <div class="view-controls">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <div class="btn-group" role="group">
                        <a href="?view=overview" class="view-btn <?php echo $view_mode === 'overview' ? 'active' : ''; ?>">
                            <i class="fas fa-tachometer-alt me-1"></i>نظرة عامة
                        </a>
                        <a href="?view=server" class="view-btn <?php echo $view_mode === 'server' ? 'active' : ''; ?>">
                            <i class="fas fa-server me-1"></i>الخادم
                        </a>
                        <a href="?view=database" class="view-btn <?php echo $view_mode === 'database' ? 'active' : ''; ?>">
                            <i class="fas fa-database me-1"></i>قاعدة البيانات
                        </a>
                        <a href="?view=application" class="view-btn <?php echo $view_mode === 'application' ? 'active' : ''; ?>">
                            <i class="fas fa-code me-1"></i>التطبيق
                        </a>
                        <a href="?view=logs" class="view-btn <?php echo $view_mode === 'logs' ? 'active' : ''; ?>">
                            <i class="fas fa-file-alt me-1"></i>السجلات
                        </a>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="d-flex justify-content-end align-items-center">
                        <small class="text-muted me-2">آخر تحديث: <?php echo date('H:i:s'); ?></small>
                        <span class="real-time-indicator"></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- محتوى العرض -->
        <?php if ($view_mode === 'overview'): ?>
            <!-- نظرة عامة -->
            <div class="row mb-3">
                <!-- مقاييس الخادم -->
                <div class="col-md-3">
                    <div class="metric-card border-primary">
                        <div class="d-flex align-items-center">
                            <div class="metric-icon me-3 bg-primary">
                                <i class="fas fa-microchip"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">استخدام المعالج</h6>
                                <h4 class="mb-0 text-primary"><?php echo $performance_data['server']['cpu_usage']; ?>%</h4>
                            </div>
                        </div>
                        <div class="progress mt-2" style="height: 6px;">
                            <div class="progress-bar bg-primary" style="width: <?php echo $performance_data['server']['cpu_usage']; ?>%"></div>
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="metric-card border-warning">
                        <div class="d-flex align-items-center">
                            <div class="metric-icon me-3 bg-warning">
                                <i class="fas fa-memory"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">استخدام الذاكرة</h6>
                                <h4 class="mb-0 text-warning"><?php echo $performance_data['server']['memory_usage']; ?>%</h4>
                            </div>
                        </div>
                        <div class="progress mt-2" style="height: 6px;">
                            <div class="progress-bar bg-warning" style="width: <?php echo $performance_data['server']['memory_usage']; ?>%"></div>
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="metric-card border-info">
                        <div class="d-flex align-items-center">
                            <div class="metric-icon me-3 bg-info">
                                <i class="fas fa-hdd"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">استخدام القرص</h6>
                                <h4 class="mb-0 text-info"><?php echo $performance_data['server']['disk_usage']; ?>%</h4>
                            </div>
                        </div>
                        <div class="progress mt-2" style="height: 6px;">
                            <div class="progress-bar bg-info" style="width: <?php echo $performance_data['server']['disk_usage']; ?>%"></div>
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="metric-card border-success">
                        <div class="d-flex align-items-center">
                            <div class="metric-icon me-3 bg-success">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">زمن الاستجابة</h6>
                                <h4 class="mb-0 text-success"><?php echo $performance_data['application']['response_time']; ?>ms</h4>
                            </div>
                        </div>
                        <small class="text-muted">متوسط آخر ساعة</small>
                    </div>
                </div>
            </div>

            <!-- الرسوم البيانية -->
            <div class="row mb-3">
                <div class="col-md-8">
                    <div class="chart-card">
                        <h6><i class="fas fa-chart-area me-2"></i>استخدام الموارد (آخر 24 ساعة)</h6>
                        <canvas id="resourceChart" height="100"></canvas>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="chart-card">
                        <h6><i class="fas fa-database me-2"></i>قاعدة البيانات</h6>
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="progress-circle bg-light">
                                    <canvas id="dbConnectionsChart" width="60" height="60"></canvas>
                                    <div class="position-absolute">
                                        <small class="fw-bold"><?php echo $performance_data['database']['connections']; ?></small>
                                    </div>
                                </div>
                                <small class="text-muted">اتصالات نشطة</small>
                            </div>
                            <div class="col-6">
                                <div class="progress-circle bg-light">
                                    <canvas id="cacheHitChart" width="60" height="60"></canvas>
                                    <div class="position-absolute">
                                        <small class="fw-bold"><?php echo $performance_data['database']['cache_hit_ratio']; ?>%</small>
                                    </div>
                                </div>
                                <small class="text-muted">نسبة إصابة التخزين المؤقت</small>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-6">
                                <small class="text-muted">الاستعلامات/ثانية</small>
                                <div class="fw-bold text-primary"><?php echo $performance_data['database']['queries_per_second']; ?></div>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">استعلامات بطيئة</small>
                                <div class="fw-bold text-warning"><?php echo $performance_data['database']['slow_queries']; ?></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات النظام -->
            <div class="row">
                <div class="col-md-6">
                    <div class="chart-card">
                        <h6><i class="fas fa-info-circle me-2"></i>معلومات النظام</h6>
                        <div class="row">
                            <div class="col-6">
                                <ul class="list-unstyled small">
                                    <li><strong>وقت التشغيل:</strong> <?php echo $performance_data['server']['uptime']; ?></li>
                                    <li><strong>متوسط التحميل:</strong> <?php echo $performance_data['server']['load_average']; ?></li>
                                    <li><strong>إصدار PHP:</strong> <?php echo $performance_data['application']['version']; ?></li>
                                    <li><strong>ذاكرة PHP:</strong> <?php echo $performance_data['application']['php_memory']; ?></li>
                                </ul>
                            </div>
                            <div class="col-6">
                                <ul class="list-unstyled small">
                                    <li><strong>الجلسات النشطة:</strong> <?php echo $performance_data['application']['active_sessions']; ?></li>
                                    <li><strong>الطلبات/دقيقة:</strong> <?php echo $performance_data['application']['requests_per_minute']; ?></li>
                                    <li><strong>معدل الأخطاء:</strong> <?php echo $performance_data['application']['error_rate']; ?>%</li>
                                    <li><strong>حجم التخزين المؤقت:</strong> <?php echo $performance_data['application']['cache_size']; ?></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="chart-card">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>آخر الأحداث</h6>
                        <div style="max-height: 200px; overflow-y: auto;">
                            <?php foreach (array_slice($events_log, 0, 5) as $event): ?>
                                <div class="event-item <?php echo $event['type']; ?>">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <small class="fw-bold"><?php echo $event['message']; ?></small>
                                            <br>
                                            <small class="text-muted"><?php echo $event['source']; ?></small>
                                        </div>
                                        <small class="text-muted"><?php echo date('H:i', strtotime($event['time'])); ?></small>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>

        <?php else: ?>
            <!-- عرض تفصيلي حسب النوع -->
            <div class="chart-card">
                <h5>عرض تفصيلي - <?php echo ucfirst($view_mode); ?></h5>
                <p class="text-muted">سيتم تطوير العروض التفصيلية قريباً...</p>
            </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // رسم بياني لاستخدام الموارد
        const ctx = document.getElementById('resourceChart').getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: Array.from({length: 24}, (_, i) => i + ':00'),
                datasets: [{
                    label: 'المعالج %',
                    data: <?php echo json_encode($chart_data['cpu']); ?>,
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4
                }, {
                    label: 'الذاكرة %',
                    data: <?php echo json_encode($chart_data['memory']); ?>,
                    borderColor: '#ffc107',
                    backgroundColor: 'rgba(255, 193, 7, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                },
                plugins: {
                    legend: {
                        position: 'top'
                    }
                }
            }
        });

        // تحديث تلقائي كل 30 ثانية
        setInterval(function() {
            // محاكاة تحديث البيانات
            document.querySelector('.real-time-indicator').style.animation = 'none';
            setTimeout(() => {
                document.querySelector('.real-time-indicator').style.animation = 'pulse 2s infinite';
            }, 100);
        }, 30000);

        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.metric-card, .chart-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(15px)';

                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 80);
            });
        });
    </script>
</body>
</html>