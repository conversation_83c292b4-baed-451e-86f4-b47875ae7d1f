<?php
/**
 * صفحة إدارة الصلاحيات والأدوار بأسلوب Odoo
 * Permissions and Roles Management Page - Odoo Style
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// التحقق من الصلاحيات
$user_groups = isset($_SESSION['groups']) ? $_SESSION['groups'] : array();
if (!in_array('admin', $user_groups)) {
    header('Location: ../dashboard.php?error=access_denied');
    exit();
}

// تحميل نظام قاعدة البيانات
require_once '../config/odoo_config.php';
require_once '../config/odoo_database.php';

$db = OdooDatabase::getInstance();
$message = '';
$message_type = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = isset($_POST['action']) ? $_POST['action'] : '';
    
    try {
        switch ($action) {
            case 'create_role':
                $role_name = isset($_POST['role_name']) ? $_POST['role_name'] : '';
                $permissions = isset($_POST['permissions']) ? $_POST['permissions'] : array();
                
                if (empty($role_name)) {
                    throw new Exception('اسم الدور مطلوب');
                }
                
                $message = "تم إنشاء الدور '{$role_name}' بنجاح";
                $message_type = 'success';
                break;
                
            case 'update_permissions':
                $role_id = isset($_POST['role_id']) ? $_POST['role_id'] : '';
                $permissions = isset($_POST['permissions']) ? $_POST['permissions'] : array();
                
                $message = "تم تحديث الصلاحيات بنجاح";
                $message_type = 'success';
                break;
                
            default:
                throw new Exception('إجراء غير صحيح');
        }
    } catch (Exception $e) {
        $message = 'خطأ: ' . $e->getMessage();
        $message_type = 'error';
    }
}

// الأدوار المتاحة
$roles = array(
    array(
        'id' => 1,
        'name' => 'مدير النظام',
        'technical_name' => 'admin',
        'description' => 'صلاحيات كاملة لجميع أجزاء النظام',
        'users_count' => 1,
        'color' => 'danger',
        'icon' => 'fas fa-crown'
    ),
    array(
        'id' => 2,
        'name' => 'مدير عام',
        'technical_name' => 'manager',
        'description' => 'صلاحيات إدارية محدودة',
        'users_count' => 2,
        'color' => 'warning',
        'icon' => 'fas fa-user-tie'
    ),
    array(
        'id' => 3,
        'name' => 'مستخدم عادي',
        'technical_name' => 'user',
        'description' => 'صلاحيات أساسية للاستخدام',
        'users_count' => 5,
        'color' => 'primary',
        'icon' => 'fas fa-user'
    ),
    array(
        'id' => 4,
        'name' => 'محاسب',
        'technical_name' => 'accountant',
        'description' => 'صلاحيات المحاسبة والتقارير المالية',
        'users_count' => 2,
        'color' => 'success',
        'icon' => 'fas fa-calculator'
    ),
    array(
        'id' => 5,
        'name' => 'مندوب مبيعات',
        'technical_name' => 'sales',
        'description' => 'صلاحيات المبيعات والعملاء',
        'users_count' => 3,
        'color' => 'info',
        'icon' => 'fas fa-handshake'
    ),
    array(
        'id' => 6,
        'name' => 'قراءة فقط',
        'technical_name' => 'readonly',
        'description' => 'عرض البيانات بدون تعديل',
        'users_count' => 1,
        'color' => 'secondary',
        'icon' => 'fas fa-eye'
    )
);

// الصلاحيات المتاحة مجمعة حسب الوحدة
$permissions = array(
    'base' => array(
        'name' => 'الوحدة الأساسية',
        'icon' => 'fas fa-cogs',
        'permissions' => array(
            'base.read' => 'عرض البيانات الأساسية',
            'base.write' => 'تعديل البيانات الأساسية',
            'base.create' => 'إنشاء بيانات جديدة',
            'base.delete' => 'حذف البيانات',
            'base.admin' => 'إدارة النظام'
        )
    ),
    'account' => array(
        'name' => 'المحاسبة',
        'icon' => 'fas fa-calculator',
        'permissions' => array(
            'account.read' => 'عرض البيانات المحاسبية',
            'account.write' => 'تعديل القيود المحاسبية',
            'account.create' => 'إنشاء قيود جديدة',
            'account.validate' => 'اعتماد القيود',
            'account.reports' => 'التقارير المالية'
        )
    ),
    'sale' => array(
        'name' => 'المبيعات',
        'icon' => 'fas fa-shopping-cart',
        'permissions' => array(
            'sale.read' => 'عرض أوامر البيع',
            'sale.write' => 'تعديل أوامر البيع',
            'sale.create' => 'إنشاء أوامر بيع جديدة',
            'sale.confirm' => 'تأكيد أوامر البيع',
            'sale.invoice' => 'إنشاء فواتير'
        )
    ),
    'purchase' => array(
        'name' => 'المشتريات',
        'icon' => 'fas fa-shopping-bag',
        'permissions' => array(
            'purchase.read' => 'عرض أوامر الشراء',
            'purchase.write' => 'تعديل أوامر الشراء',
            'purchase.create' => 'إنشاء أوامر شراء جديدة',
            'purchase.approve' => 'اعتماد أوامر الشراء',
            'purchase.receive' => 'استلام البضائع'
        )
    ),
    'stock' => array(
        'name' => 'المخزون',
        'icon' => 'fas fa-boxes',
        'permissions' => array(
            'stock.read' => 'عرض المخزون',
            'stock.write' => 'تعديل المخزون',
            'stock.move' => 'نقل المخزون',
            'stock.adjust' => 'تسوية المخزون',
            'stock.reports' => 'تقارير المخزون'
        )
    ),
    'hr' => array(
        'name' => 'الموارد البشرية',
        'icon' => 'fas fa-users',
        'permissions' => array(
            'hr.read' => 'عرض بيانات الموظفين',
            'hr.write' => 'تعديل بيانات الموظفين',
            'hr.payroll' => 'إدارة الرواتب',
            'hr.attendance' => 'إدارة الحضور',
            'hr.reports' => 'تقارير الموارد البشرية'
        )
    )
);

// مصفوفة الصلاحيات لكل دور
$role_permissions = array(
    'admin' => array_keys(array_merge(...array_column($permissions, 'permissions'))),
    'manager' => array(
        'base.read', 'base.write', 'base.create',
        'account.read', 'account.reports',
        'sale.read', 'sale.write', 'sale.create', 'sale.confirm',
        'purchase.read', 'purchase.write', 'purchase.create',
        'stock.read', 'stock.write', 'stock.move',
        'hr.read', 'hr.write', 'hr.reports'
    ),
    'user' => array(
        'base.read',
        'account.read',
        'sale.read', 'sale.write',
        'purchase.read',
        'stock.read',
        'hr.read'
    ),
    'accountant' => array(
        'base.read',
        'account.read', 'account.write', 'account.create', 'account.validate', 'account.reports',
        'sale.read',
        'purchase.read'
    ),
    'sales' => array(
        'base.read',
        'sale.read', 'sale.write', 'sale.create', 'sale.confirm', 'sale.invoice',
        'stock.read'
    ),
    'readonly' => array(
        'base.read',
        'account.read',
        'sale.read',
        'purchase.read',
        'stock.read',
        'hr.read'
    )
);

// طريقة العرض المحددة
$view_mode = isset($_GET['view']) ? $_GET['view'] : 'roles';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الصلاحيات والأدوار - نظام ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --odoo-primary: #714B67;
            --odoo-secondary: #8B5A8C;
        }
        
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 0.85rem;
        }
        
        .navbar-odoo {
            background: linear-gradient(45deg, var(--odoo-primary), var(--odoo-secondary));
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .page-header {
            background: linear-gradient(45deg, #E74C3C, #C0392B);
            color: white;
            padding: 1.5rem;
            border-radius: 15px;
            margin-bottom: 1.5rem;
        }
        
        .view-controls {
            background: white;
            border-radius: 10px;
            padding: 0.8rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        
        .view-btn {
            border: 1px solid #dee2e6;
            background: white;
            color: #6c757d;
            padding: 0.4rem 0.8rem;
            border-radius: 5px;
            transition: all 0.3s ease;
            text-decoration: none;
            font-size: 0.8rem;
        }
        
        .view-btn.active {
            background: #E74C3C;
            color: white;
            border-color: #E74C3C;
        }
        
        .view-btn:hover {
            background: #C0392B;
            color: white;
            border-color: #C0392B;
            text-decoration: none;
        }
        
        .role-card {
            background: white;
            border-radius: 12px;
            padding: 1.2rem;
            margin-bottom: 0.8rem;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
            transition: all 0.3s ease;
            border-left: 3px solid;
        }
        
        .role-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.12);
        }
        
        .role-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }
        
        .permission-card {
            background: white;
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 0.8rem;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
            border-left: 3px solid #17a2b8;
        }
        
        .breadcrumb-odoo {
            background: white;
            border-radius: 8px;
            padding: 0.8rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            font-size: 0.8rem;
        }
        
        .stats-card {
            background: white;
            border-radius: 12px;
            padding: 1.2rem;
            text-align: center;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
            border-left: 3px solid;
            margin-bottom: 1rem;
        }
        
        .permission-matrix {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
        }
        
        .permission-matrix th {
            background: linear-gradient(45deg, #E74C3C, #C0392B);
            color: white;
            border: none;
            padding: 0.8rem;
            font-size: 0.75rem;
        }
        
        .permission-matrix td {
            padding: 0.5rem;
            border-color: #f8f9fa;
            vertical-align: middle;
            font-size: 0.75rem;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-odoo">
        <div class="container-fluid">
            <a class="navbar-brand text-white" href="../dashboard.php">
                <i class="fas fa-cube me-2"></i>
                نظام ERP - إدارة الصلاحيات
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="../dashboard.php">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
                <a class="nav-link text-white" href="system_admin.php">
                    <i class="fas fa-tools me-1"></i>إدارة النظام
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-3">
        <!-- مسار التنقل -->
        <nav class="breadcrumb-odoo">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="../dashboard.php">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="system_admin.php">إدارة النظام</a></li>
                <li class="breadcrumb-item active">إدارة الصلاحيات والأدوار</li>
            </ol>
        </nav>

        <!-- العنوان الرئيسي -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h3><i class="fas fa-shield-alt me-2"></i>إدارة الصلاحيات والأدوار</h3>
                    <p class="mb-0 small">إدارة أدوار المستخدمين وصلاحيات الوصول للنظام</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#addRoleModal">
                        <i class="fas fa-plus me-2"></i>إضافة دور جديد
                    </button>
                </div>
            </div>
        </div>

        <!-- رسائل النظام -->
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $message_type === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
                <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- إحصائيات الأدوار -->
        <div class="row mb-3">
            <div class="col-md-3">
                <div class="stats-card border-primary">
                    <h3 class="text-primary"><?php echo count($roles); ?></h3>
                    <p class="mb-0">إجمالي الأدوار</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card border-success">
                    <h3 class="text-success"><?php echo count($permissions); ?></h3>
                    <p class="mb-0">وحدات النظام</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card border-info">
                    <h3 class="text-info"><?php
                        $total_permissions = 0;
                        foreach($permissions as $module) {
                            $total_permissions += count($module['permissions']);
                        }
                        echo $total_permissions;
                    ?></h3>
                    <p class="mb-0">إجمالي الصلاحيات</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card border-warning">
                    <h3 class="text-warning"><?php
                        $total_users = 0;
                        foreach($roles as $role) {
                            $total_users += $role['users_count'];
                        }
                        echo $total_users;
                    ?></h3>
                    <p class="mb-0">إجمالي المستخدمين</p>
                </div>
            </div>
        </div>

        <!-- أدوات التحكم والعرض -->
        <div class="view-controls">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="btn-group" role="group">
                        <a href="?view=roles" class="view-btn <?php echo $view_mode === 'roles' ? 'active' : ''; ?>">
                            <i class="fas fa-users-cog me-1"></i>الأدوار
                        </a>
                        <a href="?view=permissions" class="view-btn <?php echo $view_mode === 'permissions' ? 'active' : ''; ?>">
                            <i class="fas fa-key me-1"></i>الصلاحيات
                        </a>
                        <a href="?view=matrix" class="view-btn <?php echo $view_mode === 'matrix' ? 'active' : ''; ?>">
                            <i class="fas fa-table me-1"></i>مصفوفة الصلاحيات
                        </a>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="d-flex justify-content-end">
                        <input type="text" class="form-control search-box" placeholder="البحث..." style="max-width: 200px; font-size: 0.8rem;">
                    </div>
                </div>
            </div>
        </div>

        <!-- محتوى العرض -->
        <?php if ($view_mode === 'roles'): ?>
            <!-- عرض الأدوار -->
            <div class="row">
                <?php foreach ($roles as $role): ?>
                    <div class="col-md-6 col-lg-4">
                        <div class="role-card border-<?php echo $role['color']; ?>">
                            <div class="d-flex align-items-center mb-2">
                                <div class="role-icon me-3 bg-<?php echo $role['color']; ?>">
                                    <i class="<?php echo $role['icon']; ?>"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1"><?php echo $role['name']; ?></h6>
                                    <p class="text-muted mb-0 small"><?php echo $role['technical_name']; ?></p>
                                </div>
                                <div>
                                    <span class="badge bg-<?php echo $role['color']; ?>"><?php echo $role['users_count']; ?> مستخدم</span>
                                </div>
                            </div>

                            <div class="mb-2">
                                <small class="text-muted"><?php echo $role['description']; ?></small>
                            </div>

                            <div class="mb-2">
                                <small class="text-muted">الصلاحيات:</small><br>
                                <div class="progress" style="height: 8px;">
                                    <?php
                                    $role_perms = isset($role_permissions[$role['technical_name']]) ? $role_permissions[$role['technical_name']] : array();
                                    $total_perms = array_merge(...array_column($permissions, 'permissions'));
                                    $percentage = count($total_perms) > 0 ? (count($role_perms) / count($total_perms)) * 100 : 0;
                                    ?>
                                    <div class="progress-bar bg-<?php echo $role['color']; ?>" style="width: <?php echo $percentage; ?>%"></div>
                                </div>
                                <small class="text-muted"><?php echo count($role_perms); ?> من <?php echo count($total_perms); ?> صلاحية</small>
                            </div>

                            <div class="d-flex justify-content-between">
                                <button class="btn btn-outline-primary btn-sm" onclick="editRole(<?php echo $role['id']; ?>)" style="font-size: 0.7rem;">
                                    <i class="fas fa-edit me-1"></i>تعديل
                                </button>
                                <button class="btn btn-outline-info btn-sm" onclick="viewPermissions('<?php echo $role['technical_name']; ?>')" style="font-size: 0.7rem;">
                                    <i class="fas fa-eye me-1"></i>الصلاحيات
                                </button>
                                <?php if ($role['technical_name'] !== 'admin'): ?>
                                    <button class="btn btn-outline-danger btn-sm" style="font-size: 0.7rem;">
                                        <i class="fas fa-trash me-1"></i>حذف
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

        <?php elseif ($view_mode === 'permissions'): ?>
            <!-- عرض الصلاحيات حسب الوحدة -->
            <div class="row">
                <?php foreach ($permissions as $module_key => $module): ?>
                    <div class="col-md-6">
                        <div class="permission-card">
                            <div class="d-flex align-items-center mb-2">
                                <div class="role-icon me-3 bg-info">
                                    <i class="<?php echo $module['icon']; ?>"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1"><?php echo $module['name']; ?></h6>
                                    <small class="text-muted"><?php echo count($module['permissions']); ?> صلاحية</small>
                                </div>
                            </div>

                            <div class="list-group list-group-flush">
                                <?php foreach ($module['permissions'] as $perm_key => $perm_name): ?>
                                    <div class="list-group-item border-0 px-0 py-1">
                                        <small>
                                            <i class="fas fa-key me-2 text-muted"></i>
                                            <strong><?php echo $perm_key; ?></strong><br>
                                            <span class="text-muted ms-3"><?php echo $perm_name; ?></span>
                                        </small>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

        <?php else: ?>
            <!-- مصفوفة الصلاحيات -->
            <div class="permission-matrix">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>الصلاحية</th>
                            <?php foreach ($roles as $role): ?>
                                <th class="text-center">
                                    <i class="<?php echo $role['icon']; ?> me-1"></i>
                                    <?php echo $role['name']; ?>
                                </th>
                            <?php endforeach; ?>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($permissions as $module_key => $module): ?>
                            <tr class="table-light">
                                <td colspan="<?php echo count($roles) + 1; ?>">
                                    <strong><i class="<?php echo $module['icon']; ?> me-2"></i><?php echo $module['name']; ?></strong>
                                </td>
                            </tr>
                            <?php foreach ($module['permissions'] as $perm_key => $perm_name): ?>
                                <tr>
                                    <td>
                                        <small>
                                            <strong><?php echo $perm_key; ?></strong><br>
                                            <span class="text-muted"><?php echo $perm_name; ?></span>
                                        </small>
                                    </td>
                                    <?php foreach ($roles as $role): ?>
                                        <td class="text-center">
                                            <?php
                                            $role_perms = isset($role_permissions[$role['technical_name']]) ? $role_permissions[$role['technical_name']] : array();
                                            $has_permission = in_array($perm_key, $role_perms);
                                            ?>
                                            <?php if ($has_permission): ?>
                                                <i class="fas fa-check text-success"></i>
                                            <?php else: ?>
                                                <i class="fas fa-times text-danger"></i>
                                            <?php endif; ?>
                                        </td>
                                    <?php endforeach; ?>
                                </tr>
                            <?php endforeach; ?>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editRole(roleId) {
            alert('سيتم تطوير نافذة تعديل الدور قريباً');
        }

        function viewPermissions(roleName) {
            alert('عرض صلاحيات الدور: ' + roleName);
        }

        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const items = document.querySelectorAll('.role-card, .permission-card');
            items.forEach((item, index) => {
                item.style.opacity = '0';
                item.style.transform = 'translateY(15px)';

                setTimeout(() => {
                    item.style.transition = 'all 0.5s ease';
                    item.style.opacity = '1';
                    item.style.transform = 'translateY(0)';
                }, index * 80);
            });
        });

        // البحث المباشر
        document.querySelector('.search-box').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const items = document.querySelectorAll('.role-card, .permission-card, tbody tr');

            items.forEach(item => {
                const text = item.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    item.style.display = '';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
