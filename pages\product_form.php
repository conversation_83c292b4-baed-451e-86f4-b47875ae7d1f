<?php
/**
 * نموذج إضافة منتج - بأسلوب Odoo
 * Product Form - Odoo Style
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// تضمين الملفات المطلوبة
require_once '../config/database.php';
require_once '../models/ProductProduct.php';

// إنشاء اتصال قاعدة البيانات
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    $pdo = null;
}

$product_model = new ProductProduct($pdo);

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = isset($_POST['action']) ? $_POST['action'] : '';
    
    if ($action === 'create') {
        $data = array(
            'name' => $_POST['name'],
            'default_code' => isset($_POST['default_code']) ? $_POST['default_code'] : '',
            'list_price' => isset($_POST['list_price']) ? floatval($_POST['list_price']) : 0.0,
            'standard_price' => isset($_POST['standard_price']) ? floatval($_POST['standard_price']) : 0.0,
            'type' => isset($_POST['type']) ? $_POST['type'] : 'product',
            'categ_id' => isset($_POST['categ_id']) ? intval($_POST['categ_id']) : 1,
            'sale_ok' => isset($_POST['sale_ok']) ? 1 : 0,
            'purchase_ok' => isset($_POST['purchase_ok']) ? 1 : 0,
            'description' => isset($_POST['description']) ? $_POST['description'] : '',
            'active' => 1
        );
        
        try {
            $product_model->create($data);
            $success_message = "تم إنشاء المنتج بنجاح";
        } catch (Exception $e) {
            $error_message = "خطأ في إنشاء المنتج: " . $e->getMessage();
        }
    }
}

// قائمة فئات المنتجات
$categories = array(
    1 => 'الإلكترونيات',
    2 => 'الخدمات',
    3 => 'المكتبية',
    4 => 'المواد الاستهلاكية',
    5 => 'الأثاث',
    6 => 'المعدات',
    7 => 'البرمجيات',
    8 => 'الكتب والمراجع',
    9 => 'الملابس',
    10 => 'أخرى'
);

// قائمة وحدات القياس
$uom_categories = array(
    'قطعة' => 'قطعة',
    'كيلوجرام' => 'كيلوجرام',
    'متر' => 'متر',
    'لتر' => 'لتر',
    'صندوق' => 'صندوق',
    'علبة' => 'علبة',
    'حزمة' => 'حزمة',
    'دزينة' => 'دزينة',
    'ساعة' => 'ساعة',
    'يوم' => 'يوم'
);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة منتج - نظام ERP</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --odoo-primary: #714B67;
            --odoo-secondary: #875A7B;
            --odoo-accent: #D4AF37;
            --odoo-success: #28a745;
            --odoo-info: #17a2b8;
            --odoo-warning: #ffc107;
            --odoo-danger: #dc3545;
            --odoo-light: #f8f9fa;
            --odoo-dark: #2F3349;
        }
        
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background: var(--odoo-light);
            margin: 0;
            padding: 0;
        }
        
        .main-container {
            display: flex;
            min-height: 100vh;
        }
        
        .content-area {
            flex: 1;
            padding: 0;
            overflow-x: hidden;
        }
        
        /* رأس الصفحة */
        .page-header {
            background: white;
            border-bottom: 1px solid #e9ecef;
            padding: 20px 30px;
            margin-bottom: 0;
        }
        
        .breadcrumb-odoo {
            background: none;
            padding: 0;
            margin: 0;
            font-size: 0.9rem;
        }
        
        .breadcrumb-odoo .breadcrumb-item {
            color: #6c757d;
        }
        
        .breadcrumb-odoo .breadcrumb-item.active {
            color: var(--odoo-primary);
            font-weight: 600;
        }
        
        .page-title {
            color: var(--odoo-dark);
            font-size: 1.8rem;
            font-weight: 700;
            margin: 10px 0 0 0;
        }
        
        /* أزرار الإجراءات */
        .action-buttons {
            background: white;
            border-bottom: 1px solid #e9ecef;
            padding: 15px 30px;
        }
        
        .btn-odoo {
            background: var(--odoo-primary);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 4px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 0.95rem;
        }
        
        .btn-odoo:hover {
            background: var(--odoo-secondary);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(113, 75, 103, 0.3);
        }
        
        .btn-outline-odoo {
            background: transparent;
            border: 1px solid var(--odoo-primary);
            color: var(--odoo-primary);
        }
        
        .btn-outline-odoo:hover {
            background: var(--odoo-primary);
            color: white;
        }
        
        .btn-secondary-odoo {
            background: #6c757d;
            border: none;
            color: white;
        }
        
        .btn-secondary-odoo:hover {
            background: #5a6268;
            color: white;
        }
        
        /* منطقة النموذج */
        .form-container {
            padding: 30px;
        }
        
        .form-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
            overflow: hidden;
        }
        
        .form-header {
            background: linear-gradient(135deg, var(--odoo-warning), #fd7e14);
            color: white;
            padding: 20px 30px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .form-header h4 {
            margin: 0;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .form-body {
            padding: 30px;
        }
        
        .form-section {
            margin-bottom: 30px;
        }
        
        .section-title {
            color: var(--odoo-dark);
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid var(--odoo-warning);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .form-label {
            font-weight: 500;
            color: var(--odoo-dark);
            margin-bottom: 8px;
            font-size: 0.9rem;
        }
        
        .required-field::after {
            content: " *";
            color: var(--odoo-danger);
            font-weight: bold;
        }
        
        .form-control, .form-select {
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px 12px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--odoo-warning);
            box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
        }
        
        .form-text {
            font-size: 0.8rem;
            color: #6c757d;
            margin-top: 5px;
        }
        
        /* رسائل التنبيه */
        .alert {
            border: none;
            border-radius: 6px;
            padding: 15px 20px;
            margin-bottom: 20px;
            font-size: 0.9rem;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border-left: 4px solid #28a745;
        }
        
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border-left: 4px solid #dc3545;
        }
        
        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 768px) {
            .page-header, .action-buttons, .form-container {
                padding: 15px 20px;
            }
            
            .form-body {
                padding: 20px;
            }
            
            .form-header {
                padding: 15px 20px;
            }
        }
        
        /* تحسينات إضافية */
        .input-group-text {
            background: var(--odoo-light);
            border-color: #dee2e6;
            color: var(--odoo-dark);
        }
        
        .product-type-cards {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .type-card {
            flex: 1;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }
        
        .type-card:hover {
            border-color: var(--odoo-warning);
            background: rgba(255, 193, 7, 0.05);
        }
        
        .type-card.active {
            border-color: var(--odoo-warning);
            background: rgba(255, 193, 7, 0.1);
        }
        
        .type-card .icon {
            font-size: 1.5rem;
            color: var(--odoo-warning);
            margin-bottom: 8px;
        }
        
        .type-card .title {
            font-weight: 600;
            color: var(--odoo-dark);
            margin-bottom: 3px;
            font-size: 0.9rem;
        }
        
        .type-card .description {
            font-size: 0.75rem;
            color: #6c757d;
        }
        
        .btn-group-actions {
            gap: 10px;
            justify-content: flex-end;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            margin-top: 30px;
        }
        
        .form-check-input:checked {
            background-color: var(--odoo-warning);
            border-color: var(--odoo-warning);
        }
        
        .price-input {
            position: relative;
        }
        
        .currency-symbol {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            font-weight: 500;
        }
        
        .price-input input {
            padding-left: 35px;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- الشريط الجانبي -->
        <?php include '../includes/sidebar.php'; ?>
        
        <!-- منطقة المحتوى -->
        <div class="content-area">
            <!-- رأس الصفحة -->
            <div class="page-header">
                <nav class="breadcrumb-odoo">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="../dashboard.php">
                                <i class="fas fa-home"></i> الرئيسية
                            </a>
                        </li>
                        <li class="breadcrumb-item">المنتجات</li>
                        <li class="breadcrumb-item active">إضافة منتج</li>
                    </ol>
                </nav>
                <h1 class="page-title">
                    <i class="fas fa-box me-2"></i>إضافة منتج جديد
                </h1>
            </div>
            
            <!-- أزرار الإجراءات -->
            <div class="action-buttons">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="d-flex gap-2">
                        <button type="submit" form="productForm" class="btn-odoo">
                            <i class="fas fa-save"></i>
                            حفظ المنتج
                        </button>
                        <button type="button" class="btn-outline-odoo" onclick="resetForm()">
                            <i class="fas fa-undo"></i>
                            إعادة تعيين
                        </button>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <a href="products_enhanced.php" class="btn-secondary-odoo">
                            <i class="fas fa-list"></i>
                            قائمة المنتجات
                        </a>
                        <a href="../dashboard.php" class="btn-secondary-odoo">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- رسائل التنبيه -->
            <?php if (isset($success_message)): ?>
                <div class="alert alert-success mx-4">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo $success_message; ?>
                </div>
            <?php endif; ?>
            
            <?php if (isset($error_message)): ?>
                <div class="alert alert-danger mx-4">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <?php echo $error_message; ?>
                </div>
            <?php endif; ?>

            <!-- نموذج إضافة المنتج -->
            <div class="form-container">
                <div class="form-card">
                    <div class="form-header">
                        <h4>
                            <i class="fas fa-box"></i>
                            معلومات المنتج
                        </h4>
                    </div>

                    <form id="productForm" method="POST" enctype="multipart/form-data">
                        <input type="hidden" name="action" value="create">

                        <div class="form-body">
                            <!-- القسم الأول: نوع المنتج -->
                            <div class="form-section">
                                <h5 class="section-title">
                                    <i class="fas fa-tags"></i>
                                    نوع المنتج
                                </h5>

                                <div class="product-type-cards">
                                    <div class="type-card active" onclick="selectProductType('product')">
                                        <div class="icon">
                                            <i class="fas fa-box"></i>
                                        </div>
                                        <div class="title">منتج قابل للتخزين</div>
                                        <div class="description">منتج مادي يمكن تخزينه</div>
                                    </div>
                                    <div class="type-card" onclick="selectProductType('consu')">
                                        <div class="icon">
                                            <i class="fas fa-recycle"></i>
                                        </div>
                                        <div class="title">منتج استهلاكي</div>
                                        <div class="description">منتج يستهلك عند الاستخدام</div>
                                    </div>
                                    <div class="type-card" onclick="selectProductType('service')">
                                        <div class="icon">
                                            <i class="fas fa-concierge-bell"></i>
                                        </div>
                                        <div class="title">خدمة</div>
                                        <div class="description">خدمة غير مادية</div>
                                    </div>
                                </div>
                                <input type="hidden" name="type" id="productType" value="product">

                                <!-- خيارات البيع والشراء -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="sale_ok" id="saleOk" checked>
                                            <label class="form-check-label" for="saleOk">
                                                <i class="fas fa-shopping-cart me-1"></i>
                                                <strong>يمكن بيعه</strong> - متاح للعملاء
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="purchase_ok" id="purchaseOk">
                                            <label class="form-check-label" for="purchaseOk">
                                                <i class="fas fa-shopping-bag me-1"></i>
                                                <strong>يمكن شراؤه</strong> - متاح من الموردين
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- القسم الثاني: المعلومات الأساسية -->
                            <div class="form-section">
                                <h5 class="section-title">
                                    <i class="fas fa-info-circle"></i>
                                    المعلومات الأساسية
                                </h5>

                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label class="form-label required-field">اسم المنتج</label>
                                            <input type="text" class="form-control" name="name" id="productName" required>
                                            <div class="form-text">اسم المنتج كما سيظهر للعملاء</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">الكود المرجعي</label>
                                            <input type="text" class="form-control" name="default_code" id="productCode">
                                            <div class="form-text">كود فريد للمنتج</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">فئة المنتج</label>
                                            <select class="form-select" name="categ_id" id="productCategory">
                                                <?php foreach ($categories as $id => $name): ?>
                                                    <option value="<?php echo $id; ?>">
                                                        <?php echo htmlspecialchars($name); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">وحدة القياس</label>
                                            <select class="form-select" name="uom" id="productUom">
                                                <?php foreach ($uom_categories as $code => $name): ?>
                                                    <option value="<?php echo htmlspecialchars($code); ?>">
                                                        <?php echo htmlspecialchars($name); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">وصف المنتج</label>
                                    <textarea class="form-control" name="description" id="productDescription" rows="3" placeholder="وصف تفصيلي للمنتج..."></textarea>
                                </div>
                            </div>

                            <!-- القسم الثالث: الأسعار -->
                            <div class="form-section">
                                <h5 class="section-title">
                                    <i class="fas fa-dollar-sign"></i>
                                    الأسعار والتكلفة
                                </h5>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">سعر البيع</label>
                                            <div class="price-input">
                                                <span class="currency-symbol">ر.س</span>
                                                <input type="number" step="0.01" class="form-control" name="list_price" id="productListPrice" min="0" onchange="calculateMargin()">
                                            </div>
                                            <div class="form-text">السعر المعروض للعملاء</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">سعر التكلفة</label>
                                            <div class="price-input">
                                                <span class="currency-symbol">ر.س</span>
                                                <input type="number" step="0.01" class="form-control" name="standard_price" id="productStandardPrice" min="0" onchange="calculateMargin()">
                                            </div>
                                            <div class="form-text">تكلفة المنتج الفعلية</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row" id="marginSection">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">هامش الربح</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="profitMargin" readonly>
                                                <span class="input-group-text">ر.س</span>
                                            </div>
                                            <div class="form-text">الفرق بين سعر البيع والتكلفة</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">نسبة الربح</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="profitPercentage" readonly>
                                                <span class="input-group-text">%</span>
                                            </div>
                                            <div class="form-text">نسبة الربح من التكلفة</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- أزرار الإجراءات -->
                            <div class="d-flex btn-group-actions">
                                <a href="products_enhanced.php" class="btn btn-secondary-odoo">
                                    <i class="fas fa-times me-1"></i>إلغاء
                                </a>
                                <button type="button" class="btn btn-outline-odoo" onclick="resetForm()">
                                    <i class="fas fa-undo me-1"></i>إعادة تعيين
                                </button>
                                <button type="submit" class="btn-odoo">
                                    <i class="fas fa-save me-1"></i>حفظ المنتج
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحديد نوع المنتج
        function selectProductType(type) {
            const cards = document.querySelectorAll('.type-card');
            cards.forEach(card => card.classList.remove('active'));

            event.target.closest('.type-card').classList.add('active');
            document.getElementById('productType').value = type;

            // تحديث الواجهة حسب النوع
            const marginSection = document.getElementById('marginSection');
            if (type === 'service') {
                marginSection.style.display = 'none';
            } else {
                marginSection.style.display = 'flex';
            }
        }

        // حساب هامش الربح
        function calculateMargin() {
            const listPrice = parseFloat(document.getElementById('productListPrice').value) || 0;
            const standardPrice = parseFloat(document.getElementById('productStandardPrice').value) || 0;

            const margin = listPrice - standardPrice;
            const percentage = standardPrice > 0 ? ((margin / standardPrice) * 100) : 0;

            document.getElementById('profitMargin').value = margin.toFixed(2);
            document.getElementById('profitPercentage').value = percentage.toFixed(2);
        }

        // إعادة تعيين النموذج
        function resetForm() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟')) {
                document.getElementById('productForm').reset();

                // إعادة تعيين نوع المنتج
                selectProductType('product');

                // إعادة تعيين القيم الافتراضية
                document.getElementById('saleOk').checked = true;
                document.getElementById('purchaseOk').checked = false;
                document.getElementById('productCategory').value = '1';
                document.getElementById('productUom').value = 'قطعة';

                // مسح حسابات الربح
                document.getElementById('profitMargin').value = '';
                document.getElementById('profitPercentage').value = '';
            }
        }

        // التحقق من صحة النموذج
        document.getElementById('productForm').addEventListener('submit', function(e) {
            const name = document.getElementById('productName').value.trim();

            if (!name) {
                e.preventDefault();
                alert('يرجى إدخال اسم المنتج');
                document.getElementById('productName').focus();
                return false;
            }

            const saleOk = document.getElementById('saleOk').checked;
            const purchaseOk = document.getElementById('purchaseOk').checked;

            if (!saleOk && !purchaseOk) {
                e.preventDefault();
                alert('يجب تحديد إما "يمكن بيعه" أو "يمكن شراؤه" أو كلاهما');
                return false;
            }

            // تأكيد الحفظ
            if (!confirm('هل أنت متأكد من حفظ بيانات المنتج؟')) {
                e.preventDefault();
                return false;
            }
        });

        // تحسينات UX
        document.addEventListener('DOMContentLoaded', function() {
            // إنشاء كود المنتج تلقائياً من الاسم
            const nameInput = document.getElementById('productName');
            const codeInput = document.getElementById('productCode');

            nameInput.addEventListener('input', function(e) {
                if (!codeInput.value) {
                    const name = e.target.value.toUpperCase()
                        .replace(/\s+/g, '_')
                        .replace(/[^A-Z0-9_]/g, '');
                    codeInput.value = name.substring(0, 10);
                }
            });

            // حساب الربح عند تحميل الصفحة
            calculateMargin();
        });
    </script>
</body>
</html>
