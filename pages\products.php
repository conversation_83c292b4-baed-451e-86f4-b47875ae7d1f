<?php
session_start();
require_once '../config/database_simple.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

// بيانات تجريبية للمنتجات
$products = array(
    array(
        'id' => 1,
        'code' => 'PROD-001',
        'name' => 'لابتوب ديل XPS 13',
        'category' => 'أجهزة كمبيوتر',
        'unit' => 'قطعة',
        'cost_price' => 3500,
        'selling_price' => 4200,
        'current_stock' => 25,
        'min_stock' => 5,
        'max_stock' => 100,
        'is_active' => true,
        'barcode' => '1234567890123'
    ),
    array(
        'id' => 2,
        'code' => 'PROD-002',
        'name' => 'طابعة HP LaserJet',
        'category' => 'طابعات',
        'unit' => 'قطعة',
        'cost_price' => 800,
        'selling_price' => 950,
        'current_stock' => 15,
        'min_stock' => 3,
        'max_stock' => 50,
        'is_active' => true,
        'barcode' => '2345678901234'
    ),
    array(
        'id' => 3,
        'code' => 'PROD-003',
        'name' => 'ماوس لاسلكي لوجيتك',
        'category' => 'ملحقات',
        'unit' => 'قطعة',
        'cost_price' => 45,
        'selling_price' => 65,
        'current_stock' => 2,
        'min_stock' => 10,
        'max_stock' => 200,
        'is_active' => true,
        'barcode' => '3456789012345'
    ),
    array(
        'id' => 4,
        'code' => 'PROD-004',
        'name' => 'كيبورد ميكانيكي',
        'category' => 'ملحقات',
        'unit' => 'قطعة',
        'cost_price' => 120,
        'selling_price' => 180,
        'current_stock' => 8,
        'min_stock' => 5,
        'max_stock' => 80,
        'is_active' => false,
        'barcode' => '4567890123456'
    ),
    array(
        'id' => 5,
        'code' => 'PROD-005',
        'name' => 'شاشة سامسونج 24 بوصة',
        'category' => 'شاشات',
        'unit' => 'قطعة',
        'cost_price' => 650,
        'selling_price' => 820,
        'current_stock' => 12,
        'min_stock' => 3,
        'max_stock' => 40,
        'is_active' => true,
        'barcode' => '5678901234567'
    )
);

// دالة لتحديد حالة المخزون
function getStockStatus($current, $min, $max) {
    if ($current <= $min) return 'low';
    if ($current >= $max * 0.8) return 'high';
    return 'normal';
}

// دالة لتحديد لون حالة المخزون
function getStockColor($status) {
    switch($status) {
        case 'low': return 'danger';
        case 'high': return 'success';
        case 'normal': return 'warning';
        default: return 'secondary';
    }
}

// دالة لتحديد نص حالة المخزون
function getStockText($status) {
    switch($status) {
        case 'low': return 'مخزون منخفض';
        case 'high': return 'مخزون عالي';
        case 'normal': return 'مخزون طبيعي';
        default: return 'غير محدد';
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المنتجات - نظام ERP المحاسبي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
        }

        .product-card {
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            border-radius: 15px;
            overflow: hidden;
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .product-image {
            width: 100%;
            height: 200px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
        }

        .product-info {
            padding: 20px;
        }

        .product-code {
            background: #e9ecef;
            color: #495057;
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .price-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
        }

        .stock-indicator {
            width: 100%;
            height: 8px;
            border-radius: 10px;
            background: #e9ecef;
            overflow: hidden;
            margin: 10px 0;
        }

        .stock-fill {
            height: 100%;
            border-radius: 10px;
            transition: width 0.3s ease;
        }

        .filter-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            margin-bottom: 20px;
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .barcode {
            font-family: 'Courier New', monospace;
            background: #f8f9fa;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="../demo.php">
                <i class="fas fa-chart-line me-2"></i>
                نظام ERP المحاسبي
            </a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="../demo.php">لوحة التحكم</a>
                <a class="nav-link" href="companies.php">الشركات</a>
                <a class="nav-link" href="customers.php">العملاء</a>
                <a class="nav-link" href="invoices.php">الفواتير</a>
                <a class="nav-link active" href="products.php">المنتجات</a>
                <a class="nav-link" href="reports.php">التقارير</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- رأس الصفحة -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2><i class="fas fa-box me-2"></i>إدارة المنتجات والمخزون</h2>
                <p class="text-muted">إدارة قاعدة بيانات المنتجات ومتابعة المخزون</p>
            </div>
            <div>
                <button class="btn btn-info me-2" onclick="importProducts()">
                    <i class="fas fa-upload me-2"></i>
                    استيراد
                </button>
                <button class="btn btn-success me-2" onclick="exportProducts()">
                    <i class="fas fa-download me-2"></i>
                    تصدير
                </button>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addProductModal">
                    <i class="fas fa-plus me-2"></i>
                    منتج جديد
                </button>
            </div>
        </div>

        <!-- إحصائيات المنتجات -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number"><?php echo count($products); ?></div>
                    <div>إجمالي المنتجات</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
                    <div class="stats-number"><?php
                        $active_count = 0;
                        foreach($products as $p) {
                            if($p['is_active']) $active_count++;
                        }
                        echo $active_count;
                    ?></div>
                    <div>المنتجات النشطة</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                    <div class="stats-number"><?php
                        $low_stock_count = 0;
                        foreach($products as $p) {
                            if(getStockStatus($p['current_stock'], $p['min_stock'], $p['max_stock']) == 'low') $low_stock_count++;
                        }
                        echo $low_stock_count;
                    ?></div>
                    <div>مخزون منخفض</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                    <div class="stats-number"><?php
                        $total_value = 0;
                        foreach($products as $p) {
                            $total_value += $p['current_stock'] * $p['cost_price'];
                        }
                        echo formatCurrency($total_value);
                    ?></div>
                    <div>قيمة المخزون</div>
                </div>
            </div>
        </div>

        <!-- قسم الفلترة -->
        <div class="filter-section">
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">البحث</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                        <input type="text" class="form-control" placeholder="اسم المنتج أو الكود..." id="searchInput">
                    </div>
                </div>
                <div class="col-md-2">
                    <label class="form-label">الفئة</label>
                    <select class="form-select" id="categoryFilter">
                        <option value="">جميع الفئات</option>
                        <option value="أجهزة كمبيوتر">أجهزة كمبيوتر</option>
                        <option value="طابعات">طابعات</option>
                        <option value="ملحقات">ملحقات</option>
                        <option value="شاشات">شاشات</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">حالة المخزون</label>
                    <select class="form-select" id="stockFilter">
                        <option value="">جميع الحالات</option>
                        <option value="low">مخزون منخفض</option>
                        <option value="normal">مخزون طبيعي</option>
                        <option value="high">مخزون عالي</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">الحالة</label>
                    <select class="form-select" id="statusFilter">
                        <option value="">جميع الحالات</option>
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">ترتيب حسب</label>
                    <select class="form-select" id="sortBy">
                        <option value="name">الاسم</option>
                        <option value="code">الكود</option>
                        <option value="stock">المخزون</option>
                        <option value="price">السعر</option>
                    </select>
                </div>
                <div class="col-md-1">
                    <label class="form-label">&nbsp;</label>
                    <button class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- قائمة المنتجات -->
        <div class="row" id="productsContainer">
            <?php foreach ($products as $product):
                $stockStatus = getStockStatus($product['current_stock'], $product['min_stock'], $product['max_stock']);
                $stockPercentage = ($product['current_stock'] / $product['max_stock']) * 100;
                $profit = $product['selling_price'] - $product['cost_price'];
                $profitMargin = ($profit / $product['selling_price']) * 100;
            ?>
            <div class="col-lg-4 col-md-6">
                <div class="card product-card">
                    <!-- صورة المنتج -->
                    <div class="product-image">
                        <i class="fas fa-box"></i>
                    </div>

                    <div class="product-info">
                        <!-- معلومات أساسية -->
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <div>
                                <h6 class="card-title mb-1"><?php echo $product['name']; ?></h6>
                                <span class="product-code"><?php echo $product['code']; ?></span>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-<?php echo $product['is_active'] ? 'success' : 'danger'; ?>">
                                    <?php echo $product['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                </span>
                            </div>
                        </div>

                        <!-- الفئة والوحدة -->
                        <div class="mb-2">
                            <small class="text-muted">
                                <i class="fas fa-tag me-1"></i><?php echo $product['category']; ?>
                                <span class="mx-2">|</span>
                                <i class="fas fa-cube me-1"></i><?php echo $product['unit']; ?>
                            </small>
                        </div>

                        <!-- معلومات الأسعار -->
                        <div class="price-info">
                            <div class="row">
                                <div class="col-6">
                                    <small class="text-muted">سعر التكلفة</small>
                                    <div class="fw-bold"><?php echo formatCurrency($product['cost_price']); ?></div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">سعر البيع</small>
                                    <div class="fw-bold text-success"><?php echo formatCurrency($product['selling_price']); ?></div>
                                </div>
                            </div>
                            <div class="mt-2">
                                <small class="text-muted">هامش الربح: </small>
                                <span class="badge bg-info"><?php echo round($profitMargin); ?>%</span>
                                <small class="text-success">(<?php echo formatCurrency($profit); ?>)</small>
                            </div>
                        </div>

                        <!-- معلومات المخزون -->
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <small class="text-muted">المخزون الحالي</small>
                                <span class="badge bg-<?php echo getStockColor($stockStatus); ?>">
                                    <?php echo getStockText($stockStatus); ?>
                                </span>
                            </div>
                            <div class="d-flex justify-content-between mb-1">
                                <span class="fw-bold"><?php echo $product['current_stock']; ?> <?php echo $product['unit']; ?></span>
                                <small class="text-muted">الحد الأدنى: <?php echo $product['min_stock']; ?></small>
                            </div>
                            <div class="stock-indicator">
                                <div class="stock-fill bg-<?php echo getStockColor($stockStatus); ?>"
                                     style="width: <?php echo min($stockPercentage, 100); ?>%"></div>
                            </div>
                        </div>

                        <!-- الباركود -->
                        <div class="mb-3">
                            <small class="text-muted">الباركود:</small>
                            <div class="barcode"><?php echo $product['barcode']; ?></div>
                        </div>

                        <!-- الإجراءات -->
                        <div class="d-grid gap-2">
                            <div class="btn-group" role="group">
                                <button class="btn btn-outline-primary btn-sm" onclick="viewProduct(<?php echo $product['id']; ?>)">
                                    <i class="fas fa-eye me-1"></i>
                                    عرض
                                </button>
                                <button class="btn btn-outline-success btn-sm" onclick="editProduct(<?php echo $product['id']; ?>)">
                                    <i class="fas fa-edit me-1"></i>
                                    تعديل
                                </button>
                                <button class="btn btn-outline-info btn-sm" onclick="stockMovement(<?php echo $product['id']; ?>)">
                                    <i class="fas fa-exchange-alt me-1"></i>
                                    حركة
                                </button>
                            </div>

                            <div class="btn-group" role="group">
                                <button class="btn btn-outline-warning btn-sm" onclick="adjustStock(<?php echo $product['id']; ?>)">
                                    <i class="fas fa-plus-minus me-1"></i>
                                    تعديل المخزون
                                </button>
                                <button class="btn btn-outline-secondary btn-sm" onclick="printBarcode(<?php echo $product['id']; ?>)">
                                    <i class="fas fa-barcode me-1"></i>
                                    طباعة باركود
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <!-- ترقيم الصفحات -->
        <nav class="mt-4">
            <ul class="pagination justify-content-center">
                <li class="page-item disabled">
                    <a class="page-link" href="#" tabindex="-1">السابق</a>
                </li>
                <li class="page-item active"><a class="page-link" href="#">1</a></li>
                <li class="page-item"><a class="page-link" href="#">2</a></li>
                <li class="page-item"><a class="page-link" href="#">3</a></li>
                <li class="page-item">
                    <a class="page-link" href="#">التالي</a>
                </li>
            </ul>
        </nav>
    </div>

    <!-- نافذة إضافة منتج جديد -->
    <div class="modal fade" id="addProductModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-plus me-2"></i>
                        إضافة منتج جديد
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addProductForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم المنتج *</label>
                                    <input type="text" class="form-control" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">كود المنتج</label>
                                    <input type="text" class="form-control" name="code" placeholder="سيتم إنشاؤه تلقائياً">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الفئة</label>
                                    <select class="form-select" name="category">
                                        <option value="">اختر الفئة</option>
                                        <option value="أجهزة كمبيوتر">أجهزة كمبيوتر</option>
                                        <option value="طابعات">طابعات</option>
                                        <option value="ملحقات">ملحقات</option>
                                        <option value="شاشات">شاشات</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">وحدة القياس</label>
                                    <select class="form-select" name="unit">
                                        <option value="قطعة">قطعة</option>
                                        <option value="كيلوجرام">كيلوجرام</option>
                                        <option value="متر">متر</option>
                                        <option value="لتر">لتر</option>
                                        <option value="صندوق">صندوق</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">سعر التكلفة</label>
                                    <input type="number" class="form-control" name="cost_price" step="0.01" min="0">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">سعر البيع</label>
                                    <input type="number" class="form-control" name="selling_price" step="0.01" min="0">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">المخزون الحالي</label>
                                    <input type="number" class="form-control" name="current_stock" value="0" min="0">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">الحد الأدنى</label>
                                    <input type="number" class="form-control" name="min_stock" value="0" min="0">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">الحد الأقصى</label>
                                    <input type="number" class="form-control" name="max_stock" value="100" min="0">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">الباركود</label>
                            <div class="input-group">
                                <input type="text" class="form-control" name="barcode">
                                <button type="button" class="btn btn-outline-secondary" onclick="generateBarcode()">
                                    <i class="fas fa-magic me-1"></i>
                                    إنشاء تلقائي
                                </button>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">الوصف</label>
                            <textarea class="form-control" name="description" rows="3"></textarea>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_active" id="is_active" checked>
                                <label class="form-check-label" for="is_active">
                                    منتج نشط
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveProduct()">
                        <i class="fas fa-save me-2"></i>
                        حفظ المنتج
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function viewProduct(id) {
            alert('عرض تفاصيل المنتج رقم: ' + id);
        }

        function editProduct(id) {
            alert('تعديل المنتج رقم: ' + id);
        }

        function stockMovement(id) {
            alert('عرض حركات مخزون المنتج رقم: ' + id);
        }

        function adjustStock(id) {
            const adjustment = prompt('أدخل كمية التعديل (+ للإضافة، - للخصم):');
            if (adjustment && !isNaN(adjustment)) {
                alert('تم تعديل مخزون المنتج رقم ' + id + ' بكمية: ' + adjustment);
            }
        }

        function printBarcode(id) {
            alert('طباعة باركود المنتج رقم: ' + id);
        }

        function importProducts() {
            alert('استيراد المنتجات من ملف Excel');
        }

        function exportProducts() {
            alert('تصدير المنتجات إلى Excel');
        }

        function saveProduct() {
            const form = document.getElementById('addProductForm');
            const formData = new FormData(form);

            if (!formData.get('name')) {
                alert('يرجى إدخال اسم المنتج');
                return;
            }

            alert('تم حفظ المنتج بنجاح!');

            const modal = bootstrap.Modal.getInstance(document.getElementById('addProductModal'));
            modal.hide();
            form.reset();
        }

        function generateBarcode() {
            const barcode = Math.floor(Math.random() * 9000000000000) + 1000000000000;
            document.querySelector('input[name="barcode"]').value = barcode;
        }

        function clearFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('categoryFilter').value = '';
            document.getElementById('stockFilter').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('sortBy').value = 'name';
            filterProducts();
        }

        function filterProducts() {
            // منطق الفلترة
            console.log('تطبيق الفلاتر...');
        }

        // إضافة مستمعي الأحداث
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('searchInput').addEventListener('input', filterProducts);
            document.getElementById('categoryFilter').addEventListener('change', filterProducts);
            document.getElementById('stockFilter').addEventListener('change', filterProducts);
            document.getElementById('statusFilter').addEventListener('change', filterProducts);
            document.getElementById('sortBy').addEventListener('change', filterProducts);
        });
    </script>
</body>
</html>