<?php
/**
 * صفحة المنتجات المحسنة - بأسلوب Odoo
 * Enhanced Products Page - Odoo Style
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// تضمين الملفات المطلوبة
require_once '../config/database.php';
require_once '../models/ProductTemplate.php';

// إنشاء اتصال قاعدة البيانات
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    $pdo = null; // سيتم استخدام البيانات الوهمية
}

// إنشاء نموذج المنتج مع البيانات الوهمية
class ProductTemplateDemo {
    private $demo_data;
    
    public function __construct() {
        $this->demo_data = [
            [
                'id' => 1,
                'name' => 'لابتوب Dell Inspiron 15',
                'default_code' => 'DELL-INS-15',
                'list_price' => 2500.00,
                'standard_price' => 2000.00,
                'categ_id' => 1,
                'type' => 'product',
                'sale_ok' => 1,
                'purchase_ok' => 1,
                'description' => 'لابتوب Dell Inspiron 15 بمعالج Intel Core i5',
                'active' => 1,
                'create_date' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 2,
                'name' => 'خدمة الصيانة الشهرية',
                'default_code' => 'MAINT-MONTHLY',
                'list_price' => 150.00,
                'standard_price' => 100.00,
                'categ_id' => 2,
                'type' => 'service',
                'sale_ok' => 1,
                'purchase_ok' => 0,
                'description' => 'خدمة صيانة شهرية للأجهزة الإلكترونية',
                'active' => 1,
                'create_date' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 3,
                'name' => 'طابعة HP LaserJet Pro',
                'default_code' => 'HP-LJ-PRO',
                'list_price' => 800.00,
                'standard_price' => 650.00,
                'categ_id' => 1,
                'type' => 'product',
                'sale_ok' => 1,
                'purchase_ok' => 1,
                'description' => 'طابعة ليزر احترافية من HP',
                'active' => 1,
                'create_date' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 4,
                'name' => 'ورق A4 - علبة 500 ورقة',
                'default_code' => 'PAPER-A4-500',
                'list_price' => 25.00,
                'standard_price' => 18.00,
                'categ_id' => 3,
                'type' => 'consu',
                'sale_ok' => 1,
                'purchase_ok' => 1,
                'description' => 'ورق طباعة A4 عالي الجودة',
                'active' => 1,
                'create_date' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 5,
                'name' => 'استشارة تقنية',
                'default_code' => 'CONSULT-TECH',
                'list_price' => 200.00,
                'standard_price' => 150.00,
                'categ_id' => 2,
                'type' => 'service',
                'sale_ok' => 1,
                'purchase_ok' => 0,
                'description' => 'استشارة تقنية متخصصة',
                'active' => 1,
                'create_date' => date('Y-m-d H:i:s')
            ]
        ];
    }
    
    public function get_demo_data() {
        return $this->demo_data;
    }
    
    public function search($conditions = [], $order = []) {
        return $this->demo_data;
    }
    
    public function create($data) {
        // محاكاة إنشاء منتج جديد
        $new_id = max(array_column($this->demo_data, 'id')) + 1;
        $data['id'] = $new_id;
        $data['create_date'] = date('Y-m-d H:i:s');
        $this->demo_data[] = $data;
        return $new_id;
    }
    
    public function update($id, $data) {
        // محاكاة تحديث منتج
        foreach ($this->demo_data as &$product) {
            if ($product['id'] == $id) {
                $product = array_merge($product, $data);
                return true;
            }
        }
        return false;
    }
    
    public function delete($id) {
        // محاكاة حذف منتج
        foreach ($this->demo_data as $key => $product) {
            if ($product['id'] == $id) {
                unset($this->demo_data[$key]);
                return true;
            }
        }
        return false;
    }
}

$product_model = new ProductTemplateDemo();

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'create':
            $data = [
                'name' => $_POST['name'],
                'default_code' => $_POST['default_code'] ?? '',
                'list_price' => floatval($_POST['list_price'] ?? 0),
                'standard_price' => floatval($_POST['standard_price'] ?? 0),
                'categ_id' => intval($_POST['categ_id'] ?? 1),
                'type' => $_POST['type'] ?? 'product',
                'sale_ok' => isset($_POST['sale_ok']) ? 1 : 0,
                'purchase_ok' => isset($_POST['purchase_ok']) ? 1 : 0,
                'description' => $_POST['description'] ?? '',
                'active' => 1
            ];
            
            try {
                $product_model->create($data);
                $success_message = "تم إنشاء المنتج بنجاح";
            } catch (Exception $e) {
                $error_message = "خطأ في إنشاء المنتج: " . $e->getMessage();
            }
            break;
            
        case 'update':
            $id = $_POST['id'];
            $data = [
                'name' => $_POST['name'],
                'default_code' => $_POST['default_code'] ?? '',
                'list_price' => floatval($_POST['list_price'] ?? 0),
                'standard_price' => floatval($_POST['standard_price'] ?? 0),
                'categ_id' => intval($_POST['categ_id'] ?? 1),
                'type' => $_POST['type'] ?? 'product',
                'sale_ok' => isset($_POST['sale_ok']) ? 1 : 0,
                'purchase_ok' => isset($_POST['purchase_ok']) ? 1 : 0,
                'description' => $_POST['description'] ?? ''
            ];
            
            try {
                $product_model->update($id, $data);
                $success_message = "تم تحديث المنتج بنجاح";
            } catch (Exception $e) {
                $error_message = "خطأ في تحديث المنتج: " . $e->getMessage();
            }
            break;
            
        case 'delete':
            $id = $_POST['id'];
            try {
                $product_model->delete($id);
                $success_message = "تم حذف المنتج بنجاح";
            } catch (Exception $e) {
                $error_message = "خطأ في حذف المنتج: " . $e->getMessage();
            }
            break;
    }
}

// جلب المنتجات
$products = $product_model->get_demo_data();

// معاملات البحث والفلترة
$search_term = $_GET['search'] ?? '';
$filter_type = $_GET['filter'] ?? 'all';
$view_mode = $_GET['view'] ?? 'kanban';

// تطبيق البحث
if (!empty($search_term)) {
    $products = array_filter($products, function($product) use ($search_term) {
        return stripos($product['name'], $search_term) !== false ||
               stripos($product['default_code'], $search_term) !== false ||
               stripos($product['description'], $search_term) !== false;
    });
}

// تطبيق الفلاتر
if ($filter_type === 'saleable') {
    $products = array_filter($products, function($product) {
        return $product['sale_ok'] == 1;
    });
} elseif ($filter_type === 'purchasable') {
    $products = array_filter($products, function($product) {
        return $product['purchase_ok'] == 1;
    });
} elseif ($filter_type === 'services') {
    $products = array_filter($products, function($product) {
        return $product['type'] === 'service';
    });
} elseif ($filter_type === 'consumables') {
    $products = array_filter($products, function($product) {
        return $product['type'] === 'consu';
    });
}

// إحصائيات
$total_products = count($products);
$saleable = count(array_filter($products, function($p) { return $p['sale_ok'] == 1; }));
$purchasable = count(array_filter($products, function($p) { return $p['purchase_ok'] == 1; }));
$services = count(array_filter($products, function($p) { return $p['type'] === 'service'; }));
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المنتجات - نظام ERP</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --odoo-primary: #714B67;
            --odoo-secondary: #875A7B;
            --odoo-accent: #D4AF37;
            --odoo-success: #28a745;
            --odoo-info: #17a2b8;
            --odoo-warning: #ffc107;
            --odoo-danger: #dc3545;
            --odoo-light: #f8f9fa;
            --odoo-dark: #2F3349;
        }
        
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background: var(--odoo-light);
            margin: 0;
            padding: 0;
        }
        
        .main-container {
            display: flex;
            min-height: 100vh;
        }
        
        .content-area {
            flex: 1;
            padding: 0;
            overflow-x: hidden;
        }
        
        /* رأس الصفحة */
        .page-header {
            background: white;
            border-bottom: 1px solid #e9ecef;
            padding: 20px 30px;
            margin-bottom: 0;
        }
        
        .breadcrumb-odoo {
            background: none;
            padding: 0;
            margin: 0;
            font-size: 0.9rem;
        }
        
        .breadcrumb-odoo .breadcrumb-item {
            color: #6c757d;
        }
        
        .breadcrumb-odoo .breadcrumb-item.active {
            color: var(--odoo-primary);
            font-weight: 600;
        }
        
        .page-title {
            color: var(--odoo-dark);
            font-size: 1.8rem;
            font-weight: 700;
            margin: 10px 0 0 0;
        }
        
        /* أزرار الإجراءات */
        .action-buttons {
            background: white;
            border-bottom: 1px solid #e9ecef;
            padding: 15px 30px;
        }
        
        .btn-odoo {
            background: var(--odoo-primary);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-odoo:hover {
            background: var(--odoo-secondary);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(113, 75, 103, 0.3);
        }
        
        .btn-outline-odoo {
            background: transparent;
            border: 1px solid var(--odoo-primary);
            color: var(--odoo-primary);
        }
        
        .btn-outline-odoo:hover {
            background: var(--odoo-primary);
            color: white;
        }
        
        /* بطاقات الإحصائيات */
        .stats-container {
            padding: 20px 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid var(--odoo-primary);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--odoo-primary);
            margin: 0;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
            margin: 5px 0 0 0;
        }
        
        .stat-icon {
            font-size: 2.5rem;
            color: var(--odoo-primary);
            opacity: 0.7;
        }
        
        /* منطقة المحتوى */
        .content-section {
            padding: 20px 30px;
        }
        
        /* فلاتر البحث */
        .filter-tabs {
            display: flex;
            gap: 5px;
            margin-bottom: 20px;
        }
        
        .filter-tab {
            background: white;
            border: 1px solid #dee2e6;
            color: #6c757d;
            padding: 8px 16px;
            border-radius: 4px;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 6px;
            cursor: pointer;
        }
        
        .filter-tab:hover {
            background: #f8f9fa;
            color: var(--odoo-primary);
            text-decoration: none;
        }
        
        .filter-tab.active {
            background: var(--odoo-primary);
            color: white;
            border-color: var(--odoo-primary);
        }
        
        /* مربع البحث */
        .search-box {
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 8px 12px;
            font-size: 0.9rem;
        }
        
        .search-box:focus {
            border-color: var(--odoo-primary);
            box-shadow: 0 0 0 0.2rem rgba(113, 75, 103, 0.25);
        }
        
        /* مفتاح العرض */
        .view-switcher {
            display: flex;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .view-btn {
            background: white;
            border: none;
            padding: 8px 12px;
            color: #6c757d;
            transition: all 0.3s ease;
            border-right: 1px solid #dee2e6;
        }
        
        .view-btn:last-child {
            border-right: none;
        }
        
        .view-btn:hover {
            background: #f8f9fa;
            color: var(--odoo-primary);
        }
        
        .view-btn.active {
            background: var(--odoo-primary);
            color: white;
        }
        
        /* عرض البطاقات */
        .product-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
            overflow: hidden;
        }
        
        .product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .product-image {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(45deg, var(--odoo-primary), var(--odoo-secondary));
            color: white;
            font-weight: 600;
            font-size: 1.5rem;
        }
        
        .product-type-badge {
            font-size: 0.75rem;
            padding: 2px 8px;
            border-radius: 12px;
        }
        
        .price-display {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--odoo-success);
        }
        
        /* أزرار الإجراءات */
        .action-btn {
            background: none;
            border: 1px solid #dee2e6;
            color: #6c757d;
            padding: 6px 10px;
            border-radius: 4px;
            transition: all 0.3s ease;
            font-size: 0.8rem;
        }
        
        .btn-edit:hover {
            background: var(--odoo-info);
            color: white;
            border-color: var(--odoo-info);
        }
        
        .btn-delete:hover {
            background: var(--odoo-danger);
            color: white;
            border-color: var(--odoo-danger);
        }
        
        /* عرض القائمة */
        .table-responsive {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .table {
            margin: 0;
        }
        
        .table th {
            background: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
            font-weight: 600;
            color: var(--odoo-dark);
            padding: 12px;
            font-size: 0.9rem;
        }
        
        .table td {
            padding: 12px;
            vertical-align: middle;
            border-bottom: 1px solid #e9ecef;
        }
        
        .table tbody tr:hover {
            background: #f8f9fa;
        }
        
        /* النوافذ المنبثقة */
        .modal-content {
            border: none;
            border-radius: 8px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .modal-header {
            background: var(--odoo-primary);
            color: white;
            border-radius: 8px 8px 0 0;
            padding: 15px 20px;
        }
        
        .modal-title {
            font-weight: 600;
        }
        
        .btn-close {
            filter: invert(1);
        }
        
        .form-label {
            font-weight: 500;
            color: var(--odoo-dark);
            margin-bottom: 5px;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--odoo-primary);
            box-shadow: 0 0 0 0.2rem rgba(113, 75, 103, 0.25);
        }
        
        /* رسائل التنبيه */
        .alert {
            border: none;
            border-radius: 6px;
            padding: 12px 16px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border-left: 4px solid #28a745;
        }
        
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border-left: 4px solid #dc3545;
        }
        
        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 768px) {
            .page-header, .action-buttons, .content-section {
                padding: 15px 20px;
            }
            
            .filter-tabs {
                flex-wrap: wrap;
            }
            
            .stats-container {
                padding: 15px 20px;
            }
            
            .product-card {
                margin-bottom: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- الشريط الجانبي -->
        <?php include '../includes/sidebar.php'; ?>
        
        <!-- منطقة المحتوى -->
        <div class="content-area">
            <!-- رأس الصفحة -->
            <div class="page-header">
                <nav class="breadcrumb-odoo">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="../dashboard.php">
                                <i class="fas fa-home"></i> الرئيسية
                            </a>
                        </li>
                        <li class="breadcrumb-item">المنتجات</li>
                        <li class="breadcrumb-item active">قائمة المنتجات</li>
                    </ol>
                </nav>
                <h1 class="page-title">
                    <i class="fas fa-boxes me-2"></i>المنتجات
                </h1>
            </div>
            
            <!-- أزرار الإجراءات -->
            <div class="action-buttons">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="d-flex gap-2">
                        <button class="btn-odoo" data-bs-toggle="modal" data-bs-target="#productModal" onclick="openCreateModal()">
                            <i class="fas fa-plus"></i>
                            إنشاء منتج جديد
                        </button>
                        <button class="btn-outline-odoo" onclick="importProducts()">
                            <i class="fas fa-upload"></i>
                            استيراد
                        </button>
                        <button class="btn-outline-odoo" onclick="exportProducts()">
                            <i class="fas fa-download"></i>
                            تصدير
                        </button>
                    </div>
                    
                    <div class="d-flex align-items-center gap-3">
                        <span class="text-muted">
                            <i class="fas fa-boxes me-1"></i>
                            <?php echo $total_products; ?> منتج
                        </span>
                    </div>
                </div>
            </div>
            
            <!-- رسائل التنبيه -->
            <?php if (isset($success_message)): ?>
                <div class="alert alert-success mx-4">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo $success_message; ?>
                </div>
            <?php endif; ?>
            
            <?php if (isset($error_message)): ?>
                <div class="alert alert-danger mx-4">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <?php echo $error_message; ?>
                </div>
            <?php endif; ?>
            
            <!-- بطاقات الإحصائيات -->
            <div class="stats-container">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="stat-number"><?php echo $total_products; ?></h3>
                                    <p class="stat-label">إجمالي المنتجات</p>
                                </div>
                                <i class="fas fa-boxes stat-icon"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="stat-number"><?php echo $saleable; ?></h3>
                                    <p class="stat-label">قابل للبيع</p>
                                </div>
                                <i class="fas fa-shopping-cart stat-icon"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="stat-number"><?php echo $purchasable; ?></h3>
                                    <p class="stat-label">قابل للشراء</p>
                                </div>
                                <i class="fas fa-shopping-bag stat-icon"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="stat-number"><?php echo $services; ?></h3>
                                    <p class="stat-label">الخدمات</p>
                                </div>
                                <i class="fas fa-concierge-bell stat-icon"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- منطقة المحتوى الرئيسي -->
            <div class="content-section">
                <div class="container-fluid">
                    <!-- شريط البحث والفلاتر -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <!-- تبويبات الفلاتر -->
                            <div class="filter-tabs">
                                <button class="filter-tab <?php echo $filter_type === 'all' ? 'active' : ''; ?>"
                                        onclick="applyFilter('all')">
                                    <i class="fas fa-boxes me-1"></i>الكل
                                </button>
                                <button class="filter-tab <?php echo $filter_type === 'saleable' ? 'active' : ''; ?>"
                                        onclick="applyFilter('saleable')">
                                    <i class="fas fa-shopping-cart me-1"></i>قابل للبيع
                                </button>
                                <button class="filter-tab <?php echo $filter_type === 'purchasable' ? 'active' : ''; ?>"
                                        onclick="applyFilter('purchasable')">
                                    <i class="fas fa-shopping-bag me-1"></i>قابل للشراء
                                </button>
                                <button class="filter-tab <?php echo $filter_type === 'services' ? 'active' : ''; ?>"
                                        onclick="applyFilter('services')">
                                    <i class="fas fa-concierge-bell me-1"></i>الخدمات
                                </button>
                                <button class="filter-tab <?php echo $filter_type === 'consumables' ? 'active' : ''; ?>"
                                        onclick="applyFilter('consumables')">
                                    <i class="fas fa-recycle me-1"></i>المواد الاستهلاكية
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <!-- مربع البحث -->
                            <div class="input-group">
                                <input type="text" class="form-control search-box"
                                       placeholder="البحث في المنتجات..."
                                       value="<?php echo htmlspecialchars($search_term); ?>"
                                       onkeyup="searchProducts(this.value)">
                                <button class="btn btn-outline-secondary" type="button" onclick="clearSearch()">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- شريط أدوات العرض -->
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="d-flex align-items-center">
                            <!-- مفتاح العرض -->
                            <div class="view-switcher me-3">
                                <button class="view-btn <?php echo $view_mode === 'kanban' ? 'active' : ''; ?>"
                                        onclick="switchView('kanban')" title="عرض البطاقات">
                                    <i class="fas fa-th-large"></i>
                                </button>
                                <button class="view-btn <?php echo $view_mode === 'list' ? 'active' : ''; ?>"
                                        onclick="switchView('list')" title="عرض القائمة">
                                    <i class="fas fa-list"></i>
                                </button>
                            </div>

                            <!-- عدد النتائج -->
                            <span class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                <?php echo count($products); ?> منتج
                            </span>
                        </div>

                        <div class="d-flex align-items-center">
                            <!-- ترتيب -->
                            <select class="form-select form-select-sm" style="width: auto;" onchange="sortProducts(this.value)">
                                <option value="name_asc">الاسم (أ-ي)</option>
                                <option value="name_desc">الاسم (ي-أ)</option>
                                <option value="price_desc">السعر (الأعلى)</option>
                                <option value="price_asc">السعر (الأقل)</option>
                            </select>
                        </div>
                    </div>

                    <!-- عرض المنتجات -->
                    <div id="productsContainer" class="<?php echo $view_mode; ?>-view">
                        <?php if (empty($products)): ?>
                            <!-- رسالة عدم وجود بيانات -->
                            <div class="text-center py-5">
                                <div class="mb-4">
                                    <i class="fas fa-boxes fa-4x text-muted"></i>
                                </div>
                                <h4 class="text-muted">لا توجد منتجات</h4>
                                <p class="text-muted">ابدأ بإضافة منتج جديد لرؤية البيانات هنا</p>
                                <button class="btn-odoo" data-bs-toggle="modal" data-bs-target="#productModal" onclick="openCreateModal()">
                                    <i class="fas fa-plus me-1"></i>إضافة منتج جديد
                                </button>
                            </div>
                        <?php else: ?>
                            <?php if ($view_mode === 'kanban'): ?>
                                <!-- عرض البطاقات -->
                                <div class="row">
                                    <?php foreach ($products as $product): ?>
                                        <div class="col-lg-4 col-md-6 mb-3">
                                            <div class="product-card">
                                                <div class="card-body">
                                                    <div class="d-flex align-items-start mb-3">
                                                        <!-- صورة المنتج -->
                                                        <div class="product-image me-3">
                                                            <?php if ($product['type'] === 'service'): ?>
                                                                <i class="fas fa-concierge-bell"></i>
                                                            <?php elseif ($product['type'] === 'consu'): ?>
                                                                <i class="fas fa-recycle"></i>
                                                            <?php else: ?>
                                                                <i class="fas fa-box"></i>
                                                            <?php endif; ?>
                                                        </div>

                                                        <!-- معلومات المنتج -->
                                                        <div class="flex-grow-1">
                                                            <h6 class="card-title mb-1">
                                                                <?php echo htmlspecialchars($product['name']); ?>
                                                            </h6>

                                                            <?php if (!empty($product['default_code'])): ?>
                                                                <small class="text-muted d-block mb-2">
                                                                    كود: <?php echo htmlspecialchars($product['default_code']); ?>
                                                                </small>
                                                            <?php endif; ?>

                                                            <!-- شارات النوع -->
                                                            <div class="mb-2">
                                                                <?php if ($product['sale_ok']): ?>
                                                                    <span class="badge bg-success product-type-badge me-1">للبيع</span>
                                                                <?php endif; ?>
                                                                <?php if ($product['purchase_ok']): ?>
                                                                    <span class="badge bg-info product-type-badge me-1">للشراء</span>
                                                                <?php endif; ?>

                                                                <?php if ($product['type'] === 'service'): ?>
                                                                    <span class="badge bg-warning product-type-badge">خدمة</span>
                                                                <?php elseif ($product['type'] === 'consu'): ?>
                                                                    <span class="badge bg-secondary product-type-badge">استهلاكي</span>
                                                                <?php else: ?>
                                                                    <span class="badge bg-primary product-type-badge">منتج</span>
                                                                <?php endif; ?>
                                                            </div>

                                                            <!-- السعر -->
                                                            <div class="price-display">
                                                                <?php echo number_format($product['list_price'], 2); ?> ر.س
                                                            </div>
                                                        </div>

                                                        <!-- قائمة الإجراءات -->
                                                        <div class="dropdown">
                                                            <button class="btn btn-sm btn-outline-secondary"
                                                                    data-bs-toggle="dropdown">
                                                                <i class="fas fa-ellipsis-v"></i>
                                                            </button>
                                                            <ul class="dropdown-menu">
                                                                <li>
                                                                    <a class="dropdown-item" href="#"
                                                                       onclick="editProduct(<?php echo $product['id']; ?>)">
                                                                        <i class="fas fa-edit me-2"></i>تعديل
                                                                    </a>
                                                                </li>
                                                                <li>
                                                                    <a class="dropdown-item" href="#"
                                                                       onclick="viewProduct(<?php echo $product['id']; ?>)">
                                                                        <i class="fas fa-eye me-2"></i>عرض
                                                                    </a>
                                                                </li>
                                                                <li><hr class="dropdown-divider"></li>
                                                                <li>
                                                                    <a class="dropdown-item text-danger" href="#"
                                                                       onclick="deleteProduct(<?php echo $product['id']; ?>)">
                                                                        <i class="fas fa-trash me-2"></i>حذف
                                                                    </a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </div>

                                                    <!-- الوصف -->
                                                    <?php if (!empty($product['description'])): ?>
                                                        <div class="product-description">
                                                            <small class="text-muted">
                                                                <?php echo htmlspecialchars(substr($product['description'], 0, 100)); ?>
                                                                <?php if (strlen($product['description']) > 100): ?>...<?php endif; ?>
                                                            </small>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php else: ?>
                                <!-- عرض القائمة -->
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>المنتج</th>
                                                <th>الكود</th>
                                                <th>النوع</th>
                                                <th>سعر البيع</th>
                                                <th>سعر التكلفة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($products as $product): ?>
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div class="product-image me-2" style="width: 40px; height: 40px; font-size: 1rem;">
                                                                <?php if ($product['type'] === 'service'): ?>
                                                                    <i class="fas fa-concierge-bell"></i>
                                                                <?php elseif ($product['type'] === 'consu'): ?>
                                                                    <i class="fas fa-recycle"></i>
                                                                <?php else: ?>
                                                                    <i class="fas fa-box"></i>
                                                                <?php endif; ?>
                                                            </div>
                                                            <div>
                                                                <div class="fw-bold"><?php echo htmlspecialchars($product['name']); ?></div>
                                                                <?php if (!empty($product['description'])): ?>
                                                                    <small class="text-muted">
                                                                        <?php echo htmlspecialchars(substr($product['description'], 0, 50)); ?>
                                                                        <?php if (strlen($product['description']) > 50): ?>...<?php endif; ?>
                                                                    </small>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td><?php echo htmlspecialchars($product['default_code'] ?? ''); ?></td>
                                                    <td>
                                                        <?php if ($product['sale_ok']): ?>
                                                            <span class="badge bg-success me-1">للبيع</span>
                                                        <?php endif; ?>
                                                        <?php if ($product['purchase_ok']): ?>
                                                            <span class="badge bg-info me-1">للشراء</span>
                                                        <?php endif; ?>
                                                        <br>
                                                        <?php if ($product['type'] === 'service'): ?>
                                                            <span class="badge bg-warning">خدمة</span>
                                                        <?php elseif ($product['type'] === 'consu'): ?>
                                                            <span class="badge bg-secondary">استهلاكي</span>
                                                        <?php else: ?>
                                                            <span class="badge bg-primary">منتج</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td class="price-display"><?php echo number_format($product['list_price'], 2); ?> ر.س</td>
                                                    <td><?php echo number_format($product['standard_price'], 2); ?> ر.س</td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            <button class="action-btn btn-edit"
                                                                    onclick="editProduct(<?php echo $product['id']; ?>)"
                                                                    title="تعديل">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            <button class="action-btn btn-delete"
                                                                    onclick="deleteProduct(<?php echo $product['id']; ?>)"
                                                                    title="حذف">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة حوار المنتج -->
    <div class="modal fade" id="productModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="productModalTitle">إنشاء منتج جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="productForm" method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" id="formAction" value="create">
                        <input type="hidden" name="id" id="productId" value="">

                        <div class="row">
                            <!-- المعلومات الأساسية -->
                            <div class="col-md-6">
                                <h6 class="mb-3">المعلومات الأساسية</h6>

                                <div class="mb-3">
                                    <label class="form-label">اسم المنتج *</label>
                                    <input type="text" class="form-control" name="name" id="productName" required>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">الكود المرجعي</label>
                                    <input type="text" class="form-control" name="default_code" id="productCode">
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">سعر البيع</label>
                                            <input type="number" step="0.01" class="form-control" name="list_price" id="productListPrice">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">سعر التكلفة</label>
                                            <input type="number" step="0.01" class="form-control" name="standard_price" id="productStandardPrice">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">نوع المنتج</label>
                                    <select class="form-select" name="type" id="productType">
                                        <option value="product">منتج قابل للتخزين</option>
                                        <option value="consu">منتج استهلاكي</option>
                                        <option value="service">خدمة</option>
                                    </select>
                                </div>
                            </div>

                            <!-- التصنيف والخيارات -->
                            <div class="col-md-6">
                                <h6 class="mb-3">التصنيف والخيارات</h6>

                                <div class="mb-3">
                                    <label class="form-label">فئة المنتج</label>
                                    <select class="form-select" name="categ_id" id="productCategory">
                                        <option value="1">الإلكترونيات</option>
                                        <option value="2">الخدمات</option>
                                        <option value="3">المكتبية</option>
                                        <option value="4">أخرى</option>
                                    </select>
                                </div>

                                <!-- خيارات البيع والشراء -->
                                <div class="mb-3">
                                    <label class="form-label">خيارات المنتج</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="sale_ok" id="saleOk" checked>
                                        <label class="form-check-label" for="saleOk">
                                            يمكن بيعه
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="purchase_ok" id="purchaseOk">
                                        <label class="form-check-label" for="purchaseOk">
                                            يمكن شراؤه
                                        </label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">الوصف</label>
                                    <textarea class="form-control" name="description" id="productDescription" rows="4"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn-odoo">حفظ</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // متغيرات عامة
        let currentFilter = '<?php echo $filter_type; ?>';
        let currentView = '<?php echo $view_mode; ?>';
        let currentSearch = '<?php echo $search_term; ?>';

        // تطبيق الفلتر
        function applyFilter(filter) {
            const url = new URL(window.location);
            url.searchParams.set('filter', filter);
            window.location.href = url.toString();
        }

        // تبديل طريقة العرض
        function switchView(view) {
            const url = new URL(window.location);
            url.searchParams.set('view', view);
            window.location.href = url.toString();
        }

        // البحث في المنتجات
        function searchProducts(term) {
            const url = new URL(window.location);
            if (term.trim()) {
                url.searchParams.set('search', term);
            } else {
                url.searchParams.delete('search');
            }

            // تأخير البحث لتحسين الأداء
            clearTimeout(window.searchTimeout);
            window.searchTimeout = setTimeout(() => {
                window.location.href = url.toString();
            }, 500);
        }

        // مسح البحث
        function clearSearch() {
            const url = new URL(window.location);
            url.searchParams.delete('search');
            window.location.href = url.toString();
        }

        // ترتيب المنتجات
        function sortProducts(sortBy) {
            console.log('Sorting by:', sortBy);
        }

        // فتح نافذة إنشاء منتج جديد
        function openCreateModal() {
            document.getElementById('productModalTitle').textContent = 'إنشاء منتج جديد';
            document.getElementById('formAction').value = 'create';
            document.getElementById('productId').value = '';
            document.getElementById('productForm').reset();
            document.getElementById('saleOk').checked = true;
        }

        // تعديل منتج
        function editProduct(id) {
            document.getElementById('productModalTitle').textContent = 'تعديل المنتج';
            document.getElementById('formAction').value = 'update';
            document.getElementById('productId').value = id;

            // فتح النافذة
            const modal = new bootstrap.Modal(document.getElementById('productModal'));
            modal.show();
        }

        // عرض تفاصيل المنتج
        function viewProduct(id) {
            window.location.href = `product_details.php?id=${id}`;
        }

        // حذف منتج
        function deleteProduct(id) {
            if (confirm('هل أنت متأكد من حذف هذا المنتج؟ لا يمكن التراجع عن هذا الإجراء.')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="id" value="${id}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        // استيراد المنتجات
        function importProducts() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.csv,.xlsx,.xls';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    alert('سيتم تطوير ميزة الاستيراد قريباً');
                }
            };
            input.click();
        }

        // تصدير المنتجات
        function exportProducts() {
            alert('سيتم تطوير ميزة التصدير قريباً');
        }

        // تحسينات UX
        document.addEventListener('DOMContentLoaded', function() {
            // تأثيرات hover للبطاقات
            document.querySelectorAll('.product-card').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // تحسين البحث
            const searchInput = document.querySelector('.search-box');
            if (searchInput) {
                searchInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        searchProducts(this.value);
                    }
                });
            }

            // تحسين النماذج
            const productForm = document.getElementById('productForm');
            if (productForm) {
                productForm.addEventListener('submit', function(e) {
                    const name = document.getElementById('productName').value.trim();
                    if (!name) {
                        e.preventDefault();
                        alert('يرجى إدخال اسم المنتج');
                        document.getElementById('productName').focus();
                        return false;
                    }
                });
            }
        });
    </script>
</body>
</html>
