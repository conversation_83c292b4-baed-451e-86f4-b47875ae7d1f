<?php
/**
 * صفحة إدارة المنتجات بأسلوب Odoo
 * Products Management Page - Odoo Style
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// تحميل نظام Odoo
require_once '../config/odoo_config.php';
require_once '../config/odoo_database.php';

$odoo_db = OdooDatabase::getInstance();

// تحميل النماذج
require_once '../models/BaseModel.php';
require_once '../models/ProductTemplate.php';

$product_model = new ProductTemplate($odoo_db);

// جلب المنتجات من قاعدة البيانات
try {
    $products = $product_model->search_read(
        array(array('active', '=', true)),
        null,
        array('order' => 'name ASC')
    );
} catch (Exception $e) {
    $products = $product_model->get_demo_data();
}

// فلترة حسب النوع
$filter_type = $_GET['filter'] ?? 'all';
if ($filter_type === 'service') {
    $products = array_filter($products, function($p) { return $p['type'] === 'service'; });
} elseif ($filter_type === 'product') {
    $products = array_filter($products, function($p) { return $p['type'] === 'product'; });
} elseif ($filter_type === 'consumable') {
    $products = array_filter($products, function($p) { return $p['type'] === 'consu'; });
}

// طريقة العرض المحددة
$view_mode = $_GET['view'] ?? 'cards';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المنتجات - نظام ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --odoo-primary: #714B67;
            --odoo-secondary: #8B5A8C;
        }
        
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar-odoo {
            background: linear-gradient(45deg, var(--odoo-primary), var(--odoo-secondary));
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .page-header {
            background: linear-gradient(45deg, #F56C6C, #E74C3C);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
        }
        
        .view-controls {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        
        .view-btn, .filter-btn {
            border: 1px solid #dee2e6;
            background: white;
            color: #6c757d;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: all 0.3s ease;
            text-decoration: none;
        }
        
        .view-btn.active, .filter-btn.active {
            background: #F56C6C;
            color: white;
            border-color: #F56C6C;
        }
        
        .view-btn:hover, .filter-btn:hover {
            background: #E74C3C;
            color: white;
            border-color: #E74C3C;
            text-decoration: none;
        }
        
        .product-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            border-left: 4px solid #dee2e6;
        }
        
        .product-card.service {
            border-left-color: #17a2b8;
        }
        
        .product-card.product {
            border-left-color: #28a745;
        }
        
        .product-card.consumable {
            border-left-color: #ffc107;
        }
        
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }
        
        .product-image {
            width: 80px;
            height: 80px;
            border-radius: 10px;
            background: linear-gradient(45deg, #F56C6C, #E74C3C);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
        }
        
        .table-odoo {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .table-odoo th {
            background: linear-gradient(45deg, #F56C6C, #E74C3C);
            color: white;
            border: none;
            padding: 1rem;
        }
        
        .table-odoo td {
            padding: 1rem;
            border-color: #f8f9fa;
            vertical-align: middle;
        }
        
        .breadcrumb-odoo {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 4px solid;
            margin-bottom: 1rem;
        }
        
        .price-tag {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-odoo">
        <div class="container-fluid">
            <a class="navbar-brand text-white" href="../dashboard.php">
                <i class="fas fa-cube me-2"></i>
                نظام ERP - إدارة المنتجات
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="../dashboard.php">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- مسار التنقل -->
        <nav class="breadcrumb-odoo">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="../dashboard.php">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="#">إدارة المخزون</a></li>
                <li class="breadcrumb-item active">المنتجات</li>
            </ol>
        </nav>

        <!-- العنوان الرئيسي -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2><i class="fas fa-boxes me-2"></i>إدارة المنتجات</h2>
                    <p class="mb-0">إدارة المنتجات والخدمات والمواد الاستهلاكية</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-light">
                        <i class="fas fa-plus me-2"></i>إضافة منتج جديد
                    </button>
                </div>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card border-success">
                    <h3 class="text-success"><?php echo count(array_filter($product_model->get_demo_data(), function($p) { return $p['type'] === 'product'; })); ?></h3>
                    <p class="mb-0">منتجات مخزنة</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card border-info">
                    <h3 class="text-info"><?php echo count(array_filter($product_model->get_demo_data(), function($p) { return $p['type'] === 'service'; })); ?></h3>
                    <p class="mb-0">خدمات</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card border-warning">
                    <h3 class="text-warning"><?php echo count(array_filter($product_model->get_demo_data(), function($p) { return $p['type'] === 'consu'; })); ?></h3>
                    <p class="mb-0">مواد استهلاكية</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card border-primary">
                    <h3 class="text-primary"><?php echo count($product_model->get_demo_data()); ?></h3>
                    <p class="mb-0">إجمالي المنتجات</p>
                </div>
            </div>
        </div>

        <!-- أدوات التحكم والعرض -->
        <div class="view-controls">
            <div class="row align-items-center">
                <div class="col-md-4">
                    <div class="btn-group" role="group">
                        <a href="?view=cards&filter=<?php echo $filter_type; ?>" class="view-btn <?php echo $view_mode === 'cards' ? 'active' : ''; ?>">
                            <i class="fas fa-th-large me-2"></i>بطاقات
                        </a>
                        <a href="?view=table&filter=<?php echo $filter_type; ?>" class="view-btn <?php echo $view_mode === 'table' ? 'active' : ''; ?>">
                            <i class="fas fa-table me-2"></i>جدول
                        </a>
                        <a href="?view=kanban&filter=<?php echo $filter_type; ?>" class="view-btn <?php echo $view_mode === 'kanban' ? 'active' : ''; ?>">
                            <i class="fas fa-columns me-2"></i>كانبان
                        </a>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="btn-group" role="group">
                        <a href="?view=<?php echo $view_mode; ?>&filter=all" class="filter-btn <?php echo $filter_type === 'all' ? 'active' : ''; ?>">
                            الكل
                        </a>
                        <a href="?view=<?php echo $view_mode; ?>&filter=product" class="filter-btn <?php echo $filter_type === 'product' ? 'active' : ''; ?>">
                            منتجات مخزنة
                        </a>
                        <a href="?view=<?php echo $view_mode; ?>&filter=service" class="filter-btn <?php echo $filter_type === 'service' ? 'active' : ''; ?>">
                            خدمات
                        </a>
                        <a href="?view=<?php echo $view_mode; ?>&filter=consumable" class="filter-btn <?php echo $filter_type === 'consumable' ? 'active' : ''; ?>">
                            استهلاكية
                        </a>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="d-flex justify-content-end">
                        <input type="text" class="form-control search-box" placeholder="البحث في المنتجات..." style="max-width: 250px;">
                    </div>
                </div>
            </div>
        </div>

        <!-- محتوى العرض -->
        <?php if ($view_mode === 'cards'): ?>
            <!-- عرض البطاقات -->
            <div class="row">
                <?php foreach ($products as $product): 
                    $card_class = $product['type'] ?? 'product';
                    $type_icon = array(
                        'product' => 'fas fa-box',
                        'service' => 'fas fa-cogs',
                        'consu' => 'fas fa-shopping-basket'
                    );
                    $type_name = array(
                        'product' => 'منتج مخزن',
                        'service' => 'خدمة',
                        'consu' => 'مادة استهلاكية'
                    );
                ?>
                    <div class="col-md-6 col-lg-4">
                        <div class="product-card <?php echo $card_class; ?>">
                            <div class="d-flex align-items-center mb-3">
                                <div class="product-image me-3">
                                    <i class="<?php echo $type_icon[$product['type']] ?? 'fas fa-box'; ?>"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h5 class="mb-1"><?php echo $product['name']; ?></h5>
                                    <p class="text-muted mb-0"><?php echo $product['default_code'] ?? 'بدون كود'; ?></p>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <span class="badge bg-secondary"><?php echo $type_name[$product['type']] ?? 'منتج'; ?></span>
                                <?php if ($product['sale_ok']): ?>
                                    <span class="badge bg-success">للبيع</span>
                                <?php endif; ?>
                                <?php if ($product['purchase_ok']): ?>
                                    <span class="badge bg-primary">للشراء</span>
                                <?php endif; ?>
                            </div>
                            
                            <div class="mb-3">
                                <div class="price-tag text-center">
                                    <?php echo number_format($product['list_price'], 2); ?> ريال
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <small class="text-muted">
                                    <i class="fas fa-layer-group me-1"></i>الفئة: <?php echo $product['categ_id'] ?? 'عام'; ?><br>
                                    <i class="fas fa-weight me-1"></i>الوزن: <?php echo $product['weight'] ?? '0'; ?> كجم
                                </small>
                            </div>
                            
                            <div class="d-flex justify-content-between">
                                <button class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-edit me-1"></i>تعديل
                                </button>
                                <button class="btn btn-outline-info btn-sm">
                                    <i class="fas fa-eye me-1"></i>عرض
                                </button>
                                <button class="btn btn-outline-success btn-sm">
                                    <i class="fas fa-shopping-cart me-1"></i>بيع
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

        <?php elseif ($view_mode === 'table'): ?>
            <!-- عرض الجدول -->
            <div class="table-odoo">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>المنتج</th>
                            <th>الكود</th>
                            <th>النوع</th>
                            <th>السعر</th>
                            <th>الوزن</th>
                            <th>الفئة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($products as $product): 
                            $type_icon = array(
                                'product' => 'fas fa-box',
                                'service' => 'fas fa-cogs',
                                'consu' => 'fas fa-shopping-basket'
                            );
                            $type_name = array(
                                'product' => 'منتج مخزن',
                                'service' => 'خدمة',
                                'consu' => 'مادة استهلاكية'
                            );
                        ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="product-image me-3" style="width: 40px; height: 40px; font-size: 1rem;">
                                            <i class="<?php echo $type_icon[$product['type']] ?? 'fas fa-box'; ?>"></i>
                                        </div>
                                        <div>
                                            <strong><?php echo $product['name']; ?></strong>
                                        </div>
                                    </div>
                                </td>
                                <td><?php echo $product['default_code'] ?? 'بدون كود'; ?></td>
                                <td>
                                    <span class="badge bg-secondary"><?php echo $type_name[$product['type']] ?? 'منتج'; ?></span>
                                </td>
                                <td>
                                    <strong><?php echo number_format($product['list_price'], 2); ?> ريال</strong>
                                </td>
                                <td><?php echo $product['weight'] ?? '0'; ?> كجم</td>
                                <td><?php echo $product['categ_id'] ?? 'عام'; ?></td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-info">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-success">
                                            <i class="fas fa-shopping-cart"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

        <?php else: ?>
            <!-- عرض كانبان -->
            <div class="row">
                <div class="col-md-4">
                    <h5 class="text-success"><i class="fas fa-box me-2"></i>منتجات مخزنة</h5>
                    <?php foreach (array_filter($products, function($p) { return $p['type'] === 'product'; }) as $product): ?>
                        <div class="product-card product mb-3">
                            <h6><?php echo $product['name']; ?></h6>
                            <p class="text-muted small"><?php echo $product['default_code'] ?? 'بدون كود'; ?></p>
                            <div class="price-tag text-center">
                                <?php echo number_format($product['list_price'], 2); ?> ريال
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <div class="col-md-4">
                    <h5 class="text-info"><i class="fas fa-cogs me-2"></i>خدمات</h5>
                    <?php foreach (array_filter($products, function($p) { return $p['type'] === 'service'; }) as $product): ?>
                        <div class="product-card service mb-3">
                            <h6><?php echo $product['name']; ?></h6>
                            <p class="text-muted small"><?php echo $product['default_code'] ?? 'بدون كود'; ?></p>
                            <div class="price-tag text-center">
                                <?php echo number_format($product['list_price'], 2); ?> ريال
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <div class="col-md-4">
                    <h5 class="text-warning"><i class="fas fa-shopping-basket me-2"></i>مواد استهلاكية</h5>
                    <?php foreach (array_filter($products, function($p) { return $p['type'] === 'consu'; }) as $product): ?>
                        <div class="product-card consumable mb-3">
                            <h6><?php echo $product['name']; ?></h6>
                            <p class="text-muted small"><?php echo $product['default_code'] ?? 'بدون كود'; ?></p>
                            <div class="price-tag text-center">
                                <?php echo number_format($product['list_price'], 2); ?> ريال
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const items = document.querySelectorAll('.product-card');
            items.forEach((item, index) => {
                item.style.opacity = '0';
                item.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    item.style.transition = 'all 0.6s ease';
                    item.style.opacity = '1';
                    item.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
        
        // البحث المباشر
        document.querySelector('.search-box').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const items = document.querySelectorAll('.product-card, tbody tr');
            
            items.forEach(item => {
                const text = item.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    item.style.display = '';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
