<?php
/**
 * صفحة المنتجات بأسلوب Odoo الكامل
 * Products Page - Complete Odoo Style
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// تحميل النماذج
require_once '../models/ProductTemplate.php';

// تهيئة النماذج
$product_model = new ProductTemplate();

$message = '';
$message_type = '';

// معالجة الطلبات AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    $action = isset($_POST['action']) ? $_POST['action'] : '';
    
    switch ($action) {
        case 'delete_product':
            $product_id = isset($_POST['product_id']) ? $_POST['product_id'] : 0;
            if ($product_id) {
                try {
                    echo json_encode(array('success' => true, 'message' => 'تم حذف المنتج بنجاح'));
                    exit();
                } catch (Exception $e) {
                    echo json_encode(array('success' => false, 'message' => 'خطأ في حذف المنتج: ' . $e->getMessage()));
                    exit();
                }
            }
            break;
            
        case 'toggle_active':
            $product_id = isset($_POST['product_id']) ? $_POST['product_id'] : 0;
            if ($product_id) {
                try {
                    echo json_encode(array('success' => true, 'message' => 'تم تحديث حالة المنتج بنجاح'));
                    exit();
                } catch (Exception $e) {
                    echo json_encode(array('success' => false, 'message' => 'خطأ في تحديث الحالة: ' . $e->getMessage()));
                    exit();
                }
            }
            break;
            
        case 'bulk_action':
            $selected_ids = isset($_POST['selected_ids']) ? $_POST['selected_ids'] : array();
            $bulk_action = isset($_POST['bulk_action']) ? $_POST['bulk_action'] : '';
            
            if (!empty($selected_ids) && $bulk_action) {
                $success_count = count($selected_ids);
                echo json_encode(array(
                    'success' => true, 
                    'message' => "تم تنفيذ العملية على {$success_count} منتج بنجاح"
                ));
                exit();
            }
            break;
    }
}

// معاملات الفلترة والعرض
$view_mode = isset($_GET['view']) ? $_GET['view'] : 'kanban';
$filter_type = isset($_GET['type']) ? $_GET['type'] : 'all';
$filter_active = isset($_GET['active']) ? $_GET['active'] : 'all';
$search_term = isset($_GET['search']) ? $_GET['search'] : '';

// جلب البيانات
$products = $product_model->get_demo_data();

// تطبيق الفلاتر
if ($filter_type !== 'all') {
    $filtered_products = array();
    foreach ($products as $product) {
        if ($filter_type === 'product' && $product['type'] === 'product') {
            $filtered_products[] = $product;
        } elseif ($filter_type === 'service' && $product['type'] === 'service') {
            $filtered_products[] = $product;
        } elseif ($filter_type === 'consumable' && $product['type'] === 'consumable') {
            $filtered_products[] = $product;
        }
    }
    $products = $filtered_products;
}

if ($filter_active !== 'all') {
    $filtered_products = array();
    foreach ($products as $product) {
        if (($filter_active === 'active' && $product['active']) || 
            ($filter_active === 'inactive' && !$product['active'])) {
            $filtered_products[] = $product;
        }
    }
    $products = $filtered_products;
}

// تطبيق البحث
if (!empty($search_term)) {
    $filtered_products = array();
    foreach ($products as $product) {
        $barcode = isset($product['barcode']) ? $product['barcode'] : '';
        $default_code = isset($product['default_code']) ? $product['default_code'] : '';
        if (stripos($product['name'], $search_term) !== false || 
            stripos($barcode, $search_term) !== false ||
            stripos($default_code, $search_term) !== false) {
            $filtered_products[] = $product;
        }
    }
    $products = $filtered_products;
}

// إحصائيات سريعة
$all_products = $product_model->get_demo_data();
$product_count = 0;
$service_count = 0;
$consumable_count = 0;
$active_count = 0;

foreach ($all_products as $product) {
    if ($product['type'] === 'product') $product_count++;
    if ($product['type'] === 'service') $service_count++;
    if ($product['type'] === 'consumable') $consumable_count++;
    if ($product['active']) $active_count++;
}

$stats = array(
    'total' => count($all_products),
    'products' => $product_count,
    'services' => $service_count,
    'consumables' => $consumable_count,
    'active' => $active_count
);

// أنواع المنتجات
$product_types = array(
    'product' => array('name' => 'منتج مخزني', 'icon' => 'fas fa-box', 'color' => '#2196f3'),
    'service' => array('name' => 'خدمة', 'icon' => 'fas fa-cogs', 'color' => '#4caf50'),
    'consumable' => array('name' => 'مستهلك', 'icon' => 'fas fa-shopping-cart', 'color' => '#ff9800')
);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المنتجات - نظام ERP</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Odoo Style -->
    <link href="../assets/css/odoo-style.css" rel="stylesheet">
    <link href="../assets/css/odoo-advanced.css" rel="stylesheet">
    <link href="../assets/css/journal-entries-enhanced.css" rel="stylesheet">
    
    <style>
        .product-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .product-card.inactive {
            opacity: 0.6;
        }
        
        .product-image {
            height: 200px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }
        
        .product-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, transparent 0%, rgba(255,255,255,0.1) 50%, transparent 100%);
            transform: translateX(-100%);
            transition: transform 0.6s ease;
        }
        
        .product-card:hover .product-image::before {
            transform: translateX(100%);
        }
        
        .product-image i {
            font-size: 3rem;
            color: #875A7B;
            opacity: 0.3;
        }
        
        .product-type-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 500;
            color: white;
        }
        
        .product-type-badge.product {
            background: #2196f3;
        }
        
        .product-type-badge.service {
            background: #4caf50;
        }
        
        .product-type-badge.consumable {
            background: #ff9800;
        }
        
        .product-info {
            padding: 20px;
        }
        
        .product-name {
            font-weight: 600;
            font-size: 1.1rem;
            margin-bottom: 8px;
            color: #2c3e50;
            line-height: 1.3;
        }
        
        .product-code {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 10px;
        }
        
        .product-price {
            font-size: 1.3rem;
            font-weight: bold;
            color: #28a745;
            margin-bottom: 15px;
        }
        
        .product-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .product-detail {
            font-size: 0.85rem;
            color: #6c757d;
        }
        
        .product-detail strong {
            color: #495057;
        }
        
        .product-actions {
            display: flex;
            gap: 8px;
            justify-content: space-between;
            align-items: center;
        }
        
        .product-status {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .stock-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
        }
        
        .stock-indicator.in-stock {
            background: #28a745;
        }
        
        .stock-indicator.low-stock {
            background: #ffc107;
        }
        
        .stock-indicator.out-of-stock {
            background: #dc3545;
        }
        
        .stats-row {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .stat-item {
            text-align: center;
            padding: 15px;
            border-radius: 8px;
            background: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .stat-item:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }
        
        .stat-number {
            font-size: 1.8rem;
            font-weight: bold;
            color: #714B67;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
        }
        
        .category-filter {
            background: white;
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .category-item {
            display: flex;
            align-items: center;
            padding: 10px 15px;
            border-radius: 8px;
            margin-bottom: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .category-item:hover {
            background: #f8f9fa;
        }
        
        .category-item.active {
            background: #e3f2fd;
            color: #1976d2;
        }
        
        .category-icon {
            width: 35px;
            height: 35px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 12px;
            color: white;
            font-size: 0.9rem;
        }
        
        .category-info {
            flex: 1;
        }
        
        .category-name {
            font-weight: 500;
            margin-bottom: 2px;
        }
        
        .category-count {
            font-size: 0.8rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <?php include '../includes/navbar.php'; ?>
    
    <div class="o_main_content">
        <div class="o_content">
            <!-- الشريط الجانبي -->
            <?php include '../includes/sidebar.php'; ?>
            
            <!-- المحتوى الرئيسي -->
            <div class="o_action_manager">
                <!-- شريط التحكم -->
                <div class="o_control_panel">
                    <div class="o_cp_left">
                        <!-- مسار التنقل -->
                        <div class="o_breadcrumb">
                            <a href="dashboard_odoo.php" class="o_breadcrumb_item">الرئيسية</a>
                            <span>/</span>
                            <span class="o_breadcrumb_item active">المنتجات</span>
                        </div>
                    </div>
                    <div class="o_cp_right">
                        <!-- أزرار الإجراءات -->
                        <div class="btn-group me-2">
                            <button class="btn btn-primary btn-sm" onclick="createProduct()">
                                <i class="fas fa-plus me-1"></i>إنشاء
                            </button>
                            <button class="btn btn-outline-primary btn-sm" onclick="importProducts()">
                                <i class="fas fa-upload me-1"></i>استيراد
                            </button>
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-outline-secondary btn-sm" onclick="refreshData()">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                            <button class="btn btn-outline-secondary btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="fas fa-cog"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="exportData('excel')">
                                    <i class="fas fa-file-excel me-2"></i>تصدير Excel
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="exportData('pdf')">
                                    <i class="fas fa-file-pdf me-2"></i>تصدير PDF
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" onclick="printData()">
                                    <i class="fas fa-print me-2"></i>طباعة
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- الإحصائيات السريعة -->
                <div class="stats-row mx-3">
                    <div class="row">
                        <div class="col-lg-2 col-md-4 col-6 mb-2">
                            <div class="stat-item" onclick="filterByType('all')">
                                <div class="stat-number"><?php echo $stats['total']; ?></div>
                                <div class="stat-label">إجمالي المنتجات</div>
                            </div>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6 mb-2">
                            <div class="stat-item" onclick="filterByType('product')">
                                <div class="stat-number"><?php echo $stats['products']; ?></div>
                                <div class="stat-label">منتجات مخزنية</div>
                            </div>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6 mb-2">
                            <div class="stat-item" onclick="filterByType('service')">
                                <div class="stat-number"><?php echo $stats['services']; ?></div>
                                <div class="stat-label">خدمات</div>
                            </div>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6 mb-2">
                            <div class="stat-item" onclick="filterByType('consumable')">
                                <div class="stat-number"><?php echo $stats['consumables']; ?></div>
                                <div class="stat-label">مستهلكات</div>
                            </div>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6 mb-2">
                            <div class="stat-item" onclick="filterByActive('active')">
                                <div class="stat-number"><?php echo $stats['active']; ?></div>
                                <div class="stat-label">نشطة</div>
                            </div>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6 mb-2">
                            <div class="stat-item" onclick="filterByActive('inactive')">
                                <div class="stat-number"><?php echo $stats['total'] - $stats['active']; ?></div>
                                <div class="stat-label">غير نشطة</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- شريط البحث والفلترة -->
                <div class="o_searchview">
                    <div class="d-flex align-items-center gap-3">
                        <!-- البحث -->
                        <div class="flex-grow-1">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" class="o_searchview_input form-control"
                                       placeholder="البحث في المنتجات..."
                                       id="searchInput"
                                       value="<?php echo htmlspecialchars($search_term); ?>">
                            </div>
                        </div>

                        <!-- الفلاتر النشطة -->
                        <div class="d-flex align-items-center gap-2">
                            <?php if ($filter_type !== 'all'): ?>
                                <span class="o_facet">
                                    <i class="fas fa-times o_facet_remove" onclick="removeFilter('type')"></i>
                                    النوع: <?php echo $product_types[$filter_type]['name']; ?>
                                </span>
                            <?php endif; ?>

                            <?php if ($filter_active !== 'all'): ?>
                                <span class="o_facet">
                                    <i class="fas fa-times o_facet_remove" onclick="removeFilter('active')"></i>
                                    الحالة: <?php echo $filter_active === 'active' ? 'نشط' : 'غير نشط'; ?>
                                </span>
                            <?php endif; ?>
                        </div>

                        <!-- فلترة حسب النوع -->
                        <select class="form-select form-select-sm" style="width: auto;" onchange="filterByType(this.value)">
                            <option value="all">جميع الأنواع</option>
                            <?php foreach ($product_types as $type => $info): ?>
                                <option value="<?php echo $type; ?>" <?php echo $filter_type == $type ? 'selected' : ''; ?>>
                                    <?php echo $info['name']; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>

                        <!-- فلترة حسب الحالة -->
                        <select class="form-select form-select-sm" style="width: auto;" onchange="filterByActive(this.value)">
                            <option value="all">جميع الحالات</option>
                            <option value="active" <?php echo $filter_active == 'active' ? 'selected' : ''; ?>>نشط</option>
                            <option value="inactive" <?php echo $filter_active == 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                        </select>

                        <!-- أزرار العرض -->
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-secondary <?php echo $view_mode === 'list' ? 'active' : ''; ?>"
                                    onclick="changeView('list')" title="عرض قائمة">
                                <i class="fas fa-list"></i>
                            </button>
                            <button class="btn btn-outline-secondary <?php echo $view_mode === 'kanban' ? 'active' : ''; ?>"
                                    onclick="changeView('kanban')" title="عرض بطاقات">
                                <i class="fas fa-th-large"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- شريط الإجراءات المجمعة -->
                <div class="bulk-actions" id="bulkActions">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <span id="selectedCount">0</span> منتج محدد
                        </div>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-success" onclick="bulkAction('activate')">
                                <i class="fas fa-check me-1"></i>تفعيل المحدد
                            </button>
                            <button class="btn btn-warning" onclick="bulkAction('deactivate')">
                                <i class="fas fa-pause me-1"></i>إلغاء تفعيل المحدد
                            </button>
                            <button class="btn btn-danger" onclick="bulkAction('delete')">
                                <i class="fas fa-trash me-1"></i>حذف المحدد
                            </button>
                            <button class="btn btn-secondary" onclick="clearSelection()">
                                <i class="fas fa-times me-1"></i>إلغاء التحديد
                            </button>
                        </div>
                    </div>
                </div>

                <!-- عرض المنتجات -->
                <div class="row">
                    <!-- فلتر الفئات (جانبي) -->
                    <div class="col-lg-3 d-none d-lg-block">
                        <div class="category-filter">
                            <h6 class="mb-3">
                                <i class="fas fa-filter me-2"></i>فلترة حسب النوع
                            </h6>

                            <div class="category-item <?php echo $filter_type === 'all' ? 'active' : ''; ?>" onclick="filterByType('all')">
                                <div class="category-icon" style="background: #6c757d;">
                                    <i class="fas fa-th"></i>
                                </div>
                                <div class="category-info">
                                    <div class="category-name">جميع المنتجات</div>
                                    <div class="category-count"><?php echo $stats['total']; ?> منتج</div>
                                </div>
                            </div>

                            <?php foreach ($product_types as $type => $info): ?>
                                <div class="category-item <?php echo $filter_type === $type ? 'active' : ''; ?>" onclick="filterByType('<?php echo $type; ?>')">
                                    <div class="category-icon" style="background: <?php echo $info['color']; ?>;">
                                        <i class="<?php echo $info['icon']; ?>"></i>
                                    </div>
                                    <div class="category-info">
                                        <div class="category-name"><?php echo $info['name']; ?></div>
                                        <div class="category-count"><?php echo $stats[($type === 'product' ? 'products' : ($type === 'service' ? 'services' : 'consumables'))]; ?> منتج</div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <!-- قائمة المنتجات -->
                    <div class="col-lg-9">
                        <div class="flex-grow-1" style="overflow: auto;">
                            <?php if ($view_mode === 'kanban'): ?>
                                <!-- عرض البطاقات -->
                                <div class="o_kanban_view">
                                    <div class="row">
                                        <?php foreach ($products as $product): ?>
                                            <div class="col-md-6 col-xl-4 mb-3">
                                                <div class="product-card <?php echo !$product['active'] ? 'inactive' : ''; ?>" data-id="<?php echo $product['id']; ?>">
                                                    <div class="product-image">
                                                        <i class="<?php echo $product_types[$product['type']]['icon']; ?>"></i>
                                                        <div class="product-type-badge <?php echo $product['type']; ?>">
                                                            <?php echo $product_types[$product['type']]['name']; ?>
                                                        </div>
                                                    </div>

                                                    <div class="product-info">
                                                        <div class="product-name"><?php echo $product['name']; ?></div>

                                                        <?php if (!empty($product['default_code'])): ?>
                                                            <div class="product-code">
                                                                <i class="fas fa-barcode me-1"></i>
                                                                <?php echo $product['default_code']; ?>
                                                            </div>
                                                        <?php endif; ?>

                                                        <div class="product-price">
                                                            <?php echo number_format($product['list_price'], 2); ?> ر.س
                                                        </div>

                                                        <div class="product-details">
                                                            <div class="product-detail">
                                                                <strong>التكلفة:</strong><br>
                                                                <?php echo number_format($product['standard_price'], 2); ?> ر.س
                                                            </div>
                                                            <div class="product-detail">
                                                                <strong>المخزون:</strong><br>
                                                                <?php echo isset($product['qty_available']) ? $product['qty_available'] : '0'; ?> وحدة
                                                            </div>
                                                        </div>

                                                        <div class="product-actions">
                                                            <div class="product-status">
                                                                <span class="stock-indicator <?php echo (isset($product['qty_available']) && $product['qty_available'] > 10) ? 'in-stock' : ((isset($product['qty_available']) && $product['qty_available'] > 0) ? 'low-stock' : 'out-of-stock'); ?>"></span>
                                                                <span class="o_status_badge <?php echo $product['active'] ? 'posted' : 'cancelled'; ?>">
                                                                    <?php echo $product['active'] ? 'نشط' : 'غير نشط'; ?>
                                                                </span>
                                                            </div>

                                                            <div class="btn-group btn-group-sm">
                                                                <button class="btn btn-outline-primary btn-sm" onclick="viewProduct(<?php echo $product['id']; ?>)" title="عرض">
                                                                    <i class="fas fa-eye"></i>
                                                                </button>
                                                                <button class="btn btn-outline-warning btn-sm" onclick="editProduct(<?php echo $product['id']; ?>)" title="تعديل">
                                                                    <i class="fas fa-edit"></i>
                                                                </button>
                                                                <button class="btn btn-outline-secondary btn-sm dropdown-toggle" data-bs-toggle="dropdown" title="المزيد">
                                                                    <i class="fas fa-ellipsis-v"></i>
                                                                </button>
                                                                <ul class="dropdown-menu">
                                                                    <li><a class="dropdown-item" href="#" onclick="duplicateProduct(<?php echo $product['id']; ?>)">
                                                                        <i class="fas fa-copy me-2"></i>نسخ
                                                                    </a></li>
                                                                    <li><a class="dropdown-item" href="#" onclick="toggleActive(<?php echo $product['id']; ?>)">
                                                                        <i class="fas fa-<?php echo $product['active'] ? 'pause' : 'play'; ?> me-2"></i>
                                                                        <?php echo $product['active'] ? 'إلغاء تفعيل' : 'تفعيل'; ?>
                                                                    </a></li>
                                                                    <li><hr class="dropdown-divider"></li>
                                                                    <li><a class="dropdown-item text-danger" href="#" onclick="deleteProduct(<?php echo $product['id']; ?>)">
                                                                        <i class="fas fa-trash me-2"></i>حذف
                                                                    </a></li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php else: ?>
                                <!-- عرض الجدول -->
                                <div class="o_list_view">
                                    <table class="o_list_table">
                                        <thead>
                                            <tr>
                                                <th class="o_list_record_selector">
                                                    <input type="checkbox" id="selectAll">
                                                </th>
                                                <th class="o_column_sortable" data-sort="name">
                                                    اسم المنتج
                                                </th>
                                                <th class="o_column_sortable" data-sort="default_code">
                                                    الرمز
                                                </th>
                                                <th class="o_column_sortable" data-sort="type">
                                                    النوع
                                                </th>
                                                <th class="o_column_sortable o_list_monetary" data-sort="list_price">
                                                    سعر البيع
                                                </th>
                                                <th class="o_column_sortable o_list_monetary" data-sort="standard_price">
                                                    التكلفة
                                                </th>
                                                <th class="o_column_sortable" data-sort="active">
                                                    الحالة
                                                </th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($products as $product): ?>
                                                <tr class="o_data_row <?php echo !$product['active'] ? 'o_row_readonly' : ''; ?>" data-id="<?php echo $product['id']; ?>">
                                                    <td class="o_list_record_selector">
                                                        <input type="checkbox" class="record-checkbox" value="<?php echo $product['id']; ?>">
                                                    </td>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div class="me-2">
                                                                <i class="<?php echo $product_types[$product['type']]['icon']; ?>" style="color: <?php echo $product_types[$product['type']]['color']; ?>"></i>
                                                            </div>
                                                            <div>
                                                                <strong><?php echo $product['name']; ?></strong>
                                                                <?php if (!empty($product['default_code'])): ?>
                                                                    <br><small class="text-muted">[<?php echo $product['default_code']; ?>]</small>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td><?php echo !empty($product['default_code']) ? $product['default_code'] : '-'; ?></td>
                                                    <td>
                                                        <span class="badge" style="background: <?php echo $product_types[$product['type']]['color']; ?>; color: white;">
                                                            <?php echo $product_types[$product['type']]['name']; ?>
                                                        </span>
                                                    </td>
                                                    <td class="o_list_monetary">
                                                        <?php echo number_format($product['list_price'], 2); ?> ر.س
                                                    </td>
                                                    <td class="o_list_monetary">
                                                        <?php echo number_format($product['standard_price'], 2); ?> ر.س
                                                    </td>
                                                    <td>
                                                        <span class="o_status_badge <?php echo $product['active'] ? 'posted' : 'cancelled'; ?>">
                                                            <?php echo $product['active'] ? 'نشط' : 'غير نشط'; ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            <button class="btn btn-outline-primary btn-sm" onclick="viewProduct(<?php echo $product['id']; ?>)" title="عرض">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            <button class="btn btn-outline-warning btn-sm" onclick="editProduct(<?php echo $product['id']; ?>)" title="تعديل">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            <button class="btn btn-outline-<?php echo $product['active'] ? 'secondary' : 'success'; ?> btn-sm"
                                                                    onclick="toggleActive(<?php echo $product['id']; ?>)"
                                                                    title="<?php echo $product['active'] ? 'إلغاء تفعيل' : 'تفعيل'; ?>">
                                                                <i class="fas fa-<?php echo $product['active'] ? 'pause' : 'play'; ?>"></i>
                                                            </button>
                                                            <button class="btn btn-outline-secondary btn-sm dropdown-toggle" data-bs-toggle="dropdown" title="المزيد">
                                                                <i class="fas fa-ellipsis-v"></i>
                                                            </button>
                                                            <ul class="dropdown-menu">
                                                                <li><a class="dropdown-item" href="#" onclick="duplicateProduct(<?php echo $product['id']; ?>)">
                                                                    <i class="fas fa-copy me-2"></i>نسخ
                                                                </a></li>
                                                                <li><a class="dropdown-item" href="#" onclick="updateStock(<?php echo $product['id']; ?>)">
                                                                    <i class="fas fa-warehouse me-2"></i>تحديث المخزون
                                                                </a></li>
                                                                <li><hr class="dropdown-divider"></li>
                                                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteProduct(<?php echo $product['id']; ?>)">
                                                                    <i class="fas fa-trash me-2"></i>حذف
                                                                </a></li>
                                                            </ul>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تراكب التحميل -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <div class="mt-2">جاري المعالجة...</div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // متغيرات عامة
        let selectedRecords = new Set();

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializeEventListeners();
            updateSelectedCount();
            animateCards();
        });

        // تهيئة معالجات الأحداث
        function initializeEventListeners() {
            // معالج تحديد الكل
            const selectAllCheckbox = document.getElementById('selectAll');
            if (selectAllCheckbox) {
                selectAllCheckbox.addEventListener('change', function() {
                    const checkboxes = document.querySelectorAll('.record-checkbox');
                    checkboxes.forEach(checkbox => {
                        checkbox.checked = this.checked;
                        toggleRowSelection(checkbox, false);
                    });
                    updateSelectedCount();
                });
            }

            // معالج تحديد الصفوف الفردية
            document.querySelectorAll('.record-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    toggleRowSelection(this);
                    updateSelectAllState();
                    updateSelectedCount();
                });
            });

            // معالج البحث
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                let searchTimeout;
                searchInput.addEventListener('input', function() {
                    clearTimeout(searchTimeout);
                    searchTimeout = setTimeout(() => {
                        performSearch(this.value);
                    }, 300);
                });
            }
        }

        // تحديد/إلغاء تحديد صف
        function toggleRowSelection(checkbox, updateSelectAll = true) {
            const row = checkbox.closest('tr') || checkbox.closest('.product-card');
            const recordId = checkbox.value;

            if (checkbox.checked) {
                row.classList.add('o_selected_row');
                selectedRecords.add(recordId);
            } else {
                row.classList.remove('o_selected_row');
                selectedRecords.delete(recordId);
            }

            if (updateSelectAll) {
                updateSelectAllState();
            }
        }

        // تحديث حالة "تحديد الكل"
        function updateSelectAllState() {
            const allCheckboxes = document.querySelectorAll('.record-checkbox');
            const checkedCheckboxes = document.querySelectorAll('.record-checkbox:checked');
            const selectAllCheckbox = document.getElementById('selectAll');

            if (!selectAllCheckbox) return;

            if (checkedCheckboxes.length === allCheckboxes.length && allCheckboxes.length > 0) {
                selectAllCheckbox.checked = true;
                selectAllCheckbox.indeterminate = false;
            } else if (checkedCheckboxes.length > 0) {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = true;
            } else {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = false;
            }
        }

        // تحديث عدد المحدد
        function updateSelectedCount() {
            const count = selectedRecords.size;
            const selectedCountElement = document.getElementById('selectedCount');
            const bulkActionsElement = document.getElementById('bulkActions');

            if (selectedCountElement) {
                selectedCountElement.textContent = count;
            }

            if (bulkActionsElement) {
                if (count > 0) {
                    bulkActionsElement.classList.add('show');
                } else {
                    bulkActionsElement.classList.remove('show');
                }
            }
        }

        // إلغاء التحديد
        function clearSelection() {
            selectedRecords.clear();
            document.querySelectorAll('.record-checkbox').forEach(checkbox => {
                checkbox.checked = false;
                toggleRowSelection(checkbox, false);
            });
            updateSelectAllState();
            updateSelectedCount();
        }

        // وظائف الإجراءات
        function createProduct() {
            window.location.href = 'create_product.php';
        }

        function viewProduct(productId) {
            window.open('view_product.php?id=' + productId, '_blank', 'width=900,height=700');
        }

        function editProduct(productId) {
            window.location.href = 'edit_product.php?id=' + productId;
        }

        function toggleActive(productId) {
            if (confirm('هل أنت متأكد من تغيير حالة هذا المنتج؟')) {
                showLoading();

                fetch(window.location.href, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=toggle_active&product_id=' + productId
                })
                .then(response => response.json())
                .then(data => {
                    hideLoading();
                    if (data.success) {
                        showMessage(data.message, 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showMessage(data.message || 'حدث خطأ أثناء التحديث', 'danger');
                    }
                })
                .catch(error => {
                    hideLoading();
                    showMessage('حدث خطأ في الاتصال', 'danger');
                });
            }
        }

        function duplicateProduct(productId) {
            window.location.href = 'create_product.php?duplicate=' + productId;
        }

        function updateStock(productId) {
            showMessage('ميزة تحديث المخزون قيد التطوير', 'info');
        }

        function deleteProduct(productId) {
            if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
                showLoading();

                fetch(window.location.href, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=delete_product&product_id=' + productId
                })
                .then(response => response.json())
                .then(data => {
                    hideLoading();
                    if (data.success) {
                        showMessage(data.message, 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showMessage(data.message || 'حدث خطأ أثناء الحذف', 'danger');
                    }
                })
                .catch(error => {
                    hideLoading();
                    showMessage('حدث خطأ في الاتصال', 'danger');
                });
            }
        }

        // الإجراءات المجمعة
        function bulkAction(action) {
            if (selectedRecords.size === 0) {
                showMessage('يرجى تحديد منتج واحد على الأقل', 'warning');
                return;
            }

            const actionText = action === 'activate' ? 'تفعيل' :
                              action === 'deactivate' ? 'إلغاء تفعيل' : 'حذف';
            if (confirm(`هل أنت متأكد من ${actionText} ${selectedRecords.size} منتج؟`)) {
                showLoading();

                const formData = new FormData();
                formData.append('action', 'bulk_action');
                formData.append('bulk_action', action);
                selectedRecords.forEach(id => {
                    formData.append('selected_ids[]', id);
                });

                fetch(window.location.href, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    hideLoading();
                    if (data.success) {
                        showMessage(data.message, 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showMessage(data.message || 'حدث خطأ أثناء العملية', 'danger');
                    }
                })
                .catch(error => {
                    hideLoading();
                    showMessage('حدث خطأ في الاتصال', 'danger');
                });
            }
        }

        // وظائف الفلترة والبحث
        function filterByType(type) {
            const url = new URL(window.location);
            url.searchParams.set('type', type);
            window.location.href = url.toString();
        }

        function filterByActive(active) {
            const url = new URL(window.location);
            url.searchParams.set('active', active);
            window.location.href = url.toString();
        }

        function changeView(viewMode) {
            const url = new URL(window.location);
            url.searchParams.set('view', viewMode);
            window.location.href = url.toString();
        }

        function performSearch(searchTerm) {
            const url = new URL(window.location);
            if (searchTerm.trim()) {
                url.searchParams.set('search', searchTerm);
            } else {
                url.searchParams.delete('search');
            }
            window.location.href = url.toString();
        }

        function removeFilter(filterType) {
            const url = new URL(window.location);
            url.searchParams.delete(filterType);
            window.location.href = url.toString();
        }

        function refreshData() {
            location.reload();
        }

        function importProducts() {
            showMessage('ميزة الاستيراد قيد التطوير', 'info');
        }

        function exportData(format) {
            showMessage(`جاري تصدير البيانات بصيغة ${format}...`, 'info');
        }

        function printData() {
            window.print();
        }

        // وظائف المساعدة
        function showLoading() {
            const overlay = document.getElementById('loadingOverlay');
            if (overlay) {
                overlay.style.display = 'flex';
            }
        }

        function hideLoading() {
            const overlay = document.getElementById('loadingOverlay');
            if (overlay) {
                overlay.style.display = 'none';
            }
        }

        function showMessage(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';

            const iconClass = type === 'success' ? 'check-circle' :
                             type === 'danger' ? 'exclamation-triangle' :
                             type === 'warning' ? 'exclamation-triangle' : 'info-circle';

            alertDiv.innerHTML = `
                <i class="fas fa-${iconClass} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(alertDiv);

            setTimeout(() => {
                alertDiv.remove();
            }, 4000);
        }

        // تأثيرات بصرية
        function animateCards() {
            // تأثير hover للبطاقات
            document.querySelectorAll('.product-card').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // تأثير النقر على الصفوف
            document.querySelectorAll('.o_data_row').forEach(row => {
                row.addEventListener('click', function(e) {
                    if (!e.target.closest('input') && !e.target.closest('button') && !e.target.closest('.dropdown')) {
                        const checkbox = this.querySelector('.record-checkbox');
                        if (checkbox) {
                            checkbox.checked = !checkbox.checked;
                            checkbox.dispatchEvent(new Event('change'));
                        }
                    }
                });
            });

            // تأثير النقر على الإحصائيات
            document.querySelectorAll('.stat-item').forEach(item => {
                item.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });

            // تأثير النقر على فلاتر الفئات
            document.querySelectorAll('.category-item').forEach(item => {
                item.addEventListener('click', function() {
                    this.style.transform = 'scale(0.98)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });
        }
    </script>
</body>
</html>
