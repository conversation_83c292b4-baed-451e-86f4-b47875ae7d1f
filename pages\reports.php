<?php
session_start();
require_once '../config/database_simple.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

// بيانات تجريبية للتقارير
$sales_data = array(
    array('month' => 'يناير', 'sales' => 125000, 'invoices' => 45),
    array('month' => 'فبراير', 'sales' => 138000, 'invoices' => 52),
    array('month' => 'مارس', 'sales' => 142000, 'invoices' => 48),
    array('month' => 'أبريل', 'sales' => 156000, 'invoices' => 58),
    array('month' => 'مايو', 'sales' => 168000, 'invoices' => 62),
    array('month' => 'يونيو', 'sales' => 175000, 'invoices' => 65)
);

$top_customers = array(
    array('name' => 'شركة الأمل للتجارة', 'total' => 85000, 'invoices' => 12),
    array('name' => 'مؤسسة النور للمقاولات', 'total' => 72000, 'invoices' => 8),
    array('name' => 'شركة المستقبل للتكنولوجيا', 'total' => 65000, 'invoices' => 15),
    array('name' => 'مجموعة الخليج التجارية', 'total' => 58000, 'invoices' => 10),
    array('name' => 'شركة الرياض للاستثمار', 'total' => 45000, 'invoices' => 7)
);

$product_sales = array(
    array('name' => 'منتج أ', 'quantity' => 150, 'revenue' => 45000),
    array('name' => 'منتج ب', 'quantity' => 120, 'revenue' => 38000),
    array('name' => 'منتج ج', 'quantity' => 95, 'revenue' => 32000),
    array('name' => 'منتج د', 'quantity' => 80, 'revenue' => 28000),
    array('name' => 'منتج هـ', 'quantity' => 65, 'revenue' => 22000)
);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير والإحصائيات - نظام ERP المحاسبي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
        }
        
        .report-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }
        
        .report-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .report-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px 15px 0 0;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            padding: 20px;
        }
        
        .stat-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .stat-label {
            font-size: 1rem;
            opacity: 0.9;
        }
        
        .report-filters {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .table-modern {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .table-modern thead {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .progress-modern {
            height: 8px;
            border-radius: 10px;
            background-color: #e9ecef;
        }
        
        .progress-bar-modern {
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="../demo.php">
                <i class="fas fa-chart-line me-2"></i>
                نظام ERP المحاسبي
            </a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="../demo.php">لوحة التحكم</a>
                <a class="nav-link" href="companies.php">الشركات</a>
                <a class="nav-link" href="customers.php">العملاء</a>
                <a class="nav-link" href="invoices.php">الفواتير</a>
                <a class="nav-link active" href="reports.php">التقارير</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- رأس الصفحة -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2><i class="fas fa-chart-bar me-2"></i>التقارير والإحصائيات</h2>
                <p class="text-muted">تحليل الأداء والإحصائيات المالية</p>
            </div>
            <div>
                <button class="btn btn-success me-2" onclick="exportReport()">
                    <i class="fas fa-download me-2"></i>
                    تصدير PDF
                </button>
                <button class="btn btn-primary" onclick="scheduleReport()">
                    <i class="fas fa-clock me-2"></i>
                    جدولة التقرير
                </button>
            </div>
        </div>

        <!-- فلاتر التقارير -->
        <div class="report-filters">
            <h5 class="mb-3"><i class="fas fa-filter me-2"></i>فلاتر التقرير</h5>
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">نوع التقرير</label>
                    <select class="form-select" id="reportType">
                        <option value="sales">تقرير المبيعات</option>
                        <option value="customers">تقرير العملاء</option>
                        <option value="products">تقرير المنتجات</option>
                        <option value="financial">التقرير المالي</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="dateFrom" value="2024-01-01">
                </div>
                <div class="col-md-2">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="dateTo" value="2024-06-30">
                </div>
                <div class="col-md-2">
                    <label class="form-label">الشركة</label>
                    <select class="form-select" id="companyFilter">
                        <option value="">جميع الشركات</option>
                        <option value="1">الشركة الرئيسية</option>
                        <option value="2">شركة التجارة المتقدمة</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">الفرع</label>
                    <select class="form-select" id="branchFilter">
                        <option value="">جميع الفروع</option>
                        <option value="1">الفرع الرئيسي</option>
                        <option value="2">فرع الرياض</option>
                        <option value="3">فرع جدة</option>
                    </select>
                </div>
                <div class="col-md-1">
                    <label class="form-label">&nbsp;</label>
                    <button class="btn btn-primary w-100" onclick="generateReport()">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- الإحصائيات السريعة -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stat-box">
                    <div class="stat-number"><?php echo formatCurrency(array_sum(array_column($sales_data, 'sales'))); ?></div>
                    <div class="stat-label">إجمالي المبيعات</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-box" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
                    <div class="stat-number"><?php echo array_sum(array_column($sales_data, 'invoices')); ?></div>
                    <div class="stat-label">عدد الفواتير</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-box" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                    <div class="stat-number"><?php echo count($top_customers); ?></div>
                    <div class="stat-label">العملاء النشطين</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-box" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                    <div class="stat-number"><?php echo round(array_sum(array_column($sales_data, 'sales')) / count($sales_data)); ?></div>
                    <div class="stat-label">متوسط المبيعات الشهرية</div>
                </div>
            </div>
        </div>

        <!-- الرسوم البيانية -->
        <div class="row">
            <!-- رسم بياني للمبيعات -->
            <div class="col-lg-8">
                <div class="report-card">
                    <div class="report-header">
                        <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>تطور المبيعات الشهرية</h5>
                    </div>
                    <div class="chart-container">
                        <canvas id="salesChart"></canvas>
                    </div>
                </div>
            </div>
            
            <!-- رسم دائري للعملاء -->
            <div class="col-lg-4">
                <div class="report-card">
                    <div class="report-header">
                        <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>توزيع العملاء</h5>
                    </div>
                    <div class="chart-container">
                        <canvas id="customersChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- جداول التقارير -->
        <div class="row mt-4">
            <!-- أفضل العملاء -->
            <div class="col-lg-6">
                <div class="table-modern">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th colspan="4" class="text-center py-3">
                                        <i class="fas fa-crown me-2"></i>أفضل العملاء
                                    </th>
                                </tr>
                                <tr>
                                    <th>العميل</th>
                                    <th>المبيعات</th>
                                    <th>الفواتير</th>
                                    <th>النسبة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php 
                                $total_sales = array_sum(array_column($top_customers, 'total'));
                                foreach ($top_customers as $customer): 
                                    $percentage = round(($customer['total'] / $total_sales) * 100);
                                ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 35px; height: 35px;">
                                                <i class="fas fa-user text-white"></i>
                                            </div>
                                            <div>
                                                <div class="fw-bold"><?php echo $customer['name']; ?></div>
                                                <small class="text-muted"><?php echo $customer['invoices']; ?> فاتورة</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="fw-bold"><?php echo formatCurrency($customer['total']); ?></td>
                                    <td>
                                        <span class="badge bg-info"><?php echo $customer['invoices']; ?></span>
                                    </td>
                                    <td>
                                        <div class="progress progress-modern">
                                            <div class="progress-bar progress-bar-modern bg-success" style="width: <?php echo $percentage; ?>%"></div>
                                        </div>
                                        <small><?php echo $percentage; ?>%</small>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- أفضل المنتجات -->
            <div class="col-lg-6">
                <div class="table-modern">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th colspan="4" class="text-center py-3">
                                        <i class="fas fa-box me-2"></i>أفضل المنتجات
                                    </th>
                                </tr>
                                <tr>
                                    <th>المنتج</th>
                                    <th>الكمية</th>
                                    <th>الإيرادات</th>
                                    <th>الأداء</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php 
                                $max_revenue = max(array_column($product_sales, 'revenue'));
                                foreach ($product_sales as $product): 
                                    $performance = round(($product['revenue'] / $max_revenue) * 100);
                                ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="bg-success rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 35px; height: 35px;">
                                                <i class="fas fa-box text-white"></i>
                                            </div>
                                            <div class="fw-bold"><?php echo $product['name']; ?></div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning"><?php echo $product['quantity']; ?></span>
                                    </td>
                                    <td class="fw-bold"><?php echo formatCurrency($product['revenue']); ?></td>
                                    <td>
                                        <div class="progress progress-modern">
                                            <div class="progress-bar progress-bar-modern bg-warning" style="width: <?php echo $performance; ?>%"></div>
                                        </div>
                                        <small><?php echo $performance; ?>%</small>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- أزرار التصدير -->
        <div class="row mt-4">
            <div class="col-12 text-center">
                <div class="btn-group" role="group">
                    <button class="btn btn-outline-success" onclick="exportExcel()">
                        <i class="fas fa-file-excel me-2"></i>
                        تصدير Excel
                    </button>
                    <button class="btn btn-outline-danger" onclick="exportPDF()">
                        <i class="fas fa-file-pdf me-2"></i>
                        تصدير PDF
                    </button>
                    <button class="btn btn-outline-primary" onclick="printReport()">
                        <i class="fas fa-print me-2"></i>
                        طباعة
                    </button>
                    <button class="btn btn-outline-info" onclick="emailReport()">
                        <i class="fas fa-envelope me-2"></i>
                        إرسال بالبريد
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // رسم بياني للمبيعات
        const salesCtx = document.getElementById('salesChart').getContext('2d');
        new Chart(salesCtx, {
            type: 'line',
            data: {
                labels: <?php echo json_encode(array_column($sales_data, 'month')); ?>,
                datasets: [{
                    label: 'المبيعات',
                    data: <?php echo json_encode(array_column($sales_data, 'sales')); ?>,
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value.toLocaleString() + ' ر.س';
                            }
                        }
                    }
                }
            }
        });

        // رسم دائري للعملاء
        const customersCtx = document.getElementById('customersChart').getContext('2d');
        new Chart(customersCtx, {
            type: 'doughnut',
            data: {
                labels: <?php echo json_encode(array_column($top_customers, 'name')); ?>,
                datasets: [{
                    data: <?php echo json_encode(array_column($top_customers, 'total')); ?>,
                    backgroundColor: [
                        '#667eea',
                        '#764ba2',
                        '#f093fb',
                        '#f5576c',
                        '#4facfe'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // دوال التصدير والطباعة
        function generateReport() {
            alert('تم إنشاء التقرير بنجاح');
        }

        function exportReport() {
            alert('تصدير التقرير كـ PDF');
        }

        function scheduleReport() {
            alert('جدولة التقرير');
        }

        function exportExcel() {
            alert('تصدير إلى Excel');
        }

        function exportPDF() {
            alert('تصدير إلى PDF');
        }

        function printReport() {
            window.print();
        }

        function emailReport() {
            alert('إرسال التقرير بالبريد الإلكتروني');
        }
    </script>
</body>
</html>
