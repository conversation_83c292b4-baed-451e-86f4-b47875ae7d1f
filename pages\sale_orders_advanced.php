<?php
/**
 * صفحة طلبات المبيعات المتقدمة - بأسلوب Odoo
 * Advanced Sale Orders Page - Odoo Style
 */

session_start();
require_once '../config/database.php';
require_once '../models/SaleOrder.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// إنشاء كائن طلبات المبيعات
$sale_order = new SaleOrder();

// معالجة الإجراءات
$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        switch ($action) {
            case 'confirm':
                $sale_order->confirm($_POST['order_id']);
                $message = 'تم تأكيد طلب المبيعات بنجاح';
                $message_type = 'success';
                break;
                
            case 'cancel':
                $sale_order->cancel($_POST['order_id']);
                $message = 'تم إلغاء طلب المبيعات';
                $message_type = 'warning';
                break;
                
            case 'create_invoice':
                $sale_order->createInvoice($_POST['order_id']);
                $message = 'تم إنشاء الفاتورة بنجاح';
                $message_type = 'success';
                break;
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $message_type = 'error';
    }
}

// فلاتر البحث
$filters = [
    'search' => $_GET['search'] ?? '',
    'state' => $_GET['state'] ?? '',
    'partner_id' => $_GET['partner_id'] ?? '',
    'date_from' => $_GET['date_from'] ?? '',
    'date_to' => $_GET['date_to'] ?? '',
    'limit' => 50
];

// جلب طلبات المبيعات
$orders = $sale_order->search($filters);

// جلب الإحصائيات
$statistics = $sale_order->getStatistics($filters);

// جلب الشركاء للفلتر
$partners_sql = "SELECT id, name FROM partners WHERE customer_rank > 0 ORDER BY name";
$partners = $db->query($partners_sql)->fetchAll();

// عرض الصفحة
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طلبات المبيعات - نظام ERP</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        .o_main_content {
            background: #f8f9fa;
            min-height: 100vh;
        }
        
        .o_control_panel {
            background: white;
            border-bottom: 1px solid #dee2e6;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .o_breadcrumb {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .o_breadcrumb_item {
            color: #6c757d;
            text-decoration: none;
        }
        
        .o_breadcrumb_item.active {
            color: #495057;
            font-weight: 500;
        }
        
        .btn-odoo {
            background: #714B67;
            border: none;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-odoo:hover {
            background: #875A7B;
            color: white;
            transform: translateY(-1px);
        }
        
        .btn-outline-odoo {
            border: 1px solid #714B67;
            color: #714B67;
            background: transparent;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-outline-odoo:hover {
            background: #714B67;
            color: white;
        }
        
        .stats-card {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
        }
        
        .stats-number {
            font-size: 2rem;
            font-weight: 700;
            color: #714B67;
        }
        
        .stats-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .state-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .state-draft {
            background: #e3f2fd;
            color: #1976d2;
        }
        
        .state-sent {
            background: #fff3e0;
            color: #f57c00;
        }
        
        .state-sale {
            background: #e8f5e8;
            color: #388e3c;
        }
        
        .state-done {
            background: #f3e5f5;
            color: #7b1fa2;
        }
        
        .state-cancel {
            background: #ffebee;
            color: #d32f2f;
        }
        
        .filters-panel {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .table-container {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .table th {
            background: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
            font-weight: 600;
            color: #495057;
        }
        
        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }
        
        .action-buttons .btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <?php include '../includes/navbar.php'; ?>
    
    <div class="o_main_content">
        <div class="container-fluid">
            <!-- الشريط الجانبي -->
            <?php include '../includes/sidebar.php'; ?>
            
            <!-- المحتوى الرئيسي -->
            <div class="main-content">
                <!-- شريط التحكم -->
                <div class="o_control_panel">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="o_breadcrumb">
                                <a href="../dashboard.php" class="o_breadcrumb_item">الرئيسية</a>
                                <span>/</span>
                                <a href="#" class="o_breadcrumb_item">المبيعات</a>
                                <span>/</span>
                                <span class="o_breadcrumb_item active">طلبات المبيعات</span>
                            </div>
                        </div>
                        <div class="col-md-6 text-end">
                            <a href="sale_order_form.php" class="btn-odoo">
                                <i class="fas fa-plus me-1"></i>طلب مبيعات جديد
                            </a>
                            <button class="btn-outline-odoo" onclick="exportData()">
                                <i class="fas fa-download me-1"></i>تصدير
                            </button>
                        </div>
                    </div>
                </div>

                <!-- رسائل النظام -->
                <?php if ($message): ?>
                <div class="alert alert-<?php echo $message_type === 'error' ? 'danger' : $message_type; ?> alert-dismissible fade show">
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <!-- الإحصائيات -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="stats-number"><?php echo number_format($statistics['total_orders']); ?></div>
                            <div class="stats-label">إجمالي الطلبات</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="stats-number"><?php echo number_format($statistics['confirmed_orders']); ?></div>
                            <div class="stats-label">طلبات مؤكدة</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="stats-number"><?php echo number_format($statistics['total_amount'], 2); ?></div>
                            <div class="stats-label">إجمالي المبلغ (ر.س)</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="stats-number"><?php echo number_format($statistics['average_amount'], 2); ?></div>
                            <div class="stats-label">متوسط قيمة الطلب (ر.س)</div>
                        </div>
                    </div>
                </div>

                <!-- فلاتر البحث -->
                <div class="filters-panel">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">البحث</label>
                            <input type="text" class="form-control" name="search" value="<?php echo htmlspecialchars($filters['search']); ?>" placeholder="رقم الطلب أو اسم العميل">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">الحالة</label>
                            <select class="form-select" name="state">
                                <option value="">جميع الحالات</option>
                                <option value="draft" <?php echo $filters['state'] === 'draft' ? 'selected' : ''; ?>>مسودة</option>
                                <option value="sent" <?php echo $filters['state'] === 'sent' ? 'selected' : ''; ?>>مرسل</option>
                                <option value="sale" <?php echo $filters['state'] === 'sale' ? 'selected' : ''; ?>>مؤكد</option>
                                <option value="done" <?php echo $filters['state'] === 'done' ? 'selected' : ''; ?>>مكتمل</option>
                                <option value="cancel" <?php echo $filters['state'] === 'cancel' ? 'selected' : ''; ?>>ملغي</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">العميل</label>
                            <select class="form-select" name="partner_id">
                                <option value="">جميع العملاء</option>
                                <?php foreach ($partners as $partner): ?>
                                <option value="<?php echo $partner['id']; ?>" <?php echo $filters['partner_id'] == $partner['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($partner['name']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" name="date_from" value="<?php echo $filters['date_from']; ?>">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" name="date_to" value="<?php echo $filters['date_to']; ?>">
                        </div>
                        <div class="col-12">
                            <button type="submit" class="btn-odoo">
                                <i class="fas fa-search me-1"></i>بحث
                            </button>
                            <a href="?" class="btn-outline-odoo">
                                <i class="fas fa-times me-1"></i>مسح الفلاتر
                            </a>
                        </div>
                    </form>
                </div>

                <!-- جدول طلبات المبيعات -->
                <div class="table-container">
                    <table class="table table-hover mb-0" id="ordersTable">
                        <thead>
                            <tr>
                                <th>رقم الطلب</th>
                                <th>العميل</th>
                                <th>تاريخ الطلب</th>
                                <th>الحالة</th>
                                <th>حالة الفوترة</th>
                                <th>المبلغ الإجمالي</th>
                                <th>المسؤول</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($orders as $order): ?>
                            <tr>
                                <td>
                                    <a href="sale_order_view.php?id=<?php echo $order['id']; ?>" class="text-decoration-none">
                                        <strong><?php echo htmlspecialchars($order['name']); ?></strong>
                                    </a>
                                </td>
                                <td><?php echo htmlspecialchars($order['partner_name']); ?></td>
                                <td><?php echo date('d/m/Y', strtotime($order['date_order'])); ?></td>
                                <td>
                                    <span class="state-badge state-<?php echo $order['state']; ?>">
                                        <?php
                                        $states = [
                                            'draft' => 'مسودة',
                                            'sent' => 'مرسل',
                                            'sale' => 'مؤكد',
                                            'done' => 'مكتمل',
                                            'cancel' => 'ملغي'
                                        ];
                                        echo $states[$order['state']] ?? $order['state'];
                                        ?>
                                    </span>
                                </td>
                                <td>
                                    <?php
                                    $invoice_statuses = [
                                        'no' => 'لا توجد فاتورة',
                                        'to_invoice' => 'للفوترة',
                                        'invoiced' => 'مفوتر'
                                    ];
                                    echo $invoice_statuses[$order['invoice_status']] ?? $order['invoice_status'];
                                    ?>
                                </td>
                                <td><?php echo number_format($order['amount_total'], 2); ?> ر.س</td>
                                <td><?php echo htmlspecialchars($order['user_name']); ?></td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="sale_order_view.php?id=<?php echo $order['id']; ?>" class="btn btn-sm btn-outline-primary" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <?php if ($order['state'] === 'draft' || $order['state'] === 'sent'): ?>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="confirm">
                                            <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">
                                            <button type="submit" class="btn btn-sm btn-success" title="تأكيد" onclick="return confirm('هل تريد تأكيد هذا الطلب؟')">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        </form>
                                        <?php endif; ?>
                                        <?php if ($order['state'] === 'sale' && $order['invoice_status'] === 'to_invoice'): ?>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="create_invoice">
                                            <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">
                                            <button type="submit" class="btn btn-sm btn-info" title="إنشاء فاتورة" onclick="return confirm('هل تريد إنشاء فاتورة لهذا الطلب؟')">
                                                <i class="fas fa-file-invoice"></i>
                                            </button>
                                        </form>
                                        <?php endif; ?>
                                        <?php if ($order['state'] !== 'done' && $order['state'] !== 'cancel'): ?>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="cancel">
                                            <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">
                                            <button type="submit" class="btn btn-sm btn-danger" title="إلغاء" onclick="return confirm('هل تريد إلغاء هذا الطلب؟')">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </form>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <script>
        $(document).ready(function() {
            $('#ordersTable').DataTable({
                language: {
                    url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
                },
                pageLength: 25,
                order: [[2, 'desc']],
                columnDefs: [
                    { orderable: false, targets: [7] }
                ]
            });
        });
        
        function exportData() {
            // تصدير البيانات
            window.location.href = 'export_sale_orders.php?' + new URLSearchParams(window.location.search);
        }
    </script>
</body>
</html>
