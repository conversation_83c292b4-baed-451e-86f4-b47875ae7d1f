<?php
session_start();
require_once '../config/database_simple.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

// بيانات تجريبية للإعدادات
$settings = array(
    'general' => array(
        'company_name' => 'الشركة التجريبية',
        'company_logo' => '',
        'currency' => 'ر.س',
        'tax_rate' => 15,
        'language' => 'ar',
        'timezone' => 'Asia/Riyadh',
        'date_format' => 'Y-m-d',
        'fiscal_year_start' => '01-01'
    ),
    'accounting' => array(
        'auto_journal_entries' => true,
        'invoice_prefix' => 'INV',
        'invoice_start_number' => 1,
        'payment_terms' => 30,
        'decimal_places' => 2
    ),
    'email' => array(
        'smtp_host' => 'smtp.gmail.com',
        'smtp_port' => 587,
        'smtp_username' => '',
        'smtp_password' => '',
        'from_email' => '<EMAIL>',
        'from_name' => 'نظام ERP المحاسبي'
    ),
    'backup' => array(
        'auto_backup' => true,
        'backup_frequency' => 'daily',
        'backup_time' => '02:00',
        'retention_days' => 30
    )
);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات النظام - نظام ERP المحاسبي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
        }

        .settings-nav {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            padding: 0;
            overflow: hidden;
        }

        .settings-nav .nav-link {
            border: none;
            border-radius: 0;
            padding: 20px 25px;
            color: #495057;
            font-weight: 500;
            transition: all 0.3s ease;
            border-bottom: 1px solid #e9ecef;
        }

        .settings-nav .nav-link:last-child {
            border-bottom: none;
        }

        .settings-nav .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .settings-nav .nav-link:hover:not(.active) {
            background: #f8f9fa;
            color: #667eea;
        }

        .settings-content {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            padding: 30px;
        }

        .setting-group {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .setting-group h6 {
            color: #495057;
            font-weight: 600;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }

        .form-control, .form-select {
            border-radius: 8px;
            border: 1px solid #ced4da;
            padding: 10px 15px;
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn-save {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            color: white;
        }

        .btn-save:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #667eea;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="../demo.php">
                <i class="fas fa-chart-line me-2"></i>
                نظام ERP المحاسبي
            </a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="../demo.php">لوحة التحكم</a>
                <a class="nav-link" href="companies.php">الشركات</a>
                <a class="nav-link" href="customers.php">العملاء</a>
                <a class="nav-link" href="invoices.php">الفواتير</a>
                <a class="nav-link" href="products.php">المنتجات</a>
                <a class="nav-link" href="reports.php">التقارير</a>
                <a class="nav-link active" href="settings.php">الإعدادات</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- رأس الصفحة -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2><i class="fas fa-cog me-2"></i>إعدادات النظام</h2>
                <p class="text-muted">إدارة إعدادات وتكوين النظام</p>
            </div>
            <div>
                <button class="btn btn-success me-2" onclick="exportSettings()">
                    <i class="fas fa-download me-2"></i>
                    تصدير الإعدادات
                </button>
                <button class="btn btn-info" onclick="importSettings()">
                    <i class="fas fa-upload me-2"></i>
                    استيراد الإعدادات
                </button>
            </div>
        </div>

        <div class="row">
            <!-- قائمة الإعدادات -->
            <div class="col-lg-3">
                <div class="settings-nav">
                    <div class="nav flex-column nav-pills">
                        <a class="nav-link active" data-bs-toggle="pill" href="#general">
                            <i class="fas fa-building me-2"></i>
                            الإعدادات العامة
                        </a>
                        <a class="nav-link" data-bs-toggle="pill" href="#accounting">
                            <i class="fas fa-calculator me-2"></i>
                            الإعدادات المحاسبية
                        </a>
                        <a class="nav-link" data-bs-toggle="pill" href="#email">
                            <i class="fas fa-envelope me-2"></i>
                            إعدادات البريد الإلكتروني
                        </a>
                        <a class="nav-link" data-bs-toggle="pill" href="#backup">
                            <i class="fas fa-database me-2"></i>
                            إعدادات النسخ الاحتياطي
                        </a>
                        <a class="nav-link" data-bs-toggle="pill" href="#users">
                            <i class="fas fa-users me-2"></i>
                            إدارة المستخدمين
                        </a>
                        <a class="nav-link" data-bs-toggle="pill" href="#security">
                            <i class="fas fa-shield-alt me-2"></i>
                            الأمان والخصوصية
                        </a>
                        <a class="nav-link" data-bs-toggle="pill" href="#system">
                            <i class="fas fa-server me-2"></i>
                            معلومات النظام
                        </a>
                    </div>
                </div>
            </div>

            <!-- محتوى الإعدادات -->
            <div class="col-lg-9">
                <div class="settings-content">
                    <div class="tab-content">
                        <!-- الإعدادات العامة -->
                        <div class="tab-pane fade show active" id="general">
                            <h4 class="mb-4">الإعدادات العامة</h4>

                            <form id="generalForm">
                                <div class="setting-group">
                                    <h6><i class="fas fa-info-circle me-2"></i>معلومات الشركة</h6>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">اسم الشركة</label>
                                                <input type="text" class="form-control" name="company_name"
                                                       value="<?php echo $settings['general']['company_name']; ?>">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">شعار الشركة</label>
                                                <input type="file" class="form-control" name="company_logo" accept="image/*">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="setting-group">
                                    <h6><i class="fas fa-globe me-2"></i>الإعدادات الإقليمية</h6>

                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label">العملة</label>
                                                <select class="form-select" name="currency">
                                                    <option value="ر.س" <?php echo $settings['general']['currency'] == 'ر.س' ? 'selected' : ''; ?>>ريال سعودي (ر.س)</option>
                                                    <option value="$">دولار أمريكي ($)</option>
                                                    <option value="€">يورو (€)</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label">معدل الضريبة (%)</label>
                                                <input type="number" class="form-control" name="tax_rate"
                                                       value="<?php echo $settings['general']['tax_rate']; ?>" min="0" max="100" step="0.01">
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label">المنطقة الزمنية</label>
                                                <select class="form-select" name="timezone">
                                                    <option value="Asia/Riyadh" <?php echo $settings['general']['timezone'] == 'Asia/Riyadh' ? 'selected' : ''; ?>>الرياض</option>
                                                    <option value="Asia/Dubai">دبي</option>
                                                    <option value="Asia/Kuwait">الكويت</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="setting-group">
                                    <h6><i class="fas fa-calendar me-2"></i>إعدادات التاريخ</h6>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">تنسيق التاريخ</label>
                                                <select class="form-select" name="date_format">
                                                    <option value="Y-m-d" <?php echo $settings['general']['date_format'] == 'Y-m-d' ? 'selected' : ''; ?>>2024-01-15</option>
                                                    <option value="d/m/Y">15/01/2024</option>
                                                    <option value="d-m-Y">15-01-2024</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">بداية السنة المالية</label>
                                                <input type="text" class="form-control" name="fiscal_year_start"
                                                       value="<?php echo $settings['general']['fiscal_year_start']; ?>" placeholder="MM-DD">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <button type="button" class="btn btn-save" onclick="saveSettings('general')">
                                    <i class="fas fa-save me-2"></i>
                                    حفظ الإعدادات العامة
                                </button>
                            </form>
                        </div>

                        <!-- الإعدادات المحاسبية -->
                        <div class="tab-pane fade" id="accounting">
                            <h4 class="mb-4">الإعدادات المحاسبية</h4>

                            <form id="accountingForm">
                                <div class="setting-group">
                                    <h6><i class="fas fa-file-invoice me-2"></i>إعدادات الفواتير</h6>

                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label">بادئة رقم الفاتورة</label>
                                                <input type="text" class="form-control" name="invoice_prefix"
                                                       value="<?php echo $settings['accounting']['invoice_prefix']; ?>">
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label">رقم البداية</label>
                                                <input type="number" class="form-control" name="invoice_start_number"
                                                       value="<?php echo $settings['accounting']['invoice_start_number']; ?>" min="1">
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label">مدة السداد (أيام)</label>
                                                <input type="number" class="form-control" name="payment_terms"
                                                       value="<?php echo $settings['accounting']['payment_terms']; ?>" min="0">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="setting-group">
                                    <h6><i class="fas fa-cogs me-2"></i>إعدادات متقدمة</h6>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">عدد الخانات العشرية</label>
                                                <select class="form-select" name="decimal_places">
                                                    <option value="0" <?php echo $settings['accounting']['decimal_places'] == 0 ? 'selected' : ''; ?>>0</option>
                                                    <option value="2" <?php echo $settings['accounting']['decimal_places'] == 2 ? 'selected' : ''; ?>>2</option>
                                                    <option value="3">3</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">القيود التلقائية</label>
                                                <div class="mt-2">
                                                    <label class="switch">
                                                        <input type="checkbox" name="auto_journal_entries"
                                                               <?php echo $settings['accounting']['auto_journal_entries'] ? 'checked' : ''; ?>>
                                                        <span class="slider"></span>
                                                    </label>
                                                    <span class="ms-3">إنشاء قيود محاسبية تلقائياً</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <button type="button" class="btn btn-save" onclick="saveSettings('accounting')">
                                    <i class="fas fa-save me-2"></i>
                                    حفظ الإعدادات المحاسبية
                                </button>
                            </form>
                        </div>

                        <!-- إعدادات البريد الإلكتروني -->
                        <div class="tab-pane fade" id="email">
                            <h4 class="mb-4">إعدادات البريد الإلكتروني</h4>

                            <form id="emailForm">
                                <div class="setting-group">
                                    <h6><i class="fas fa-server me-2"></i>إعدادات SMTP</h6>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">خادم SMTP</label>
                                                <input type="text" class="form-control" name="smtp_host"
                                                       value="<?php echo $settings['email']['smtp_host']; ?>">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">منفذ SMTP</label>
                                                <input type="number" class="form-control" name="smtp_port"
                                                       value="<?php echo $settings['email']['smtp_port']; ?>">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">اسم المستخدم</label>
                                                <input type="email" class="form-control" name="smtp_username"
                                                       value="<?php echo $settings['email']['smtp_username']; ?>">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">كلمة المرور</label>
                                                <input type="password" class="form-control" name="smtp_password"
                                                       placeholder="••••••••">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="setting-group">
                                    <h6><i class="fas fa-envelope me-2"></i>إعدادات المرسل</h6>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">البريد الإلكتروني للمرسل</label>
                                                <input type="email" class="form-control" name="from_email"
                                                       value="<?php echo $settings['email']['from_email']; ?>">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">اسم المرسل</label>
                                                <input type="text" class="form-control" name="from_name"
                                                       value="<?php echo $settings['email']['from_name']; ?>">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-flex gap-2">
                                    <button type="button" class="btn btn-save" onclick="saveSettings('email')">
                                        <i class="fas fa-save me-2"></i>
                                        حفظ إعدادات البريد
                                    </button>
                                    <button type="button" class="btn btn-outline-primary" onclick="testEmail()">
                                        <i class="fas fa-paper-plane me-2"></i>
                                        اختبار الإعدادات
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- إعدادات النسخ الاحتياطي -->
                        <div class="tab-pane fade" id="backup">
                            <h4 class="mb-4">إعدادات النسخ الاحتياطي</h4>

                            <form id="backupForm">
                                <div class="setting-group">
                                    <h6><i class="fas fa-clock me-2"></i>جدولة النسخ الاحتياطي</h6>

                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label">النسخ التلقائي</label>
                                                <div class="mt-2">
                                                    <label class="switch">
                                                        <input type="checkbox" name="auto_backup"
                                                               <?php echo $settings['backup']['auto_backup'] ? 'checked' : ''; ?>>
                                                        <span class="slider"></span>
                                                    </label>
                                                    <span class="ms-3">تفعيل النسخ التلقائي</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label">تكرار النسخ</label>
                                                <select class="form-select" name="backup_frequency">
                                                    <option value="daily" <?php echo $settings['backup']['backup_frequency'] == 'daily' ? 'selected' : ''; ?>>يومي</option>
                                                    <option value="weekly">أسبوعي</option>
                                                    <option value="monthly">شهري</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label">وقت النسخ</label>
                                                <input type="time" class="form-control" name="backup_time"
                                                       value="<?php echo $settings['backup']['backup_time']; ?>">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">مدة الاحتفاظ (أيام)</label>
                                                <input type="number" class="form-control" name="retention_days"
                                                       value="<?php echo $settings['backup']['retention_days']; ?>" min="1">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-flex gap-2">
                                    <button type="button" class="btn btn-save" onclick="saveSettings('backup')">
                                        <i class="fas fa-save me-2"></i>
                                        حفظ إعدادات النسخ
                                    </button>
                                    <button type="button" class="btn btn-outline-success" onclick="createBackup()">
                                        <i class="fas fa-database me-2"></i>
                                        إنشاء نسخة احتياطية الآن
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- معلومات النظام -->
                        <div class="tab-pane fade" id="system">
                            <h4 class="mb-4">معلومات النظام</h4>

                            <div class="setting-group">
                                <h6><i class="fas fa-info-circle me-2"></i>معلومات الخادم</h6>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">إصدار PHP</label>
                                            <input type="text" class="form-control" value="<?php echo PHP_VERSION; ?>" readonly>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">نظام التشغيل</label>
                                            <input type="text" class="form-control" value="<?php echo PHP_OS; ?>" readonly>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">إصدار النظام</label>
                                            <input type="text" class="form-control" value="1.0.0" readonly>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">آخر تحديث</label>
                                            <input type="text" class="form-control" value="2024-01-15" readonly>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="setting-group">
                                <h6><i class="fas fa-chart-pie me-2"></i>استخدام الموارد</h6>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="text-center">
                                            <div class="h4 text-primary">45%</div>
                                            <div class="text-muted">استخدام القرص</div>
                                            <div class="progress mt-2">
                                                <div class="progress-bar bg-primary" style="width: 45%"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="text-center">
                                            <div class="h4 text-success">62%</div>
                                            <div class="text-muted">استخدام الذاكرة</div>
                                            <div class="progress mt-2">
                                                <div class="progress-bar bg-success" style="width: 62%"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="text-center">
                                            <div class="h4 text-warning">28%</div>
                                            <div class="text-muted">استخدام المعالج</div>
                                            <div class="progress mt-2">
                                                <div class="progress-bar bg-warning" style="width: 28%"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function saveSettings(section) {
            alert('تم حفظ إعدادات ' + section + ' بنجاح!');
        }

        function exportSettings() {
            alert('تصدير جميع الإعدادات');
        }

        function importSettings() {
            alert('استيراد الإعدادات من ملف');
        }

        function testEmail() {
            alert('اختبار إعدادات البريد الإلكتروني...');
        }

        function createBackup() {
            if (confirm('هل تريد إنشاء نسخة احتياطية الآن؟')) {
                alert('جاري إنشاء النسخة الاحتياطية...');
            }
        }
    </script>
</body>
</html>