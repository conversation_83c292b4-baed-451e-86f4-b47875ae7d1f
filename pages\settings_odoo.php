<?php
/**
 * إعدادات النظام بأسلوب Odoo الكامل
 * System Settings - Complete Odoo Style
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// التحقق من صلاحيات الإدارة
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: dashboard_odoo.php');
    exit();
}

// تحميل ملفات الإعدادات
require_once '../config/odoo_config.php';

$message = '';
$message_type = '';

// معالجة حفظ الإعدادات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = isset($_POST['action']) ? $_POST['action'] : '';
    
    switch ($action) {
        case 'save_general':
            // حفظ الإعدادات العامة
            $general_settings = array(
                'company_name' => $_POST['company_name'] ?? '',
                'company_email' => $_POST['company_email'] ?? '',
                'company_phone' => $_POST['company_phone'] ?? '',
                'company_address' => $_POST['company_address'] ?? '',
                'company_website' => $_POST['company_website'] ?? '',
                'currency' => $_POST['currency'] ?? 'SAR',
                'timezone' => $_POST['timezone'] ?? 'Asia/Riyadh',
                'language' => $_POST['language'] ?? 'ar',
                'date_format' => $_POST['date_format'] ?? 'Y-m-d',
                'fiscal_year_start' => $_POST['fiscal_year_start'] ?? '01-01'
            );
            
            if (saveSettings('general', $general_settings)) {
                $message = 'تم حفظ الإعدادات العامة بنجاح';
                $message_type = 'success';
            } else {
                $message = 'خطأ في حفظ الإعدادات العامة';
                $message_type = 'danger';
            }
            break;
            
        case 'save_accounting':
            // حفظ إعدادات المحاسبة
            $accounting_settings = array(
                'decimal_precision' => intval($_POST['decimal_precision'] ?? 2),
                'auto_reconcile' => isset($_POST['auto_reconcile']),
                'multi_currency' => isset($_POST['multi_currency']),
                'analytic_accounting' => isset($_POST['analytic_accounting']),
                'budget_management' => isset($_POST['budget_management']),
                'asset_management' => isset($_POST['asset_management']),
                'cost_center' => isset($_POST['cost_center']),
                'invoice_policy' => $_POST['invoice_policy'] ?? 'order',
                'payment_terms' => $_POST['payment_terms'] ?? 'immediate',
                'tax_calculation' => $_POST['tax_calculation'] ?? 'exclusive',
                'journal_sequence' => $_POST['journal_sequence'] ?? 'monthly'
            );
            
            if (saveSettings('accounting', $accounting_settings)) {
                $message = 'تم حفظ إعدادات المحاسبة بنجاح';
                $message_type = 'success';
            } else {
                $message = 'خطأ في حفظ إعدادات المحاسبة';
                $message_type = 'danger';
            }
            break;
            
        case 'save_users':
            // حفظ إعدادات المستخدمين
            $users_settings = array(
                'session_timeout' => intval($_POST['session_timeout'] ?? 3600),
                'password_policy' => $_POST['password_policy'] ?? 'medium',
                'two_factor_auth' => isset($_POST['two_factor_auth']),
                'audit_trail' => isset($_POST['audit_trail']),
                'max_login_attempts' => intval($_POST['max_login_attempts'] ?? 5),
                'password_expiry_days' => intval($_POST['password_expiry_days'] ?? 90)
            );
            
            if (saveSettings('users', $users_settings)) {
                $message = 'تم حفظ إعدادات المستخدمين بنجاح';
                $message_type = 'success';
            } else {
                $message = 'خطأ في حفظ إعدادات المستخدمين';
                $message_type = 'danger';
            }
            break;
            
        case 'save_system':
            // حفظ إعدادات النظام
            $system_settings = array(
                'debug_mode' => isset($_POST['debug_mode']),
                'maintenance_mode' => isset($_POST['maintenance_mode']),
                'auto_backup' => isset($_POST['auto_backup']),
                'backup_frequency' => $_POST['backup_frequency'] ?? 'daily',
                'backup_retention' => intval($_POST['backup_retention'] ?? 30),
                'log_level' => $_POST['log_level'] ?? 'INFO',
                'cache_enabled' => isset($_POST['cache_enabled']),
                'api_enabled' => isset($_POST['api_enabled'])
            );
            
            if (saveSettings('system', $system_settings)) {
                $message = 'تم حفظ إعدادات النظام بنجاح';
                $message_type = 'success';
            } else {
                $message = 'خطأ في حفظ إعدادات النظام';
                $message_type = 'danger';
            }
            break;
    }
}

// دالة حفظ الإعدادات
function saveSettings($category, $settings) {
    $config_dir = '../config/';
    $config_file = $config_dir . 'settings_' . $category . '.json';
    
    // إنشاء المجلد إذا لم يكن موجوداً
    if (!file_exists($config_dir)) {
        mkdir($config_dir, 0755, true);
    }
    
    $json = json_encode($settings, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    return file_put_contents($config_file, $json) !== false;
}

// دالة تحميل الإعدادات
function loadSettings($category) {
    $config_file = '../config/settings_' . $category . '.json';
    
    if (file_exists($config_file)) {
        $settings = json_decode(file_get_contents($config_file), true);
        return $settings ? $settings : array();
    }
    
    return array();
}

// تحميل الإعدادات الحالية
$general_settings = array_merge(array(
    'company_name' => 'شركة النظام المتقدم',
    'company_email' => '<EMAIL>',
    'company_phone' => '+966 11 234 5678',
    'company_address' => 'الرياض، المملكة العربية السعودية',
    'company_website' => 'https://www.company.com',
    'currency' => 'SAR',
    'timezone' => 'Asia/Riyadh',
    'language' => 'ar',
    'date_format' => 'Y-m-d',
    'fiscal_year_start' => '01-01'
), loadSettings('general'));

$accounting_settings = array_merge(array(
    'decimal_precision' => 2,
    'auto_reconcile' => true,
    'multi_currency' => false,
    'analytic_accounting' => false,
    'budget_management' => false,
    'asset_management' => false,
    'cost_center' => false,
    'invoice_policy' => 'order',
    'payment_terms' => 'immediate',
    'tax_calculation' => 'exclusive',
    'journal_sequence' => 'monthly'
), loadSettings('accounting'));

$users_settings = array_merge(array(
    'session_timeout' => 3600,
    'password_policy' => 'medium',
    'two_factor_auth' => false,
    'audit_trail' => true,
    'max_login_attempts' => 5,
    'password_expiry_days' => 90
), loadSettings('users'));

$system_settings = array_merge(array(
    'debug_mode' => false,
    'maintenance_mode' => false,
    'auto_backup' => true,
    'backup_frequency' => 'daily',
    'backup_retention' => 30,
    'log_level' => 'INFO',
    'cache_enabled' => true,
    'api_enabled' => false
), loadSettings('system'));

// فئات الإعدادات
$settings_categories = array(
    'general' => array(
        'name' => 'الإعدادات العامة',
        'icon' => 'fas fa-building',
        'color' => '#007bff',
        'description' => 'معلومات الشركة والإعدادات الأساسية'
    ),
    'accounting' => array(
        'name' => 'إعدادات المحاسبة',
        'icon' => 'fas fa-calculator',
        'color' => '#875A7B',
        'description' => 'إعدادات النظام المحاسبي والمالي'
    ),
    'users' => array(
        'name' => 'إدارة المستخدمين',
        'icon' => 'fas fa-users-cog',
        'color' => '#28a745',
        'description' => 'إعدادات المستخدمين والأمان'
    ),
    'system' => array(
        'name' => 'إعدادات النظام',
        'icon' => 'fas fa-server',
        'color' => '#dc3545',
        'description' => 'إعدادات النظام والصيانة'
    ),
    'integrations' => array(
        'name' => 'التكاملات',
        'icon' => 'fas fa-plug',
        'color' => '#fd7e14',
        'description' => 'تكامل مع الأنظمة الخارجية'
    ),
    'modules' => array(
        'name' => 'إدارة الوحدات',
        'icon' => 'fas fa-puzzle-piece',
        'color' => '#6f42c1',
        'description' => 'تفعيل وإدارة وحدات النظام'
    )
);

// معلومات النظام
$system_info = array(
    'version' => '1.0.0',
    'php_version' => phpversion(),
    'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
    'database_version' => 'MySQL 8.0',
    'memory_limit' => ini_get('memory_limit'),
    'max_execution_time' => ini_get('max_execution_time'),
    'upload_max_filesize' => ini_get('upload_max_filesize'),
    'disk_space' => formatBytes(disk_free_space('.')),
    'last_backup' => '2024-01-15 02:00:00'
);

// دالة تنسيق الحجم
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات النظام - نظام ERP</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Odoo Style -->
    <link href="../assets/css/odoo-style.css" rel="stylesheet">
    <link href="../assets/css/odoo-advanced.css" rel="stylesheet">
    
    <style>
        .settings-sidebar {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 0;
            overflow: hidden;
        }
        
        .settings-nav {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .settings-nav-item {
            border-bottom: 1px solid #f0f0f0;
        }
        
        .settings-nav-item:last-child {
            border-bottom: none;
        }
        
        .settings-nav-link {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: #495057;
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .settings-nav-link:hover {
            background: #f8f9fa;
            color: #714B67;
            transform: translateX(5px);
        }
        
        .settings-nav-link.active {
            background: linear-gradient(90deg, #714B67, #875A7B);
            color: white;
            border-left: 4px solid #714B67;
        }
        
        .settings-nav-link.active::after {
            content: '';
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 0;
            height: 0;
            border-left: 10px solid white;
            border-top: 8px solid transparent;
            border-bottom: 8px solid transparent;
        }
        
        .settings-nav-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 1rem;
            font-size: 1rem;
            color: white;
        }
        
        .settings-nav-content {
            flex: 1;
        }
        
        .settings-nav-title {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }
        
        .settings-nav-desc {
            font-size: 0.8rem;
            opacity: 0.7;
        }
        
        .settings-content {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 2rem;
        }
        
        .settings-header {
            border-bottom: 1px solid #e9ecef;
            padding-bottom: 1rem;
            margin-bottom: 2rem;
        }
        
        .settings-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }
        
        .settings-description {
            color: #6c757d;
            margin-bottom: 0;
        }
        
        .form-section {
            margin-bottom: 2rem;
            padding-bottom: 2rem;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .form-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        
        .section-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #495057;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            margin-left: 0.5rem;
            color: #714B67;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            font-weight: 500;
            color: #495057;
            margin-bottom: 0.5rem;
        }
        
        .form-control, .form-select {
            border: 1px solid #ced4da;
            border-radius: 6px;
            padding: 0.75rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #714B67;
            box-shadow: 0 0 0 0.2rem rgba(113, 75, 103, 0.25);
        }
        
        .form-check {
            margin-bottom: 1rem;
        }
        
        .form-check-input:checked {
            background-color: #714B67;
            border-color: #714B67;
        }
        
        .form-check-input:focus {
            box-shadow: 0 0 0 0.2rem rgba(113, 75, 103, 0.25);
        }
        
        .btn-save {
            background: linear-gradient(45deg, #714B67, #875A7B);
            border: none;
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-save:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(113, 75, 103, 0.3);
            color: white;
        }
        
        .system-info-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .info-item:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 500;
            color: #495057;
        }
        
        .info-value {
            color: #6c757d;
            font-family: monospace;
        }
        
        .alert-custom {
            border: none;
            border-radius: 8px;
            padding: 1rem 1.5rem;
            margin-bottom: 1.5rem;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
            animation: fadeIn 0.3s ease;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        @media (max-width: 768px) {
            .settings-sidebar {
                margin-bottom: 1rem;
            }
            
            .settings-nav-link {
                padding: 0.75rem 1rem;
            }
            
            .settings-nav-icon {
                width: 35px;
                height: 35px;
                margin-left: 0.75rem;
            }
            
            .settings-content {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <?php include '../includes/navbar.php'; ?>
    
    <div class="o_main_content">
        <div class="o_content">
            <!-- الشريط الجانبي -->
            <?php include '../includes/sidebar.php'; ?>
            
            <!-- المحتوى الرئيسي -->
            <div class="o_action_manager">
                <!-- شريط التحكم -->
                <div class="o_control_panel">
                    <div class="o_cp_left">
                        <!-- مسار التنقل -->
                        <div class="o_breadcrumb">
                            <a href="dashboard_odoo.php" class="o_breadcrumb_item">الرئيسية</a>
                            <span>/</span>
                            <span class="o_breadcrumb_item active">إعدادات النظام</span>
                        </div>
                    </div>
                    <div class="o_cp_right">
                        <!-- أزرار الإجراءات -->
                        <div class="btn-group">
                            <button class="btn btn-outline-secondary btn-sm" onclick="resetToDefaults()">
                                <i class="fas fa-undo me-1"></i>استعادة الافتراضي
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="exportSettings()">
                                <i class="fas fa-download me-1"></i>تصدير الإعدادات
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="importSettings()">
                                <i class="fas fa-upload me-1"></i>استيراد الإعدادات
                            </button>
                        </div>
                    </div>
                </div>

                <!-- محتوى الإعدادات -->
                <div class="p-3">
                    <?php if ($message): ?>
                        <div class="alert alert-<?php echo $message_type; ?> alert-custom alert-dismissible fade show">
                            <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                            <?php echo $message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <div class="row">
                        <!-- الشريط الجانبي للإعدادات -->
                        <div class="col-lg-3 mb-4">
                            <div class="settings-sidebar">
                                <ul class="settings-nav">
                                    <?php foreach ($settings_categories as $key => $category): ?>
                                        <li class="settings-nav-item">
                                            <a href="#" class="settings-nav-link <?php echo $key === 'general' ? 'active' : ''; ?>" 
                                               onclick="showTab('<?php echo $key; ?>', this)">
                                                <div class="settings-nav-icon" style="background: <?php echo $category['color']; ?>">
                                                    <i class="<?php echo $category['icon']; ?>"></i>
                                                </div>
                                                <div class="settings-nav-content">
                                                    <div class="settings-nav-title"><?php echo $category['name']; ?></div>
                                                    <div class="settings-nav-desc"><?php echo $category['description']; ?></div>
                                                </div>
                                            </a>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        </div>
                        
                        <!-- محتوى الإعدادات -->
                        <div class="col-lg-9">
                            <div class="settings-content">
                                <!-- تبويب الإعدادات العامة -->
                                <div id="general-tab" class="tab-content active">
                                    <div class="settings-header">
                                        <h3 class="settings-title">
                                            <i class="fas fa-building me-2 text-primary"></i>
                                            الإعدادات العامة
                                        </h3>
                                        <p class="settings-description">
                                            إعدادات الشركة والمعلومات الأساسية للنظام
                                        </p>
                                    </div>

                                    <form method="POST" id="generalForm">
                                        <input type="hidden" name="action" value="save_general">

                                        <div class="form-section">
                                            <h5 class="section-title">
                                                <i class="fas fa-info-circle"></i>
                                                معلومات الشركة
                                            </h5>

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label class="form-label">اسم الشركة</label>
                                                        <input type="text" class="form-control" name="company_name"
                                                               value="<?php echo htmlspecialchars($general_settings['company_name']); ?>" required>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label class="form-label">البريد الإلكتروني</label>
                                                        <input type="email" class="form-control" name="company_email"
                                                               value="<?php echo htmlspecialchars($general_settings['company_email']); ?>">
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label class="form-label">رقم الهاتف</label>
                                                        <input type="text" class="form-control" name="company_phone"
                                                               value="<?php echo htmlspecialchars($general_settings['company_phone']); ?>">
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label class="form-label">الموقع الإلكتروني</label>
                                                        <input type="url" class="form-control" name="company_website"
                                                               value="<?php echo htmlspecialchars($general_settings['company_website']); ?>">
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <label class="form-label">العنوان</label>
                                                <textarea class="form-control" name="company_address" rows="3"><?php echo htmlspecialchars($general_settings['company_address']); ?></textarea>
                                            </div>
                                        </div>

                                        <div class="form-section">
                                            <h5 class="section-title">
                                                <i class="fas fa-globe"></i>
                                                الإعدادات الإقليمية
                                            </h5>

                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label class="form-label">العملة</label>
                                                        <select class="form-select" name="currency">
                                                            <option value="SAR" <?php echo $general_settings['currency'] === 'SAR' ? 'selected' : ''; ?>>ريال سعودي (SAR)</option>
                                                            <option value="USD" <?php echo $general_settings['currency'] === 'USD' ? 'selected' : ''; ?>>دولار أمريكي (USD)</option>
                                                            <option value="EUR" <?php echo $general_settings['currency'] === 'EUR' ? 'selected' : ''; ?>>يورو (EUR)</option>
                                                            <option value="AED" <?php echo $general_settings['currency'] === 'AED' ? 'selected' : ''; ?>>درهم إماراتي (AED)</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label class="form-label">المنطقة الزمنية</label>
                                                        <select class="form-select" name="timezone">
                                                            <option value="Asia/Riyadh" <?php echo $general_settings['timezone'] === 'Asia/Riyadh' ? 'selected' : ''; ?>>الرياض</option>
                                                            <option value="Asia/Dubai" <?php echo $general_settings['timezone'] === 'Asia/Dubai' ? 'selected' : ''; ?>>دبي</option>
                                                            <option value="Asia/Kuwait" <?php echo $general_settings['timezone'] === 'Asia/Kuwait' ? 'selected' : ''; ?>>الكويت</option>
                                                            <option value="UTC" <?php echo $general_settings['timezone'] === 'UTC' ? 'selected' : ''; ?>>UTC</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label class="form-label">اللغة</label>
                                                        <select class="form-select" name="language">
                                                            <option value="ar" <?php echo $general_settings['language'] === 'ar' ? 'selected' : ''; ?>>العربية</option>
                                                            <option value="en" <?php echo $general_settings['language'] === 'en' ? 'selected' : ''; ?>>English</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label class="form-label">تنسيق التاريخ</label>
                                                        <select class="form-select" name="date_format">
                                                            <option value="Y-m-d" <?php echo $general_settings['date_format'] === 'Y-m-d' ? 'selected' : ''; ?>>2024-01-15</option>
                                                            <option value="d/m/Y" <?php echo $general_settings['date_format'] === 'd/m/Y' ? 'selected' : ''; ?>>15/01/2024</option>
                                                            <option value="d-m-Y" <?php echo $general_settings['date_format'] === 'd-m-Y' ? 'selected' : ''; ?>>15-01-2024</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label class="form-label">بداية السنة المالية</label>
                                                        <input type="text" class="form-control" name="fiscal_year_start"
                                                               value="<?php echo htmlspecialchars($general_settings['fiscal_year_start']); ?>"
                                                               placeholder="01-01">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="text-end">
                                            <button type="submit" class="btn btn-save">
                                                <i class="fas fa-save me-2"></i>حفظ الإعدادات العامة
                                            </button>
                                        </div>
                                    </form>
                                </div>

                                <!-- تبويب إعدادات المحاسبة -->
                                <div id="accounting-tab" class="tab-content">
                                    <div class="settings-header">
                                        <h3 class="settings-title">
                                            <i class="fas fa-calculator me-2" style="color: #875A7B;"></i>
                                            إعدادات المحاسبة
                                        </h3>
                                        <p class="settings-description">
                                            إعدادات النظام المحاسبي والمالي
                                        </p>
                                    </div>

                                    <form method="POST" id="accountingForm">
                                        <input type="hidden" name="action" value="save_accounting">

                                        <div class="form-section">
                                            <h5 class="section-title">
                                                <i class="fas fa-cogs"></i>
                                                الإعدادات الأساسية
                                            </h5>

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label class="form-label">دقة الأرقام العشرية</label>
                                                        <select class="form-select" name="decimal_precision">
                                                            <option value="0" <?php echo $accounting_settings['decimal_precision'] == 0 ? 'selected' : ''; ?>>بدون أرقام عشرية</option>
                                                            <option value="1" <?php echo $accounting_settings['decimal_precision'] == 1 ? 'selected' : ''; ?>>رقم واحد</option>
                                                            <option value="2" <?php echo $accounting_settings['decimal_precision'] == 2 ? 'selected' : ''; ?>>رقمان</option>
                                                            <option value="3" <?php echo $accounting_settings['decimal_precision'] == 3 ? 'selected' : ''; ?>>ثلاثة أرقام</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label class="form-label">سياسة الفواتير</label>
                                                        <select class="form-select" name="invoice_policy">
                                                            <option value="order" <?php echo $accounting_settings['invoice_policy'] === 'order' ? 'selected' : ''; ?>>حسب الطلب</option>
                                                            <option value="delivery" <?php echo $accounting_settings['invoice_policy'] === 'delivery' ? 'selected' : ''; ?>>حسب التسليم</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label class="form-label">شروط الدفع</label>
                                                        <select class="form-select" name="payment_terms">
                                                            <option value="immediate" <?php echo $accounting_settings['payment_terms'] === 'immediate' ? 'selected' : ''; ?>>فوري</option>
                                                            <option value="15days" <?php echo $accounting_settings['payment_terms'] === '15days' ? 'selected' : ''; ?>>15 يوم</option>
                                                            <option value="30days" <?php echo $accounting_settings['payment_terms'] === '30days' ? 'selected' : ''; ?>>30 يوم</option>
                                                            <option value="60days" <?php echo $accounting_settings['payment_terms'] === '60days' ? 'selected' : ''; ?>>60 يوم</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label class="form-label">حساب الضريبة</label>
                                                        <select class="form-select" name="tax_calculation">
                                                            <option value="exclusive" <?php echo $accounting_settings['tax_calculation'] === 'exclusive' ? 'selected' : ''; ?>>خارج السعر</option>
                                                            <option value="inclusive" <?php echo $accounting_settings['tax_calculation'] === 'inclusive' ? 'selected' : ''; ?>>داخل السعر</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-section">
                                            <h5 class="section-title">
                                                <i class="fas fa-toggle-on"></i>
                                                الميزات المتقدمة
                                            </h5>

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="auto_reconcile"
                                                               <?php echo $accounting_settings['auto_reconcile'] ? 'checked' : ''; ?>>
                                                        <label class="form-check-label">
                                                            التسوية التلقائية
                                                        </label>
                                                    </div>

                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="multi_currency"
                                                               <?php echo $accounting_settings['multi_currency'] ? 'checked' : ''; ?>>
                                                        <label class="form-check-label">
                                                            العملات المتعددة
                                                        </label>
                                                    </div>

                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="analytic_accounting"
                                                               <?php echo $accounting_settings['analytic_accounting'] ? 'checked' : ''; ?>>
                                                        <label class="form-check-label">
                                                            المحاسبة التحليلية
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="budget_management"
                                                               <?php echo $accounting_settings['budget_management'] ? 'checked' : ''; ?>>
                                                        <label class="form-check-label">
                                                            إدارة الميزانيات
                                                        </label>
                                                    </div>

                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="asset_management"
                                                               <?php echo $accounting_settings['asset_management'] ? 'checked' : ''; ?>>
                                                        <label class="form-check-label">
                                                            إدارة الأصول
                                                        </label>
                                                    </div>

                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="cost_center"
                                                               <?php echo $accounting_settings['cost_center'] ? 'checked' : ''; ?>>
                                                        <label class="form-check-label">
                                                            مراكز التكلفة
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="text-end">
                                            <button type="submit" class="btn btn-save">
                                                <i class="fas fa-save me-2"></i>حفظ إعدادات المحاسبة
                                            </button>
                                        </div>
                                    </form>
                                </div>

                                <!-- تبويب إدارة المستخدمين -->
                                <div id="users-tab" class="tab-content">
                                    <div class="settings-header">
                                        <h3 class="settings-title">
                                            <i class="fas fa-users-cog me-2 text-success"></i>
                                            إدارة المستخدمين
                                        </h3>
                                        <p class="settings-description">
                                            إعدادات المستخدمين والأمان والصلاحيات
                                        </p>
                                    </div>

                                    <form method="POST" id="usersForm">
                                        <input type="hidden" name="action" value="save_users">

                                        <div class="form-section">
                                            <h5 class="section-title">
                                                <i class="fas fa-shield-alt"></i>
                                                إعدادات الأمان
                                            </h5>

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label class="form-label">مهلة الجلسة (بالثواني)</label>
                                                        <input type="number" class="form-control" name="session_timeout"
                                                               value="<?php echo $users_settings['session_timeout']; ?>" min="300" max="86400">
                                                        <small class="form-text text-muted">الافتراضي: 3600 ثانية (ساعة واحدة)</small>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label class="form-label">سياسة كلمة المرور</label>
                                                        <select class="form-select" name="password_policy">
                                                            <option value="weak" <?php echo $users_settings['password_policy'] === 'weak' ? 'selected' : ''; ?>>ضعيفة (6 أحرف)</option>
                                                            <option value="medium" <?php echo $users_settings['password_policy'] === 'medium' ? 'selected' : ''; ?>>متوسطة (8 أحرف + أرقام)</option>
                                                            <option value="strong" <?php echo $users_settings['password_policy'] === 'strong' ? 'selected' : ''; ?>>قوية (12 حرف + رموز)</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label class="form-label">عدد محاولات تسجيل الدخول</label>
                                                        <input type="number" class="form-control" name="max_login_attempts"
                                                               value="<?php echo $users_settings['max_login_attempts']; ?>" min="3" max="10">
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label class="form-label">انتهاء صلاحية كلمة المرور (بالأيام)</label>
                                                        <input type="number" class="form-control" name="password_expiry_days"
                                                               value="<?php echo $users_settings['password_expiry_days']; ?>" min="30" max="365">
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="two_factor_auth"
                                                               <?php echo $users_settings['two_factor_auth'] ? 'checked' : ''; ?>>
                                                        <label class="form-check-label">
                                                            المصادقة الثنائية
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="audit_trail"
                                                               <?php echo $users_settings['audit_trail'] ? 'checked' : ''; ?>>
                                                        <label class="form-check-label">
                                                            سجل المراجعة
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="text-end">
                                            <button type="submit" class="btn btn-save">
                                                <i class="fas fa-save me-2"></i>حفظ إعدادات المستخدمين
                                            </button>
                                        </div>
                                    </form>
                                </div>

                                <!-- تبويب إعدادات النظام -->
                                <div id="system-tab" class="tab-content">
                                    <div class="settings-header">
                                        <h3 class="settings-title">
                                            <i class="fas fa-server me-2 text-danger"></i>
                                            إعدادات النظام
                                        </h3>
                                        <p class="settings-description">
                                            إعدادات النظام والصيانة والنسخ الاحتياطي
                                        </p>
                                    </div>

                                    <form method="POST" id="systemForm">
                                        <input type="hidden" name="action" value="save_system">

                                        <div class="form-section">
                                            <h5 class="section-title">
                                                <i class="fas fa-tools"></i>
                                                إعدادات التشغيل
                                            </h5>

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="debug_mode"
                                                               <?php echo $system_settings['debug_mode'] ? 'checked' : ''; ?>>
                                                        <label class="form-check-label">
                                                            وضع التطوير (Debug Mode)
                                                        </label>
                                                    </div>

                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="maintenance_mode"
                                                               <?php echo $system_settings['maintenance_mode'] ? 'checked' : ''; ?>>
                                                        <label class="form-check-label">
                                                            وضع الصيانة
                                                        </label>
                                                    </div>

                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="cache_enabled"
                                                               <?php echo $system_settings['cache_enabled'] ? 'checked' : ''; ?>>
                                                        <label class="form-check-label">
                                                            تفعيل التخزين المؤقت
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label class="form-label">مستوى السجلات</label>
                                                        <select class="form-select" name="log_level">
                                                            <option value="DEBUG" <?php echo $system_settings['log_level'] === 'DEBUG' ? 'selected' : ''; ?>>تفصيلي (DEBUG)</option>
                                                            <option value="INFO" <?php echo $system_settings['log_level'] === 'INFO' ? 'selected' : ''; ?>>معلومات (INFO)</option>
                                                            <option value="WARNING" <?php echo $system_settings['log_level'] === 'WARNING' ? 'selected' : ''; ?>>تحذيرات (WARNING)</option>
                                                            <option value="ERROR" <?php echo $system_settings['log_level'] === 'ERROR' ? 'selected' : ''; ?>>أخطاء (ERROR)</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-section">
                                            <h5 class="section-title">
                                                <i class="fas fa-database"></i>
                                                النسخ الاحتياطي
                                            </h5>

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-check mb-3">
                                                        <input class="form-check-input" type="checkbox" name="auto_backup"
                                                               <?php echo $system_settings['auto_backup'] ? 'checked' : ''; ?>>
                                                        <label class="form-check-label">
                                                            النسخ الاحتياطي التلقائي
                                                        </label>
                                                    </div>

                                                    <div class="form-group">
                                                        <label class="form-label">تكرار النسخ الاحتياطي</label>
                                                        <select class="form-select" name="backup_frequency">
                                                            <option value="hourly" <?php echo $system_settings['backup_frequency'] === 'hourly' ? 'selected' : ''; ?>>كل ساعة</option>
                                                            <option value="daily" <?php echo $system_settings['backup_frequency'] === 'daily' ? 'selected' : ''; ?>>يومياً</option>
                                                            <option value="weekly" <?php echo $system_settings['backup_frequency'] === 'weekly' ? 'selected' : ''; ?>>أسبوعياً</option>
                                                            <option value="monthly" <?php echo $system_settings['backup_frequency'] === 'monthly' ? 'selected' : ''; ?>>شهرياً</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label class="form-label">الاحتفاظ بالنسخ (بالأيام)</label>
                                                        <input type="number" class="form-control" name="backup_retention"
                                                               value="<?php echo $system_settings['backup_retention']; ?>" min="7" max="365">
                                                    </div>

                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="api_enabled"
                                                               <?php echo $system_settings['api_enabled'] ? 'checked' : ''; ?>>
                                                        <label class="form-check-label">
                                                            تفعيل API
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="text-end">
                                            <button type="submit" class="btn btn-save">
                                                <i class="fas fa-save me-2"></i>حفظ إعدادات النظام
                                            </button>
                                        </div>
                                    </form>
                                </div>

                                <!-- تبويب معلومات النظام -->
                                <div id="integrations-tab" class="tab-content">
                                    <div class="settings-header">
                                        <h3 class="settings-title">
                                            <i class="fas fa-info-circle me-2 text-info"></i>
                                            معلومات النظام
                                        </h3>
                                        <p class="settings-description">
                                            معلومات تقنية عن النظام والخادم
                                        </p>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="system-info-card">
                                                <h6 class="mb-3">
                                                    <i class="fas fa-server me-2"></i>
                                                    معلومات الخادم
                                                </h6>

                                                <div class="info-item">
                                                    <span class="info-label">إصدار النظام:</span>
                                                    <span class="info-value"><?php echo $system_info['version']; ?></span>
                                                </div>

                                                <div class="info-item">
                                                    <span class="info-label">إصدار PHP:</span>
                                                    <span class="info-value"><?php echo $system_info['php_version']; ?></span>
                                                </div>

                                                <div class="info-item">
                                                    <span class="info-label">خادم الويب:</span>
                                                    <span class="info-value"><?php echo $system_info['server_software']; ?></span>
                                                </div>

                                                <div class="info-item">
                                                    <span class="info-label">قاعدة البيانات:</span>
                                                    <span class="info-value"><?php echo $system_info['database_version']; ?></span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-6">
                                            <div class="system-info-card">
                                                <h6 class="mb-3">
                                                    <i class="fas fa-memory me-2"></i>
                                                    موارد النظام
                                                </h6>

                                                <div class="info-item">
                                                    <span class="info-label">حد الذاكرة:</span>
                                                    <span class="info-value"><?php echo $system_info['memory_limit']; ?></span>
                                                </div>

                                                <div class="info-item">
                                                    <span class="info-label">وقت التنفيذ الأقصى:</span>
                                                    <span class="info-value"><?php echo $system_info['max_execution_time']; ?>s</span>
                                                </div>

                                                <div class="info-item">
                                                    <span class="info-label">حجم الرفع الأقصى:</span>
                                                    <span class="info-value"><?php echo $system_info['upload_max_filesize']; ?></span>
                                                </div>

                                                <div class="info-item">
                                                    <span class="info-label">المساحة المتاحة:</span>
                                                    <span class="info-value"><?php echo $system_info['disk_space']; ?></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="system-info-card">
                                        <h6 class="mb-3">
                                            <i class="fas fa-tools me-2"></i>
                                            إجراءات الصيانة
                                        </h6>

                                        <div class="row">
                                            <div class="col-md-3 mb-2">
                                                <button class="btn btn-outline-primary w-100" onclick="clearCache()">
                                                    <i class="fas fa-broom me-2"></i>مسح التخزين المؤقت
                                                </button>
                                            </div>
                                            <div class="col-md-3 mb-2">
                                                <button class="btn btn-outline-success w-100" onclick="createBackup()">
                                                    <i class="fas fa-database me-2"></i>إنشاء نسخة احتياطية
                                                </button>
                                            </div>
                                            <div class="col-md-3 mb-2">
                                                <button class="btn btn-outline-warning w-100" onclick="optimizeDatabase()">
                                                    <i class="fas fa-wrench me-2"></i>تحسين قاعدة البيانات
                                                </button>
                                            </div>
                                            <div class="col-md-3 mb-2">
                                                <button class="btn btn-outline-info w-100" onclick="checkUpdates()">
                                                    <i class="fas fa-sync me-2"></i>فحص التحديثات
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- تبويب إدارة الوحدات -->
                                <div id="modules-tab" class="tab-content">
                                    <div class="settings-header">
                                        <h3 class="settings-title">
                                            <i class="fas fa-puzzle-piece me-2" style="color: #6f42c1;"></i>
                                            إدارة الوحدات
                                        </h3>
                                        <p class="settings-description">
                                            تفعيل وإدارة وحدات النظام المختلفة
                                        </p>
                                    </div>

                                    <div class="row">
                                        <?php
                                        $modules = array(
                                            array('name' => 'المحاسبة الأساسية', 'status' => 'installed', 'description' => 'وحدة المحاسبة الأساسية'),
                                            array('name' => 'إدارة المخزون', 'status' => 'available', 'description' => 'إدارة المخزون والمنتجات'),
                                            array('name' => 'إدارة المبيعات', 'status' => 'installed', 'description' => 'وحدة المبيعات والعروض'),
                                            array('name' => 'إدارة المشتريات', 'status' => 'available', 'description' => 'وحدة المشتريات والموردين'),
                                            array('name' => 'إدارة الموارد البشرية', 'status' => 'available', 'description' => 'إدارة الموظفين والرواتب'),
                                            array('name' => 'إدارة المشاريع', 'status' => 'available', 'description' => 'إدارة المشاريع والمهام')
                                        );

                                        foreach ($modules as $module):
                                        ?>
                                            <div class="col-md-6 mb-3">
                                                <div class="card h-100">
                                                    <div class="card-body">
                                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                                            <h6 class="card-title mb-0"><?php echo $module['name']; ?></h6>
                                                            <span class="badge bg-<?php echo $module['status'] === 'installed' ? 'success' : 'secondary'; ?>">
                                                                <?php echo $module['status'] === 'installed' ? 'مثبت' : 'متاح'; ?>
                                                            </span>
                                                        </div>
                                                        <p class="card-text text-muted small"><?php echo $module['description']; ?></p>
                                                        <div class="d-flex gap-2">
                                                            <?php if ($module['status'] === 'installed'): ?>
                                                                <button class="btn btn-outline-danger btn-sm" onclick="uninstallModule('<?php echo $module['name']; ?>')">
                                                                    <i class="fas fa-minus me-1"></i>إلغاء التثبيت
                                                                </button>
                                                                <button class="btn btn-outline-secondary btn-sm" onclick="configureModule('<?php echo $module['name']; ?>')">
                                                                    <i class="fas fa-cog me-1"></i>إعدادات
                                                                </button>
                                                            <?php else: ?>
                                                                <button class="btn btn-outline-primary btn-sm" onclick="installModule('<?php echo $module['name']; ?>')">
                                                                    <i class="fas fa-plus me-1"></i>تثبيت
                                                                </button>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializeSettings();
            setupFormValidation();
        });

        // تهيئة الإعدادات
        function initializeSettings() {
            // تفعيل التبويب الأول
            showTab('general', document.querySelector('.settings-nav-link'));

            // إضافة معالجات الأحداث للنماذج
            setupFormHandlers();
        }

        // عرض التبويب
        function showTab(tabId, element) {
            // إخفاء جميع التبويبات
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // إزالة التفعيل من جميع الروابط
            document.querySelectorAll('.settings-nav-link').forEach(link => {
                link.classList.remove('active');
            });

            // تفعيل التبويب المحدد
            const targetTab = document.getElementById(tabId + '-tab');
            if (targetTab) {
                targetTab.classList.add('active');
            }

            // تفعيل الرابط المحدد
            if (element) {
                element.classList.add('active');
            }
        }

        // إعداد معالجات النماذج
        function setupFormHandlers() {
            // معالج النموذج العام
            const generalForm = document.getElementById('generalForm');
            if (generalForm) {
                generalForm.addEventListener('submit', function(e) {
                    if (!validateGeneralForm()) {
                        e.preventDefault();
                        return false;
                    }
                    showLoading();
                });
            }

            // معالج نموذج المحاسبة
            const accountingForm = document.getElementById('accountingForm');
            if (accountingForm) {
                accountingForm.addEventListener('submit', function(e) {
                    if (!validateAccountingForm()) {
                        e.preventDefault();
                        return false;
                    }
                    showLoading();
                });
            }

            // معالج نموذج المستخدمين
            const usersForm = document.getElementById('usersForm');
            if (usersForm) {
                usersForm.addEventListener('submit', function(e) {
                    if (!validateUsersForm()) {
                        e.preventDefault();
                        return false;
                    }
                    showLoading();
                });
            }

            // معالج نموذج النظام
            const systemForm = document.getElementById('systemForm');
            if (systemForm) {
                systemForm.addEventListener('submit', function(e) {
                    showLoading();
                });
            }
        }

        // التحقق من صحة النموذج العام
        function validateGeneralForm() {
            const companyName = document.querySelector('input[name="company_name"]').value.trim();

            if (!companyName) {
                showMessage('يرجى إدخال اسم الشركة', 'danger');
                return false;
            }

            if (companyName.length < 3) {
                showMessage('اسم الشركة يجب أن يكون 3 أحرف على الأقل', 'danger');
                return false;
            }

            return true;
        }

        // التحقق من صحة نموذج المحاسبة
        function validateAccountingForm() {
            const decimalPrecision = document.querySelector('select[name="decimal_precision"]').value;

            if (decimalPrecision === '') {
                showMessage('يرجى اختيار دقة الأرقام العشرية', 'danger');
                return false;
            }

            return true;
        }

        // التحقق من صحة نموذج المستخدمين
        function validateUsersForm() {
            const sessionTimeout = parseInt(document.querySelector('input[name="session_timeout"]').value);
            const maxLoginAttempts = parseInt(document.querySelector('input[name="max_login_attempts"]').value);

            if (sessionTimeout < 300 || sessionTimeout > 86400) {
                showMessage('مهلة الجلسة يجب أن تكون بين 300 و 86400 ثانية', 'danger');
                return false;
            }

            if (maxLoginAttempts < 3 || maxLoginAttempts > 10) {
                showMessage('عدد محاولات تسجيل الدخول يجب أن يكون بين 3 و 10', 'danger');
                return false;
            }

            return true;
        }

        // إعداد التحقق من النماذج
        function setupFormValidation() {
            // التحقق الفوري من الحقول
            document.querySelectorAll('input[required], select[required]').forEach(field => {
                field.addEventListener('blur', function() {
                    validateField(this);
                });

                field.addEventListener('input', function() {
                    clearFieldError(this);
                });
            });
        }

        // التحقق من حقل واحد
        function validateField(field) {
            const value = field.value.trim();

            if (field.hasAttribute('required') && !value) {
                showFieldError(field, 'هذا الحقل مطلوب');
                return false;
            }

            if (field.type === 'email' && value && !isValidEmail(value)) {
                showFieldError(field, 'البريد الإلكتروني غير صحيح');
                return false;
            }

            if (field.type === 'url' && value && !isValidUrl(value)) {
                showFieldError(field, 'الرابط غير صحيح');
                return false;
            }

            clearFieldError(field);
            return true;
        }

        // عرض خطأ الحقل
        function showFieldError(field, message) {
            clearFieldError(field);

            field.classList.add('is-invalid');

            const errorDiv = document.createElement('div');
            errorDiv.className = 'invalid-feedback';
            errorDiv.textContent = message;

            field.parentNode.appendChild(errorDiv);
        }

        // مسح خطأ الحقل
        function clearFieldError(field) {
            field.classList.remove('is-invalid');

            const errorDiv = field.parentNode.querySelector('.invalid-feedback');
            if (errorDiv) {
                errorDiv.remove();
            }
        }

        // التحقق من صحة البريد الإلكتروني
        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        // التحقق من صحة الرابط
        function isValidUrl(url) {
            try {
                new URL(url);
                return true;
            } catch {
                return false;
            }
        }

        // وظائف الصيانة
        function clearCache() {
            if (confirm('هل أنت متأكد من مسح التخزين المؤقت؟')) {
                showLoading();

                // محاكاة عملية مسح التخزين المؤقت
                setTimeout(() => {
                    hideLoading();
                    showMessage('تم مسح التخزين المؤقت بنجاح', 'success');
                }, 2000);
            }
        }

        function createBackup() {
            if (confirm('هل تريد إنشاء نسخة احتياطية الآن؟')) {
                showLoading();

                // محاكاة عملية إنشاء النسخة الاحتياطية
                setTimeout(() => {
                    hideLoading();
                    showMessage('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
                }, 3000);
            }
        }

        function optimizeDatabase() {
            if (confirm('هل تريد تحسين قاعدة البيانات؟ قد تستغرق هذه العملية بعض الوقت.')) {
                showLoading();

                // محاكاة عملية تحسين قاعدة البيانات
                setTimeout(() => {
                    hideLoading();
                    showMessage('تم تحسين قاعدة البيانات بنجاح', 'success');
                }, 4000);
            }
        }

        function checkUpdates() {
            showLoading();

            // محاكاة فحص التحديثات
            setTimeout(() => {
                hideLoading();
                showMessage('النظام محدث إلى أحدث إصدار', 'info');
            }, 2000);
        }

        // وظائف إدارة الوحدات
        function installModule(moduleName) {
            if (confirm(`هل تريد تثبيت وحدة "${moduleName}"؟`)) {
                showLoading();

                setTimeout(() => {
                    hideLoading();
                    showMessage(`تم تثبيت وحدة "${moduleName}" بنجاح`, 'success');
                    location.reload();
                }, 2000);
            }
        }

        function uninstallModule(moduleName) {
            if (confirm(`هل أنت متأكد من إلغاء تثبيت وحدة "${moduleName}"؟`)) {
                showLoading();

                setTimeout(() => {
                    hideLoading();
                    showMessage(`تم إلغاء تثبيت وحدة "${moduleName}" بنجاح`, 'success');
                    location.reload();
                }, 2000);
            }
        }

        function configureModule(moduleName) {
            showMessage(`إعدادات وحدة "${moduleName}" قيد التطوير`, 'info');
        }

        // وظائف الإعدادات المتقدمة
        function resetToDefaults() {
            if (confirm('هل أنت متأكد من استعادة الإعدادات الافتراضية؟ سيتم فقدان جميع التخصيصات الحالية.')) {
                showLoading();

                setTimeout(() => {
                    hideLoading();
                    showMessage('تم استعادة الإعدادات الافتراضية بنجاح', 'success');
                    location.reload();
                }, 2000);
            }
        }

        function exportSettings() {
            showLoading();

            // محاكاة تصدير الإعدادات
            setTimeout(() => {
                hideLoading();

                // إنشاء ملف JSON وهمي للتحميل
                const settings = {
                    general: <?php echo json_encode($general_settings); ?>,
                    accounting: <?php echo json_encode($accounting_settings); ?>,
                    users: <?php echo json_encode($users_settings); ?>,
                    system: <?php echo json_encode($system_settings); ?>
                };

                const dataStr = JSON.stringify(settings, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});

                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = 'erp_settings_' + new Date().toISOString().split('T')[0] + '.json';
                link.click();

                showMessage('تم تصدير الإعدادات بنجاح', 'success');
            }, 1000);
        }

        function importSettings() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';

            input.onchange = function(e) {
                const file = e.target.files[0];
                if (!file) return;

                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        const settings = JSON.parse(e.target.result);

                        if (confirm('هل تريد استيراد هذه الإعدادات؟ سيتم استبدال الإعدادات الحالية.')) {
                            showLoading();

                            // محاكاة استيراد الإعدادات
                            setTimeout(() => {
                                hideLoading();
                                showMessage('تم استيراد الإعدادات بنجاح', 'success');
                                location.reload();
                            }, 2000);
                        }
                    } catch (error) {
                        showMessage('ملف الإعدادات غير صحيح', 'danger');
                    }
                };
                reader.readAsText(file);
            };

            input.click();
        }

        // وظائف المساعدة
        function showLoading() {
            // إنشاء تراكب التحميل إذا لم يكن موجوداً
            let overlay = document.getElementById('loadingOverlay');
            if (!overlay) {
                overlay = document.createElement('div');
                overlay.id = 'loadingOverlay';
                overlay.className = 'loading-overlay';
                overlay.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0,0,0,0.5);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 9999;
                `;

                overlay.innerHTML = `
                    <div style="background: white; padding: 20px; border-radius: 8px; text-align: center;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                        <div class="mt-2">جاري المعالجة...</div>
                    </div>
                `;

                document.body.appendChild(overlay);
            }

            overlay.style.display = 'flex';
        }

        function hideLoading() {
            const overlay = document.getElementById('loadingOverlay');
            if (overlay) {
                overlay.style.display = 'none';
            }
        }

        function showMessage(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';

            const iconClass = type === 'success' ? 'check-circle' :
                             type === 'danger' ? 'exclamation-triangle' :
                             type === 'warning' ? 'exclamation-triangle' : 'info-circle';

            alertDiv.innerHTML = `
                <i class="fas fa-${iconClass} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(alertDiv);

            setTimeout(() => {
                alertDiv.remove();
            }, 4000);
        }

        // تأثيرات بصرية إضافية
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير hover للبطاقات
            document.querySelectorAll('.card').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                    this.style.boxShadow = '0 4px 15px rgba(0,0,0,0.1)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = '';
                });
            });

            // تأثير النقر على أزرار الصيانة
            document.querySelectorAll('.btn-outline-primary, .btn-outline-success, .btn-outline-warning, .btn-outline-info').forEach(btn => {
                btn.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });
        });
    </script>
</body>
</html>
