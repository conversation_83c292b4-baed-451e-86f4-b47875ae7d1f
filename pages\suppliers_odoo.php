<?php
/**
 * صفحة الموردين المحسنة - بأسلوب Odoo
 * Enhanced Suppliers Page - Odoo Style
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// تضمين الملفات المطلوبة
require_once '../config/database.php';
require_once '../models/ResPartner.php';

// إنشاء اتصال قاعدة البيانات
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    $pdo = null; // سيتم استخدام البيانات الوهمية
}

$partner_model = new ResPartner($pdo);

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'create':
            $data = [
                'name' => $_POST['name'],
                'email' => $_POST['email'] ?? '',
                'phone' => $_POST['phone'] ?? '',
                'mobile' => $_POST['mobile'] ?? '',
                'street' => $_POST['street'] ?? '',
                'city' => $_POST['city'] ?? '',
                'is_company' => isset($_POST['is_company']) ? 1 : 0,
                'customer_rank' => 0,
                'supplier_rank' => 1, // دائماً مورد
                'vat' => $_POST['vat'] ?? '',
                'website' => $_POST['website'] ?? '',
                'active' => 1
            ];
            
            try {
                $partner_model->create($data);
                $success_message = "تم إنشاء المورد بنجاح";
            } catch (Exception $e) {
                $error_message = "خطأ في إنشاء المورد: " . $e->getMessage();
            }
            break;
            
        case 'update':
            $id = $_POST['id'];
            $data = [
                'name' => $_POST['name'],
                'email' => $_POST['email'] ?? '',
                'phone' => $_POST['phone'] ?? '',
                'mobile' => $_POST['mobile'] ?? '',
                'street' => $_POST['street'] ?? '',
                'city' => $_POST['city'] ?? '',
                'is_company' => isset($_POST['is_company']) ? 1 : 0,
                'vat' => $_POST['vat'] ?? '',
                'website' => $_POST['website'] ?? ''
            ];
            
            try {
                $partner_model->update($id, $data);
                $success_message = "تم تحديث المورد بنجاح";
            } catch (Exception $e) {
                $error_message = "خطأ في تحديث المورد: " . $e->getMessage();
            }
            break;
            
        case 'delete':
            $id = $_POST['id'];
            try {
                $partner_model->delete($id);
                $success_message = "تم حذف المورد بنجاح";
            } catch (Exception $e) {
                $error_message = "خطأ في حذف المورد: " . $e->getMessage();
            }
            break;
    }
}

// جلب الموردين
try {
    $all_partners = $partner_model->search([], ['name' => 'ASC']);
    
    // فلترة الموردين فقط
    $suppliers = [];
    foreach ($all_partners as $partner) {
        if ($partner['supplier_rank'] > 0) {
            $suppliers[] = $partner;
        }
    }
    
    // إذا لم توجد بيانات، استخدم البيانات الوهمية
    if (empty($suppliers)) {
        $demo_data = $partner_model->get_demo_data();
        foreach ($demo_data as $partner) {
            if ($partner['supplier_rank'] > 0) {
                $suppliers[] = $partner;
            }
        }
    }
} catch (Exception $e) {
    // استخدام البيانات الوهمية في حالة الخطأ
    $demo_data = $partner_model->get_demo_data();
    $suppliers = [];
    foreach ($demo_data as $partner) {
        if ($partner['supplier_rank'] > 0) {
            $suppliers[] = $partner;
        }
    }
}

// معاملات البحث والفلترة
$search_term = $_GET['search'] ?? '';
$filter_type = $_GET['filter'] ?? 'all';
$view_mode = $_GET['view'] ?? 'kanban';

// تطبيق البحث
if (!empty($search_term)) {
    $suppliers = array_filter($suppliers, function($supplier) use ($search_term) {
        return stripos($supplier['name'], $search_term) !== false ||
               stripos($supplier['email'], $search_term) !== false ||
               stripos($supplier['phone'], $search_term) !== false;
    });
}

// تطبيق الفلاتر
if ($filter_type === 'companies') {
    $suppliers = array_filter($suppliers, function($supplier) {
        return $supplier['is_company'] == 1;
    });
} elseif ($filter_type === 'individuals') {
    $suppliers = array_filter($suppliers, function($supplier) {
        return $supplier['is_company'] == 0;
    });
}

// إحصائيات
$total_suppliers = count($suppliers);
$companies = count(array_filter($suppliers, function($s) { return $s['is_company'] == 1; }));
$individuals = count(array_filter($suppliers, function($s) { return $s['is_company'] == 0; }));
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الموردين - نظام ERP</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --odoo-primary: #714B67;
            --odoo-secondary: #875A7B;
            --odoo-accent: #D4AF37;
            --odoo-success: #28a745;
            --odoo-info: #17a2b8;
            --odoo-warning: #ffc107;
            --odoo-danger: #dc3545;
            --odoo-light: #f8f9fa;
            --odoo-dark: #2F3349;
        }
        
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background: var(--odoo-light);
            margin: 0;
            padding: 0;
        }
        
        .main-container {
            display: flex;
            min-height: 100vh;
        }
        
        .content-area {
            flex: 1;
            padding: 0;
            overflow-x: hidden;
        }
        
        /* رأس الصفحة */
        .page-header {
            background: white;
            border-bottom: 1px solid #e9ecef;
            padding: 20px 30px;
            margin-bottom: 0;
        }
        
        .breadcrumb-odoo {
            background: none;
            padding: 0;
            margin: 0;
            font-size: 0.9rem;
        }
        
        .breadcrumb-odoo .breadcrumb-item {
            color: #6c757d;
        }
        
        .breadcrumb-odoo .breadcrumb-item.active {
            color: var(--odoo-primary);
            font-weight: 600;
        }
        
        .page-title {
            color: var(--odoo-dark);
            font-size: 1.8rem;
            font-weight: 700;
            margin: 10px 0 0 0;
        }
        
        /* أزرار الإجراءات */
        .action-buttons {
            background: white;
            border-bottom: 1px solid #e9ecef;
            padding: 15px 30px;
        }
        
        .btn-odoo {
            background: var(--odoo-primary);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-odoo:hover {
            background: var(--odoo-secondary);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(113, 75, 103, 0.3);
        }
        
        .btn-outline-odoo {
            background: transparent;
            border: 1px solid var(--odoo-primary);
            color: var(--odoo-primary);
        }
        
        .btn-outline-odoo:hover {
            background: var(--odoo-primary);
            color: white;
        }
        
        /* بطاقات الإحصائيات */
        .stats-container {
            padding: 20px 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid var(--odoo-warning);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--odoo-warning);
            margin: 0;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
            margin: 5px 0 0 0;
        }
        
        .stat-icon {
            font-size: 2.5rem;
            color: var(--odoo-warning);
            opacity: 0.7;
        }
        
        /* منطقة المحتوى */
        .content-section {
            padding: 20px 30px;
        }
        
        /* فلاتر البحث */
        .filter-tabs {
            display: flex;
            gap: 5px;
            margin-bottom: 20px;
        }
        
        .filter-tab {
            background: white;
            border: 1px solid #dee2e6;
            color: #6c757d;
            padding: 8px 16px;
            border-radius: 4px;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 6px;
            cursor: pointer;
        }
        
        .filter-tab:hover {
            background: #f8f9fa;
            color: var(--odoo-primary);
            text-decoration: none;
        }
        
        .filter-tab.active {
            background: var(--odoo-primary);
            color: white;
            border-color: var(--odoo-primary);
        }
        
        /* مربع البحث */
        .search-box {
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 8px 12px;
            font-size: 0.9rem;
        }
        
        .search-box:focus {
            border-color: var(--odoo-primary);
            box-shadow: 0 0 0 0.2rem rgba(113, 75, 103, 0.25);
        }
        
        /* مفتاح العرض */
        .view-switcher {
            display: flex;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .view-btn {
            background: white;
            border: none;
            padding: 8px 12px;
            color: #6c757d;
            transition: all 0.3s ease;
            border-right: 1px solid #dee2e6;
        }
        
        .view-btn:last-child {
            border-right: none;
        }
        
        .view-btn:hover {
            background: #f8f9fa;
            color: var(--odoo-primary);
        }
        
        .view-btn.active {
            background: var(--odoo-primary);
            color: white;
        }
        
        /* عرض البطاقات */
        .supplier-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
            overflow: hidden;
        }
        
        .supplier-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .supplier-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 1.2rem;
        }
        
        .supplier-type-badge {
            font-size: 0.75rem;
            padding: 2px 8px;
            border-radius: 12px;
        }
        
        .contact-details {
            font-size: 0.85rem;
        }
        
        /* أزرار الإجراءات */
        .action-btn {
            background: none;
            border: 1px solid #dee2e6;
            color: #6c757d;
            padding: 6px 10px;
            border-radius: 4px;
            transition: all 0.3s ease;
            font-size: 0.8rem;
        }
        
        .btn-edit:hover {
            background: var(--odoo-info);
            color: white;
            border-color: var(--odoo-info);
        }
        
        .btn-delete:hover {
            background: var(--odoo-danger);
            color: white;
            border-color: var(--odoo-danger);
        }
        
        /* عرض القائمة */
        .table-responsive {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .table {
            margin: 0;
        }
        
        .table th {
            background: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
            font-weight: 600;
            color: var(--odoo-dark);
            padding: 12px;
            font-size: 0.9rem;
        }
        
        .table td {
            padding: 12px;
            vertical-align: middle;
            border-bottom: 1px solid #e9ecef;
        }
        
        .table tbody tr:hover {
            background: #f8f9fa;
        }
        
        /* النوافذ المنبثقة */
        .modal-content {
            border: none;
            border-radius: 8px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .modal-header {
            background: var(--odoo-primary);
            color: white;
            border-radius: 8px 8px 0 0;
            padding: 15px 20px;
        }
        
        .modal-title {
            font-weight: 600;
        }
        
        .btn-close {
            filter: invert(1);
        }
        
        .form-label {
            font-weight: 500;
            color: var(--odoo-dark);
            margin-bottom: 5px;
        }
        
        .form-control:focus {
            border-color: var(--odoo-primary);
            box-shadow: 0 0 0 0.2rem rgba(113, 75, 103, 0.25);
        }
        
        /* رسائل التنبيه */
        .alert {
            border: none;
            border-radius: 6px;
            padding: 12px 16px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border-left: 4px solid #28a745;
        }
        
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border-left: 4px solid #dc3545;
        }
        
        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 768px) {
            .page-header, .action-buttons, .content-section {
                padding: 15px 20px;
            }
            
            .filter-tabs {
                flex-wrap: wrap;
            }
            
            .stats-container {
                padding: 15px 20px;
            }
            
            .supplier-card {
                margin-bottom: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- الشريط الجانبي -->
        <?php include '../includes/sidebar.php'; ?>
        
        <!-- منطقة المحتوى -->
        <div class="content-area">
            <!-- رأس الصفحة -->
            <div class="page-header">
                <nav class="breadcrumb-odoo">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="../dashboard.php">
                                <i class="fas fa-home"></i> الرئيسية
                            </a>
                        </li>
                        <li class="breadcrumb-item">الشركاء</li>
                        <li class="breadcrumb-item active">الموردين</li>
                    </ol>
                </nav>
                <h1 class="page-title">
                    <i class="fas fa-truck me-2"></i>الموردين
                </h1>
            </div>
            
            <!-- أزرار الإجراءات -->
            <div class="action-buttons">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="d-flex gap-2">
                        <button class="btn-odoo" data-bs-toggle="modal" data-bs-target="#supplierModal" onclick="openCreateModal()">
                            <i class="fas fa-plus"></i>
                            إنشاء مورد جديد
                        </button>
                        <button class="btn-outline-odoo" onclick="importSuppliers()">
                            <i class="fas fa-upload"></i>
                            استيراد
                        </button>
                        <button class="btn-outline-odoo" onclick="exportSuppliers()">
                            <i class="fas fa-download"></i>
                            تصدير
                        </button>
                    </div>
                    
                    <div class="d-flex align-items-center gap-3">
                        <span class="text-muted">
                            <i class="fas fa-truck me-1"></i>
                            <?php echo $total_suppliers; ?> مورد
                        </span>
                    </div>
                </div>
            </div>
            
            <!-- رسائل التنبيه -->
            <?php if (isset($success_message)): ?>
                <div class="alert alert-success mx-4">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo $success_message; ?>
                </div>
            <?php endif; ?>
            
            <?php if (isset($error_message)): ?>
                <div class="alert alert-danger mx-4">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <?php echo $error_message; ?>
                </div>
            <?php endif; ?>
            
            <!-- بطاقات الإحصائيات -->
            <div class="stats-container">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="stat-number"><?php echo $total_suppliers; ?></h3>
                                    <p class="stat-label">إجمالي الموردين</p>
                                </div>
                                <i class="fas fa-truck stat-icon"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="stat-number"><?php echo $companies; ?></h3>
                                    <p class="stat-label">الشركات</p>
                                </div>
                                <i class="fas fa-building stat-icon"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="stat-number"><?php echo $individuals; ?></h3>
                                    <p class="stat-label">الأفراد</p>
                                </div>
                                <i class="fas fa-user stat-icon"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="stat-number"><?php echo count(array_filter($suppliers, function($s) { return $s['active'] ?? 1; })); ?></h3>
                                    <p class="stat-label">النشطين</p>
                                </div>
                                <i class="fas fa-check-circle stat-icon"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
