<?php
/**
 * صفحة إدارة النظام الرئيسية
 * Main System Administration Page
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// التحقق من الصلاحيات
$user_groups = isset($_SESSION['groups']) ? $_SESSION['groups'] : array();
if (!in_array('admin', $user_groups)) {
    header('Location: ../dashboard.php?error=access_denied');
    exit();
}

// تحميل نظام الوحدات
require_once '../includes/odoo_modules.php';
$module_manager = OdooModuleManager::getInstance();

// الحصول على إحصائيات النظام
$system_stats = array(
    'installed_modules' => count($module_manager->getInstalledModules()),
    'available_modules' => count($module_manager->getAvailableModules()),
    'total_modules' => count($module_manager->getAllModules()),
    'active_users' => 1, // يمكن تطويرها لاحقاً
    'companies' => 3,
    'database_size' => '2.5 MB'
);

// أدوات إدارة النظام
$admin_tools = array(
    array(
        'name' => 'إدارة الوحدات',
        'description' => 'تثبيت وإلغاء تثبيت وحدات النظام',
        'icon' => 'fas fa-puzzle-piece',
        'color' => '#875A7B',
        'url' => 'modules.php',
        'category' => 'modules'
    ),
    array(
        'name' => 'إدارة المستخدمين',
        'description' => 'إضافة وتعديل وحذف المستخدمين',
        'icon' => 'fas fa-users-cog',
        'color' => '#00A09D',
        'url' => 'users_new.php',
        'category' => 'users'
    ),
    array(
        'name' => 'إدارة الشركات',
        'description' => 'إعداد وإدارة الشركات في النظام',
        'icon' => 'fas fa-building',
        'color' => '#F56C6C',
        'url' => 'companies.php',
        'category' => 'companies'
    ),
    array(
        'name' => 'إعدادات النظام',
        'description' => 'تكوين الإعدادات العامة للنظام',
        'icon' => 'fas fa-cogs',
        'color' => '#E67E22',
        'url' => 'settings.php',
        'category' => 'settings'
    ),
    array(
        'name' => 'النسخ الاحتياطي',
        'description' => 'إنشاء واستعادة النسخ الاحتياطية',
        'icon' => 'fas fa-database',
        'color' => '#9B59B6',
        'url' => 'backup.php',
        'category' => 'backup'
    ),
    array(
        'name' => 'سجلات النظام',
        'description' => 'عرض ومراقبة سجلات النظام',
        'icon' => 'fas fa-file-alt',
        'color' => '#3498DB',
        'url' => 'logs.php',
        'category' => 'logs'
    ),
    array(
        'name' => 'مراقبة الأداء',
        'description' => 'مراقبة أداء النظام والخادم',
        'icon' => 'fas fa-chart-line',
        'color' => '#2ECC71',
        'url' => 'performance.php',
        'category' => 'performance'
    ),
    array(
        'name' => 'الأمان والصلاحيات',
        'description' => 'إدارة أمان النظام والصلاحيات',
        'icon' => 'fas fa-shield-alt',
        'color' => '#E74C3C',
        'url' => 'permissions.php',
        'category' => 'security'
    ),
    array(
        'name' => 'اختبار النظام',
        'description' => 'تشغيل اختبارات شاملة للنظام',
        'icon' => 'fas fa-vial',
        'color' => '#F39C12',
        'url' => '../test_odoo_system.php',
        'category' => 'testing'
    )
);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة النظام - نظام ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --odoo-primary: #714B67;
            --odoo-secondary: #8B5A8C;
        }
        
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar-odoo {
            background: linear-gradient(45deg, var(--odoo-primary), var(--odoo-secondary));
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 0;
            border-radius: 0 0 30px 30px;
            margin-bottom: 2rem;
        }
        
        .tool-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            border-left: 4px solid #dee2e6;
            text-decoration: none;
            color: inherit;
        }
        
        .tool-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
            text-decoration: none;
            color: inherit;
        }
        
        .tool-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            margin-bottom: 1rem;
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 4px solid;
            margin-bottom: 1rem;
        }
        
        .breadcrumb-odoo {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        
        .category-section {
            margin-bottom: 3rem;
        }
        
        .category-title {
            color: var(--odoo-primary);
            border-bottom: 2px solid var(--odoo-primary);
            padding-bottom: 0.5rem;
            margin-bottom: 1.5rem;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-odoo">
        <div class="container-fluid">
            <a class="navbar-brand text-white" href="../dashboard.php">
                <i class="fas fa-cube me-2"></i>
                نظام ERP - إدارة النظام
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="../dashboard.php">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
            </div>
        </div>
    </nav>

    <!-- العنوان الرئيسي -->
    <div class="admin-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-tools me-3"></i>إدارة النظام</h1>
                    <p class="lead mb-0">مركز التحكم الشامل في نظام Odoo ERP</p>
                </div>
                <div class="col-md-4 text-end">
                    <i class="fas fa-server fa-4x opacity-50"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <!-- مسار التنقل -->
        <nav class="breadcrumb-odoo">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="../dashboard.php">الرئيسية</a></li>
                <li class="breadcrumb-item active">إدارة النظام</li>
            </ol>
        </nav>

        <!-- إحصائيات النظام -->
        <div class="row mb-4">
            <div class="col-md-2">
                <div class="stats-card border-primary">
                    <h3 class="text-primary"><?php echo $system_stats['installed_modules']; ?></h3>
                    <p class="mb-0">وحدات مثبتة</p>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stats-card border-info">
                    <h3 class="text-info"><?php echo $system_stats['available_modules']; ?></h3>
                    <p class="mb-0">وحدات متاحة</p>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stats-card border-success">
                    <h3 class="text-success"><?php echo $system_stats['active_users']; ?></h3>
                    <p class="mb-0">مستخدمين نشطين</p>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stats-card border-warning">
                    <h3 class="text-warning"><?php echo $system_stats['companies']; ?></h3>
                    <p class="mb-0">شركات</p>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stats-card border-danger">
                    <h3 class="text-danger"><?php echo $system_stats['database_size']; ?></h3>
                    <p class="mb-0">حجم قاعدة البيانات</p>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stats-card border-secondary">
                    <h3 class="text-secondary">99%</h3>
                    <p class="mb-0">حالة النظام</p>
                </div>
            </div>
        </div>

        <!-- أدوات الإدارة -->
        <div class="category-section">
            <h3 class="category-title">
                <i class="fas fa-tools me-2"></i>أدوات إدارة النظام
            </h3>
            <div class="row">
                <?php foreach ($admin_tools as $tool): ?>
                    <div class="col-md-4 col-lg-3">
                        <a href="<?php echo $tool['url']; ?>" class="tool-card d-block">
                            <div class="tool-icon mx-auto" style="background-color: <?php echo $tool['color']; ?>">
                                <i class="<?php echo $tool['icon']; ?>"></i>
                            </div>
                            <h5 class="text-center"><?php echo $tool['name']; ?></h5>
                            <p class="text-muted text-center small"><?php echo $tool['description']; ?></p>
                        </a>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- روابط سريعة -->
        <div class="category-section">
            <h3 class="category-title">
                <i class="fas fa-bolt me-2"></i>روابط سريعة
            </h3>
            <div class="row">
                <div class="col-md-3">
                    <a href="../test_database_models.php" class="tool-card d-block">
                        <div class="tool-icon mx-auto" style="background-color: #17a2b8;">
                            <i class="fas fa-database"></i>
                        </div>
                        <h6 class="text-center">اختبار قاعدة البيانات</h6>
                        <p class="text-muted text-center small">فحص النماذج والعلاقات</p>
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="../test_odoo_system.php" class="tool-card d-block">
                        <div class="tool-icon mx-auto" style="background-color: #28a745;">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <h6 class="text-center">اختبار النظام</h6>
                        <p class="text-muted text-center small">فحص شامل للنظام</p>
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="../status.php" class="tool-card d-block">
                        <div class="tool-icon mx-auto" style="background-color: #ffc107;">
                            <i class="fas fa-heartbeat"></i>
                        </div>
                        <h6 class="text-center">حالة النظام</h6>
                        <p class="text-muted text-center small">مراقبة حالة الخادم</p>
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="../odoo_launcher.php" class="tool-card d-block">
                        <div class="tool-icon mx-auto" style="background-color: #dc3545;">
                            <i class="fas fa-rocket"></i>
                        </div>
                        <h6 class="text-center">مشغل Odoo</h6>
                        <p class="text-muted text-center small">إعادة تشغيل النظام</p>
                    </a>
                </div>
            </div>
        </div>

        <!-- معلومات النظام -->
        <div class="row">
            <div class="col-12">
                <div class="tool-card">
                    <h5><i class="fas fa-info-circle me-2"></i>معلومات النظام</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li><strong>إصدار Odoo:</strong> 17.0</li>
                                <li><strong>إصدار النظام:</strong> 1.0.0</li>
                                <li><strong>إصدار PHP:</strong> <?php echo PHP_VERSION; ?></li>
                                <li><strong>نظام التشغيل:</strong> <?php echo PHP_OS; ?></li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li><strong>المستخدم الحالي:</strong> <?php echo $_SESSION['username']; ?></li>
                                <li><strong>الشركة:</strong> <?php echo $_SESSION['company_name']; ?></li>
                                <li><strong>وقت تسجيل الدخول:</strong> <?php echo $_SESSION['login_time']; ?></li>
                                <li><strong>الوضع:</strong> <?php echo isset($_SESSION['demo_mode']) && $_SESSION['demo_mode'] ? 'تجريبي' : 'إنتاج'; ?></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.tool-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
