<?php
/**
 * صفحة ميزان المراجعة بأسلوب Odoo
 * Trial Balance Page - Odoo Style
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// تحميل نظام Odoo
require_once '../config/odoo_config.php';
require_once '../config/odoo_database.php';

$odoo_db = OdooDatabase::getInstance();

// تحميل النماذج
require_once '../models/BaseModel.php';
require_once '../models/AccountAccount.php';
require_once '../models/AccountFinancialReport.php';
require_once '../models/ResCurrency.php';

$account_model = new AccountAccount($odoo_db);
$report_model = new AccountFinancialReport($odoo_db);
$currency_model = new ResCurrency($odoo_db);

// معالجة المعاملات
$date_from = isset($_GET['date_from']) ? $_GET['date_from'] : date('Y-01-01');
$date_to = isset($_GET['date_to']) ? $_GET['date_to'] : date('Y-m-d');
$show_zero = isset($_GET['show_zero']) ? true : false;

// إنشاء ميزان المراجعة
$trial_balance = $report_model->generate_trial_balance($date_from, $date_to);

// فلترة الحسابات ذات الرصيد صفر إذا لم يتم طلب عرضها
if (!$show_zero) {
    $filtered_accounts = array();
    foreach ($trial_balance['accounts'] as $account_data) {
        if ($account_data['debit'] != 0 || $account_data['credit'] != 0) {
            $filtered_accounts[] = $account_data;
        }
    }
    $trial_balance['accounts'] = $filtered_accounts;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ميزان المراجعة - نظام ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --odoo-primary: #714B67;
            --odoo-secondary: #8B5A8C;
        }
        
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 0.85rem;
        }
        
        .navbar-odoo {
            background: linear-gradient(45deg, var(--odoo-primary), var(--odoo-secondary));
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .page-header {
            background: linear-gradient(45deg, #8E44AD, #9B59B6);
            color: white;
            padding: 1.5rem;
            border-radius: 15px;
            margin-bottom: 1.5rem;
        }
        
        .trial-controls {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        
        .trial-table {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
        }
        
        .trial-table th {
            background: linear-gradient(45deg, #8E44AD, #9B59B6);
            color: white;
            border: none;
            padding: 1rem;
            font-size: 0.85rem;
        }
        
        .trial-table td {
            padding: 0.8rem 1rem;
            border-color: #f8f9fa;
            vertical-align: middle;
            font-size: 0.8rem;
        }
        
        .breadcrumb-odoo {
            background: white;
            border-radius: 8px;
            padding: 0.8rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            font-size: 0.8rem;
        }
        
        .summary-card {
            background: white;
            border-radius: 12px;
            padding: 1.2rem;
            text-align: center;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
            border-left: 4px solid;
            margin-bottom: 1rem;
        }
        
        .amount-debit { color: #28a745; font-weight: bold; }
        .amount-credit { color: #dc3545; font-weight: bold; }
        .amount-balance { color: #007bff; font-weight: bold; }
        .amount-zero { color: #6c757d; }
        
        .balance-positive { color: #28a745; }
        .balance-negative { color: #dc3545; }
        
        .total-row {
            background: #f8f9fa;
            font-weight: bold;
            border-top: 2px solid #8E44AD;
        }
        
        .account-code {
            font-family: monospace;
            font-weight: bold;
        }
        
        .account-type-badge {
            font-size: 0.7rem;
            padding: 0.2rem 0.5rem;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-odoo">
        <div class="container-fluid">
            <a class="navbar-brand text-white" href="../dashboard.php">
                <i class="fas fa-cube me-2"></i>
                نظام ERP - ميزان المراجعة
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="../dashboard.php">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
                <a class="nav-link text-white" href="financial_reports.php">
                    <i class="fas fa-chart-bar me-1"></i>التقارير المالية
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-3">
        <!-- مسار التنقل -->
        <nav class="breadcrumb-odoo">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="../dashboard.php">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="#">المحاسبة</a></li>
                <li class="breadcrumb-item"><a href="financial_reports.php">التقارير المالية</a></li>
                <li class="breadcrumb-item active">ميزان المراجعة</li>
            </ol>
        </nav>

        <!-- العنوان الرئيسي -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h3><i class="fas fa-balance-scale me-2"></i>ميزان المراجعة</h3>
                    <p class="mb-0 small">عرض أرصدة جميع الحسابات للفترة المحددة</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-light btn-sm" onclick="window.print()">
                        <i class="fas fa-print me-2"></i>طباعة
                    </button>
                    <button class="btn btn-outline-light btn-sm" onclick="exportTrialBalance()">
                        <i class="fas fa-download me-2"></i>تصدير
                    </button>
                </div>
            </div>
        </div>

        <!-- أدوات التحكم -->
        <div class="trial-controls">
            <form method="GET" class="row align-items-end">
                <div class="col-md-3">
                    <label class="form-label small">من تاريخ:</label>
                    <input type="date" name="date_from" value="<?php echo $date_from; ?>" class="form-control form-control-sm">
                </div>
                <div class="col-md-3">
                    <label class="form-label small">إلى تاريخ:</label>
                    <input type="date" name="date_to" value="<?php echo $date_to; ?>" class="form-control form-control-sm">
                </div>
                <div class="col-md-3">
                    <div class="form-check mt-4">
                        <input class="form-check-input" type="checkbox" name="show_zero" id="showZero" <?php echo $show_zero ? 'checked' : ''; ?>>
                        <label class="form-check-label small" for="showZero">
                            عرض الحسابات ذات الرصيد صفر
                        </label>
                    </div>
                </div>
                <div class="col-md-3">
                    <button type="submit" class="btn btn-primary btn-sm w-100">
                        <i class="fas fa-search me-1"></i>تحديث التقرير
                    </button>
                </div>
            </form>
        </div>

        <!-- ملخص سريع -->
        <div class="row mb-3">
            <div class="col-md-3">
                <div class="summary-card border-success">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <i class="fas fa-plus-circle text-success" style="font-size: 1.5rem;"></i>
                    </div>
                    <h4 class="text-success"><?php echo number_format($trial_balance['totals']['debit'], 2); ?> ر.س</h4>
                    <p class="mb-0 small">إجمالي المدين</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="summary-card border-danger">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <i class="fas fa-minus-circle text-danger" style="font-size: 1.5rem;"></i>
                    </div>
                    <h4 class="text-danger"><?php echo number_format($trial_balance['totals']['credit'], 2); ?> ر.س</h4>
                    <p class="mb-0 small">إجمالي الدائن</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="summary-card border-primary">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <i class="fas fa-equals text-primary" style="font-size: 1.5rem;"></i>
                    </div>
                    <h4 class="text-primary"><?php echo number_format(abs($trial_balance['totals']['debit'] - $trial_balance['totals']['credit']), 2); ?> ر.س</h4>
                    <p class="mb-0 small">الفرق</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="summary-card border-info">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <i class="fas fa-list-ol text-info" style="font-size: 1.5rem;"></i>
                    </div>
                    <h4 class="text-info"><?php echo count($trial_balance['accounts']); ?></h4>
                    <p class="mb-0 small">عدد الحسابات</p>
                </div>
            </div>
        </div>

        <!-- جدول ميزان المراجعة -->
        <div class="trial-table">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>كود الحساب</th>
                        <th>اسم الحساب</th>
                        <th>نوع الحساب</th>
                        <th class="text-center">مدين</th>
                        <th class="text-center">دائن</th>
                        <th class="text-center">الرصيد</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($trial_balance['accounts'] as $account_data): 
                        $account = $account_data['account'];
                        $account_types = array(
                            'asset' => array('name' => 'أصول', 'color' => 'success'),
                            'liability' => array('name' => 'خصوم', 'color' => 'danger'),
                            'equity' => array('name' => 'حقوق ملكية', 'color' => 'primary'),
                            'income' => array('name' => 'إيرادات', 'color' => 'info'),
                            'expense' => array('name' => 'مصروفات', 'color' => 'warning')
                        );
                        $type_info = $account_types[$account['account_type']];
                    ?>
                        <tr>
                            <td>
                                <span class="account-code"><?php echo $account['code']; ?></span>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <span><?php echo $account['name']; ?></span>
                                    <?php if ($account['reconcile']): ?>
                                        <span class="badge bg-info account-type-badge ms-2">قابل للتسوية</span>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo $type_info['color']; ?> account-type-badge">
                                    <?php echo $type_info['name']; ?>
                                </span>
                            </td>
                            <td class="text-center">
                                <?php if ($account_data['debit'] > 0): ?>
                                    <span class="amount-debit"><?php echo number_format($account_data['debit'], 2); ?></span>
                                <?php else: ?>
                                    <span class="amount-zero">-</span>
                                <?php endif; ?>
                            </td>
                            <td class="text-center">
                                <?php if ($account_data['credit'] > 0): ?>
                                    <span class="amount-credit"><?php echo number_format($account_data['credit'], 2); ?></span>
                                <?php else: ?>
                                    <span class="amount-zero">-</span>
                                <?php endif; ?>
                            </td>
                            <td class="text-center">
                                <span class="amount-balance <?php echo $account_data['balance'] >= 0 ? 'balance-positive' : 'balance-negative'; ?>">
                                    <?php echo number_format($account_data['balance'], 2); ?>
                                </span>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
                <tfoot>
                    <tr class="total-row">
                        <td colspan="3"><strong>الإجمالي:</strong></td>
                        <td class="text-center">
                            <strong class="amount-debit"><?php echo number_format($trial_balance['totals']['debit'], 2); ?></strong>
                        </td>
                        <td class="text-center">
                            <strong class="amount-credit"><?php echo number_format($trial_balance['totals']['credit'], 2); ?></strong>
                        </td>
                        <td class="text-center">
                            <strong class="amount-balance">
                                <?php echo number_format($trial_balance['totals']['debit'] - $trial_balance['totals']['credit'], 2); ?>
                            </strong>
                        </td>
                    </tr>
                </tfoot>
            </table>
        </div>

        <!-- معلومات إضافية -->
        <div class="row mt-3">
            <div class="col-md-12">
                <div class="trial-controls">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-info-circle me-2"></i>معلومات التقرير</h6>
                            <ul class="list-unstyled small">
                                <li><strong>الفترة:</strong> من <?php echo $date_from; ?> إلى <?php echo $date_to; ?></li>
                                <li><strong>تاريخ الإنشاء:</strong> <?php echo date('Y-m-d H:i:s'); ?></li>
                                <li><strong>عدد الحسابات:</strong> <?php echo count($trial_balance['accounts']); ?> حساب</li>
                                <li><strong>حالة التوازن:</strong> 
                                    <?php if (abs($trial_balance['totals']['debit'] - $trial_balance['totals']['credit']) < 0.01): ?>
                                        <span class="text-success">متوازن</span>
                                    <?php else: ?>
                                        <span class="text-danger">غير متوازن</span>
                                    <?php endif; ?>
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-chart-pie me-2"></i>توزيع الحسابات</h6>
                            <div class="row">
                                <?php
                                $type_counts = array();
                                foreach ($trial_balance['accounts'] as $account_data) {
                                    $type = $account_data['account']['account_type'];
                                    if (!isset($type_counts[$type])) {
                                        $type_counts[$type] = 0;
                                    }
                                    $type_counts[$type]++;
                                }
                                
                                $account_types = array(
                                    'asset' => array('name' => 'أصول', 'color' => 'success'),
                                    'liability' => array('name' => 'خصوم', 'color' => 'danger'),
                                    'equity' => array('name' => 'حقوق ملكية', 'color' => 'primary'),
                                    'income' => array('name' => 'إيرادات', 'color' => 'info'),
                                    'expense' => array('name' => 'مصروفات', 'color' => 'warning')
                                );
                                
                                foreach ($type_counts as $type => $count):
                                    $type_info = $account_types[$type];
                                ?>
                                    <div class="col-6">
                                        <small>
                                            <span class="badge bg-<?php echo $type_info['color']; ?> me-1"><?php echo $count; ?></span>
                                            <?php echo $type_info['name']; ?>
                                        </small>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function exportTrialBalance() {
            alert('سيتم تطوير ميزة التصدير قريباً');
        }

        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const elements = document.querySelectorAll('.summary-card, .trial-table, .trial-controls');
            elements.forEach((element, index) => {
                element.style.opacity = '0';
                element.style.transform = 'translateY(15px)';
                
                setTimeout(() => {
                    element.style.transition = 'all 0.5s ease';
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
