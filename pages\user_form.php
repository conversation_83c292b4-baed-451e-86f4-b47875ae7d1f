<?php
/**
 * نموذج إضافة مستخدم - بأسلوب Odoo
 * User Form - Odoo Style
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// تضمين الملفات المطلوبة
require_once '../config/database.php';
require_once '../models/ResUsers.php';

// إنشاء اتصال قاعدة البيانات
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    $pdo = null;
}

// إنشاء نموذج المستخدم مع البيانات الوهمية
class ResUsersDemo {
    private $demo_data;
    
    public function __construct() {
        $this->demo_data = array(
            array(
                'id' => 1,
                'name' => 'أحمد محمد',
                'login' => 'ahmed.mohamed',
                'email' => '<EMAIL>',
                'phone' => '+966 50 123 4567',
                'job_title' => 'مدير النظام',
                'department' => 'تقنية المعلومات',
                'active' => 1,
                'is_admin' => 1,
                'create_date' => date('Y-m-d H:i:s')
            ),
            array(
                'id' => 2,
                'name' => 'فاطمة أحمد',
                'login' => 'fatima.ahmed',
                'email' => '<EMAIL>',
                'phone' => '+966 55 987 6543',
                'job_title' => 'محاسبة',
                'department' => 'المالية',
                'active' => 1,
                'is_admin' => 0,
                'create_date' => date('Y-m-d H:i:s')
            )
        );
    }
    
    public function create($data) {
        $new_id = max(array_column($this->demo_data, 'id')) + 1;
        $data['id'] = $new_id;
        $data['create_date'] = date('Y-m-d H:i:s');
        $this->demo_data[] = $data;
        return $new_id;
    }
    
    public function get_demo_data() {
        return $this->demo_data;
    }
}

$user_model = new ResUsersDemo();

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = isset($_POST['action']) ? $_POST['action'] : '';
    
    if ($action === 'create') {
        $data = array(
            'name' => $_POST['name'],
            'login' => $_POST['login'],
            'email' => isset($_POST['email']) ? $_POST['email'] : '',
            'password' => password_hash($_POST['password'], PASSWORD_DEFAULT),
            'phone' => isset($_POST['phone']) ? $_POST['phone'] : '',
            'job_title' => isset($_POST['job_title']) ? $_POST['job_title'] : '',
            'department' => isset($_POST['department']) ? $_POST['department'] : '',
            'is_admin' => isset($_POST['is_admin']) ? 1 : 0,
            'active' => 1
        );
        
        try {
            $user_model->create($data);
            $success_message = "تم إنشاء المستخدم بنجاح";
        } catch (Exception $e) {
            $error_message = "خطأ في إنشاء المستخدم: " . $e->getMessage();
        }
    }
}

// قائمة الأقسام
$departments = array(
    'تقنية المعلومات',
    'المالية والمحاسبة',
    'الموارد البشرية',
    'المبيعات',
    'التسويق',
    'العمليات',
    'خدمة العملاء',
    'الإدارة العامة',
    'المشتريات',
    'المخازن',
    'الجودة',
    'البحث والتطوير'
);

// قائمة المناصب
$job_titles = array(
    'مدير عام',
    'مدير قسم',
    'مشرف',
    'موظف',
    'محاسب',
    'مطور',
    'مصمم',
    'مندوب مبيعات',
    'أخصائي تسويق',
    'أخصائي موارد بشرية',
    'فني',
    'سكرتير',
    'مساعد إداري'
);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة مستخدم - نظام ERP</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --odoo-primary: #714B67;
            --odoo-secondary: #875A7B;
            --odoo-accent: #D4AF37;
            --odoo-success: #28a745;
            --odoo-info: #17a2b8;
            --odoo-warning: #ffc107;
            --odoo-danger: #dc3545;
            --odoo-light: #f8f9fa;
            --odoo-dark: #2F3349;
        }
        
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background: var(--odoo-light);
            margin: 0;
            padding: 0;
        }
        
        .main-container {
            display: flex;
            min-height: 100vh;
        }
        
        .content-area {
            flex: 1;
            padding: 0;
            overflow-x: hidden;
        }
        
        /* رأس الصفحة */
        .page-header {
            background: white;
            border-bottom: 1px solid #e9ecef;
            padding: 20px 30px;
            margin-bottom: 0;
        }
        
        .breadcrumb-odoo {
            background: none;
            padding: 0;
            margin: 0;
            font-size: 0.9rem;
        }
        
        .breadcrumb-odoo .breadcrumb-item {
            color: #6c757d;
        }
        
        .breadcrumb-odoo .breadcrumb-item.active {
            color: var(--odoo-primary);
            font-weight: 600;
        }
        
        .page-title {
            color: var(--odoo-dark);
            font-size: 1.8rem;
            font-weight: 700;
            margin: 10px 0 0 0;
        }
        
        /* أزرار الإجراءات */
        .action-buttons {
            background: white;
            border-bottom: 1px solid #e9ecef;
            padding: 15px 30px;
        }
        
        .btn-odoo {
            background: var(--odoo-primary);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 4px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 0.95rem;
        }
        
        .btn-odoo:hover {
            background: var(--odoo-secondary);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(113, 75, 103, 0.3);
        }
        
        .btn-outline-odoo {
            background: transparent;
            border: 1px solid var(--odoo-primary);
            color: var(--odoo-primary);
        }
        
        .btn-outline-odoo:hover {
            background: var(--odoo-primary);
            color: white;
        }
        
        .btn-secondary-odoo {
            background: #6c757d;
            border: none;
            color: white;
        }
        
        .btn-secondary-odoo:hover {
            background: #5a6268;
            color: white;
        }
        
        /* منطقة النموذج */
        .form-container {
            padding: 30px;
        }
        
        .form-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
            overflow: hidden;
        }
        
        .form-header {
            background: linear-gradient(135deg, var(--odoo-info), #20c997);
            color: white;
            padding: 20px 30px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .form-header h4 {
            margin: 0;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .form-body {
            padding: 30px;
        }
        
        .form-section {
            margin-bottom: 30px;
        }
        
        .section-title {
            color: var(--odoo-dark);
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid var(--odoo-info);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .form-label {
            font-weight: 500;
            color: var(--odoo-dark);
            margin-bottom: 8px;
            font-size: 0.9rem;
        }
        
        .required-field::after {
            content: " *";
            color: var(--odoo-danger);
            font-weight: bold;
        }
        
        .form-control, .form-select {
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px 12px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--odoo-info);
            box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.25);
        }
        
        .form-text {
            font-size: 0.8rem;
            color: #6c757d;
            margin-top: 5px;
        }
        
        .password-strength {
            margin-top: 5px;
        }
        
        .strength-bar {
            height: 4px;
            border-radius: 2px;
            background: #e9ecef;
            overflow: hidden;
        }
        
        .strength-fill {
            height: 100%;
            transition: all 0.3s ease;
            border-radius: 2px;
        }
        
        .strength-weak { background: var(--odoo-danger); width: 25%; }
        .strength-fair { background: var(--odoo-warning); width: 50%; }
        .strength-good { background: var(--odoo-info); width: 75%; }
        .strength-strong { background: var(--odoo-success); width: 100%; }
        
        /* رسائل التنبيه */
        .alert {
            border: none;
            border-radius: 6px;
            padding: 15px 20px;
            margin-bottom: 20px;
            font-size: 0.9rem;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border-left: 4px solid #28a745;
        }
        
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border-left: 4px solid #dc3545;
        }
        
        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 768px) {
            .page-header, .action-buttons, .form-container {
                padding: 15px 20px;
            }
            
            .form-body {
                padding: 20px;
            }
            
            .form-header {
                padding: 15px 20px;
            }
        }
        
        /* تحسينات إضافية */
        .input-group-text {
            background: var(--odoo-light);
            border-color: #dee2e6;
            color: var(--odoo-dark);
        }
        
        .avatar-upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 50%;
            width: 120px;
            height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
            margin: 0 auto;
        }
        
        .avatar-upload-area:hover {
            border-color: var(--odoo-info);
            background: rgba(23, 162, 184, 0.05);
        }
        
        .avatar-icon {
            font-size: 2rem;
            color: var(--odoo-info);
        }
        
        .btn-group-actions {
            gap: 10px;
            justify-content: flex-end;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            margin-top: 30px;
        }
        
        .permission-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .permission-title {
            font-weight: 600;
            color: var(--odoo-dark);
            margin-bottom: 10px;
        }
        
        .form-check {
            margin-bottom: 8px;
        }
        
        .form-check-input:checked {
            background-color: var(--odoo-info);
            border-color: var(--odoo-info);
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- الشريط الجانبي -->
        <?php include '../includes/sidebar.php'; ?>
        
        <!-- منطقة المحتوى -->
        <div class="content-area">
            <!-- رأس الصفحة -->
            <div class="page-header">
                <nav class="breadcrumb-odoo">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="../dashboard.php">
                                <i class="fas fa-home"></i> الرئيسية
                            </a>
                        </li>
                        <li class="breadcrumb-item">الإعدادات</li>
                        <li class="breadcrumb-item active">إضافة مستخدم</li>
                    </ol>
                </nav>
                <h1 class="page-title">
                    <i class="fas fa-user-plus me-2"></i>إضافة مستخدم جديد
                </h1>
            </div>
            
            <!-- أزرار الإجراءات -->
            <div class="action-buttons">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="d-flex gap-2">
                        <button type="submit" form="userForm" class="btn-odoo">
                            <i class="fas fa-save"></i>
                            حفظ المستخدم
                        </button>
                        <button type="button" class="btn-outline-odoo" onclick="resetForm()">
                            <i class="fas fa-undo"></i>
                            إعادة تعيين
                        </button>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <a href="users_list.php" class="btn-secondary-odoo">
                            <i class="fas fa-list"></i>
                            قائمة المستخدمين
                        </a>
                        <a href="../dashboard.php" class="btn-secondary-odoo">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- رسائل التنبيه -->
            <?php if (isset($success_message)): ?>
                <div class="alert alert-success mx-4">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo $success_message; ?>
                </div>
            <?php endif; ?>
            
            <?php if (isset($error_message)): ?>
                <div class="alert alert-danger mx-4">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <?php echo $error_message; ?>
                </div>
            <?php endif; ?>

            <!-- نموذج إضافة المستخدم -->
            <div class="form-container">
                <div class="form-card">
                    <div class="form-header">
                        <h4>
                            <i class="fas fa-user-plus"></i>
                            معلومات المستخدم
                        </h4>
                    </div>

                    <form id="userForm" method="POST" enctype="multipart/form-data">
                        <input type="hidden" name="action" value="create">

                        <div class="form-body">
                            <!-- القسم الأول: المعلومات الشخصية -->
                            <div class="form-section">
                                <h5 class="section-title">
                                    <i class="fas fa-user"></i>
                                    المعلومات الشخصية
                                </h5>

                                <div class="row">
                                    <div class="col-md-3 text-center">
                                        <!-- صورة المستخدم -->
                                        <div class="mb-3">
                                            <div class="avatar-upload-area" onclick="document.getElementById('avatarFile').click()">
                                                <div id="avatarPreview">
                                                    <i class="fas fa-camera avatar-icon"></i>
                                                </div>
                                            </div>
                                            <input type="file" id="avatarFile" name="avatar" accept="image/*" style="display: none;" onchange="previewAvatar(this)">
                                            <small class="text-muted">انقر لرفع الصورة</small>
                                        </div>
                                    </div>
                                    <div class="col-md-9">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label required-field">الاسم الكامل</label>
                                                    <input type="text" class="form-control" name="name" id="userName" required>
                                                    <div class="form-text">الاسم الأول والأخير</div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label required-field">اسم المستخدم</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text">
                                                            <i class="fas fa-at"></i>
                                                        </span>
                                                        <input type="text" class="form-control" name="login" id="userLogin" required>
                                                    </div>
                                                    <div class="form-text">اسم فريد للدخول للنظام</div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label required-field">البريد الإلكتروني</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text">
                                                            <i class="fas fa-envelope"></i>
                                                        </span>
                                                        <input type="email" class="form-control" name="email" id="userEmail" required>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">رقم الهاتف</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text">
                                                            <i class="fas fa-phone"></i>
                                                        </span>
                                                        <input type="tel" class="form-control" name="phone" id="userPhone">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- القسم الثاني: كلمة المرور -->
                            <div class="form-section">
                                <h5 class="section-title">
                                    <i class="fas fa-lock"></i>
                                    كلمة المرور
                                </h5>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label required-field">كلمة المرور</label>
                                            <div class="input-group">
                                                <span class="input-group-text">
                                                    <i class="fas fa-key"></i>
                                                </span>
                                                <input type="password" class="form-control" name="password" id="userPassword" required onkeyup="checkPasswordStrength()">
                                                <button type="button" class="btn btn-outline-secondary" onclick="togglePassword('userPassword')">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                            <div class="password-strength">
                                                <div class="strength-bar">
                                                    <div id="strengthFill" class="strength-fill"></div>
                                                </div>
                                                <small id="strengthText" class="text-muted">أدخل كلمة مرور قوية</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label required-field">تأكيد كلمة المرور</label>
                                            <div class="input-group">
                                                <span class="input-group-text">
                                                    <i class="fas fa-key"></i>
                                                </span>
                                                <input type="password" class="form-control" name="password_confirm" id="passwordConfirm" required onkeyup="checkPasswordMatch()">
                                                <button type="button" class="btn btn-outline-secondary" onclick="togglePassword('passwordConfirm')">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                            <div id="passwordMatch" class="form-text"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- القسم الثالث: معلومات العمل -->
                            <div class="form-section">
                                <h5 class="section-title">
                                    <i class="fas fa-briefcase"></i>
                                    معلومات العمل
                                </h5>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">المنصب الوظيفي</label>
                                            <select class="form-select" name="job_title" id="jobTitle">
                                                <option value="">اختر المنصب</option>
                                                <?php foreach ($job_titles as $title): ?>
                                                    <option value="<?php echo htmlspecialchars($title); ?>">
                                                        <?php echo htmlspecialchars($title); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">القسم</label>
                                            <select class="form-select" name="department" id="department">
                                                <option value="">اختر القسم</option>
                                                <?php foreach ($departments as $dept): ?>
                                                    <option value="<?php echo htmlspecialchars($dept); ?>">
                                                        <?php echo htmlspecialchars($dept); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- القسم الرابع: الصلاحيات -->
                            <div class="form-section">
                                <h5 class="section-title">
                                    <i class="fas fa-shield-alt"></i>
                                    الصلاحيات والأدوار
                                </h5>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="permission-card">
                                            <div class="permission-title">صلاحيات النظام</div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="is_admin" id="isAdmin">
                                                <label class="form-check-label" for="isAdmin">
                                                    <strong>مدير النظام</strong>
                                                    <br><small class="text-muted">صلاحيات كاملة لإدارة النظام</small>
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="can_manage_users" id="canManageUsers">
                                                <label class="form-check-label" for="canManageUsers">
                                                    إدارة المستخدمين
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="can_manage_settings" id="canManageSettings">
                                                <label class="form-check-label" for="canManageSettings">
                                                    إدارة الإعدادات
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="permission-card">
                                            <div class="permission-title">صلاحيات الوحدات</div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="can_access_accounting" id="canAccessAccounting" checked>
                                                <label class="form-check-label" for="canAccessAccounting">
                                                    وحدة المحاسبة
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="can_access_sales" id="canAccessSales" checked>
                                                <label class="form-check-label" for="canAccessSales">
                                                    وحدة المبيعات
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="can_access_purchases" id="canAccessPurchases">
                                                <label class="form-check-label" for="canAccessPurchases">
                                                    وحدة المشتريات
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="can_access_inventory" id="canAccessInventory">
                                                <label class="form-check-label" for="canAccessInventory">
                                                    وحدة المخزون
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- أزرار الإجراءات -->
                            <div class="d-flex btn-group-actions">
                                <a href="../dashboard.php" class="btn btn-secondary-odoo">
                                    <i class="fas fa-times me-1"></i>إلغاء
                                </a>
                                <button type="button" class="btn btn-outline-odoo" onclick="resetForm()">
                                    <i class="fas fa-undo me-1"></i>إعادة تعيين
                                </button>
                                <button type="submit" class="btn-odoo">
                                    <i class="fas fa-save me-1"></i>حفظ المستخدم
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // معاينة الصورة الشخصية
        function previewAvatar(input) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();

                reader.onload = function(e) {
                    document.getElementById('avatarPreview').innerHTML =
                        '<img src="' + e.target.result + '" alt="صورة المستخدم" style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%;">';
                };

                reader.readAsDataURL(input.files[0]);
            }
        }

        // إظهار/إخفاء كلمة المرور
        function togglePassword(fieldId) {
            const field = document.getElementById(fieldId);
            const button = field.nextElementSibling;
            const icon = button.querySelector('i');

            if (field.type === 'password') {
                field.type = 'text';
                icon.className = 'fas fa-eye-slash';
            } else {
                field.type = 'password';
                icon.className = 'fas fa-eye';
            }
        }

        // فحص قوة كلمة المرور
        function checkPasswordStrength() {
            const password = document.getElementById('userPassword').value;
            const strengthFill = document.getElementById('strengthFill');
            const strengthText = document.getElementById('strengthText');

            let strength = 0;
            let text = '';
            let className = '';

            if (password.length >= 8) strength++;
            if (/[a-z]/.test(password)) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^A-Za-z0-9]/.test(password)) strength++;

            switch (strength) {
                case 0:
                case 1:
                    text = 'ضعيفة جداً';
                    className = 'strength-weak';
                    break;
                case 2:
                    text = 'ضعيفة';
                    className = 'strength-weak';
                    break;
                case 3:
                    text = 'متوسطة';
                    className = 'strength-fair';
                    break;
                case 4:
                    text = 'جيدة';
                    className = 'strength-good';
                    break;
                case 5:
                    text = 'قوية جداً';
                    className = 'strength-strong';
                    break;
            }

            strengthFill.className = 'strength-fill ' + className;
            strengthText.textContent = text;
        }

        // فحص تطابق كلمة المرور
        function checkPasswordMatch() {
            const password = document.getElementById('userPassword').value;
            const confirm = document.getElementById('passwordConfirm').value;
            const matchDiv = document.getElementById('passwordMatch');

            if (confirm === '') {
                matchDiv.textContent = '';
                matchDiv.className = 'form-text';
            } else if (password === confirm) {
                matchDiv.textContent = '✓ كلمات المرور متطابقة';
                matchDiv.className = 'form-text text-success';
            } else {
                matchDiv.textContent = '✗ كلمات المرور غير متطابقة';
                matchDiv.className = 'form-text text-danger';
            }
        }

        // إعادة تعيين النموذج
        function resetForm() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟')) {
                document.getElementById('userForm').reset();
                document.getElementById('avatarPreview').innerHTML = '<i class="fas fa-camera avatar-icon"></i>';
                document.getElementById('strengthFill').className = 'strength-fill';
                document.getElementById('strengthText').textContent = 'أدخل كلمة مرور قوية';
                document.getElementById('passwordMatch').textContent = '';
            }
        }

        // التحقق من صحة النموذج
        document.getElementById('userForm').addEventListener('submit', function(e) {
            const name = document.getElementById('userName').value.trim();
            const login = document.getElementById('userLogin').value.trim();
            const email = document.getElementById('userEmail').value.trim();
            const password = document.getElementById('userPassword').value;
            const confirm = document.getElementById('passwordConfirm').value;

            if (!name) {
                e.preventDefault();
                alert('يرجى إدخال الاسم الكامل');
                document.getElementById('userName').focus();
                return false;
            }

            if (!login) {
                e.preventDefault();
                alert('يرجى إدخال اسم المستخدم');
                document.getElementById('userLogin').focus();
                return false;
            }

            if (!email) {
                e.preventDefault();
                alert('يرجى إدخال البريد الإلكتروني');
                document.getElementById('userEmail').focus();
                return false;
            }

            if (password.length < 8) {
                e.preventDefault();
                alert('كلمة المرور يجب أن تكون 8 أحرف على الأقل');
                document.getElementById('userPassword').focus();
                return false;
            }

            if (password !== confirm) {
                e.preventDefault();
                alert('كلمات المرور غير متطابقة');
                document.getElementById('passwordConfirm').focus();
                return false;
            }

            // تأكيد الحفظ
            if (!confirm('هل أنت متأكد من حفظ بيانات المستخدم؟')) {
                e.preventDefault();
                return false;
            }
        });

        // تحسينات UX
        document.addEventListener('DOMContentLoaded', function() {
            // إنشاء اسم المستخدم تلقائياً من الاسم
            const nameInput = document.getElementById('userName');
            const loginInput = document.getElementById('userLogin');

            nameInput.addEventListener('input', function(e) {
                if (!loginInput.value) {
                    const name = e.target.value.toLowerCase()
                        .replace(/\s+/g, '.')
                        .replace(/[^a-z0-9.]/g, '');
                    loginInput.value = name;
                }
            });

            // تنسيق رقم الهاتف
            const phoneInput = document.getElementById('userPhone');
            phoneInput.addEventListener('input', function(e) {
                let value = e.target.value.replace(/\D/g, '');
                if (value.startsWith('966')) {
                    value = '+' + value;
                } else if (value.startsWith('0')) {
                    value = '+966' + value.substring(1);
                }
                e.target.value = value;
            });

            // إدارة صلاحيات المدير
            const adminCheckbox = document.getElementById('isAdmin');
            const otherCheckboxes = document.querySelectorAll('input[type="checkbox"]:not(#isAdmin)');

            adminCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    otherCheckboxes.forEach(cb => cb.checked = true);
                }
            });
        });
    </script>
</body>
</html>
