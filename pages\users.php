<?php
/**
 * صفحة إدارة المستخدمين بأسلوب Odoo
 * Odoo-Style User Management Page
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// التحقق من الصلاحيات
if (!in_array('admin', $_SESSION['groups'] ?? array())) {
    header('Location: ../dashboard.php?error=access_denied');
    exit();
}

// تحميل نظام قاعدة البيانات
require_once '../config/odoo_config.php';
require_once '../config/odoo_database.php';

$db = OdooDatabase::getInstance();
$message = '';
$message_type = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        switch ($action) {
            case 'create':
                $name = $_POST['name'] ?? '';
                $email = $_POST['email'] ?? '';
                $password = $_POST['password'] ?? '';
                $groups = $_POST['groups'] ?? array();
                
                if (empty($name) || empty($email) || empty($password)) {
                    throw new Exception('جميع الحقول مطلوبة');
                }
                
                // تشفير كلمة المرور
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                
                // إدراج المستخدم (في الوضع التجريبي)
                $message = "تم إنشاء المستخدم '{$name}' بنجاح";
                $message_type = 'success';
                break;
                
            case 'update':
                $user_id = $_POST['user_id'] ?? '';
                $name = $_POST['name'] ?? '';
                $email = $_POST['email'] ?? '';
                $groups = $_POST['groups'] ?? array();
                
                $message = "تم تحديث المستخدم بنجاح";
                $message_type = 'success';
                break;
                
            case 'delete':
                $user_id = $_POST['user_id'] ?? '';
                
                if ($user_id == $_SESSION['user_id']) {
                    throw new Exception('لا يمكن حذف المستخدم الحالي');
                }
                
                $message = "تم حذف المستخدم بنجاح";
                $message_type = 'success';
                break;
                
            default:
                throw new Exception('إجراء غير صحيح');
        }
    } catch (Exception $e) {
        $message = 'خطأ: ' . $e->getMessage();
        $message_type = 'error';
    }
}

// بيانات المستخدمين التجريبية
$users = array(
    array(
        'id' => 1,
        'name' => 'مدير النظام',
        'email' => '<EMAIL>',
        'groups' => array('admin', 'manager', 'user'),
        'active' => true,
        'last_login' => '2024-01-15 10:30:00',
        'company_id' => 1,
        'company_name' => 'شركتي'
    ),
    array(
        'id' => 2,
        'name' => 'أحمد محمد',
        'email' => '<EMAIL>',
        'groups' => array('manager', 'user'),
        'active' => true,
        'last_login' => '2024-01-15 09:15:00',
        'company_id' => 1,
        'company_name' => 'شركتي'
    ),
    array(
        'id' => 3,
        'name' => 'فاطمة علي',
        'email' => '<EMAIL>',
        'groups' => array('user'),
        'active' => true,
        'last_login' => '2024-01-15 08:45:00',
        'company_id' => 1,
        'company_name' => 'شركتي'
    ),
    array(
        'id' => 4,
        'name' => 'محمد سالم',
        'email' => '<EMAIL>',
        'groups' => array('user'),
        'active' => false,
        'last_login' => '2024-01-10 14:20:00',
        'company_id' => 1,
        'company_name' => 'شركتي'
    )
);

// مجموعات المستخدمين المتاحة
$available_groups = array(
    'admin' => array('name' => 'مدير النظام', 'color' => 'danger'),
    'manager' => array('name' => 'مدير', 'color' => 'warning'),
    'user' => array('name' => 'مستخدم', 'color' => 'primary'),
    'readonly' => array('name' => 'قراءة فقط', 'color' => 'secondary')
);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين - نظام ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --odoo-primary: #714B67;
            --odoo-secondary: #8B5A8C;
        }
        
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar-odoo {
            background: linear-gradient(45deg, var(--odoo-primary), var(--odoo-secondary));
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .page-header {
            background: linear-gradient(45deg, var(--odoo-primary), var(--odoo-secondary));
            color: white;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
        }
        
        .user-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }
        
        .user-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(45deg, var(--odoo-primary), var(--odoo-secondary));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
        }
        
        .btn-odoo {
            background: linear-gradient(45deg, var(--odoo-primary), var(--odoo-secondary));
            border: none;
            color: white;
            padding: 0.5rem 1.5rem;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        
        .btn-odoo:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(113, 75, 103, 0.3);
            color: white;
        }
        
        .breadcrumb-odoo {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 4px solid;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-odoo">
        <div class="container-fluid">
            <a class="navbar-brand text-white" href="../dashboard.php">
                <i class="fas fa-cube me-2"></i>
                نظام ERP - إدارة المستخدمين
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="../dashboard.php">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
                <a class="nav-link text-white" href="system_admin.php">
                    <i class="fas fa-tools me-1"></i>إدارة النظام
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- مسار التنقل -->
        <nav class="breadcrumb-odoo">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="../dashboard.php">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="system_admin.php">إدارة النظام</a></li>
                <li class="breadcrumb-item active">إدارة المستخدمين</li>
            </ol>
        </nav>

        <!-- العنوان الرئيسي -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2><i class="fas fa-users-cog me-2"></i>إدارة المستخدمين</h2>
                    <p class="mb-0">إضافة وتعديل وإدارة مستخدمي النظام</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-light" data-bs-toggle="modal" data-bs-target="#addUserModal">
                        <i class="fas fa-user-plus me-2"></i>إضافة مستخدم جديد
                    </button>
                </div>
            </div>
        </div>

        <!-- رسائل النظام -->
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $message_type === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
                <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- إحصائيات المستخدمين -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card border-success">
                    <h3 class="text-success"><?php echo count(array_filter($users, function($u) { return $u['active']; })); ?></h3>
                    <p class="mb-0">مستخدمين نشطين</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card border-warning">
                    <h3 class="text-warning"><?php echo count(array_filter($users, function($u) { return !$u['active']; })); ?></h3>
                    <p class="mb-0">مستخدمين غير نشطين</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card border-danger">
                    <h3 class="text-danger"><?php echo count(array_filter($users, function($u) { return in_array('admin', $u['groups']); })); ?></h3>
                    <p class="mb-0">مديري النظام</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card border-primary">
                    <h3 class="text-primary"><?php echo count($users); ?></h3>
                    <p class="mb-0">إجمالي المستخدمين</p>
                </div>
            </div>
        </div>

        <!-- قائمة المستخدمين -->
        <div class="row">
            <?php foreach ($users as $user): ?>
                <div class="col-md-6 col-lg-4">
                    <div class="user-card">
                        <div class="d-flex align-items-center mb-3">
                            <div class="user-avatar me-3">
                                <?php echo strtoupper(substr($user['name'], 0, 2)); ?>
                            </div>
                            <div class="flex-grow-1">
                                <h5 class="mb-1"><?php echo $user['name']; ?></h5>
                                <p class="text-muted mb-0"><?php echo $user['email']; ?></p>
                            </div>
                            <div>
                                <?php if ($user['active']): ?>
                                    <span class="badge bg-success">نشط</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">غير نشط</span>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <small class="text-muted">المجموعات:</small><br>
                            <?php foreach ($user['groups'] as $group): ?>
                                <span class="badge bg-<?php echo $available_groups[$group]['color']; ?> me-1">
                                    <?php echo $available_groups[$group]['name']; ?>
                                </span>
                            <?php endforeach; ?>
                        </div>
                        
                        <div class="mb-3">
                            <small class="text-muted">
                                <i class="fas fa-building me-1"></i><?php echo $user['company_name']; ?><br>
                                <i class="fas fa-clock me-1"></i>آخر دخول: <?php echo date('Y-m-d H:i', strtotime($user['last_login'])); ?>
                            </small>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <button class="btn btn-outline-primary btn-sm" onclick="editUser(<?php echo $user['id']; ?>)">
                                <i class="fas fa-edit me-1"></i>تعديل
                            </button>
                            <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                <button class="btn btn-outline-danger btn-sm" onclick="deleteUser(<?php echo $user['id']; ?>, '<?php echo $user['name']; ?>')">
                                    <i class="fas fa-trash me-1"></i>حذف
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>

    <!-- نافذة إضافة مستخدم -->
    <div class="modal fade" id="addUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة مستخدم جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="create">
                        
                        <div class="mb-3">
                            <label class="form-label">الاسم</label>
                            <input type="text" class="form-control" name="name" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" name="email" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">كلمة المرور</label>
                            <input type="password" class="form-control" name="password" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">المجموعات</label>
                            <?php foreach ($available_groups as $group_key => $group): ?>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="groups[]" value="<?php echo $group_key; ?>" id="group_<?php echo $group_key; ?>">
                                    <label class="form-check-label" for="group_<?php echo $group_key; ?>">
                                        <?php echo $group['name']; ?>
                                    </label>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-odoo">إضافة المستخدم</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editUser(userId) {
            alert('سيتم تطوير نافذة التعديل قريباً');
        }
        
        function deleteUser(userId, userName) {
            if (confirm('هل أنت متأكد من حذف المستخدم "' + userName + '"؟')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="user_id" value="${userId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
        
        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.user-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
