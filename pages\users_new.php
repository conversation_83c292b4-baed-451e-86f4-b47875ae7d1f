<?php
/**
 * صفحة إدارة المستخدمين المحسنة بأسلوب Odoo
 * Enhanced Users Management Page - Odoo Style
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// التحقق من الصلاحيات
$user_groups = isset($_SESSION['groups']) ? $_SESSION['groups'] : array();
if (!in_array('admin', $user_groups)) {
    header('Location: ../dashboard.php?error=access_denied');
    exit();
}

// تحميل نظام قاعدة البيانات
require_once '../config/odoo_config.php';
require_once '../config/odoo_database.php';

$db = OdooDatabase::getInstance();
$message = '';
$message_type = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = isset($_POST['action']) ? $_POST['action'] : '';

    try {
        switch ($action) {
            case 'create':
                $name = isset($_POST['name']) ? $_POST['name'] : '';
                $email = isset($_POST['email']) ? $_POST['email'] : '';
                $password = isset($_POST['password']) ? $_POST['password'] : '';
                $groups = isset($_POST['groups']) ? $_POST['groups'] : array();

                if (empty($name) || empty($email) || empty($password)) {
                    throw new Exception('جميع الحقول مطلوبة');
                }

                $message = "تم إنشاء المستخدم '{$name}' بنجاح";
                $message_type = 'success';
                break;

            case 'update':
                $user_id = isset($_POST['user_id']) ? $_POST['user_id'] : '';
                $name = isset($_POST['name']) ? $_POST['name'] : '';
                $email = isset($_POST['email']) ? $_POST['email'] : '';
                $groups = isset($_POST['groups']) ? $_POST['groups'] : array();

                $message = "تم تحديث المستخدم بنجاح";
                $message_type = 'success';
                break;

            case 'delete':
                $user_id = isset($_POST['user_id']) ? $_POST['user_id'] : '';

                if ($user_id == $_SESSION['user_id']) {
                    throw new Exception('لا يمكن حذف المستخدم الحالي');
                }

                $message = "تم حذف المستخدم بنجاح";
                $message_type = 'success';
                break;

            default:
                throw new Exception('إجراء غير صحيح');
        }
    } catch (Exception $e) {
        $message = 'خطأ: ' . $e->getMessage();
        $message_type = 'error';
    }
}

// بيانات المستخدمين التجريبية
$users = array(
    array(
        'id' => 1,
        'name' => 'مدير النظام',
        'email' => '<EMAIL>',
        'groups' => array('admin', 'manager', 'user'),
        'active' => true,
        'last_login' => '2024-01-15 10:30:00',
        'company_id' => 1,
        'company_name' => 'شركتي',
        'phone' => '+966501234567',
        'department' => 'إدارة النظام'
    ),
    array(
        'id' => 2,
        'name' => 'أحمد محمد',
        'email' => '<EMAIL>',
        'groups' => array('manager', 'user'),
        'active' => true,
        'last_login' => '2024-01-15 09:15:00',
        'company_id' => 1,
        'company_name' => 'شركتي',
        'phone' => '+966502345678',
        'department' => 'المبيعات'
    ),
    array(
        'id' => 3,
        'name' => 'فاطمة علي',
        'email' => '<EMAIL>',
        'groups' => array('user'),
        'active' => true,
        'last_login' => '2024-01-15 08:45:00',
        'company_id' => 1,
        'company_name' => 'شركتي',
        'phone' => '+966503456789',
        'department' => 'المحاسبة'
    ),
    array(
        'id' => 4,
        'name' => 'محمد سالم',
        'email' => '<EMAIL>',
        'groups' => array('user'),
        'active' => false,
        'last_login' => '2024-01-10 14:20:00',
        'company_id' => 1,
        'company_name' => 'شركتي',
        'phone' => '+966504567890',
        'department' => 'المحاسبة'
    ),
    array(
        'id' => 5,
        'name' => 'سارة أحمد',
        'email' => '<EMAIL>',
        'groups' => array('manager', 'user'),
        'active' => true,
        'last_login' => '2024-01-14 16:30:00',
        'company_id' => 1,
        'company_name' => 'شركتي',
        'phone' => '+966505678901',
        'department' => 'الموارد البشرية'
    )
);

// مجموعات المستخدمين المتاحة
$available_groups = array(
    'admin' => array('name' => 'مدير النظام', 'color' => 'danger'),
    'manager' => array('name' => 'مدير', 'color' => 'warning'),
    'user' => array('name' => 'مستخدم', 'color' => 'primary'),
    'readonly' => array('name' => 'قراءة فقط', 'color' => 'secondary')
);

// فلترة حسب النوع
$filter_type = isset($_GET['filter']) ? $_GET['filter'] : 'all';
$filtered_users = $users;

if ($filter_type === 'active') {
    $filtered_users = array();
    foreach ($users as $user) {
        if ($user['active']) {
            $filtered_users[] = $user;
        }
    }
} elseif ($filter_type === 'inactive') {
    $filtered_users = array();
    foreach ($users as $user) {
        if (!$user['active']) {
            $filtered_users[] = $user;
        }
    }
} elseif ($filter_type === 'admins') {
    $filtered_users = array();
    foreach ($users as $user) {
        if (in_array('admin', $user['groups'])) {
            $filtered_users[] = $user;
        }
    }
}

// طريقة العرض المحددة
$view_mode = isset($_GET['view']) ? $_GET['view'] : 'cards';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين - نظام ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --odoo-primary: #714B67;
            --odoo-secondary: #8B5A8C;
        }

        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 0.85rem;
        }

        .navbar-odoo {
            background: linear-gradient(45deg, var(--odoo-primary), var(--odoo-secondary));
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .page-header {
            background: linear-gradient(45deg, var(--odoo-primary), var(--odoo-secondary));
            color: white;
            padding: 1.5rem;
            border-radius: 15px;
            margin-bottom: 1.5rem;
        }

        .view-controls {
            background: white;
            border-radius: 10px;
            padding: 0.8rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }

        .view-btn, .filter-btn {
            border: 1px solid #dee2e6;
            background: white;
            color: #6c757d;
            padding: 0.4rem 0.8rem;
            border-radius: 5px;
            transition: all 0.3s ease;
            text-decoration: none;
            font-size: 0.8rem;
        }

        .view-btn.active, .filter-btn.active {
            background: var(--odoo-primary);
            color: white;
            border-color: var(--odoo-primary);
        }

        .view-btn:hover, .filter-btn:hover {
            background: var(--odoo-secondary);
            color: white;
            border-color: var(--odoo-secondary);
            text-decoration: none;
        }

        .user-card {
            background: white;
            border-radius: 12px;
            padding: 1.2rem;
            margin-bottom: 0.8rem;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
            transition: all 0.3s ease;
            border-left: 3px solid var(--odoo-primary);
        }

        .user-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.12);
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(45deg, var(--odoo-primary), var(--odoo-secondary));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            font-weight: bold;
        }

        .table-odoo {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
        }

        .table-odoo th {
            background: linear-gradient(45deg, var(--odoo-primary), var(--odoo-secondary));
            color: white;
            border: none;
            padding: 0.8rem;
            font-size: 0.8rem;
        }

        .table-odoo td {
            padding: 0.8rem;
            border-color: #f8f9fa;
            vertical-align: middle;
            font-size: 0.8rem;
        }

        .btn-odoo {
            background: linear-gradient(45deg, var(--odoo-primary), var(--odoo-secondary));
            border: none;
            color: white;
            padding: 0.4rem 1rem;
            border-radius: 20px;
            transition: all 0.3s ease;
            font-size: 0.8rem;
        }

        .btn-odoo:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(113, 75, 103, 0.3);
            color: white;
        }

        .breadcrumb-odoo {
            background: white;
            border-radius: 8px;
            padding: 0.8rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            font-size: 0.8rem;
        }

        .stats-card {
            background: white;
            border-radius: 12px;
            padding: 1.2rem;
            text-align: center;
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
            border-left: 3px solid;
            margin-bottom: 1rem;
        }

        .stats-card h3 {
            font-size: 1.8rem;
            margin-bottom: 0.5rem;
        }

        .stats-card p {
            font-size: 0.75rem;
            margin-bottom: 0;
        }

        .search-box {
            background: white;
            border-radius: 20px;
            border: 1px solid #dee2e6;
            padding: 0.4rem 1rem;
            transition: all 0.3s ease;
            font-size: 0.8rem;
        }

        .search-box:focus {
            border-color: var(--odoo-primary);
            box-shadow: 0 0 0 0.15rem rgba(113, 75, 103, 0.25);
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-odoo">
        <div class="container-fluid">
            <a class="navbar-brand text-white" href="../dashboard.php">
                <i class="fas fa-cube me-2"></i>
                نظام ERP - إدارة المستخدمين
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="../dashboard.php">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
                <a class="nav-link text-white" href="system_admin.php">
                    <i class="fas fa-tools me-1"></i>إدارة النظام
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-3">
        <!-- مسار التنقل -->
        <nav class="breadcrumb-odoo">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="../dashboard.php">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="system_admin.php">إدارة النظام</a></li>
                <li class="breadcrumb-item active">إدارة المستخدمين</li>
            </ol>
        </nav>

        <!-- العنوان الرئيسي -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h3><i class="fas fa-users-cog me-2"></i>إدارة المستخدمين</h3>
                    <p class="mb-0 small">إضافة وتعديل وإدارة مستخدمي النظام والصلاحيات</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#addUserModal">
                        <i class="fas fa-user-plus me-2"></i>إضافة مستخدم جديد
                    </button>
                </div>
            </div>
        </div>

        <!-- رسائل النظام -->
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $message_type === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
                <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- إحصائيات المستخدمين -->
        <div class="row mb-3">
            <div class="col-md-3">
                <div class="stats-card border-success">
                    <h3 class="text-success"><?php
                        $active_count = 0;
                        foreach($users as $u) { if($u['active']) $active_count++; }
                        echo $active_count;
                    ?></h3>
                    <p class="mb-0">مستخدمين نشطين</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card border-warning">
                    <h3 class="text-warning"><?php
                        $inactive_count = 0;
                        foreach($users as $u) { if(!$u['active']) $inactive_count++; }
                        echo $inactive_count;
                    ?></h3>
                    <p class="mb-0">مستخدمين غير نشطين</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card border-danger">
                    <h3 class="text-danger"><?php
                        $admin_count = 0;
                        foreach($users as $u) { if(in_array('admin', $u['groups'])) $admin_count++; }
                        echo $admin_count;
                    ?></h3>
                    <p class="mb-0">مديري النظام</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card border-primary">
                    <h3 class="text-primary"><?php echo count($users); ?></h3>
                    <p class="mb-0">إجمالي المستخدمين</p>
                </div>
            </div>
        </div>

        <!-- أدوات التحكم والعرض -->
        <div class="view-controls">
            <div class="row align-items-center">
                <div class="col-md-4">
                    <div class="btn-group" role="group">
                        <a href="?view=cards&filter=<?php echo $filter_type; ?>" class="view-btn <?php echo $view_mode === 'cards' ? 'active' : ''; ?>">
                            <i class="fas fa-th-large me-1"></i>بطاقات
                        </a>
                        <a href="?view=table&filter=<?php echo $filter_type; ?>" class="view-btn <?php echo $view_mode === 'table' ? 'active' : ''; ?>">
                            <i class="fas fa-table me-1"></i>جدول
                        </a>
                        <a href="?view=list&filter=<?php echo $filter_type; ?>" class="view-btn <?php echo $view_mode === 'list' ? 'active' : ''; ?>">
                            <i class="fas fa-list me-1"></i>قائمة
                        </a>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="btn-group" role="group">
                        <a href="?view=<?php echo $view_mode; ?>&filter=all" class="filter-btn <?php echo $filter_type === 'all' ? 'active' : ''; ?>">
                            الكل
                        </a>
                        <a href="?view=<?php echo $view_mode; ?>&filter=active" class="filter-btn <?php echo $filter_type === 'active' ? 'active' : ''; ?>">
                            نشطين
                        </a>
                        <a href="?view=<?php echo $view_mode; ?>&filter=inactive" class="filter-btn <?php echo $filter_type === 'inactive' ? 'active' : ''; ?>">
                            غير نشطين
                        </a>
                        <a href="?view=<?php echo $view_mode; ?>&filter=admins" class="filter-btn <?php echo $filter_type === 'admins' ? 'active' : ''; ?>">
                            المديرين
                        </a>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="d-flex justify-content-end">
                        <input type="text" class="form-control search-box" placeholder="البحث في المستخدمين..." style="max-width: 200px;">
                    </div>
                </div>
            </div>
        </div>

        <!-- محتوى العرض -->
        <?php if ($view_mode === 'cards'): ?>
            <!-- عرض البطاقات -->
            <div class="row">
                <?php foreach ($filtered_users as $user): ?>
                    <div class="col-md-6 col-lg-4">
                        <div class="user-card">
                            <div class="d-flex align-items-center mb-2">
                                <div class="user-avatar me-3">
                                    <?php echo strtoupper(substr($user['name'], 0, 2)); ?>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1"><?php echo $user['name']; ?></h6>
                                    <p class="text-muted mb-0 small"><?php echo $user['email']; ?></p>
                                </div>
                                <div>
                                    <?php if ($user['active']): ?>
                                        <span class="badge bg-success">نشط</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">غير نشط</span>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="mb-2">
                                <small class="text-muted">المجموعات:</small><br>
                                <?php foreach ($user['groups'] as $group): ?>
                                    <span class="badge bg-<?php echo $available_groups[$group]['color']; ?> me-1" style="font-size: 0.65rem;">
                                        <?php echo $available_groups[$group]['name']; ?>
                                    </span>
                                <?php endforeach; ?>
                            </div>

                            <div class="mb-2">
                                <small class="text-muted" style="font-size: 0.7rem;">
                                    <i class="fas fa-building me-1"></i><?php echo $user['company_name']; ?><br>
                                    <i class="fas fa-users me-1"></i><?php echo $user['department']; ?><br>
                                    <i class="fas fa-phone me-1"></i><?php echo $user['phone']; ?><br>
                                    <i class="fas fa-clock me-1"></i>آخر دخول: <?php echo date('Y-m-d H:i', strtotime($user['last_login'])); ?>
                                </small>
                            </div>

                            <div class="d-flex justify-content-between">
                                <button class="btn btn-outline-primary btn-sm" onclick="editUser(<?php echo $user['id']; ?>)" style="font-size: 0.7rem;">
                                    <i class="fas fa-edit me-1"></i>تعديل
                                </button>
                                <button class="btn btn-outline-info btn-sm" style="font-size: 0.7rem;">
                                    <i class="fas fa-eye me-1"></i>عرض
                                </button>
                                <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                    <button class="btn btn-outline-danger btn-sm" onclick="deleteUser(<?php echo $user['id']; ?>, '<?php echo $user['name']; ?>')" style="font-size: 0.7rem;">
                                        <i class="fas fa-trash me-1"></i>حذف
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

        <?php elseif ($view_mode === 'table'): ?>
            <!-- عرض الجدول -->
            <div class="table-odoo">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>المستخدم</th>
                            <th>البريد الإلكتروني</th>
                            <th>القسم</th>
                            <th>الهاتف</th>
                            <th>المجموعات</th>
                            <th>الحالة</th>
                            <th>آخر دخول</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($filtered_users as $user): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="user-avatar me-2" style="width: 35px; height: 35px; font-size: 0.9rem;">
                                            <?php echo strtoupper(substr($user['name'], 0, 2)); ?>
                                        </div>
                                        <div>
                                            <strong style="font-size: 0.8rem;"><?php echo $user['name']; ?></strong>
                                        </div>
                                    </div>
                                </td>
                                <td><?php echo $user['email']; ?></td>
                                <td><?php echo $user['department']; ?></td>
                                <td><?php echo $user['phone']; ?></td>
                                <td>
                                    <?php foreach ($user['groups'] as $group): ?>
                                        <span class="badge bg-<?php echo $available_groups[$group]['color']; ?> me-1" style="font-size: 0.6rem;">
                                            <?php echo $available_groups[$group]['name']; ?>
                                        </span>
                                    <?php endforeach; ?>
                                </td>
                                <td>
                                    <?php if ($user['active']): ?>
                                        <span class="badge bg-success">نشط</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">غير نشط</span>
                                    <?php endif; ?>
                                </td>
                                <td style="font-size: 0.75rem;"><?php echo date('Y-m-d H:i', strtotime($user['last_login'])); ?></td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary btn-sm" style="font-size: 0.7rem;">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-info btn-sm" style="font-size: 0.7rem;">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                            <button class="btn btn-outline-danger btn-sm" style="font-size: 0.7rem;">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

        <?php else: ?>
            <!-- عرض القائمة -->
            <div class="list-group">
                <?php foreach ($filtered_users as $user): ?>
                    <div class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <div class="user-avatar me-3" style="width: 45px; height: 45px; font-size: 1.1rem;">
                                    <?php echo strtoupper(substr($user['name'], 0, 2)); ?>
                                </div>
                                <div>
                                    <h6 class="mb-1"><?php echo $user['name']; ?></h6>
                                    <p class="mb-1 small"><?php echo $user['email']; ?> • <?php echo $user['department']; ?></p>
                                    <small class="text-muted"><?php echo $user['phone']; ?> • آخر دخول: <?php echo date('Y-m-d H:i', strtotime($user['last_login'])); ?></small>
                                </div>
                            </div>
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <?php foreach ($user['groups'] as $group): ?>
                                        <span class="badge bg-<?php echo $available_groups[$group]['color']; ?> me-1" style="font-size: 0.6rem;">
                                            <?php echo $available_groups[$group]['name']; ?>
                                        </span>
                                    <?php endforeach; ?>
                                    <?php if ($user['active']): ?>
                                        <span class="badge bg-success">نشط</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">غير نشط</span>
                                    <?php endif; ?>
                                </div>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary btn-sm" style="font-size: 0.7rem;">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-info btn-sm" style="font-size: 0.7rem;">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                        <button class="btn btn-outline-danger btn-sm" style="font-size: 0.7rem;">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editUser(userId) {
            alert('سيتم تطوير نافذة التعديل قريباً');
        }

        function deleteUser(userId, userName) {
            if (confirm('هل أنت متأكد من حذف المستخدم "' + userName + '"؟')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="user_id" value="${userId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const items = document.querySelectorAll('.user-card, .list-group-item');
            items.forEach((item, index) => {
                item.style.opacity = '0';
                item.style.transform = 'translateY(15px)';

                setTimeout(() => {
                    item.style.transition = 'all 0.5s ease';
                    item.style.opacity = '1';
                    item.style.transform = 'translateY(0)';
                }, index * 80);
            });
        });

        // البحث المباشر
        document.querySelector('.search-box').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const items = document.querySelectorAll('.user-card, .list-group-item, tbody tr');

            items.forEach(item => {
                const text = item.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    item.style.display = '';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>