<?php
session_start();

// تحميل نظام Odoo الجديد
require_once '../config/odoo_config.php';
require_once '../config/odoo_database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// تحميل نظام Odoo المتقدم
if (file_exists('../includes/odoo_registry.php')) {
    require_once '../includes/odoo_registry.php';
    require_once '../includes/odoo_menu.php';
    require_once '../includes/odoo_actions.php';
}

$use_database = !$odoo_db->isDemoMode();

// تحميل النماذج بأسلوب Odoo
if ($use_database) {
    require_once '../models/BaseModel.php';
    require_once '../models/ResCompany.php';

    // Get the database instance and pass it to the model
    $odoo_db = OdooDatabase::getInstance();
    $company_model = new ResCompany($odoo_db);

    // جلب الشركات من قاعدة البيانات بأسلوب Odoo
    try {
        $companies = $company_model->search_read(
            array(array('active', '=', true)),
            null,
            array('order' => 'sequence ASC, name ASC')
        );
    } catch (Exception $e) {
        $companies = array();
        $error_message = "خطأ في جلب البيانات: " . $e->getMessage();
    }
} else {
    // بيانات تجريبية للشركات
    $companies = array(
        array(
            'id' => 1,
            'name' => 'الشركة الرئيسية',
            'tax_number' => '123456789012345',
            'phone' => '+966501234567',
            'email' => '<EMAIL>',
            'address' => 'الرياض، المملكة العربية السعودية',
            'branches_count' => 3,
            'users_count' => 12,
            'active' => true
        ),
        array(
            'id' => 2,
            'name' => 'شركة التجارة المتقدمة',
            'tax_number' => '987654321098765',
            'phone' => '+966507654321',
            'email' => '<EMAIL>',
            'address' => 'جدة، المملكة العربية السعودية',
            'branches_count' => 2,
            'users_count' => 8,
            'active' => true
        ),
        array(
            'id' => 3,
            'name' => 'مؤسسة الأعمال الحديثة',
            'tax_number' => '456789123456789',
            'phone' => '+966509876543',
            'email' => '<EMAIL>',
            'address' => 'الدمام، المملكة العربية السعودية',
            'branches_count' => 1,
            'users_count' => 5,
            'active' => false
        )
    );
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الشركات - نظام ERP المحاسبي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <style>
        .company-card {
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .company-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }
        
        .company-logo {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            margin-bottom: 15px;
        }
        
        .status-badge {
            position: absolute;
            top: 15px;
            left: 15px;
        }
        
        .stats-item {
            text-align: center;
            padding: 10px;
            border-radius: 8px;
            background: #f8f9fa;
            margin: 5px 0;
        }
        
        .stats-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: #495057;
        }
        
        .stats-label {
            font-size: 0.85rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="../demo.php">
                <i class="fas fa-chart-line me-2"></i>
                نظام ERP المحاسبي
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../demo.php">لوحة التحكم</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="companies.php">الشركات</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- رأس الصفحة -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2><i class="fas fa-building me-2"></i>إدارة الشركات والفروع</h2>
                <p class="text-muted">إدارة الشركات والفروع التابعة للنظام</p>
            </div>
            <div>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCompanyModal">
                    <i class="fas fa-plus me-2"></i>
                    إضافة شركة جديدة
                </button>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-building fa-2x mb-2"></i>
                        <h4><?php echo count($companies); ?></h4>
                        <small>إجمالي الشركات</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-2x mb-2"></i>
                        <h4><?php
                            $active_count = 0;
                            foreach($companies as $company) {
                                if($company['active']) $active_count++;
                            }
                            echo $active_count;
                        ?></h4>
                        <small>الشركات النشطة</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-code-branch fa-2x mb-2"></i>
                        <h4><?php
                            $total_branches = 0;
                            foreach($companies as $company) {
                                $total_branches += $company['branches_count'];
                            }
                            echo $total_branches;
                        ?></h4>
                        <small>إجمالي الفروع</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-users fa-2x mb-2"></i>
                        <h4><?php
                            $total_users = 0;
                            foreach($companies as $company) {
                                $total_users += $company['users_count'];
                            }
                            echo $total_users;
                        ?></h4>
                        <small>إجمالي المستخدمين</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- قائمة الشركات -->
        <div class="row">
            <?php foreach ($companies as $company): ?>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card company-card h-100">
                    <div class="card-body position-relative">
                        <!-- حالة الشركة -->
                        <span class="badge status-badge <?php echo $company['is_active'] ? 'bg-success' : 'bg-danger'; ?>">
                            <?php echo $company['is_active'] ? 'نشطة' : 'غير نشطة'; ?>
                        </span>
                        
                        <!-- شعار الشركة -->
                        <div class="company-logo mx-auto">
                            <i class="fas fa-building"></i>
                        </div>
                        
                        <!-- معلومات الشركة -->
                        <h5 class="card-title text-center mb-3"><?php echo $company['name']; ?></h5>
                        
                        <div class="company-info mb-3">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-receipt text-muted me-2"></i>
                                <small><?php echo $company['tax_number']; ?></small>
                            </div>
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-phone text-muted me-2"></i>
                                <small><?php echo $company['phone']; ?></small>
                            </div>
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-envelope text-muted me-2"></i>
                                <small><?php echo $company['email']; ?></small>
                            </div>
                            <div class="d-flex align-items-center">
                                <i class="fas fa-map-marker-alt text-muted me-2"></i>
                                <small><?php echo $company['address']; ?></small>
                            </div>
                        </div>
                        
                        <!-- إحصائيات -->
                        <div class="row">
                            <div class="col-6">
                                <div class="stats-item">
                                    <div class="stats-number"><?php echo $company['branches_count']; ?></div>
                                    <div class="stats-label">الفروع</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="stats-item">
                                    <div class="stats-number"><?php echo $company['users_count']; ?></div>
                                    <div class="stats-label">المستخدمين</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- الإجراءات -->
                        <div class="d-flex justify-content-between mt-3">
                            <button class="btn btn-sm btn-outline-primary" onclick="viewCompany(<?php echo $company['id']; ?>)">
                                <i class="fas fa-eye me-1"></i>
                                عرض
                            </button>
                            <button class="btn btn-sm btn-outline-success" onclick="editCompany(<?php echo $company['id']; ?>)">
                                <i class="fas fa-edit me-1"></i>
                                تعديل
                            </button>
                            <button class="btn btn-sm btn-outline-info" onclick="manageBranches(<?php echo $company['id']; ?>)">
                                <i class="fas fa-code-branch me-1"></i>
                                الفروع
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteCompany(<?php echo $company['id']; ?>)">
                                <i class="fas fa-trash me-1"></i>
                                حذف
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>

    <!-- نافذة إضافة شركة جديدة -->
    <div class="modal fade" id="addCompanyModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-plus me-2"></i>
                        إضافة شركة جديدة
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addCompanyForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم الشركة *</label>
                                    <input type="text" class="form-control" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الرقم الضريبي *</label>
                                    <input type="text" class="form-control" name="tax_number" maxlength="15" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">رقم الهاتف</label>
                                    <input type="tel" class="form-control" name="phone">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" name="email">
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">العنوان</label>
                            <textarea class="form-control" name="address" rows="3"></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">المدينة</label>
                                    <input type="text" class="form-control" name="city">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الرمز البريدي</label>
                                    <input type="text" class="form-control" name="postal_code">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveCompany()">
                        <i class="fas fa-save me-2"></i>
                        حفظ
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function viewCompany(id) {
            alert('عرض تفاصيل الشركة رقم: ' + id);
        }
        
        function editCompany(id) {
            alert('تعديل الشركة رقم: ' + id);
        }
        
        function manageBranches(id) {
            alert('إدارة فروع الشركة رقم: ' + id);
        }
        
        function deleteCompany(id) {
            if (confirm('هل أنت متأكد من حذف هذه الشركة؟')) {
                alert('تم حذف الشركة رقم: ' + id);
            }
        }
        
        function saveCompany() {
            const form = document.getElementById('addCompanyForm');
            const formData = new FormData(form);
            
            // التحقق من البيانات
            if (!formData.get('name') || !formData.get('tax_number')) {
                alert('يرجى إدخال جميع الحقول المطلوبة');
                return;
            }
            
            // محاكاة حفظ البيانات
            alert('تم حفظ الشركة بنجاح!');
            
            // إغلاق النافذة وإعادة تعيين النموذج
            const modal = bootstrap.Modal.getInstance(document.getElementById('addCompanyModal'));
            modal.hide();
            form.reset();
            
            // إعادة تحميل الصفحة (في التطبيق الحقيقي)
            setTimeout(() => {
                location.reload();
            }, 1000);
        }
        
        // تأثيرات إضافية
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير hover للبطاقات
            const cards = document.querySelectorAll('.company-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>
</html>
