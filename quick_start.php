<?php
/**
 * تشغيل سريع مع تسجيل دخول تلقائي
 * Quick Start with Auto Login
 */

session_start();

// إنشاء المجلدات الأساسية
$dirs = array('config', 'logs', 'cache', 'tmp', 'sessions');
foreach ($dirs as $dir) {
    if (!file_exists($dir)) {
        @mkdir($dir, 0755, true);
    }
}

// إنشاء ملف تكوين إذا لم يكن موجود
if (!file_exists('config/database_config.php')) {
    $config = "<?php
define('SITE_NAME', 'نظام ERP المحاسبي - بأسلوب Odoo');
define('CURRENCY', 'ر.س');
define('DEMO_MODE', true);
define('SYSTEM_INSTALLED', true);
define('ODOO_STYLE', true);
?>";
    @file_put_contents('config/database_config.php', $config);
}

// تسجيل دخول تلقائي
$_SESSION['user_id'] = 1;
$_SESSION['username'] = 'مدير النظام';
$_SESSION['email'] = '<EMAIL>';
$_SESSION['role'] = 'admin';
$_SESSION['groups'] = array('admin', 'manager', 'user');
$_SESSION['company_id'] = 1;
$_SESSION['company_name'] = 'شركتي';
$_SESSION['odoo_style'] = true;
$_SESSION['login_time'] = date('Y-m-d H:i:s');

// إنشاء بيانات تجريبية
$_SESSION['demo_data'] = array(
    'companies' => array(
        array('id' => 1, 'name' => 'شركتي', 'code' => 'MYCO', 'active' => true),
        array('id' => 2, 'name' => 'شركة تجريبية', 'code' => 'DEMO', 'active' => true)
    ),
    'partners' => array(
        array('id' => 1, 'name' => 'عميل تجريبي 1', 'customer_rank' => 1),
        array('id' => 2, 'name' => 'عميل تجريبي 2', 'customer_rank' => 1),
        array('id' => 3, 'name' => 'مورد تجريبي 1', 'supplier_rank' => 1)
    ),
    'products' => array(
        array('id' => 1, 'name' => 'منتج تجريبي 1', 'list_price' => 100.00),
        array('id' => 2, 'name' => 'منتج تجريبي 2', 'list_price' => 200.00),
        array('id' => 3, 'name' => 'خدمة تجريبية', 'list_price' => 50.00)
    )
);

// توجيه للوحة التحكم
header('Location: dashboard.php');
exit();
?>
