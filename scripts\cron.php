<?php
/**
 * مجدول المهام (Cron Jobs)
 * نظام ERP المحاسبي
 * 
 * يتم تشغيله كل دقيقة عبر crontab:
 * * * * * * php /path/to/scripts/cron.php
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/functions.php';

class CronManager {
    private $db;
    private $logFile;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
        $this->logFile = __DIR__ . '/../logs/cron.log';
        
        // إنشاء مجلد السجلات
        if (!is_dir(dirname($this->logFile))) {
            mkdir(dirname($this->logFile), 0755, true);
        }
    }
    
    /**
     * تشغيل جميع المهام المجدولة
     */
    public function run() {
        $this->log("بدء تشغيل المهام المجدولة");
        
        try {
            // إنشاء جدول المهام المجدولة
            $this->createScheduleTable();
            
            // تشغيل المهام
            $this->runDailyTasks();
            $this->runWeeklyTasks();
            $this->runMonthlyTasks();
            $this->runCustomTasks();
            
            // تنظيف السجلات القديمة
            $this->cleanupLogs();
            
            $this->log("انتهاء تشغيل المهام المجدولة");
            
        } catch (Exception $e) {
            $this->log("خطأ في تشغيل المهام: " . $e->getMessage(), 'ERROR');
        }
    }
    
    /**
     * إنشاء جدول المهام المجدولة
     */
    private function createScheduleTable() {
        $this->db->exec("
            CREATE TABLE IF NOT EXISTS scheduled_tasks (
                id INT PRIMARY KEY AUTO_INCREMENT,
                name VARCHAR(100) NOT NULL UNIQUE,
                frequency ENUM('daily', 'weekly', 'monthly', 'custom') NOT NULL,
                last_run DATETIME NULL,
                next_run DATETIME NOT NULL,
                is_active BOOLEAN DEFAULT TRUE,
                command TEXT NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ");
        
        // إدراج المهام الافتراضية
        $this->insertDefaultTasks();
    }
    
    /**
     * إدراج المهام الافتراضية
     */
    private function insertDefaultTasks() {
        $tasks = array(
            array(
                'name' => 'daily_backup',
                'frequency' => 'daily',
                'next_run' => date('Y-m-d 02:00:00'),
                'command' => 'backup_database',
                'description' => 'نسخة احتياطية يومية لقاعدة البيانات'
            ),
            array(
                'name' => 'weekly_reports',
                'frequency' => 'weekly',
                'next_run' => date('Y-m-d 03:00:00', strtotime('next monday')),
                'command' => 'generate_weekly_reports',
                'description' => 'إنشاء التقارير الأسبوعية'
            ),
            array(
                'name' => 'monthly_statements',
                'frequency' => 'monthly',
                'next_run' => date('Y-m-01 04:00:00', strtotime('next month')),
                'command' => 'generate_monthly_statements',
                'description' => 'إنشاء كشوف الحسابات الشهرية'
            ),
            array(
                'name' => 'cleanup_temp_files',
                'frequency' => 'daily',
                'next_run' => date('Y-m-d 01:00:00'),
                'command' => 'cleanup_temp_files',
                'description' => 'تنظيف الملفات المؤقتة'
            ),
            array(
                'name' => 'send_payment_reminders',
                'frequency' => 'daily',
                'next_run' => date('Y-m-d 09:00:00'),
                'command' => 'send_payment_reminders',
                'description' => 'إرسال تذكيرات الدفع'
            )
        );
        
        foreach ($tasks as $task) {
            $stmt = $this->db->prepare("
                INSERT IGNORE INTO scheduled_tasks 
                (name, frequency, next_run, command, description) 
                VALUES (?, ?, ?, ?, ?)
            ");
            
            $stmt->execute(array(
                $task['name'],
                $task['frequency'],
                $task['next_run'],
                $task['command'],
                $task['description']
            ));
        }
    }
    
    /**
     * تشغيل المهام اليومية
     */
    private function runDailyTasks() {
        $this->runTasksByFrequency('daily');
    }
    
    /**
     * تشغيل المهام الأسبوعية
     */
    private function runWeeklyTasks() {
        $this->runTasksByFrequency('weekly');
    }
    
    /**
     * تشغيل المهام الشهرية
     */
    private function runMonthlyTasks() {
        $this->runTasksByFrequency('monthly');
    }
    
    /**
     * تشغيل المهام المخصصة
     */
    private function runCustomTasks() {
        $this->runTasksByFrequency('custom');
    }
    
    /**
     * تشغيل المهام حسب التكرار
     */
    private function runTasksByFrequency($frequency) {
        $stmt = $this->db->prepare("
            SELECT * FROM scheduled_tasks 
            WHERE frequency = ? 
            AND is_active = 1 
            AND next_run <= NOW()
        ");
        
        $stmt->execute(array($frequency));
        $tasks = $stmt->fetchAll();
        
        foreach ($tasks as $task) {
            $this->runTask($task);
        }
    }
    
    /**
     * تشغيل مهمة واحدة
     */
    private function runTask($task) {
        $this->log("تشغيل المهمة: " . $task['name']);
        
        try {
            $startTime = microtime(true);
            
            // تنفيذ المهمة
            $result = $this->executeCommand($task['command']);
            
            $endTime = microtime(true);
            $executionTime = round($endTime - $startTime, 3);
            
            // تحديث وقت التشغيل التالي
            $nextRun = $this->calculateNextRun($task['frequency']);
            
            $stmt = $this->db->prepare("
                UPDATE scheduled_tasks 
                SET last_run = NOW(), next_run = ? 
                WHERE id = ?
            ");
            
            $stmt->execute(array($nextRun, $task['id']));
            
            $this->log("انتهاء المهمة: " . $task['name'] . " (وقت التنفيذ: {$executionTime}s)");
            
        } catch (Exception $e) {
            $this->log("فشل في تنفيذ المهمة " . $task['name'] . ": " . $e->getMessage(), 'ERROR');
        }
    }
    
    /**
     * تنفيذ أمر المهمة
     */
    private function executeCommand($command) {
        switch ($command) {
            case 'backup_database':
                return $this->backupDatabase();
                
            case 'generate_weekly_reports':
                return $this->generateWeeklyReports();
                
            case 'generate_monthly_statements':
                return $this->generateMonthlyStatements();
                
            case 'cleanup_temp_files':
                return $this->cleanupTempFiles();
                
            case 'send_payment_reminders':
                return $this->sendPaymentReminders();
                
            default:
                throw new Exception("أمر غير معروف: $command");
        }
    }
    
    /**
     * نسخة احتياطية لقاعدة البيانات
     */
    private function backupDatabase() {
        require_once __DIR__ . '/backup.php';
        $backup = new BackupManager();
        return $backup->createDataBackup();
    }
    
    /**
     * إنشاء التقارير الأسبوعية
     */
    private function generateWeeklyReports() {
        // إنشاء تقارير المبيعات الأسبوعية
        $companies = $this->db->query("SELECT id FROM companies WHERE is_active = 1")->fetchAll();
        
        foreach ($companies as $company) {
            $this->generateSalesReport($company['id'], 'weekly');
        }
        
        return true;
    }
    
    /**
     * إنشاء كشوف الحسابات الشهرية
     */
    private function generateMonthlyStatements() {
        // إنشاء كشوف حسابات العملاء
        $customers = $this->db->query("
            SELECT DISTINCT c.id, c.company_id 
            FROM customers c 
            JOIN invoices i ON c.id = i.customer_id 
            WHERE i.status IN ('pending', 'partial')
        ")->fetchAll();
        
        foreach ($customers as $customer) {
            $this->generateCustomerStatement($customer['id'], $customer['company_id']);
        }
        
        return true;
    }
    
    /**
     * تنظيف الملفات المؤقتة
     */
    private function cleanupTempFiles() {
        $tempDirs = array(
            __DIR__ . '/../tmp/',
            __DIR__ . '/../cache/',
            __DIR__ . '/../uploads/temp/'
        );
        
        $deletedFiles = 0;
        
        foreach ($tempDirs as $dir) {
            if (is_dir($dir)) {
                $files = glob($dir . '*');
                
                foreach ($files as $file) {
                    if (is_file($file) && (time() - filemtime($file)) > 86400) { // أقدم من يوم
                        unlink($file);
                        $deletedFiles++;
                    }
                }
            }
        }
        
        $this->log("تم حذف $deletedFiles ملف مؤقت");
        return true;
    }
    
    /**
     * إرسال تذكيرات الدفع
     */
    private function sendPaymentReminders() {
        // البحث عن الفواتير المتأخرة
        $overdueInvoices = $this->db->query("
            SELECT i.*, c.name as customer_name, c.email 
            FROM invoices i 
            JOIN customers c ON i.customer_id = c.id 
            WHERE i.status IN ('pending', 'partial') 
            AND i.due_date < CURDATE() 
            AND c.email IS NOT NULL
        ")->fetchAll();
        
        $sentReminders = 0;
        
        foreach ($overdueInvoices as $invoice) {
            if ($this->sendPaymentReminder($invoice)) {
                $sentReminders++;
            }
        }
        
        $this->log("تم إرسال $sentReminders تذكير دفع");
        return true;
    }
    
    /**
     * إرسال تذكير دفع واحد
     */
    private function sendPaymentReminder($invoice) {
        $subject = "تذكير: فاتورة متأخرة رقم " . $invoice['invoice_number'];
        $message = "عزيزي العميل،\n\n";
        $message .= "نذكركم بأن الفاتورة رقم " . $invoice['invoice_number'] . " ";
        $message .= "بمبلغ " . formatCurrency($invoice['total_amount']) . " ";
        $message .= "متأخرة عن موعد الاستحقاق.\n\n";
        $message .= "يرجى سداد المبلغ في أقرب وقت ممكن.\n\n";
        $message .= "شكراً لتعاونكم.";
        
        return sendEmail($invoice['email'], $subject, $message);
    }
    
    /**
     * حساب وقت التشغيل التالي
     */
    private function calculateNextRun($frequency) {
        switch ($frequency) {
            case 'daily':
                return date('Y-m-d H:i:s', strtotime('+1 day'));
                
            case 'weekly':
                return date('Y-m-d H:i:s', strtotime('+1 week'));
                
            case 'monthly':
                return date('Y-m-d H:i:s', strtotime('+1 month'));
                
            default:
                return date('Y-m-d H:i:s', strtotime('+1 day'));
        }
    }
    
    /**
     * تنظيف السجلات القديمة
     */
    private function cleanupLogs() {
        $logFiles = array(
            $this->logFile,
            __DIR__ . '/../logs/error.log',
            __DIR__ . '/../logs/access.log'
        );
        
        foreach ($logFiles as $logFile) {
            if (file_exists($logFile) && filesize($logFile) > 10 * 1024 * 1024) { // أكبر من 10MB
                $this->rotateLog($logFile);
            }
        }
    }
    
    /**
     * تدوير ملف السجل
     */
    private function rotateLog($logFile) {
        $rotatedFile = $logFile . '.' . date('Y-m-d');
        rename($logFile, $rotatedFile);
        
        // ضغط الملف المدور
        if (function_exists('gzencode')) {
            $data = file_get_contents($rotatedFile);
            file_put_contents($rotatedFile . '.gz', gzencode($data));
            unlink($rotatedFile);
        }
    }
    
    /**
     * تسجيل رسالة في السجل
     */
    private function log($message, $level = 'INFO') {
        $timestamp = date('Y-m-d H:i:s');
        $logEntry = "[$timestamp] [$level] $message\n";
        
        file_put_contents($this->logFile, $logEntry, FILE_APPEND | LOCK_EX);
        
        // طباعة الرسالة إذا كان يعمل من سطر الأوامر
        if (php_sapi_name() === 'cli') {
            echo $logEntry;
        }
    }
}

// تشغيل المجدول
if (php_sapi_name() === 'cli') {
    $cron = new CronManager();
    $cron->run();
} else {
    // منع الوصول من المتصفح
    http_response_code(403);
    echo "الوصول مرفوض";
}
?>
