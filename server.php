<?php
/**
 * خادم التطوير المحلي
 * نظام ERP المحاسبي
 * 
 * لتشغيل الخادم: php server.php
 */

// إعدادات الخادم
$host = 'localhost';
$port = 8000;
$documentRoot = __DIR__;

// التحقق من توفر PHP
if (version_compare(PHP_VERSION, '7.4.0', '<')) {
    die("خطأ: يتطلب النظام PHP 7.4 أو أحدث. الإصدار الحالي: " . PHP_VERSION . "\n");
}

// التحقق من التبعيات المطلوبة
$requiredExtensions = array('pdo', 'pdo_mysql', 'json', 'mbstring');
$missingExtensions = array();

foreach ($requiredExtensions as $extension) {
    if (!extension_loaded($extension)) {
        $missingExtensions[] = $extension;
    }
}

if (!empty($missingExtensions)) {
    die("خطأ: التبعيات التالية مفقودة: " . implode(', ', $missingExtensions) . "\n");
}

// التحقق من وجود ملف الإعدادات
if (!file_exists('config/database.php')) {
    echo "تحذير: ملف إعدادات قاعدة البيانات غير موجود.\n";
    echo "يرجى تشغيل install.php أولاً أو إنشاء ملف config/database.php\n\n";
}

// إنشاء المجلدات المطلوبة
$directories = array(
    'uploads',
    'logs',
    'cache',
    'tmp',
    'backups',
    'reports/generated',
    'sessions'
);

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
        echo "تم إنشاء المجلد: $dir\n";
    }
}

// إنشاء ملفات .gitkeep
foreach ($directories as $dir) {
    $gitkeepFile = $dir . '/.gitkeep';
    if (!file_exists($gitkeepFile)) {
        file_put_contents($gitkeepFile, '');
    }
}

// إعداد متغيرات البيئة للتطوير
if (!getenv('APP_ENV')) {
    putenv('APP_ENV=development');
}

// تفعيل عرض الأخطاء في بيئة التطوير
if (getenv('APP_ENV') === 'development') {
    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);
    error_reporting(E_ALL);
}

// معالج الطلبات
function handleRequest($uri) {
    global $documentRoot;
    
    // إزالة المعاملات من URI
    $path = parse_url($uri, PHP_URL_PATH);
    $filePath = $documentRoot . $path;
    
    // التحقق من الملفات الثابتة
    if (file_exists($filePath) && !is_dir($filePath)) {
        $extension = pathinfo($filePath, PATHINFO_EXTENSION);
        
        // تحديد نوع المحتوى
        $mimeTypes = array(
            'css' => 'text/css',
            'js' => 'application/javascript',
            'png' => 'image/png',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'gif' => 'image/gif',
            'svg' => 'image/svg+xml',
            'ico' => 'image/x-icon',
            'woff' => 'font/woff',
            'woff2' => 'font/woff2',
            'ttf' => 'font/ttf',
            'eot' => 'application/vnd.ms-fontobject'
        );
        
        if (isset($mimeTypes[$extension])) {
            header('Content-Type: ' . $mimeTypes[$extension]);
            readfile($filePath);
            return true;
        }
    }
    
    // إعادة توجيه للملفات PHP
    if (is_dir($filePath)) {
        $indexFile = $filePath . '/index.php';
        if (file_exists($indexFile)) {
            include $indexFile;
            return true;
        }
    }
    
    // معالجة الروابط الودية
    if (!file_exists($filePath)) {
        // إعادة توجيه للصفحة الرئيسية
        if ($path === '/' || $path === '') {
            include $documentRoot . '/index.php';
            return true;
        }
        
        // إعادة توجيه لصفحة 404
        http_response_code(404);
        if (file_exists($documentRoot . '/pages/404.php')) {
            include $documentRoot . '/pages/404.php';
        } else {
            echo "404 - الصفحة غير موجودة";
        }
        return true;
    }
    
    return false;
}

// بدء الخادم
echo "=================================\n";
echo "  نظام ERP المحاسبي - خادم التطوير\n";
echo "=================================\n\n";
echo "PHP Version: " . PHP_VERSION . "\n";
echo "Document Root: $documentRoot\n";
echo "Server: http://$host:$port\n\n";

// التحقق من توفر المنفذ
$socket = @fsockopen($host, $port, $errno, $errstr, 1);
if ($socket) {
    fclose($socket);
    die("خطأ: المنفذ $port مستخدم بالفعل.\n");
}

echo "بدء الخادم...\n";
echo "اضغط Ctrl+C لإيقاف الخادم\n\n";

// تسجيل الطلبات
$logFile = 'logs/server.log';
if (!file_exists('logs')) {
    mkdir('logs', 0755, true);
}

// بدء خادم PHP المدمج
$command = sprintf(
    'php -S %s:%d -t %s',
    $host,
    $port,
    $documentRoot
);

// إضافة معالج الإشارات لإيقاف الخادم بشكل صحيح
if (function_exists('pcntl_signal')) {
    function signal_handler() {
        echo "\n\nتم إيقاف الخادم.\n";
        exit(0);
    }
    pcntl_signal(SIGINT, 'signal_handler');
}

// تشغيل الخادم
passthru($command);
?>
