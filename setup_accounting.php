<?php
/**
 * إعداد وحدة المحاسبة
 * Accounting Module Setup
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

// تحميل نظام قاعدة البيانات
require_once 'config/odoo_config.php';
require_once 'config/odoo_database.php';

$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = OdooDatabase::getInstance();
        
        // قراءة ملف SQL
        $sql_file = 'sql/accounting_tables.sql';
        if (!file_exists($sql_file)) {
            throw new Exception('ملف SQL غير موجود');
        }
        
        $sql_content = file_get_contents($sql_file);
        
        // تقسيم الاستعلامات
        $queries = explode(';', $sql_content);
        
        $success_count = 0;
        $error_count = 0;
        $errors = array();
        
        foreach ($queries as $query) {
            $query = trim($query);
            if (empty($query) || strpos($query, '--') === 0) {
                continue;
            }
            
            try {
                $db->execute($query);
                $success_count++;
            } catch (Exception $e) {
                $error_count++;
                $errors[] = $e->getMessage();
            }
        }
        
        if ($error_count === 0) {
            $message = "تم إعداد وحدة المحاسبة بنجاح! تم تنفيذ {$success_count} استعلام.";
            $message_type = 'success';
        } else {
            $message = "تم إعداد الوحدة مع بعض الأخطاء. نجح: {$success_count}، فشل: {$error_count}";
            $message_type = 'warning';
        }
        
    } catch (Exception $e) {
        $message = 'خطأ في إعداد الوحدة: ' . $e->getMessage();
        $message_type = 'error';
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد وحدة المحاسبة - نظام ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --odoo-primary: #714B67;
            --odoo-secondary: #8B5A8C;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .setup-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
        }
        
        .setup-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .setup-header {
            background: linear-gradient(45deg, var(--odoo-primary), var(--odoo-secondary));
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .setup-body {
            padding: 2rem;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-list i {
            color: #28a745;
            margin-left: 0.5rem;
        }
        
        .btn-setup {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            color: white;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }
        
        .btn-setup:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(40, 167, 69, 0.3);
            color: white;
        }
        
        .progress-container {
            display: none;
            margin-top: 1rem;
        }
        
        .database-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-card">
            <div class="setup-header">
                <h2><i class="fas fa-calculator me-3"></i>إعداد وحدة المحاسبة</h2>
                <p class="mb-0">إعداد نظام محاسبي متكامل بأسلوب Odoo</p>
            </div>
            
            <div class="setup-body">
                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $message_type === 'success' ? 'success' : ($message_type === 'warning' ? 'warning' : 'danger'); ?> alert-dismissible fade show">
                        <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : ($message_type === 'warning' ? 'exclamation-triangle' : 'times-circle'); ?> me-2"></i>
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    
                    <?php if ($message_type === 'success'): ?>
                        <div class="text-center mt-3">
                            <a href="pages/chart_of_accounts.php" class="btn btn-primary me-2">
                                <i class="fas fa-sitemap me-2"></i>دليل الحسابات
                            </a>
                            <a href="pages/journal_entries.php" class="btn btn-info">
                                <i class="fas fa-book me-2"></i>القيود اليومية
                            </a>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (isset($errors) && count($errors) > 0): ?>
                        <div class="mt-3">
                            <h6>تفاصيل الأخطاء:</h6>
                            <ul class="list-group">
                                <?php foreach (array_slice($errors, 0, 5) as $error): ?>
                                    <li class="list-group-item list-group-item-danger small"><?php echo $error; ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
                
                <div class="database-info">
                    <h5><i class="fas fa-database me-2"></i>معلومات قاعدة البيانات</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <small class="text-muted">الخادم:</small>
                            <div><?php echo ODOO_DB_HOST; ?></div>
                        </div>
                        <div class="col-md-6">
                            <small class="text-muted">قاعدة البيانات:</small>
                            <div><?php echo ODOO_DB_NAME; ?></div>
                        </div>
                    </div>
                </div>
                
                <h5><i class="fas fa-star me-2"></i>ميزات وحدة المحاسبة</h5>
                <ul class="feature-list">
                    <li><i class="fas fa-check"></i>دليل حسابات شامل بأسلوب Odoo</li>
                    <li><i class="fas fa-check"></i>نظام اليوميات المحاسبية المتقدم</li>
                    <li><i class="fas fa-check"></i>القيود المحاسبية مع التحقق من التوازن</li>
                    <li><i class="fas fa-check"></i>نظام العملات المتعددة</li>
                    <li><i class="fas fa-check"></i>التقارير المالية (الميزانية وقائمة الدخل)</li>
                    <li><i class="fas fa-check"></i>نظام التسوية المحاسبية</li>
                    <li><i class="fas fa-check"></i>الفترات المحاسبية</li>
                    <li><i class="fas fa-check"></i>ربط مع الوحدات الأخرى</li>
                    <li><i class="fas fa-check"></i>بيانات تجريبية شاملة</li>
                    <li><i class="fas fa-check"></i>واجهات Odoo الاحترافية</li>
                </ul>
                
                <h5 class="mt-4"><i class="fas fa-table me-2"></i>الجداول التي سيتم إنشاؤها</h5>
                <div class="row">
                    <div class="col-md-6">
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">account_account - دليل الحسابات</li>
                            <li class="list-group-item">account_journal - اليوميات</li>
                            <li class="list-group-item">account_move - القيود المحاسبية</li>
                            <li class="list-group-item">account_move_line - بنود القيود</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">res_currency - العملات</li>
                            <li class="list-group-item">account_period - الفترات المحاسبية</li>
                            <li class="list-group-item">account_financial_report - التقارير المالية</li>
                            <li class="list-group-item">+ بيانات تجريبية شاملة</li>
                        </ul>
                    </div>
                </div>
                
                <?php if (!$message || $message_type !== 'success'): ?>
                    <div class="text-center mt-4">
                        <form method="POST" id="setupForm">
                            <button type="submit" class="btn btn-setup" onclick="showProgress()">
                                <i class="fas fa-rocket me-2"></i>بدء إعداد وحدة المحاسبة
                            </button>
                        </form>
                        
                        <div class="progress-container" id="progressContainer">
                            <div class="progress mt-3">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                     role="progressbar" style="width: 0%" id="progressBar"></div>
                            </div>
                            <small class="text-muted">جاري إعداد قاعدة البيانات...</small>
                        </div>
                    </div>
                <?php endif; ?>
                
                <div class="mt-4 text-center">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        سيتم إنشاء جميع الجداول والبيانات التجريبية تلقائياً
                    </small>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showProgress() {
            document.getElementById('progressContainer').style.display = 'block';
            
            let progress = 0;
            const progressBar = document.getElementById('progressBar');
            
            const interval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress > 90) progress = 90;
                
                progressBar.style.width = progress + '%';
                
                if (progress >= 90) {
                    clearInterval(interval);
                }
            }, 200);
        }
        
        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const card = document.querySelector('.setup-card');
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            
            setTimeout(() => {
                card.style.transition = 'all 0.8s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100);
        });
    </script>
</body>
</html>
