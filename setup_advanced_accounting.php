<?php
/**
 * إعداد وحدة المحاسبة المتقدمة بأسلوب Odoo
 * Advanced Accounting Module Setup - Odoo Style
 */

require_once 'config/database.php';

// الاتصال بقاعدة البيانات
$conn = new mysqli(ODOO_DB_HOST, ODOO_DB_USER, ODOO_DB_PASS, ODOO_DB_NAME);

// التحقق من الاتصال
if ($conn->connect_error) {
    die("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
}

// تعيين ترميز UTF-8
$conn->set_charset("utf8mb4");

echo "<h2>إعداد وحدة المحاسبة المتقدمة</h2>";

try {
    // إنشاء جدول الأصول الثابتة
    $sql = "CREATE TABLE IF NOT EXISTS account_asset (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        code VARCHAR(50),
        category_id INT,
        original_value DECIMAL(15,2) NOT NULL,
        salvage_value DECIMAL(15,2) DEFAULT 0,
        depreciation_method ENUM('linear', 'degressive', 'accelerated', 'manual') DEFAULT 'linear',
        depreciation_number_years INT DEFAULT 5,
        depreciation_number_months INT DEFAULT 60,
        acquisition_date DATE,
        first_depreciation_date DATE,
        last_depreciation_date DATE,
        state ENUM('draft', 'open', 'close', 'cancelled') DEFAULT 'draft',
        asset_account_id INT,
        depreciation_account_id INT,
        expense_account_id INT,
        journal_id INT,
        company_id INT DEFAULT 1,
        currency_id INT DEFAULT 1,
        partner_id INT,
        invoice_id INT,
        note TEXT,
        active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "✅ تم إنشاء جدول الأصول الثابتة بنجاح<br>";
    } else {
        echo "❌ خطأ في إنشاء جدول الأصول الثابتة: " . $conn->error . "<br>";
    }
    
    // إنشاء جدول خطوط استهلاك الأصول
    $sql = "CREATE TABLE IF NOT EXISTS account_asset_depreciation_line (
        id INT AUTO_INCREMENT PRIMARY KEY,
        asset_id INT NOT NULL,
        sequence INT NOT NULL,
        depreciation_date DATE NOT NULL,
        depreciation_amount DECIMAL(15,2) NOT NULL,
        accumulated_depreciation DECIMAL(15,2) NOT NULL,
        remaining_value DECIMAL(15,2) NOT NULL,
        move_id INT,
        move_posted BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (asset_id) REFERENCES account_asset(id) ON DELETE CASCADE
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "✅ تم إنشاء جدول خطوط استهلاك الأصول بنجاح<br>";
    } else {
        echo "❌ خطأ في إنشاء جدول خطوط استهلاك الأصول: " . $conn->error . "<br>";
    }
    
    // إنشاء جدول فئات الأصول
    $sql = "CREATE TABLE IF NOT EXISTS account_asset_category (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        code VARCHAR(50),
        depreciation_method ENUM('linear', 'degressive', 'accelerated', 'manual') DEFAULT 'linear',
        depreciation_number_years INT DEFAULT 5,
        asset_account_id INT,
        depreciation_account_id INT,
        expense_account_id INT,
        journal_id INT,
        company_id INT DEFAULT 1,
        active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "✅ تم إنشاء جدول فئات الأصول بنجاح<br>";
    } else {
        echo "❌ خطأ في إنشاء جدول فئات الأصول: " . $conn->error . "<br>";
    }
    
    // إنشاء جدول الميزانيات
    $sql = "CREATE TABLE IF NOT EXISTS crossovered_budget (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        code VARCHAR(50),
        date_from DATE NOT NULL,
        date_to DATE NOT NULL,
        state ENUM('draft', 'confirm', 'validate', 'cancel', 'done') DEFAULT 'draft',
        user_id INT,
        company_id INT DEFAULT 1,
        creating_user_id INT,
        validating_user_id INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "✅ تم إنشاء جدول الميزانيات بنجاح<br>";
    } else {
        echo "❌ خطأ في إنشاء جدول الميزانيات: " . $conn->error . "<br>";
    }
    
    // إنشاء جدول بنود الميزانية
    $sql = "CREATE TABLE IF NOT EXISTS crossovered_budget_lines (
        id INT AUTO_INCREMENT PRIMARY KEY,
        crossovered_budget_id INT NOT NULL,
        analytic_account_id INT,
        general_budget_id INT,
        date_from DATE NOT NULL,
        date_to DATE NOT NULL,
        planned_amount DECIMAL(15,2) DEFAULT 0,
        practical_amount DECIMAL(15,2) DEFAULT 0,
        theoritical_amount DECIMAL(15,2) DEFAULT 0,
        percentage DECIMAL(5,2) DEFAULT 0,
        company_id INT DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (crossovered_budget_id) REFERENCES crossovered_budget(id) ON DELETE CASCADE
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "✅ تم إنشاء جدول بنود الميزانية بنجاح<br>";
    } else {
        echo "❌ خطأ في إنشاء جدول بنود الميزانية: " . $conn->error . "<br>";
    }
    
    // إنشاء جدول مناصب الميزانية
    $sql = "CREATE TABLE IF NOT EXISTS account_budget_post (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        code VARCHAR(50),
        company_id INT DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "✅ تم إنشاء جدول مناصب الميزانية بنجاح<br>";
    } else {
        echo "❌ خطأ في إنشاء جدول مناصب الميزانية: " . $conn->error . "<br>";
    }
    
    // إنشاء جدول الحسابات التحليلية
    $sql = "CREATE TABLE IF NOT EXISTS account_analytic_account (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        code VARCHAR(50),
        active BOOLEAN DEFAULT TRUE,
        group_id INT,
        company_id INT DEFAULT 1,
        partner_id INT,
        balance DECIMAL(15,2) DEFAULT 0,
        debit DECIMAL(15,2) DEFAULT 0,
        credit DECIMAL(15,2) DEFAULT 0,
        currency_id INT DEFAULT 1,
        plan_id INT,
        root_plan_id INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "✅ تم إنشاء جدول الحسابات التحليلية بنجاح<br>";
    } else {
        echo "❌ خطأ في إنشاء جدول الحسابات التحليلية: " . $conn->error . "<br>";
    }
    
    // إنشاء جدول بنود المحاسبة التحليلية
    $sql = "CREATE TABLE IF NOT EXISTS account_analytic_line (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        date DATE NOT NULL,
        amount DECIMAL(15,2) NOT NULL,
        unit_amount DECIMAL(10,2) DEFAULT 0,
        account_id INT NOT NULL,
        partner_id INT,
        user_id INT,
        company_id INT DEFAULT 1,
        currency_id INT DEFAULT 1,
        general_account_id INT,
        move_id INT,
        ref VARCHAR(255),
        so_line VARCHAR(255),
        task_id INT,
        project_id INT,
        employee_id INT,
        department_id INT,
        category ENUM('revenue', 'expense', 'timesheet', 'other') DEFAULT 'other',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (account_id) REFERENCES account_analytic_account(id) ON DELETE CASCADE
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "✅ تم إنشاء جدول بنود المحاسبة التحليلية بنجاح<br>";
    } else {
        echo "❌ خطأ في إنشاء جدول بنود المحاسبة التحليلية: " . $conn->error . "<br>";
    }
    
    // إنشاء جدول مجموعات المحاسبة التحليلية
    $sql = "CREATE TABLE IF NOT EXISTS account_analytic_group (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        parent_id INT,
        parent_path VARCHAR(255),
        company_id INT DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (parent_id) REFERENCES account_analytic_group(id) ON DELETE SET NULL
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "✅ تم إنشاء جدول مجموعات المحاسبة التحليلية بنجاح<br>";
    } else {
        echo "❌ خطأ في إنشاء جدول مجموعات المحاسبة التحليلية: " . $conn->error . "<br>";
    }
    
    // إنشاء جدول علامات المحاسبة التحليلية
    $sql = "CREATE TABLE IF NOT EXISTS account_analytic_tag (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        color INT DEFAULT 0,
        active BOOLEAN DEFAULT TRUE,
        company_id INT DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "✅ تم إنشاء جدول علامات المحاسبة التحليلية بنجاح<br>";
    } else {
        echo "❌ خطأ في إنشاء جدول علامات المحاسبة التحليلية: " . $conn->error . "<br>";
    }
    
    // إنشاء جدول المدفوعات
    $sql = "CREATE TABLE IF NOT EXISTS account_payment (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        payment_type ENUM('outbound', 'inbound', 'transfer') NOT NULL,
        partner_type ENUM('customer', 'supplier'),
        partner_id INT,
        amount DECIMAL(15,2) NOT NULL,
        currency_id INT DEFAULT 1,
        payment_date DATE NOT NULL,
        communication TEXT,
        journal_id INT NOT NULL,
        payment_method_id INT,
        payment_method_line_id INT,
        state ENUM('draft', 'posted', 'sent', 'reconciled', 'cancelled') DEFAULT 'draft',
        move_id INT,
        reconciled_invoice_ids TEXT,
        reconciled_invoices_count INT DEFAULT 0,
        company_id INT DEFAULT 1,
        destination_account_id INT,
        outstanding_account_id INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "✅ تم إنشاء جدول المدفوعات بنجاح<br>";
    } else {
        echo "❌ خطأ في إنشاء جدول المدفوعات: " . $conn->error . "<br>";
    }
    
    // إنشاء جدول طرق الدفع
    $sql = "CREATE TABLE IF NOT EXISTS account_payment_method (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        code VARCHAR(50) NOT NULL,
        payment_type ENUM('inbound', 'outbound') NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "✅ تم إنشاء جدول طرق الدفع بنجاح<br>";
    } else {
        echo "❌ خطأ في إنشاء جدول طرق الدفع: " . $conn->error . "<br>";
    }
    
    // إنشاء جدول شروط الدفع
    $sql = "CREATE TABLE IF NOT EXISTS account_payment_term (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        active BOOLEAN DEFAULT TRUE,
        note TEXT,
        sequence INT DEFAULT 10,
        company_id INT DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "✅ تم إنشاء جدول شروط الدفع بنجاح<br>";
    } else {
        echo "❌ خطأ في إنشاء جدول شروط الدفع: " . $conn->error . "<br>";
    }
    
    // إنشاء جدول بنود شروط الدفع
    $sql = "CREATE TABLE IF NOT EXISTS account_payment_term_line (
        id INT AUTO_INCREMENT PRIMARY KEY,
        payment_id INT NOT NULL,
        value ENUM('percent', 'balance') DEFAULT 'percent',
        value_amount DECIMAL(5,2) NOT NULL,
        sequence INT DEFAULT 10,
        days INT DEFAULT 0,
        option VARCHAR(50) DEFAULT 'day_after_invoice_date',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (payment_id) REFERENCES account_payment_term(id) ON DELETE CASCADE
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "✅ تم إنشاء جدول بنود شروط الدفع بنجاح<br>";
    } else {
        echo "❌ خطأ في إنشاء جدول بنود شروط الدفع: " . $conn->error . "<br>";
    }
    
    echo "<br><h3>✅ تم إعداد وحدة المحاسبة المتقدمة بنجاح!</h3>";
    echo "<p>تم إنشاء جميع الجداول المطلوبة للوحدات الفرعية:</p>";
    echo "<ul>";
    echo "<li>✅ إدارة الأصول الثابتة</li>";
    echo "<li>✅ إدارة الميزانيات</li>";
    echo "<li>✅ المحاسبة التحليلية</li>";
    echo "<li>✅ إدارة المدفوعات</li>";
    echo "<li>✅ شروط الدفع المتقدمة</li>";
    echo "</ul>";
    
    echo "<br><a href='pages/accounting_dashboard_advanced.php' class='btn btn-primary'>الذهاب إلى لوحة التحكم المتقدمة</a>";
    
} catch (Exception $e) {
    echo "❌ خطأ في إعداد قاعدة البيانات: " . $e->getMessage();
}

$conn->close();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد وحدة المحاسبة المتقدمة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .container { max-width: 800px; margin: 50px auto; }
        .setup-header { text-align: center; margin-bottom: 30px; }
        .setup-content { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
    </style>
</head>
<body class="bg-light">
    <div class="container">
        <div class="setup-header">
            <h1><i class="fas fa-cogs text-primary"></i> إعداد وحدة المحاسبة المتقدمة</h1>
            <p class="text-muted">تم إعداد جميع الوحدات الفرعية بأسلوب Odoo الاحترافي</p>
        </div>
        <div class="setup-content">
            <!-- المحتوى سيظهر هنا من PHP -->
        </div>
    </div>
</body>
</html>
