<?php
/**
 * إعداد قاعدة البيانات التلقائي
 * Automatic Database Setup
 */

// إعدادات قاعدة البيانات
$host = 'localhost';
$username = 'root';
$password = '';
$database_name = 'erp_accounting';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد قاعدة البيانات - نظام ERP</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #714B67, #875A7B);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .setup-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 800px;
            width: 90%;
        }
        
        .setup-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .setup-header i {
            font-size: 4rem;
            color: #714B67;
            margin-bottom: 15px;
        }
        
        .setup-header h1 {
            color: #2F3349;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .setup-header p {
            color: #6c757d;
            font-size: 1.1rem;
        }
        
        .step {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .step-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .step-number {
            background: #714B67;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 15px;
        }
        
        .step-title {
            font-weight: 600;
            color: #2F3349;
            margin: 0;
        }
        
        .step-content {
            margin-right: 45px;
        }
        
        .btn-setup {
            background: #714B67;
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .btn-setup:hover {
            background: #875A7B;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(113, 75, 103, 0.3);
        }
        
        .status-success {
            color: #28a745;
            font-weight: bold;
        }
        
        .status-error {
            color: #dc3545;
            font-weight: bold;
        }
        
        .status-warning {
            color: #ffc107;
            font-weight: bold;
        }
        
        .log-output {
            background: #2F3349;
            color: #fff;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 15px;
        }
        
        .progress-bar-custom {
            background: #714B67;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-header">
            <i class="fas fa-database"></i>
            <h1>إعداد قاعدة البيانات</h1>
            <p>سيتم إنشاء قاعدة البيانات والجداول المطلوبة للنظام</p>
        </div>

        <?php
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['setup'])) {
            echo '<div class="log-output" id="setupLog">';
            echo "بدء عملية إعداد قاعدة البيانات...\n";
            
            try {
                // الاتصال بـ MySQL بدون تحديد قاعدة بيانات
                echo "الاتصال بخادم MySQL...\n";
                $pdo = new PDO("mysql:host=$host", $username, $password);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                echo "✅ تم الاتصال بخادم MySQL بنجاح\n";
                
                // إنشاء قاعدة البيانات
                echo "إنشاء قاعدة البيانات '$database_name'...\n";
                $pdo->exec("CREATE DATABASE IF NOT EXISTS $database_name CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                echo "✅ تم إنشاء قاعدة البيانات بنجاح\n";
                
                // الاتصال بقاعدة البيانات الجديدة
                echo "الاتصال بقاعدة البيانات الجديدة...\n";
                $pdo = new PDO("mysql:host=$host;dbname=$database_name", $username, $password);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                echo "✅ تم الاتصال بقاعدة البيانات بنجاح\n";
                
                // قراءة وتنفيذ ملف SQL
                echo "قراءة ملف SQL...\n";
                $sql_file = 'database_setup.sql';
                if (file_exists($sql_file)) {
                    $sql_content = file_get_contents($sql_file);
                    
                    // تقسيم الاستعلامات
                    $queries = explode(';', $sql_content);
                    $executed = 0;
                    
                    echo "تنفيذ الاستعلامات...\n";
                    foreach ($queries as $query) {
                        $query = trim($query);
                        if (!empty($query) && !preg_match('/^--/', $query)) {
                            try {
                                $pdo->exec($query);
                                $executed++;
                            } catch (PDOException $e) {
                                // تجاهل أخطاء الجداول الموجودة
                                if (strpos($e->getMessage(), 'already exists') === false) {
                                    echo "⚠️ تحذير: " . $e->getMessage() . "\n";
                                }
                            }
                        }
                    }
                    
                    echo "✅ تم تنفيذ $executed استعلام بنجاح\n";
                } else {
                    echo "❌ ملف SQL غير موجود\n";
                }
                
                // التحقق من الجداول
                echo "التحقق من الجداول المنشأة...\n";
                $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
                echo "✅ تم إنشاء " . count($tables) . " جدول: " . implode(', ', $tables) . "\n";
                
                echo "\n🎉 تم إعداد قاعدة البيانات بنجاح!\n";
                echo "يمكنك الآن استخدام النظام بالبيانات التالية:\n";
                echo "اسم المستخدم: admin\n";
                echo "كلمة المرور: admin123\n";
                
            } catch (PDOException $e) {
                echo "❌ خطأ: " . $e->getMessage() . "\n";
            }
            
            echo '</div>';
            
            echo '<div class="mt-4 text-center">';
            echo '<a href="start_here.php" class="btn btn-success btn-lg">';
            echo '<i class="fas fa-rocket me-2"></i>بدء استخدام النظام';
            echo '</a>';
            echo '</div>';
            
        } else {
        ?>
        
        <div class="step">
            <div class="step-header">
                <div class="step-number">1</div>
                <h5 class="step-title">التحقق من المتطلبات</h5>
            </div>
            <div class="step-content">
                <?php
                $requirements = [
                    'PHP Version >= 7.4' => version_compare(PHP_VERSION, '7.4.0', '>='),
                    'PDO Extension' => extension_loaded('pdo'),
                    'PDO MySQL Extension' => extension_loaded('pdo_mysql'),
                    'MySQL Server' => true // سنتحقق من هذا لاحقاً
                ];
                
                foreach ($requirements as $req => $status) {
                    $icon = $status ? '✅' : '❌';
                    $class = $status ? 'status-success' : 'status-error';
                    echo "<div class='$class'>$icon $req</div>";
                }
                ?>
            </div>
        </div>
        
        <div class="step">
            <div class="step-header">
                <div class="step-number">2</div>
                <h5 class="step-title">إعدادات قاعدة البيانات</h5>
            </div>
            <div class="step-content">
                <div class="row">
                    <div class="col-md-6">
                        <strong>الخادم:</strong> <?php echo $host; ?>
                    </div>
                    <div class="col-md-6">
                        <strong>اسم قاعدة البيانات:</strong> <?php echo $database_name; ?>
                    </div>
                    <div class="col-md-6">
                        <strong>المستخدم:</strong> <?php echo $username; ?>
                    </div>
                    <div class="col-md-6">
                        <strong>كلمة المرور:</strong> <?php echo empty($password) ? '(فارغة)' : '***'; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="step">
            <div class="step-header">
                <div class="step-number">3</div>
                <h5 class="step-title">بدء الإعداد</h5>
            </div>
            <div class="step-content">
                <p>انقر على الزر أدناه لبدء عملية إنشاء قاعدة البيانات والجداول:</p>
                <form method="POST">
                    <button type="submit" name="setup" class="btn-setup">
                        <i class="fas fa-play me-2"></i>
                        بدء إعداد قاعدة البيانات
                    </button>
                </form>
            </div>
        </div>
        
        <?php } ?>
        
        <div class="mt-4 text-center">
            <small class="text-muted">
                <i class="fas fa-info-circle me-1"></i>
                تأكد من تشغيل XAMPP وخدمة MySQL قبل بدء الإعداد
            </small>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
