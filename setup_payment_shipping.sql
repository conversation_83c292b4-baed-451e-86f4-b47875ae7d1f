-- =============================================
-- إعداد طرق الدفع والشحن
-- =============================================

-- بدء المعاملة
START TRANSACTION;

-- =============================================
-- 1. إنشاء جدول طرق الدفع
-- =============================================
CREATE TABLE IF NOT EXISTS `account_payment_method` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(128) NOT NULL,
    `code` VARCHAR(64) NOT NULL,
    `is_default` BOOLEAN DEFAULT FALSE,
    `active` BO<PERSON>EAN DEFAULT TRUE,
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME,
    UNIQUE KEY `payment_method_code_uniq` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 2. إضافة طرق الدفع الافتراضية
-- =============================================
-- طريقة الدفع النقدي
INSERT INTO `account_payment_method` (
    `name`, `code`, `is_default`, `active`, `create_uid`, `create_date`, `write_uid`, `write_date`
) VALUES (
    'نقداً', 'cash', TRUE, TRUE, 1, NOW(), 1, NOW()
);

-- طريقة الدفع بالتحويل البنكي
INSERT INTO `account_payment_method` (
    `name`, `code`, `is_default`, `active`, `create_uid`, `create_date`, `write_uid`, `write_date`
) VALUES (
    'تحويل بنكي', 'bank_transfer', FALSE, TRUE, 1, NOW(), 1, NOW()
);

-- طريقة الدفع ببطاقة الائتمان
INSERT INTO `account_payment_method` (
    `name`, `code`, `is_default`, `active`, `create_uid`, `create_date`, `write_uid`, `write_date`
) VALUES (
    'بطاقة ائتمان', 'credit_card', FALSE, TRUE, 1, NOW(), 1, NOW()
);

-- =============================================
-- 3. إنشاء جدول طرق الشحن
-- =============================================
CREATE TABLE IF NOT EXISTS `delivery_carrier` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(128) NOT NULL,
    `delivery_type` VARCHAR(32) NOT NULL DEFAULT 'fixed',
    `fixed_price` DECIMAL(10,2) DEFAULT 0.00,
    `active` BOOLEAN DEFAULT TRUE,
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 4. إضافة طرق الشحن الافتراضية
-- =============================================
-- الشحن السريع
INSERT INTO `delivery_carrier` (
    `name`, `delivery_type`, `fixed_price`, `active`, `create_uid`, `create_date`, `write_uid`, `write_date`
) VALUES (
    'الشحن السريع', 'fixed', 30.00, TRUE, 1, NOW(), 1, NOW()
);

-- الشحن العادي
INSERT INTO `delivery_carrier` (
    `name`, `delivery_type`, `fixed_price`, `active`, `create_uid`, `create_date`, `write_uid`, `write_date`
) VALUES (
    'الشحن العادي', 'fixed', 15.00, TRUE, 1, NOW(), 1, NOW()
);

-- الاستلام من المتجر
INSERT INTO `delivery_carrier` (
    `name`, `delivery_type`, `fixed_price`, `active`, `create_uid`, `create_date`, `write_uid`, `write_date`
) VALUES (
    'استلام من المتجر', 'fixed', 0.00, TRUE, 1, NOW(), 1, NOW()
);

-- =============================================
-- 5. إنشاء جدول حسابات الدفع
-- =============================================
CREATE TABLE IF NOT EXISTS `account_payment` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(128) NOT NULL,
    `payment_type` ENUM('inbound', 'outbound') NOT NULL,
    `amount` DECIMAL(10,2) NOT NULL,
    `currency_id` INT,
    `payment_date` DATE NOT NULL,
    `payment_method_id` INT,
    `partner_id` INT,
    `state` VARCHAR(32) DEFAULT 'draft',
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME,
    FOREIGN KEY (`currency_id`) REFERENCES `res_currency`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`payment_method_id`) REFERENCES `account_payment_method`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`partner_id`) REFERENCES `res_partner`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 6. إضافة إعدادات الدفع الافتراضية
-- =============================================
-- إضافة إعدادات الدفع للشركة
ALTER TABLE `res_company` 
ADD COLUMN IF NOT EXISTS `payment_acquirer_id` INT NULL AFTER `currency_id`,
ADD COLUMN IF NOT EXISTS `default_sale_payment_method_id` INT NULL AFTER `payment_acquirer_id`;

-- تحديث إعدادات الشركة
UPDATE `res_company` SET 
    `default_sale_payment_method_id` = (SELECT `id` FROM `account_payment_method` WHERE `code` = 'cash' LIMIT 1)
WHERE `id` = 1;

-- =============================================
-- تأكيد التغييرات
-- =============================================
COMMIT;

-- رسالة نجاح
SELECT 'تم إعداد طرق الدفع والشحن بنجاح!' AS message;
