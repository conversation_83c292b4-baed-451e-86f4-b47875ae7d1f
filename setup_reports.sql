-- =============================================
-- إعداد التقارير الأساسية
-- =============================================

-- بدء المعاملة
START TRANSACTION;

-- =============================================
-- 1. إنشاء جداول التقارير
-- =============================================

-- جدول أنواع التقارير
CREATE TABLE IF NOT EXISTS `ir_actions_report` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(128) NOT NULL,
    `model` VARCHAR(64) NOT NULL,
    `report_type` VARCHAR(32) NOT NULL DEFAULT 'qweb-pdf',
    `report_name` VARCHAR(128) NOT NULL,
    `report_file` VARCHAR(256),
    `paperformat_id` INT,
    `print_report_name` VARCHAR(128),
    `binding_model_id` INT,
    `binding_type` VARCHAR(32) DEFAULT 'report',
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول تنسيقات الأوراق
CREATE TABLE IF NOT EXISTS `report_paperformat` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(128) NOT NULL,
    `default` BOOLEAN DEFAULT FALSE,
    `format` VARCHAR(32) DEFAULT 'A4',
    `page_width` FLOAT DEFAULT 210.00,
    `page_height` FLOAT DEFAULT 297.00,
    `orientation` VARCHAR(16) DEFAULT 'Portrait',
    `margin_top` FLOAT DEFAULT 40.00,
    `margin_bottom` FLOAT DEFAULT 40.00,
    `margin_left` FLOAT DEFAULT 7.00,
    `margin_right` FLOAT DEFAULT 7.00,
    `header_line` BOOLEAN DEFAULT FALSE,
    `header_spacing` INT DEFAULT 35,
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول قوالب التقارير
CREATE TABLE IF NOT EXISTS `ir_ui_view` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(128) NOT NULL,
    `model` VARCHAR(64) NOT NULL,
    `key` VARCHAR(128),
    `arch` LONGTEXT,
    `type` VARCHAR(32) DEFAULT 'qweb',
    `priority` INT DEFAULT 16,
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 2. إضافة تنسيقات الأوراق الافتراضية
-- =============================================

-- تنسيق A4
INSERT INTO `report_paperformat` (
    `name`, `default`, `format`, `page_width`, `page_height`, `orientation`,
    `margin_top`, `margin_bottom`, `margin_left`, `margin_right`,
    `header_line`, `header_spacing`,
    `create_uid`, `create_date`, `write_uid`, `write_date`
) VALUES (
    'A4', TRUE, 'A4', 210.00, 297.00, 'Portrait',
    40.00, 40.00, 7.00, 7.00,
    FALSE, 35,
    1, NOW(), 1, NOW()
);
SET @a4_format_id = LAST_INSERT_ID();

-- =============================================
-- 3. إضافة تقارير المبيعات
-- =============================================

-- تقرير عرض سعر المبيعات
INSERT INTO `ir_actions_report` (
    `name`, `model`, `report_type`, `report_name`,
    `report_file`, `paperformat_id`,
    `print_report_name`, `binding_model_id`,
    `create_uid`, `create_date`, `write_uid`, `write_date`
) VALUES (
    'عرض سعر المبيعات', 'sale.order', 'qweb-pdf', 'sale.report_saleorder',
    'sale.report_saleorder', @a4_format_id,
    'عرض السعر - %(object.name)s', NULL,
    1, NOW(), 1, NOW()
);
SET @sale_order_report_id = LAST_INSERT_ID();

-- =============================================
-- 4. إضافة تقارير المشتريات
-- =============================================

-- تقرير طلب الشراء
INSERT INTO `ir_actions_report` (
    `name`, `model`, `report_type`, `report_name`,
    `report_file`, `paperformat_id`,
    `print_report_name`, `binding_model_id`,
    `create_uid`, `create_date`, `write_uid`, `write_date`
) VALUES (
    'طلب شراء', 'purchase.order', 'qweb-pdf', 'purchase.report_purchaseorder',
    'purchase.report_purchaseorder', @a4_format_id,
    'طلب الشراء - %(object.name)s', NULL,
    1, NOW(), 1, NOW()
);
SET @purchase_order_report_id = LAST_INSERT_ID();

-- =============================================
-- 5. إضافة تقارير المخزون
-- =============================================

-- تقرير إيصال التوصيل
INSERT INTO `ir_actions_report` (
    `name`, `model`, `report_type`, `report_name`,
    `report_file`, `paperformat_id`,
    `print_report_name`, `binding_model_id`,
    `create_uid`, `create_date`, `write_uid`, `write_date`
) VALUES (
    'إيصال التوصيل', 'stock.picking', 'qweb-pdf', 'stock.report_delivery_document',
    'stock.report_delivery_document', @a4_format_id,
    'إيصال التوصيل - %(object.name)s', NULL,
    1, NOW(), 1, NOW()
);
SET @delivery_slip_report_id = LAST_INSERT_ID();

-- =============================================
-- 6. إضافة تقارير المحاسبة
-- =============================================

-- تقرير الفاتورة
INSERT INTO `ir_actions_report` (
    `name`, `model`, `report_type`, `report_name`,
    `report_file`, `paperformat_id`,
    `print_report_name`, `binding_model_id`,
    `create_uid`, `create_date`, `write_uid`, `write_date`
) VALUES (
    'فاتورة', 'account.move', 'qweb-pdf', 'account.account_invoices',
    'account.account_invoices', @a4_format_id,
    'فاتورة - %(object.name)s', NULL,
    1, NOW(), 1, NOW()
);
SET @invoice_report_id = LAST_INSERT_ID();

-- =============================================
-- 7. إضافة قوالب التقارير
-- =============================================

-- قالب تقرير عرض السعر
INSERT INTO `ir_ui_view` (
    `name`, `model`, `key`, `arch`, `type`,
    `priority`, `create_uid`, `create_date`, `write_uid`, `write_date`
) VALUES (
    'sale.report_saleorder_document', 'sale.order', 'sale.report_saleorder_document',
    '<?xml version="1.0"?><t t-name="sale.report_saleorder_document">
    <t t-call="web.html_container">
        <t t-foreach="docs" t-as="doc">
            <div class="page">
                <h2>عرض سعر #<span t-field="doc.name"/></h2>
                <p>العميل: <span t-field="doc.partner_id.name"/></p>
                <p>التاريخ: <span t-field="doc.date_order" t-options="{&quot;widget&quot;: &quot;date&quot;}"/></p>
                
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>المنتج</th>
                            <th>الكمية</th>
                            <th>سعر الوحدة</th>
                            <th>الضريبة</th>
                            <th>الإجمالي</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr t-foreach="doc.order_line" t-as="line">
                            <td><span t-field="line.product_id.name"/></td>
                            <td><span t-field="line.product_uom_qty"/></td>
                            <td><span t-field="line.price_unit"/></td>
                            <td><span t-field="line.tax_id/name"/></td>
                            <td><span t-field="line.price_total"/></td>
                        </tr>
                    </tbody>
                </table>
                
                <div class="row mt-4">
                    <div class="col-6">
                        <p>الإجمالي قبل الضريبة: <span t-field="doc.amount_untaxed"/></p>
                        <p>الضريبة: <span t-field="doc.amount_tax"/></p>
                        <p>الإجمالي: <span t-field="doc.amount_total"/></p>
                    </div>
                </div>
            </div>
        </t>
    </t>
</t>',
    'qweb', 16, 1, NOW(), 1, NOW()
);

-- =============================================
-- 8. إعداد إعدادات التقارير الافتراضية
-- =============================================

-- إضافة حقول إعدادات التقارير للشركة
ALTER TABLE `res_company` 
ADD COLUMN IF NOT EXISTS `external_report_layout_id` INT NULL AFTER `currency_id`,
ADD COLUMN IF NOT EXISTS `paperformat_id` INT NULL AFTER `external_report_layout_id`;

-- تحديث إعدادات الشركة
UPDATE `res_company` SET 
    `paperformat_id` = @a4_format_id
WHERE `id` = 1;

-- =============================================
-- تأكيد التغييرات
-- =============================================
COMMIT;

-- رسالة نجاح
SELECT 'تم إعداد التقارير الأساسية بنجاح!' AS message;
