-- =============================================
-- إعداد صلاحيات المستخدمين
-- =============================================

-- بدء المعاملة
START TRANSACTION;

-- =============================================
-- 1. إنشاء جداول الصلاحيات
-- =============================================

-- جدول مجموعات الصلاحيات
CREATE TABLE IF NOT EXISTS `res_groups` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(128) NOT NULL,
    `category_id` INT,
    `comment` TEXT,
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME,
    UNIQUE KEY `groups_name_uniq` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول المستخدمين في المجموعات
CREATE TABLE IF NOT EXISTS `res_groups_users_rel` (
    `gid` INT NOT NULL,
    `uid` INT NOT NULL,
    PRIMARY KEY (`gid`, `uid`),
    FOREIGN KEY (`gid`) REFERENCES `res_groups`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`uid`) REFERENCES `res_users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الصلاحيات
CREATE TABLE IF NOT EXISTS `ir_model_access` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(128) NOT NULL,
    `model_id` VARCHAR(64) NOT NULL,
    `group_id` INT,
    `perm_read` BOOLEAN DEFAULT FALSE,
    `perm_write` BOOLEAN DEFAULT FALSE,
    `perm_create` BOOLEAN DEFAULT FALSE,
    `perm_unlink` BOOLEAN DEFAULT FALSE,
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME,
    FOREIGN KEY (`group_id`) REFERENCES `res_groups`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 2. إنشاء مجموعات الصلاحيات الأساسية
-- =============================================

-- مجموعة مدير النظام
INSERT INTO `res_groups` (
    `name`, `comment`, `create_uid`, `create_date`, `write_uid`, `write_date`
) VALUES (
    'مدير النظام', 'لديه صلاحيات كاملة على النظام', 1, NOW(), 1, NOW()
);
SET @admin_group_id = LAST_INSERT_ID();

-- مجموعة مدير المبيعات
INSERT INTO `res_groups` (
    `name`, `comment`, `create_uid`, `create_date`, `write_uid`, `write_date`
) VALUES (
    'مدير المبيعات', 'يدير كافة عمليات المبيعات والعملاء', 1, NOW(), 1, NOW()
);
SET @sales_manager_group_id = LAST_INSERT_ID();

-- مجموعة مندوب المبيعات
INSERT INTO `res_groups` (
    `name`, `comment`, `create_uid`, `create_date`, `write_uid`, `write_date`
) VALUES (
    'مندوب مبيعات', 'يستطيع إنشاء وتعديل عروض الأسعار وطلبات البيع', 1, NOW(), 1, NOW()
);
SET @sales_user_group_id = LAST_INSERT_ID();

-- مجموعة مدير المشتريات
INSERT INTO `res_groups` (
    `name`, `comment`, `create_uid`, `create_date`, `write_uid`, `write_date`
) VALUES (
    'مدير المشتريات', 'يدير كافة عمليات الشراء والموردين', 1, NOW(), 1, NOW()
);
SET @purchase_manager_group_id = LAST_INSERT_ID();

-- مجموعة مستخدم المشتريات
INSERT INTO `res_groups` (
    `name`, `comment`, `create_uid`, `create_date`, `write_uid`, `write_date`
) VALUES (
    'مستخدم المشتريات', 'يستطيع إنشاء وتعديل طلبات الشراء', 1, NOW(), 1, NOW()
);
SET @purchase_user_group_id = LAST_INSERT_ID();

-- مجموعة مدير المخزون
INSERT INTO `res_groups` (
    `name`, `comment`, `create_uid`, `create_date`, `write_uid`, `write_date`
) VALUES (
    'مدير المخزون', 'يدير كافة عمليات المخزون والمستودعات', 1, NOW(), 1, NOW()
);
SET @stock_manager_group_id = LAST_INSERT_ID();

-- =============================================
-- 3. إضافة الأذونات للمجموعات
-- =============================================

-- وظيفة مساعدة لإضافة الأذونات
DELIMITER //
CREATE PROCEDURE add_permissions(
    IN p_group_id INT,
    IN p_model_name VARCHAR(64),
    IN p_read BOOLEAN,
    IN p_write BOOLEAN,
    IN p_create BOOLEAN,
    IN p_unlink BOOLEAN
)
BEGIN
    INSERT INTO `ir_model_access` (
        `name`, `model_id`, `group_id`, `perm_read`, `perm_write`, `perm_create`, `perm_unlink`,
        `create_uid`, `create_date`, `write_uid`, `write_date`
    ) VALUES (
        CONCAT(p_model_name, ' ', (SELECT `name` FROM `res_groups` WHERE `id` = p_group_id)),
        p_model_name, p_group_id, p_read, p_write, p_create, p_unlink,
        1, NOW(), 1, NOW()
    );
END //
DELIMITER ;

-- صلاحيات مدير النظام (كاملة)
CALL add_permissions(@admin_group_id, 'res.partner', TRUE, TRUE, TRUE, TRUE);
CALL add_permissions(@admin_group_id, 'product.template', TRUE, TRUE, TRUE, TRUE);
CALL add_permissions(@admin_group_id, 'sale.order', TRUE, TRUE, TRUE, TRUE);
CALL add_permissions(@admin_group_id, 'purchase.order', TRUE, TRUE, TRUE, TRUE);
CALL add_permissions(@admin_group_id, 'stock.picking', TRUE, TRUE, TRUE, TRUE);
CALL add_permissions(@admin_group_id, 'account.invoice', TRUE, TRUE, TRUE, TRUE);

-- صلاحيات مدير المبيعات
CALL add_permissions(@sales_manager_group_id, 'res.partner', TRUE, TRUE, TRUE, FALSE);
CALL add_permissions(@sales_manager_group_id, 'product.template', TRUE, FALSE, FALSE, FALSE);
CALL add_permissions(@sales_manager_group_id, 'sale.order', TRUE, TRUE, TRUE, TRUE);
CALL add_permissions(@sales_manager_group_id, 'account.invoice', TRUE, TRUE, TRUE, FALSE);

-- صلاحيات مندوب المبيعات
CALL add_permissions(@sales_user_group_id, 'res.partner', TRUE, TRUE, TRUE, FALSE);
CALL add_permissions(@sales_user_group_id, 'product.template', TRUE, FALSE, FALSE, FALSE);
CALL add_permissions(@sales_user_group_id, 'sale.order', TRUE, TRUE, TRUE, FALSE);

-- صلاحيات مدير المشتريات
CALL add_permissions(@purchase_manager_group_id, 'res.partner', TRUE, TRUE, TRUE, FALSE);
CALL add_permissions(@purchase_manager_group_id, 'product.template', TRUE, FALSE, FALSE, FALSE);
CALL add_permissions(@purchase_manager_group_id, 'purchase.order', TRUE, TRUE, TRUE, TRUE);

-- صلاحيات مستخدم المشتريات
CALL add_permissions(@purchase_user_group_id, 'res.partner', TRUE, TRUE, TRUE, FALSE);
CALL add_permissions(@purchase_user_group_id, 'product.template', TRUE, FALSE, FALSE, FALSE);
CALL add_permissions(@purchase_user_group_id, 'purchase.order', TRUE, TRUE, TRUE, FALSE);

-- صلاحيات مدير المخزون
CALL add_permissions(@stock_manager_group_id, 'product.template', TRUE, TRUE, TRUE, FALSE);
CALL add_permissions(@stock_manager_group_id, 'stock.picking', TRUE, TRUE, TRUE, TRUE);
CALL add_permissions(@stock_manager_group_id, 'stock.quant', TRUE, TRUE, FALSE, FALSE);

-- حذف الإجراء المساعد بعد الانتهاء منه
DROP PROCEDURE IF EXISTS add_permissions;

-- =============================================
-- 4. ربط المستخدمين بالمجموعات
-- =============================================

-- ربط المستخدم المسؤول بمجموعة مدير النظام
INSERT IGNORE INTO `res_groups_users_rel` (`gid`, `uid`) VALUES (@admin_group_id, 1);

-- =============================================
-- 5. تحديث صلاحيات المستخدمين
-- =============================================

-- تحديث صلاحيات المستخدمين للعمل مع النظام متعدد الشركات
ALTER TABLE `res_users` 
ADD COLUMN IF NOT EXISTS `company_id` INT DEFAULT 1 AFTER `active`,
ADD COLUMN IF NOT EXISTS `company_ids` VARCHAR(128) DEFAULT '1' AFTER `company_id`;

-- تحديث المستخدمين الموجودين
UPDATE `res_users` SET 
    `company_id` = 1,
    `company_ids` = '1'
WHERE `id` = 1;

-- =============================================
-- 6. إنشاء ملف تعريف المستخدمين
-- =============================================
CREATE TABLE IF NOT EXISTS `res_users_settings` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `user_id` INT NOT NULL,
    `company_id` INT,
    `theme` VARCHAR(32) DEFAULT 'light',
    `notification_type` VARCHAR(32) DEFAULT 'email',
    `create_uid` INT,
    `create_date` DATETIME,
    `write_uid` INT,
    `write_date` DATETIME,
    FOREIGN KEY (`user_id`) REFERENCES `res_users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`company_id`) REFERENCES `res_company`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إعدادات المستخدم الافتراضية
INSERT IGNORE INTO `res_users_settings` (
    `user_id`, `company_id`, `theme`, `notification_type`, `create_uid`, `create_date`, `write_uid`, `write_date`
) VALUES (
    1, 1, 'light', 'email', 1, NOW(), 1, NOW()
);

-- =============================================
-- تأكيد التغييرات
-- =============================================
COMMIT;

-- رسالة نجاح
SELECT 'تم إعداد صلاحيات المستخدمين بنجاح!' AS message;
