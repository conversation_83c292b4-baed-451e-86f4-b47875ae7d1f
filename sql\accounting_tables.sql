-- جداول وحدة المحاسبة بأسلوب Odoo
-- Accounting Module Tables - Odoo Style

-- جدول دليل الحسابات
CREATE TABLE IF NOT EXISTS account_account (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(255) NOT NULL COMMENT 'اسم الحساب',
    code VARCHAR(50) NOT NULL UNIQUE COMMENT 'رمز الحساب',
    account_type ENUM('asset', 'liability', 'equity', 'income', 'expense') NOT NULL COMMENT 'نوع الحساب',
    parent_id INT NULL COMMENT 'الحساب الأب',
    level INT DEFAULT 1 COMMENT 'مستوى الحساب',
    reconcile BOOLEAN DEFAULT FALSE COMMENT 'قابل للتسوية',
    active BOOLEAN DEFAULT TRUE COMMENT 'نشط',
    company_id INT DEFAULT 1 COMMENT 'الشركة',
    currency_id INT DEFAULT 1 COMMENT 'العملة',
    note TEXT NULL COMMENT 'ملاحظات',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES account_account(id) ON DELETE SET NULL,
    INDEX idx_account_code (code),
    INDEX idx_account_type (account_type),
    INDEX idx_account_parent (parent_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='دليل الحسابات';

-- جدول اليوميات
CREATE TABLE IF NOT EXISTS account_journal (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL COMMENT 'اسم اليومية',
    code VARCHAR(10) NOT NULL UNIQUE COMMENT 'رمز اليومية',
    type ENUM('sale', 'purchase', 'cash', 'bank', 'general') NOT NULL COMMENT 'نوع اليومية',
    default_account_id INT NULL COMMENT 'الحساب الافتراضي',
    sequence_id INT NULL COMMENT 'تسلسل الترقيم',
    active BOOLEAN DEFAULT TRUE COMMENT 'نشط',
    company_id INT DEFAULT 1 COMMENT 'الشركة',
    currency_id INT DEFAULT 1 COMMENT 'العملة',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (default_account_id) REFERENCES account_account(id) ON DELETE SET NULL,
    INDEX idx_journal_type (type),
    INDEX idx_journal_code (code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='اليوميات المحاسبية';

-- جدول القيود المحاسبية
CREATE TABLE IF NOT EXISTS account_move (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NULL COMMENT 'رقم القيد',
    ref VARCHAR(255) NULL COMMENT 'المرجع',
    date DATE NOT NULL COMMENT 'تاريخ القيد',
    journal_id INT NOT NULL COMMENT 'اليومية',
    state ENUM('draft', 'posted', 'cancelled') DEFAULT 'draft' COMMENT 'الحالة',
    amount_total DECIMAL(15,2) DEFAULT 0.00 COMMENT 'إجمالي المبلغ',
    narration TEXT NULL COMMENT 'البيان',
    company_id INT DEFAULT 1 COMMENT 'الشركة',
    currency_id INT DEFAULT 1 COMMENT 'العملة',
    created_by INT NULL COMMENT 'المنشئ',
    posted_by INT NULL COMMENT 'المعتمد',
    posted_at TIMESTAMP NULL COMMENT 'تاريخ الاعتماد',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (journal_id) REFERENCES account_journal(id) ON DELETE RESTRICT,
    INDEX idx_move_date (date),
    INDEX idx_move_journal (journal_id),
    INDEX idx_move_state (state),
    INDEX idx_move_ref (ref)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='القيود المحاسبية';

-- جدول بنود القيود المحاسبية
CREATE TABLE IF NOT EXISTS account_move_line (
    id INT AUTO_INCREMENT PRIMARY KEY,
    move_id INT NOT NULL COMMENT 'القيد المحاسبي',
    account_id INT NOT NULL COMMENT 'الحساب',
    name VARCHAR(255) NOT NULL COMMENT 'البيان',
    debit DECIMAL(15,2) DEFAULT 0.00 COMMENT 'مدين',
    credit DECIMAL(15,2) DEFAULT 0.00 COMMENT 'دائن',
    balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'الرصيد',
    partner_id INT NULL COMMENT 'الشريك',
    date DATE NOT NULL COMMENT 'التاريخ',
    ref VARCHAR(255) NULL COMMENT 'المرجع',
    reconciled BOOLEAN DEFAULT FALSE COMMENT 'مسوى',
    reconcile_ref VARCHAR(255) NULL COMMENT 'مرجع التسوية',
    company_id INT DEFAULT 1 COMMENT 'الشركة',
    currency_id INT DEFAULT 1 COMMENT 'العملة',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (move_id) REFERENCES account_move(id) ON DELETE CASCADE,
    FOREIGN KEY (account_id) REFERENCES account_account(id) ON DELETE RESTRICT,
    INDEX idx_move_line_move (move_id),
    INDEX idx_move_line_account (account_id),
    INDEX idx_move_line_date (date),
    INDEX idx_move_line_partner (partner_id),
    INDEX idx_move_line_reconciled (reconciled)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='بنود القيود المحاسبية';

-- جدول العملات
CREATE TABLE IF NOT EXISTS res_currency (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL COMMENT 'اسم العملة',
    symbol VARCHAR(10) NOT NULL COMMENT 'رمز العملة',
    rate DECIMAL(12,6) DEFAULT 1.000000 COMMENT 'سعر الصرف',
    active BOOLEAN DEFAULT TRUE COMMENT 'نشط',
    position ENUM('before', 'after') DEFAULT 'after' COMMENT 'موضع الرمز',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_currency_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='العملات';

-- جدول الفترات المحاسبية
CREATE TABLE IF NOT EXISTS account_period (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL COMMENT 'اسم الفترة',
    code VARCHAR(50) NOT NULL COMMENT 'رمز الفترة',
    date_start DATE NOT NULL COMMENT 'تاريخ البداية',
    date_end DATE NOT NULL COMMENT 'تاريخ النهاية',
    state ENUM('draft', 'open', 'closed') DEFAULT 'draft' COMMENT 'الحالة',
    company_id INT DEFAULT 1 COMMENT 'الشركة',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_period_dates (date_start, date_end),
    INDEX idx_period_state (state)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='الفترات المحاسبية';

-- جدول التقارير المالية
CREATE TABLE IF NOT EXISTS account_financial_report (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL COMMENT 'اسم التقرير',
    parent_id INT NULL COMMENT 'التقرير الأب',
    sequence INT DEFAULT 10 COMMENT 'التسلسل',
    level INT DEFAULT 1 COMMENT 'المستوى',
    type ENUM('sum', 'accounts', 'account_type', 'account_report') DEFAULT 'sum' COMMENT 'النوع',
    account_ids TEXT NULL COMMENT 'الحسابات المرتبطة',
    account_type_ids TEXT NULL COMMENT 'أنواع الحسابات',
    sign ENUM('1', '-1') DEFAULT '1' COMMENT 'الإشارة',
    display_detail ENUM('no_detail', 'detail_flat', 'detail_with_hierarchy') DEFAULT 'detail_flat' COMMENT 'عرض التفاصيل',
    style_overwrite TEXT NULL COMMENT 'تنسيق مخصص',
    active BOOLEAN DEFAULT TRUE COMMENT 'نشط',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES account_financial_report(id) ON DELETE SET NULL,
    INDEX idx_report_parent (parent_id),
    INDEX idx_report_sequence (sequence)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='التقارير المالية';

-- إدراج البيانات التجريبية

-- إدراج العملات
INSERT INTO res_currency (name, symbol, rate, position) VALUES
('الريال السعودي', 'ر.س', 1.000000, 'after'),
('الدولار الأمريكي', '$', 3.750000, 'before'),
('اليورو', '€', 4.100000, 'before'),
('الجنيه الإسترليني', '£', 4.800000, 'before');

-- إدراج دليل الحسابات
INSERT INTO account_account (name, code, account_type, parent_id, level, reconcile) VALUES
-- الأصول
('الأصول', '1', 'asset', NULL, 1, FALSE),
('الأصول المتداولة', '11', 'asset', 1, 2, FALSE),
('النقدية وما في حكمها', '111', 'asset', 2, 3, FALSE),
('الصندوق', '1111', 'asset', 3, 4, TRUE),
('البنك الأهلي', '1112', 'asset', 3, 4, TRUE),
('بنك الراجحي', '1113', 'asset', 3, 4, TRUE),
('العملاء', '112', 'asset', 2, 3, FALSE),
('حسابات العملاء', '1121', 'asset', 7, 4, TRUE),
('أوراق القبض', '1122', 'asset', 7, 4, TRUE),
('المخزون', '113', 'asset', 2, 3, FALSE),
('مخزون البضائع', '1131', 'asset', 10, 4, FALSE),
('الأصول الثابتة', '12', 'asset', 1, 2, FALSE),
('الأراضي والمباني', '121', 'asset', 12, 3, FALSE),
('الأراضي', '1211', 'asset', 13, 4, FALSE),
('المباني', '1212', 'asset', 13, 4, FALSE),
('الآلات والمعدات', '122', 'asset', 12, 3, FALSE),
('الآلات', '1221', 'asset', 16, 4, FALSE),
('المعدات', '1222', 'asset', 16, 4, FALSE),

-- الخصوم
('الخصوم', '2', 'liability', NULL, 1, FALSE),
('الخصوم المتداولة', '21', 'liability', 19, 2, FALSE),
('الموردون', '211', 'liability', 20, 3, FALSE),
('حسابات الموردين', '2111', 'liability', 21, 4, TRUE),
('أوراق الدفع', '2112', 'liability', 21, 4, TRUE),
('المصروفات المستحقة', '212', 'liability', 20, 3, FALSE),
('رواتب مستحقة', '2121', 'liability', 24, 4, TRUE),
('الخصوم طويلة الأجل', '22', 'liability', 19, 2, FALSE),
('القروض طويلة الأجل', '221', 'liability', 26, 3, FALSE),

-- حقوق الملكية
('حقوق الملكية', '3', 'equity', NULL, 1, FALSE),
('رأس المال', '31', 'equity', 28, 2, FALSE),
('رأس المال المدفوع', '311', 'equity', 29, 3, TRUE),
('الأرباح المحتجزة', '32', 'equity', 28, 2, FALSE),
('أرباح السنوات السابقة', '321', 'equity', 31, 3, TRUE),
('أرباح السنة الحالية', '322', 'equity', 31, 3, TRUE),

-- الإيرادات
('الإيرادات', '4', 'income', NULL, 1, FALSE),
('إيرادات المبيعات', '41', 'income', 34, 2, FALSE),
('مبيعات البضائع', '411', 'income', 35, 3, TRUE),
('إيرادات الخدمات', '412', 'income', 35, 3, TRUE),
('إيرادات أخرى', '42', 'income', 34, 2, FALSE),
('إيرادات فوائد', '421', 'income', 38, 3, TRUE),

-- المصروفات
('المصروفات', '5', 'expense', NULL, 1, FALSE),
('تكلفة البضاعة المباعة', '51', 'expense', 40, 2, FALSE),
('تكلفة المبيعات', '511', 'expense', 41, 3, TRUE),
('مصروفات التشغيل', '52', 'expense', 40, 2, FALSE),
('مصروفات الرواتب', '521', 'expense', 43, 3, TRUE),
('مصروفات الإيجار', '522', 'expense', 43, 3, TRUE),
('مصروفات الكهرباء', '523', 'expense', 43, 3, TRUE),
('مصروفات الهاتف', '524', 'expense', 43, 3, TRUE),
('مصروفات إدارية', '53', 'expense', 40, 2, FALSE),
('مصروفات القرطاسية', '531', 'expense', 48, 3, TRUE),
('مصروفات السفر', '532', 'expense', 48, 3, TRUE);

-- إدراج اليوميات
INSERT INTO account_journal (name, code, type, default_account_id) VALUES
('يومية المبيعات', 'SAL', 'sale', 36),
('يومية المشتريات', 'PUR', 'purchase', 22),
('يومية الصندوق', 'CSH', 'cash', 4),
('يومية البنك الأهلي', 'BNK1', 'bank', 5),
('يومية البنك الراجحي', 'BNK2', 'bank', 6),
('اليومية العامة', 'MISC', 'general', NULL);

-- إدراج الفترات المحاسبية
INSERT INTO account_period (name, code, date_start, date_end, state) VALUES
('يناير 2024', '2024-01', '2024-01-01', '2024-01-31', 'open'),
('فبراير 2024', '2024-02', '2024-02-01', '2024-02-29', 'open'),
('مارس 2024', '2024-03', '2024-03-01', '2024-03-31', 'open'),
('أبريل 2024', '2024-04', '2024-04-01', '2024-04-30', 'draft'),
('مايو 2024', '2024-05', '2024-05-01', '2024-05-31', 'draft'),
('يونيو 2024', '2024-06', '2024-06-01', '2024-06-30', 'draft');

-- إدراج التقارير المالية
INSERT INTO account_financial_report (name, parent_id, sequence, level, type, account_type_ids, sign) VALUES
('الميزانية العمومية', NULL, 1, 1, 'sum', NULL, '1'),
('الأصول', 1, 1, 2, 'account_type', 'asset', '1'),
('الخصوم وحقوق الملكية', 1, 2, 2, 'sum', NULL, '1'),
('الخصوم', 3, 1, 3, 'account_type', 'liability', '-1'),
('حقوق الملكية', 3, 2, 3, 'account_type', 'equity', '-1'),
('قائمة الدخل', NULL, 2, 1, 'sum', NULL, '1'),
('الإيرادات', 6, 1, 2, 'account_type', 'income', '-1'),
('المصروفات', 6, 2, 2, 'account_type', 'expense', '1'),
('صافي الربح', 6, 3, 2, 'sum', NULL, '1');

-- جدول الشركاء (العملاء والموردين)
CREATE TABLE IF NOT EXISTS res_partner (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(50),
    mobile VARCHAR(50),
    street TEXT,
    street2 TEXT,
    city VARCHAR(100),
    state VARCHAR(100),
    zip VARCHAR(20),
    country VARCHAR(100),
    website VARCHAR(255),
    vat VARCHAR(50),
    is_company BOOLEAN DEFAULT FALSE,
    customer_rank INT DEFAULT 0,
    supplier_rank INT DEFAULT 0,
    parent_id INT,
    category_ids TEXT,
    customer_account_id INT,
    supplier_account_id INT,
    credit_limit DECIMAL(15,2) DEFAULT 0.00,
    payment_terms VARCHAR(50) DEFAULT 'immediate',
    active BOOLEAN DEFAULT TRUE,
    image TEXT,
    comment TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES res_partner(id) ON DELETE SET NULL,
    FOREIGN KEY (customer_account_id) REFERENCES account_account(id) ON DELETE SET NULL,
    FOREIGN KEY (supplier_account_id) REFERENCES account_account(id) ON DELETE SET NULL,
    INDEX idx_partner_name (name),
    INDEX idx_partner_email (email),
    INDEX idx_partner_customer (customer_rank),
    INDEX idx_partner_supplier (supplier_rank)
);

-- جدول الفترات المحاسبية
CREATE TABLE IF NOT EXISTS account_period (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50) NOT NULL UNIQUE,
    date_start DATE NOT NULL,
    date_end DATE NOT NULL,
    state ENUM('draft', 'open', 'closed') DEFAULT 'draft',
    company_id INT DEFAULT 1,
    fiscalyear_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_period_dates (date_start, date_end),
    INDEX idx_period_state (state),
    INDEX idx_period_code (code)
);

-- إدراج بيانات تجريبية للشركاء
INSERT INTO res_partner (name, email, phone, mobile, street, city, country, is_company, customer_rank, supplier_rank, customer_account_id, supplier_account_id, credit_limit, payment_terms) VALUES
('شركة الأحمد للتجارة', '<EMAIL>', '+************', '+************', 'شارع الملك فهد', 'الرياض', 'المملكة العربية السعودية', TRUE, 1, 0, 8, NULL, 50000.00, '30_days'),
('مؤسسة البناء الحديث', '<EMAIL>', '+************', '+************', 'طريق الملك عبدالعزيز', 'جدة', 'المملكة العربية السعودية', TRUE, 0, 1, NULL, 22, 0.00, 'immediate'),
('أحمد محمد السالم', '<EMAIL>', '+************', '+************', 'حي النخيل', 'الدمام', 'المملكة العربية السعودية', FALSE, 1, 0, 8, NULL, 10000.00, '15_days'),
('شركة التقنية المتقدمة', '<EMAIL>', '+************', '+************', 'شارع التحلية', 'الرياض', 'المملكة العربية السعودية', TRUE, 1, 1, 8, 22, 75000.00, '45_days'),
('فاطمة عبدالله النور', '<EMAIL>', '+************', '+************', 'حي الملز', 'الرياض', 'المملكة العربية السعودية', FALSE, 1, 0, 8, NULL, 5000.00, 'immediate'),
('مجموعة الخليج للاستثمار', '<EMAIL>', '+************', '+************', 'برج المملكة', 'الرياض', 'المملكة العربية السعودية', TRUE, 0, 1, NULL, 22, 0.00, '60_days');

-- إدراج بيانات تجريبية للفترات المحاسبية
INSERT INTO account_period (name, code, date_start, date_end, state, company_id) VALUES
('يناير 2024', '2024-01', '2024-01-01', '2024-01-31', 'closed', 1),
('فبراير 2024', '2024-02', '2024-02-01', '2024-02-29', 'closed', 1),
('مارس 2024', '2024-03', '2024-03-01', '2024-03-31', 'closed', 1),
('أبريل 2024', '2024-04', '2024-04-01', '2024-04-30', 'open', 1),
('مايو 2024', '2024-05', '2024-05-01', '2024-05-31', 'open', 1),
('يونيو 2024', '2024-06', '2024-06-01', '2024-06-30', 'draft', 1);

-- إدراج قيود محاسبية تجريبية
INSERT INTO account_move (name, ref, date, journal_id, state, amount_total, narration, company_id, currency_id, created_by, posted_by, posted_at) VALUES
('SAL/2024/0001', 'INV-001', '2024-01-15', 1, 'posted', 11500.00, 'فاتورة مبيعات رقم INV-001', 1, 1, 1, 1, '2024-01-15 10:30:00'),
('PUR/2024/0001', 'BILL-001', '2024-01-14', 2, 'posted', 5750.00, 'فاتورة مشتريات رقم BILL-001', 1, 1, 1, 1, '2024-01-14 14:20:00'),
('CSH/2024/0001', 'CASH-001', '2024-01-13', 3, 'posted', 2000.00, 'تحصيل نقدي من العميل', 1, 1, 1, 1, '2024-01-13 16:45:00'),
('MISC/2024/0001', 'ADJ-001', '2024-01-12', 6, 'draft', 1200.00, 'قيد تسوية مصروفات', 1, 1, 1, NULL, NULL),
('BNK1/2024/0001', 'TRF-001', '2024-01-11', 4, 'posted', 15000.00, 'تحويل بنكي من العميل', 1, 1, 1, 1, '2024-01-11 11:30:00');

-- إدراج بنود القيود المحاسبية
INSERT INTO account_move_line (move_id, account_id, name, debit, credit, balance, partner_id, date, ref, reconciled, reconcile_ref, company_id, currency_id) VALUES
-- قيد مبيعات (SAL/2024/0001)
(1, 8, 'فاتورة مبيعات INV-001', 11500.00, 0.00, 11500.00, 1, '2024-01-15', 'INV-001', FALSE, NULL, 1, 1),
(1, 36, 'فاتورة مبيعات INV-001', 0.00, 10000.00, -10000.00, NULL, '2024-01-15', 'INV-001', FALSE, NULL, 1, 1),
(1, 25, 'ضريبة القيمة المضافة', 0.00, 1500.00, -1500.00, NULL, '2024-01-15', 'INV-001', FALSE, NULL, 1, 1),

-- قيد مشتريات (PUR/2024/0001)
(2, 42, 'فاتورة مشتريات BILL-001', 5000.00, 0.00, 5000.00, NULL, '2024-01-14', 'BILL-001', FALSE, NULL, 1, 1),
(2, 25, 'ضريبة القيمة المضافة', 750.00, 0.00, 750.00, NULL, '2024-01-14', 'BILL-001', FALSE, NULL, 1, 1),
(2, 22, 'فاتورة مشتريات BILL-001', 0.00, 5750.00, -5750.00, 2, '2024-01-14', 'BILL-001', FALSE, NULL, 1, 1),

-- قيد تحصيل نقدي (CSH/2024/0001)
(3, 4, 'تحصيل نقدي من العميل', 2000.00, 0.00, 2000.00, NULL, '2024-01-13', 'CASH-001', FALSE, NULL, 1, 1),
(3, 8, 'تحصيل نقدي من العميل', 0.00, 2000.00, -2000.00, 1, '2024-01-13', 'CASH-001', FALSE, NULL, 1, 1),

-- قيد تسوية (MISC/2024/0001)
(4, 44, 'مصروفات الرواتب', 1200.00, 0.00, 1200.00, NULL, '2024-01-12', 'ADJ-001', FALSE, NULL, 1, 1),
(4, 25, 'رواتب مستحقة', 0.00, 1200.00, -1200.00, NULL, '2024-01-12', 'ADJ-001', FALSE, NULL, 1, 1),

-- قيد تحويل بنكي (BNK1/2024/0001)
(5, 5, 'تحويل بنكي من العميل', 15000.00, 0.00, 15000.00, NULL, '2024-01-11', 'TRF-001', FALSE, NULL, 1, 1),
(5, 8, 'تحويل بنكي من العميل', 0.00, 15000.00, -15000.00, 1, '2024-01-11', 'TRF-001', FALSE, NULL, 1, 1);
