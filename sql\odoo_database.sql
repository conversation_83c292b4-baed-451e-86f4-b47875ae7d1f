-- ===================================
-- قاعدة بيانات نظام ERP المحاسبي - بأسلوب Odoo
-- ===================================

CREATE DATABASE IF NOT EXISTS erp_accounting CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE erp_accounting;

-- ===================================
-- الجداول الأساسية (Base Models)
-- ===================================

-- جدول الشركات (res.company في Odoo)
CREATE TABLE res_company (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    display_name VARCHAR(255),
    code VARCHAR(10) UNIQUE,
    tax_number VARCHAR(15) UNIQUE,
    commercial_register VARCHAR(20),
    phone VARCHAR(20),
    mobile VARCHAR(20),
    email VARCHAR(100),
    website VARCHAR(255),
    address TEXT,
    street VARCHAR(255),
    street2 VARCHAR(255),
    city VARCHAR(100),
    state VARCHAR(100),
    country VARCHAR(100) DEFAULT 'السعودية',
    postal_code VARCHAR(10),
    logo LONGBLOB,
    currency_id INT DEFAULT 1,
    parent_id INT,
    active BOOLEAN DEFAULT TRUE,
    sequence INT DEFAULT 10,
    create_uid INT DEFAULT 1,
    write_uid INT DEFAULT 1,
    create_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    write_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_company_name (name),
    INDEX idx_company_code (code),
    INDEX idx_company_active (active),
    FOREIGN KEY (parent_id) REFERENCES res_company(id) ON DELETE SET NULL
);

-- جدول المستخدمين (res.users في Odoo)
CREATE TABLE res_users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    login VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(100),
    password VARCHAR(255) NOT NULL,
    active BOOLEAN DEFAULT TRUE,
    company_id INT NOT NULL DEFAULT 1,
    company_ids TEXT, -- JSON array of company IDs
    groups_id TEXT, -- JSON array of group IDs
    lang VARCHAR(10) DEFAULT 'ar_SA',
    tz VARCHAR(50) DEFAULT 'Asia/Riyadh',
    signature TEXT,
    avatar LONGBLOB,
    last_login TIMESTAMP NULL,
    login_count INT DEFAULT 0,
    create_uid INT DEFAULT 1,
    write_uid INT DEFAULT 1,
    create_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    write_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_users_login (login),
    INDEX idx_users_email (email),
    INDEX idx_users_active (active),
    FOREIGN KEY (company_id) REFERENCES res_company(id) ON DELETE RESTRICT
);

-- جدول العملات (res.currency في Odoo)
CREATE TABLE res_currency (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(3) NOT NULL UNIQUE, -- ISO code
    symbol VARCHAR(10) NOT NULL,
    rate DECIMAL(12,6) DEFAULT 1.000000,
    rounding DECIMAL(12,6) DEFAULT 0.010000,
    decimal_places INT DEFAULT 2,
    position VARCHAR(10) DEFAULT 'after', -- before or after
    active BOOLEAN DEFAULT TRUE,
    create_uid INT DEFAULT 1,
    write_uid INT DEFAULT 1,
    create_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    write_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_currency_name (name),
    INDEX idx_currency_active (active)
);

-- ===================================
-- جداول الشركاء والعملاء (Partners)
-- ===================================

-- جدول الشركاء (res.partner في Odoo)
CREATE TABLE res_partner (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    display_name VARCHAR(255),
    ref VARCHAR(50), -- Internal reference
    lang VARCHAR(10) DEFAULT 'ar_SA',
    tz VARCHAR(50) DEFAULT 'Asia/Riyadh',
    
    -- Contact Information
    email VARCHAR(100),
    phone VARCHAR(20),
    mobile VARCHAR(20),
    website VARCHAR(255),
    
    -- Address Information
    street VARCHAR(255),
    street2 VARCHAR(255),
    city VARCHAR(100),
    state_id INT,
    country_id INT DEFAULT 1,
    zip VARCHAR(10),
    
    -- Business Information
    is_company BOOLEAN DEFAULT FALSE,
    parent_id INT,
    child_ids TEXT, -- JSON array of child partner IDs
    category_id TEXT, -- JSON array of category IDs
    
    -- Partner Type
    customer_rank INT DEFAULT 0,
    supplier_rank INT DEFAULT 0,
    employee BOOLEAN DEFAULT FALSE,
    
    -- Financial Information
    property_account_receivable_id INT,
    property_account_payable_id INT,
    property_payment_term_id INT,
    property_supplier_payment_term_id INT,
    credit_limit DECIMAL(15,2) DEFAULT 0.00,
    
    -- Tax Information
    vat VARCHAR(15), -- Tax ID
    commercial_partner_id INT,
    commercial_company_name VARCHAR(255),
    
    -- Additional Fields
    title VARCHAR(50),
    function VARCHAR(100),
    comment TEXT,
    image LONGBLOB,
    color INT DEFAULT 0,
    
    -- System Fields
    active BOOLEAN DEFAULT TRUE,
    company_id INT NOT NULL DEFAULT 1,
    create_uid INT DEFAULT 1,
    write_uid INT DEFAULT 1,
    create_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    write_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_partner_name (name),
    INDEX idx_partner_ref (ref),
    INDEX idx_partner_email (email),
    INDEX idx_partner_vat (vat),
    INDEX idx_partner_customer (customer_rank),
    INDEX idx_partner_supplier (supplier_rank),
    INDEX idx_partner_company (is_company),
    INDEX idx_partner_active (active),
    FOREIGN KEY (parent_id) REFERENCES res_partner(id) ON DELETE SET NULL,
    FOREIGN KEY (commercial_partner_id) REFERENCES res_partner(id) ON DELETE SET NULL,
    FOREIGN KEY (company_id) REFERENCES res_company(id) ON DELETE RESTRICT
);

-- ===================================
-- جداول المنتجات والمخزون (Products & Inventory)
-- ===================================

-- جدول فئات المنتجات (product.category في Odoo)
CREATE TABLE product_category (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    complete_name VARCHAR(500),
    parent_id INT,
    child_id TEXT, -- JSON array of child category IDs
    parent_path VARCHAR(500),
    property_account_income_categ_id INT,
    property_account_expense_categ_id INT,
    property_stock_account_input_categ_id INT,
    property_stock_account_output_categ_id INT,
    property_stock_valuation_account_id INT,
    removal_strategy_id INT,
    active BOOLEAN DEFAULT TRUE,
    company_id INT NOT NULL DEFAULT 1,
    create_uid INT DEFAULT 1,
    write_uid INT DEFAULT 1,
    create_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    write_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_category_name (name),
    INDEX idx_category_parent (parent_id),
    INDEX idx_category_active (active),
    FOREIGN KEY (parent_id) REFERENCES product_category(id) ON DELETE SET NULL,
    FOREIGN KEY (company_id) REFERENCES res_company(id) ON DELETE RESTRICT
);

-- جدول فئات وحدات القياس (uom.category في Odoo)
CREATE TABLE uom_category (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    create_uid INT DEFAULT 1,
    write_uid INT DEFAULT 1,
    create_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    write_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_uom_cat_name (name)
);

-- جدول وحدات القياس (uom.uom في Odoo)
CREATE TABLE uom_uom (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    category_id INT NOT NULL,
    factor DECIMAL(12,6) DEFAULT 1.000000,
    factor_inv DECIMAL(12,6) DEFAULT 1.000000,
    rounding DECIMAL(12,6) DEFAULT 0.010000,
    uom_type VARCHAR(20) DEFAULT 'reference', -- reference, bigger, smaller
    active BOOLEAN DEFAULT TRUE,
    create_uid INT DEFAULT 1,
    write_uid INT DEFAULT 1,
    create_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    write_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_uom_name (name),
    INDEX idx_uom_category (category_id),
    INDEX idx_uom_active (active),
    FOREIGN KEY (category_id) REFERENCES uom_category(id) ON DELETE RESTRICT
);

-- جدول قوالب المنتجات (product.template في Odoo)
CREATE TABLE product_template (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    sequence INT DEFAULT 1,
    description TEXT,
    description_purchase TEXT,
    description_sale TEXT,
    type VARCHAR(20) DEFAULT 'consu', -- consu, service, product
    rental BOOLEAN DEFAULT FALSE,
    categ_id INT NOT NULL,
    list_price DECIMAL(15,2) DEFAULT 0.00,
    volume DECIMAL(12,3) DEFAULT 0.000,
    weight DECIMAL(12,3) DEFAULT 0.000,
    sale_ok BOOLEAN DEFAULT TRUE,
    purchase_ok BOOLEAN DEFAULT TRUE,
    uom_id INT NOT NULL,
    uom_po_id INT NOT NULL,
    company_id INT NOT NULL DEFAULT 1,
    active BOOLEAN DEFAULT TRUE,
    color INT DEFAULT 0,
    image_1920 LONGBLOB,
    can_image_1024_be_zoomed BOOLEAN DEFAULT FALSE,
    has_configurable_attributes BOOLEAN DEFAULT FALSE,
    create_uid INT DEFAULT 1,
    write_uid INT DEFAULT 1,
    create_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    write_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_template_name (name),
    INDEX idx_template_categ (categ_id),
    INDEX idx_template_type (type),
    INDEX idx_template_active (active),
    FOREIGN KEY (categ_id) REFERENCES product_category(id) ON DELETE RESTRICT,
    FOREIGN KEY (uom_id) REFERENCES uom_uom(id) ON DELETE RESTRICT,
    FOREIGN KEY (uom_po_id) REFERENCES uom_uom(id) ON DELETE RESTRICT,
    FOREIGN KEY (company_id) REFERENCES res_company(id) ON DELETE RESTRICT
);

-- جدول المنتجات (product.product في Odoo)
CREATE TABLE product_product (
    id INT PRIMARY KEY AUTO_INCREMENT,
    product_tmpl_id INT NOT NULL,
    default_code VARCHAR(50), -- Internal Reference/SKU
    barcode VARCHAR(50),
    combination_indices VARCHAR(255),
    price_extra DECIMAL(15,2) DEFAULT 0.00,
    active BOOLEAN DEFAULT TRUE,
    create_uid INT DEFAULT 1,
    write_uid INT DEFAULT 1,
    create_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    write_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_product_tmpl (product_tmpl_id),
    INDEX idx_product_code (default_code),
    INDEX idx_product_barcode (barcode),
    INDEX idx_product_active (active),
    FOREIGN KEY (product_tmpl_id) REFERENCES product_template(id) ON DELETE CASCADE
);

-- ===================================
-- جداول المحاسبة (Accounting)
-- ===================================

-- جدول دليل الحسابات (account.account في Odoo)
CREATE TABLE account_account (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(20) NOT NULL,
    user_type_id INT NOT NULL,
    internal_type VARCHAR(20) NOT NULL, -- receivable, payable, liquidity, other
    internal_group VARCHAR(50),
    reconcile BOOLEAN DEFAULT FALSE,
    deprecated BOOLEAN DEFAULT FALSE,
    company_id INT NOT NULL DEFAULT 1,
    currency_id INT,
    create_uid INT DEFAULT 1,
    write_uid INT DEFAULT 1,
    create_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    write_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE KEY unique_code_company (code, company_id),
    INDEX idx_account_name (name),
    INDEX idx_account_code (code),
    INDEX idx_account_type (user_type_id),
    INDEX idx_account_internal_type (internal_type),
    FOREIGN KEY (company_id) REFERENCES res_company(id) ON DELETE RESTRICT,
    FOREIGN KEY (currency_id) REFERENCES res_currency(id) ON DELETE SET NULL
);

-- جدول أنواع الحسابات (account.account.type في Odoo)
CREATE TABLE account_account_type (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL, -- receivable, payable, bank, cash, asset, liability, equity, income, expense
    internal_group VARCHAR(50),
    include_initial_balance BOOLEAN DEFAULT FALSE,
    create_uid INT DEFAULT 1,
    write_uid INT DEFAULT 1,
    create_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    write_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_account_type_name (name),
    INDEX idx_account_type_type (type)
);

-- جدول اليومية المحاسبية (account.journal في Odoo)
CREATE TABLE account_journal (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(10) NOT NULL,
    type VARCHAR(20) NOT NULL, -- sale, purchase, cash, bank, general
    default_account_id INT,
    sequence INT DEFAULT 10,
    active BOOLEAN DEFAULT TRUE,
    company_id INT NOT NULL DEFAULT 1,
    currency_id INT,
    create_uid INT DEFAULT 1,
    write_uid INT DEFAULT 1,
    create_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    write_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE KEY unique_code_company (code, company_id),
    INDEX idx_journal_name (name),
    INDEX idx_journal_code (code),
    INDEX idx_journal_type (type),
    FOREIGN KEY (default_account_id) REFERENCES account_account(id) ON DELETE SET NULL,
    FOREIGN KEY (company_id) REFERENCES res_company(id) ON DELETE RESTRICT,
    FOREIGN KEY (currency_id) REFERENCES res_currency(id) ON DELETE SET NULL
);

-- جدول القيود المحاسبية (account.move في Odoo)
CREATE TABLE account_move (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255),
    ref VARCHAR(255),
    date DATE NOT NULL,
    journal_id INT NOT NULL,
    company_id INT NOT NULL DEFAULT 1,
    currency_id INT,
    state VARCHAR(20) DEFAULT 'draft', -- draft, posted, cancel
    move_type VARCHAR(20) DEFAULT 'entry', -- entry, out_invoice, out_refund, in_invoice, in_refund, out_receipt, in_receipt
    partner_id INT,
    amount_untaxed DECIMAL(15,2) DEFAULT 0.00,
    amount_tax DECIMAL(15,2) DEFAULT 0.00,
    amount_total DECIMAL(15,2) DEFAULT 0.00,
    amount_residual DECIMAL(15,2) DEFAULT 0.00,
    payment_state VARCHAR(20) DEFAULT 'not_paid', -- not_paid, in_payment, paid, partial, reversed, invoicing_legacy
    invoice_date DATE,
    invoice_date_due DATE,
    invoice_payment_term_id INT,
    narration TEXT,
    create_uid INT DEFAULT 1,
    write_uid INT DEFAULT 1,
    create_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    write_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_move_name (name),
    INDEX idx_move_date (date),
    INDEX idx_move_journal (journal_id),
    INDEX idx_move_partner (partner_id),
    INDEX idx_move_state (state),
    INDEX idx_move_type (move_type),
    FOREIGN KEY (journal_id) REFERENCES account_journal(id) ON DELETE RESTRICT,
    FOREIGN KEY (company_id) REFERENCES res_company(id) ON DELETE RESTRICT,
    FOREIGN KEY (currency_id) REFERENCES res_currency(id) ON DELETE SET NULL,
    FOREIGN KEY (partner_id) REFERENCES res_partner(id) ON DELETE SET NULL
);
