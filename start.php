<?php
/**
 * صفحة البداية السريعة
 * نظام ERP المحاسبي
 */
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مرحباً بك في نظام ERP المحاسبي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/odoo-style.css" rel="stylesheet">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #714B67 0%, #875A7B 100%);
            color: white;
            padding: 4rem 0;
            text-align: center;
        }
        .feature-card {
            transition: all 0.3s ease;
            height: 100%;
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .quick-start-btn {
            font-size: 1.2rem;
            padding: 1rem 2rem;
            margin: 0.5rem;
        }
    </style>
</head>
<body>
    <!-- القسم الرئيسي -->
    <div class="hero-section">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8 text-center">
                    <i class="fas fa-chart-line fa-5x mb-4"></i>
                    <h1 class="display-4 fw-bold mb-3">نظام ERP المحاسبي</h1>
                    <p class="lead mb-4">نظام محاسبي متكامل بأسلوب Odoo، جاهز للاستخدام مع XAMPP</p>
                    
                    <!-- أزرار البداية السريعة -->
                    <div class="d-flex flex-wrap justify-content-center">
                        <a href="launch.php" class="btn btn-warning btn-lg quick-start-btn">
                            <i class="fas fa-rocket me-2"></i>
                            تشغيل فوري (الأسرع)
                        </a>
                        <a href="auto_setup.php" class="btn btn-light btn-lg quick-start-btn">
                            <i class="fas fa-magic me-2"></i>
                            تثبيت تلقائي
                        </a>
                        <a href="run.php" class="btn btn-outline-light btn-lg quick-start-btn">
                            <i class="fas fa-play me-2"></i>
                            تشغيل مباشر
                        </a>
                    </div>
                    
                    <!-- شرح طرق التشغيل -->
                    <div class="row mt-4">
                        <div class="col-md-4">
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-rocket me-2"></i>التشغيل الفوري</h6>
                                <small>تثبيت وتشغيل في ثوان معدودة بدون أي تدخل</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="alert alert-light">
                                <h6><i class="fas fa-magic me-2"></i>التثبيت التلقائي</h6>
                                <small>عرض خطوات التثبيت مع إعداد شامل</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-play me-2"></i>التشغيل المباشر</h6>
                                <small>دخول فوري للنظام بدون انتظار</small>
                            </div>
                        </div>
                    </div>

                    <!-- بيانات تسجيل الدخول -->
                    <div class="alert alert-light mt-4 d-inline-block">
                        <h6 class="text-dark mb-2">
                            <i class="fas fa-key me-2"></i>
                            بيانات تسجيل الدخول
                        </h6>
                        <div class="text-dark">
                            <strong>البريد الإلكتروني:</strong> <EMAIL><br>
                            <strong>كلمة المرور:</strong> admin123
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الميزات الرئيسية -->
    <div class="container my-5">
        <div class="row text-center mb-5">
            <div class="col-12">
                <h2 class="fw-bold mb-3">الميزات الرئيسية</h2>
                <p class="text-muted">نظام شامل لإدارة جميع العمليات المحاسبية والتجارية</p>
            </div>
        </div>
        
        <div class="row g-4">
            <div class="col-md-6 col-lg-4">
                <div class="card feature-card">
                    <div class="card-body text-center p-4">
                        <i class="fas fa-building fa-3x text-primary mb-3"></i>
                        <h5 class="card-title">إدارة الشركات</h5>
                        <p class="card-text">إدارة متعددة الشركات والفروع مع هيكل تنظيمي مرن</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 col-lg-4">
                <div class="card feature-card">
                    <div class="card-body text-center p-4">
                        <i class="fas fa-users fa-3x text-success mb-3"></i>
                        <h5 class="card-title">إدارة العملاء</h5>
                        <p class="card-text">قاعدة بيانات شاملة للعملاء مع متابعة الأرصدة والحدود الائتمانية</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 col-lg-4">
                <div class="card feature-card">
                    <div class="card-body text-center p-4">
                        <i class="fas fa-file-invoice fa-3x text-info mb-3"></i>
                        <h5 class="card-title">إدارة الفواتير</h5>
                        <p class="card-text">نظام فواتير متقدم مع تتبع المدفوعات والاستحقاقات</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 col-lg-4">
                <div class="card feature-card">
                    <div class="card-body text-center p-4">
                        <i class="fas fa-boxes fa-3x text-warning mb-3"></i>
                        <h5 class="card-title">إدارة المخزون</h5>
                        <p class="card-text">متابعة المنتجات والمخزون مع تنبيهات الحدود الدنيا والعليا</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 col-lg-4">
                <div class="card feature-card">
                    <div class="card-body text-center p-4">
                        <i class="fas fa-chart-bar fa-3x text-danger mb-3"></i>
                        <h5 class="card-title">التقارير والإحصائيات</h5>
                        <p class="card-text">تقارير مالية شاملة مع رسوم بيانية تفاعلية</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 col-lg-4">
                <div class="card feature-card">
                    <div class="card-body text-center p-4">
                        <i class="fas fa-cogs fa-3x text-secondary mb-3"></i>
                        <h5 class="card-title">إعدادات متقدمة</h5>
                        <p class="card-text">تخصيص النظام حسب احتياجات شركتك مع إعدادات مرنة</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- المتطلبات التقنية -->
    <div class="bg-light py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-6">
                    <h3 class="fw-bold mb-3">المتطلبات التقنية</h3>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            PHP 5.2+ (متوافق مع XAMPP)
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Apache Web Server
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            MySQL (اختياري للنسخة التجريبية)
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            متصفح حديث يدعم JavaScript
                        </li>
                    </ul>
                </div>
                
                <div class="col-lg-6">
                    <h3 class="fw-bold mb-3">خطوات التشغيل</h3>
                    <ol class="list-unstyled">
                        <li class="mb-2">
                            <span class="badge bg-primary me-2">1</span>
                            تشغيل XAMPP وتفعيل Apache
                        </li>
                        <li class="mb-2">
                            <span class="badge bg-primary me-2">2</span>
                            وضع الملفات في مجلد htdocs/acc
                        </li>
                        <li class="mb-2">
                            <span class="badge bg-primary me-2">3</span>
                            فتح المتصفح والذهاب إلى localhost/acc
                        </li>
                        <li class="mb-2">
                            <span class="badge bg-primary me-2">4</span>
                            تسجيل الدخول والبدء في الاستخدام
                        </li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- الروابط السريعة -->
    <div class="container py-5">
        <div class="row text-center">
            <div class="col-12">
                <h3 class="fw-bold mb-4">الروابط السريعة</h3>
            </div>
        </div>
        
        <div class="row g-3 justify-content-center">
            <div class="col-md-6 col-lg-3">
                <a href="demo.php" class="btn btn-outline-primary w-100 p-3">
                    <i class="fas fa-home fa-2x d-block mb-2"></i>
                    الصفحة الرئيسية
                </a>
            </div>
            
            <div class="col-md-6 col-lg-3">
                <a href="pages/companies.php" class="btn btn-outline-success w-100 p-3">
                    <i class="fas fa-building fa-2x d-block mb-2"></i>
                    إدارة الشركات
                </a>
            </div>
            
            <div class="col-md-6 col-lg-3">
                <a href="pages/customers.php" class="btn btn-outline-info w-100 p-3">
                    <i class="fas fa-users fa-2x d-block mb-2"></i>
                    إدارة العملاء
                </a>
            </div>
            
            <div class="col-md-6 col-lg-3">
                <a href="pages/invoices.php" class="btn btn-outline-warning w-100 p-3">
                    <i class="fas fa-file-invoice fa-2x d-block mb-2"></i>
                    إدارة الفواتير
                </a>
            </div>
        </div>
    </div>

    <!-- التذييل -->
    <footer class="bg-dark text-white py-4">
        <div class="container text-center">
            <p class="mb-0">
                <i class="fas fa-heart text-danger me-1"></i>
                نظام ERP المحاسبي - مبني بأسلوب Odoo
                <i class="fas fa-heart text-danger ms-1"></i>
            </p>
            <small class="text-muted">جاهز للاستخدام مع XAMPP</small>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تأثيرات بسيطة
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير hover للبطاقات
            const cards = document.querySelectorAll('.feature-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>
</html>
