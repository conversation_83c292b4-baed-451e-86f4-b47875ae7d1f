<?php
/**
 * صفحة البداية - نظام ERP
 * Start Page - ERP System
 */
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام ERP - صفحة البداية</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #714B67, #875A7B);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .start-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 800px;
            width: 90%;
        }
        
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo i {
            font-size: 4rem;
            color: #714B67;
            margin-bottom: 15px;
        }
        
        .logo h1 {
            color: #2F3349;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .logo p {
            color: #6c757d;
            font-size: 1.1rem;
        }
        
        .quick-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .quick-link {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            text-decoration: none;
            color: #2F3349;
            transition: all 0.3s ease;
        }
        
        .quick-link:hover {
            background: #714B67;
            color: white;
            border-color: #714B67;
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(113, 75, 103, 0.3);
        }
        
        .quick-link i {
            font-size: 2.5rem;
            margin-bottom: 15px;
            display: block;
        }
        
        .quick-link h5 {
            margin-bottom: 10px;
            font-weight: 600;
        }
        
        .quick-link p {
            font-size: 0.9rem;
            margin: 0;
            opacity: 0.8;
        }
        
        .status-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 15px;
            margin-top: 30px;
        }
        
        .status-info h6 {
            color: #0066cc;
            margin-bottom: 10px;
        }
        
        .status-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .status-list li {
            padding: 5px 0;
            color: #333;
        }
        
        .status-list li i {
            color: #28a745;
            margin-left: 10px;
        }
        
        @media (max-width: 768px) {
            .start-container {
                padding: 20px;
                margin: 20px;
            }
            
            .quick-links {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="start-container">
        <div class="logo">
            <i class="fas fa-chart-line"></i>
            <h1>نظام ERP المتكامل</h1>
            <p>نظام إدارة موارد المؤسسة بأسلوب Odoo</p>
        </div>
        
        <div class="quick-links">
            <a href="login.php" class="quick-link">
                <i class="fas fa-sign-in-alt"></i>
                <h5>تسجيل الدخول</h5>
                <p>الدخول للنظام</p>
            </a>
            
            <a href="dashboard.php" class="quick-link">
                <i class="fas fa-tachometer-alt"></i>
                <h5>لوحة التحكم</h5>
                <p>الصفحة الرئيسية</p>
            </a>
            
            <a href="pages/customers_odoo.php" class="quick-link">
                <i class="fas fa-users"></i>
                <h5>العملاء</h5>
                <p>إدارة العملاء</p>
            </a>
            
            <a href="pages/products_enhanced.php" class="quick-link">
                <i class="fas fa-boxes"></i>
                <h5>المنتجات</h5>
                <p>إدارة المنتجات</p>
            </a>
            
            <a href="pages/suppliers_odoo.php" class="quick-link">
                <i class="fas fa-truck"></i>
                <h5>الموردين</h5>
                <p>إدارة الموردين</p>
            </a>
            
            <a href="pages/partners_fixed.php" class="quick-link">
                <i class="fas fa-handshake"></i>
                <h5>الشركاء</h5>
                <p>إدارة الشركاء</p>
            </a>
            
            <a href="pages/journal_entries_odoo.php" class="quick-link">
                <i class="fas fa-book"></i>
                <h5>قيود اليومية</h5>
                <p>المحاسبة</p>
            </a>
            
            <a href="pages/settings_odoo.php" class="quick-link">
                <i class="fas fa-cog"></i>
                <h5>الإعدادات</h5>
                <p>إعدادات النظام</p>
            </a>
        </div>
        
        <div class="status-info">
            <h6><i class="fas fa-info-circle me-2"></i>حالة النظام</h6>
            <ul class="status-list">
                <li><i class="fas fa-check"></i>النماذج مكتملة ومربوطة</li>
                <li><i class="fas fa-check"></i>واجهات Odoo جاهزة</li>
                <?php
                // التحقق من قاعدة البيانات
                try {
                    $pdo = new PDO("mysql:host=localhost;dbname=erp_accounting", "root", "");
                    echo '<li><i class="fas fa-check"></i>قاعدة البيانات متصلة</li>';
                    echo '<li><i class="fas fa-check"></i>النظام جاهز للاستخدام</li>';
                } catch (PDOException $e) {
                    echo '<li><i class="fas fa-exclamation-triangle" style="color: #ffc107;"></i>قاعدة البيانات تحتاج إعداد</li>';
                    echo '<li><i class="fas fa-tools" style="color: #17a2b8;"></i><a href="setup_database.php" style="color: #17a2b8; text-decoration: none;">انقر هنا لإعداد قاعدة البيانات</a></li>';
                }
                ?>
            </ul>
        </div>
        
        <div class="text-center mt-4">
            <h6>النماذج الجديدة:</h6>
            <div class="row mt-3">
                <div class="col-md-3">
                    <a href="pages/company_form.php" class="btn btn-outline-primary btn-sm w-100 mb-2">
                        <i class="fas fa-building me-1"></i>إضافة شركة
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="pages/user_form.php" class="btn btn-outline-info btn-sm w-100 mb-2">
                        <i class="fas fa-user-plus me-1"></i>إضافة مستخدم
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="pages/product_form.php" class="btn btn-outline-warning btn-sm w-100 mb-2">
                        <i class="fas fa-box me-1"></i>إضافة منتج
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="pages/partner_form.php" class="btn btn-outline-success btn-sm w-100 mb-2">
                        <i class="fas fa-handshake me-1"></i>إضافة شريك
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
