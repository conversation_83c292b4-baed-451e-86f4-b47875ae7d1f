<?php
/**
 * صفحة حالة النظام
 * System Status Page
 */

session_start();

// جمع معلومات النظام
$system_info = array(
    'php_version' => PHP_VERSION,
    'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'غير معروف',
    'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? '',
    'current_time' => date('Y-m-d H:i:s'),
    'timezone' => date_default_timezone_get(),
    'memory_limit' => ini_get('memory_limit'),
    'max_execution_time' => ini_get('max_execution_time'),
    'upload_max_filesize' => ini_get('upload_max_filesize'),
    'post_max_size' => ini_get('post_max_size')
);

// فحص المتطلبات
$requirements = array(
    'php_version' => version_compare(PHP_VERSION, '5.2.0', '>='),
    'pdo_available' => class_exists('PDO'),
    'mysql_available' => extension_loaded('mysql') || extension_loaded('mysqli') || extension_loaded('pdo_mysql'),
    'json_available' => function_exists('json_encode'),
    'session_available' => function_exists('session_start'),
    'writable_config' => is_writable('config') || is_writable('.'),
    'htaccess_exists' => file_exists('.htaccess')
);

// فحص الملفات المطلوبة
$required_files = array(
    'config/database_simple.php' => file_exists('config/database_simple.php'),
    'config/database_odoo.php' => file_exists('config/database_odoo.php'),
    'models/BaseModel.php' => file_exists('models/BaseModel.php'),
    'assets/css/odoo-style.css' => file_exists('assets/css/odoo-style.css'),
    'sql/odoo_database.sql' => file_exists('sql/odoo_database.sql')
);

// فحص قاعدة البيانات
$database_status = array(
    'config_exists' => file_exists('config/database_config.php'),
    'connection' => false,
    'tables_exist' => false,
    'demo_mode' => false
);

if ($database_status['config_exists']) {
    try {
        require_once 'config/database_config.php';
        
        if (defined('DEMO_MODE') && DEMO_MODE) {
            $database_status['demo_mode'] = true;
            $database_status['connection'] = true;
        } else {
            require_once 'config/database_odoo.php';
            $database_status['connection'] = true;
            
            // فحص وجود الجداول
            $tables = array('res_company', 'res_users', 'res_partner');
            $existing_tables = 0;
            
            foreach ($tables as $table) {
                try {
                    $result = $database->query("SELECT 1 FROM {$table} LIMIT 1");
                    if ($result) $existing_tables++;
                } catch (Exception $e) {
                    // الجدول غير موجود
                }
            }
            
            $database_status['tables_exist'] = $existing_tables >= count($tables);
        }
        
    } catch (Exception $e) {
        $database_status['connection'] = false;
        $database_status['error'] = $e->getMessage();
    }
}

// فحص الجلسة
$session_status = array(
    'logged_in' => isset($_SESSION['user_id']),
    'user_id' => $_SESSION['user_id'] ?? null,
    'username' => $_SESSION['username'] ?? null,
    'email' => $_SESSION['email'] ?? null,
    'company_id' => $_SESSION['company_id'] ?? null
);

// حساب النسبة الإجمالية للجاهزية
$total_checks = count($requirements) + count($required_files) + 3; // 3 for database checks
$passed_checks = array_sum($requirements) + array_sum($required_files);

if ($database_status['config_exists']) $passed_checks++;
if ($database_status['connection']) $passed_checks++;
if ($database_status['tables_exist'] || $database_status['demo_mode']) $passed_checks++;

$readiness_percentage = round(($passed_checks / $total_checks) * 100);

// تحديد حالة النظام العامة
if ($readiness_percentage >= 90) {
    $system_status = 'excellent';
    $status_text = 'ممتاز';
    $status_color = 'success';
} elseif ($readiness_percentage >= 70) {
    $system_status = 'good';
    $status_text = 'جيد';
    $status_color = 'warning';
} else {
    $system_status = 'poor';
    $status_text = 'يحتاج إعداد';
    $status_color = 'danger';
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حالة النظام - نظام ERP المحاسبي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/odoo-style.css" rel="stylesheet">
    <style>
        .status-card {
            transition: all 0.3s ease;
        }
        .status-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .progress-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: bold;
            margin: 0 auto;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <!-- العنوان الرئيسي -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h3 class="mb-0">
                            <i class="fas fa-heartbeat me-2"></i>
                            حالة النظام
                        </h3>
                    </div>
                    <div class="card-body text-center">
                        <div class="row align-items-center">
                            <div class="col-md-4">
                                <div class="progress-circle bg-<?php echo $status_color; ?> text-white">
                                    <?php echo $readiness_percentage; ?>%
                                </div>
                                <h4 class="mt-3 text-<?php echo $status_color; ?>"><?php echo $status_text; ?></h4>
                            </div>
                            <div class="col-md-8">
                                <div class="progress mb-3" style="height: 20px;">
                                    <div class="progress-bar bg-<?php echo $status_color; ?>" 
                                         style="width: <?php echo $readiness_percentage; ?>%">
                                        <?php echo $readiness_percentage; ?>%
                                    </div>
                                </div>
                                <p class="lead">
                                    النظام جاهز بنسبة <strong><?php echo $readiness_percentage; ?>%</strong>
                                </p>
                                
                                <?php if ($readiness_percentage < 100): ?>
                                    <div class="mt-3">
                                        <a href="auto_setup.php" class="btn btn-primary">
                                            <i class="fas fa-magic me-2"></i>
                                            إكمال الإعداد تلقائياً
                                        </a>
                                    </div>
                                <?php else: ?>
                                    <div class="mt-3">
                                        <a href="demo.php" class="btn btn-success">
                                            <i class="fas fa-play me-2"></i>
                                            تشغيل النظام
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- تفاصيل الحالة -->
        <div class="row">
            <!-- المتطلبات -->
            <div class="col-md-6 mb-4">
                <div class="card status-card">
                    <div class="card-header">
                        <h5><i class="fas fa-check-circle me-2"></i>المتطلبات</h5>
                    </div>
                    <div class="card-body">
                        <?php foreach ($requirements as $req => $status): ?>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>
                                    <?php
                                    $req_names = array(
                                        'php_version' => 'PHP 5.2+',
                                        'pdo_available' => 'PDO متوفر',
                                        'mysql_available' => 'MySQL متوفر',
                                        'json_available' => 'JSON متوفر',
                                        'session_available' => 'Sessions متوفرة',
                                        'writable_config' => 'مجلد قابل للكتابة',
                                        'htaccess_exists' => '.htaccess موجود'
                                    );
                                    echo $req_names[$req] ?? $req;
                                    ?>
                                </span>
                                <span class="badge bg-<?php echo $status ? 'success' : 'danger'; ?>">
                                    <i class="fas fa-<?php echo $status ? 'check' : 'times'; ?>"></i>
                                </span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- الملفات المطلوبة -->
            <div class="col-md-6 mb-4">
                <div class="card status-card">
                    <div class="card-header">
                        <h5><i class="fas fa-file-code me-2"></i>الملفات المطلوبة</h5>
                    </div>
                    <div class="card-body">
                        <?php foreach ($required_files as $file => $exists): ?>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-truncate" title="<?php echo $file; ?>">
                                    <?php echo basename($file); ?>
                                </span>
                                <span class="badge bg-<?php echo $exists ? 'success' : 'danger'; ?>">
                                    <i class="fas fa-<?php echo $exists ? 'check' : 'times'; ?>"></i>
                                </span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- قاعدة البيانات -->
            <div class="col-md-6 mb-4">
                <div class="card status-card">
                    <div class="card-header">
                        <h5><i class="fas fa-database me-2"></i>قاعدة البيانات</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>ملف التكوين</span>
                            <span class="badge bg-<?php echo $database_status['config_exists'] ? 'success' : 'warning'; ?>">
                                <i class="fas fa-<?php echo $database_status['config_exists'] ? 'check' : 'exclamation'; ?>"></i>
                            </span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>الاتصال</span>
                            <span class="badge bg-<?php echo $database_status['connection'] ? 'success' : 'danger'; ?>">
                                <i class="fas fa-<?php echo $database_status['connection'] ? 'check' : 'times'; ?>"></i>
                            </span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>الجداول</span>
                            <span class="badge bg-<?php echo ($database_status['tables_exist'] || $database_status['demo_mode']) ? 'success' : 'warning'; ?>">
                                <i class="fas fa-<?php echo ($database_status['tables_exist'] || $database_status['demo_mode']) ? 'check' : 'exclamation'; ?>"></i>
                            </span>
                        </div>
                        <?php if ($database_status['demo_mode']): ?>
                            <div class="alert alert-info mt-2">
                                <small><i class="fas fa-info-circle me-1"></i>الوضع التجريبي مفعل</small>
                            </div>
                        <?php endif; ?>
                        <?php if (isset($database_status['error'])): ?>
                            <div class="alert alert-danger mt-2">
                                <small><?php echo $database_status['error']; ?></small>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- الجلسة -->
            <div class="col-md-6 mb-4">
                <div class="card status-card">
                    <div class="card-header">
                        <h5><i class="fas fa-user me-2"></i>الجلسة الحالية</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($session_status['logged_in']): ?>
                            <div class="alert alert-success">
                                <h6><i class="fas fa-check-circle me-2"></i>مسجل دخول</h6>
                                <small>
                                    <strong>المستخدم:</strong> <?php echo $session_status['username']; ?><br>
                                    <strong>البريد:</strong> <?php echo $session_status['email']; ?><br>
                                    <strong>الشركة:</strong> <?php echo $session_status['company_id']; ?>
                                </small>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle me-2"></i>غير مسجل دخول</h6>
                                <a href="pages/login.php" class="btn btn-sm btn-primary">تسجيل الدخول</a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات النظام -->
        <div class="row">
            <div class="col-12">
                <div class="card status-card">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle me-2"></i>معلومات النظام</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php foreach ($system_info as $key => $value): ?>
                                <div class="col-md-6 mb-2">
                                    <strong>
                                        <?php
                                        $info_names = array(
                                            'php_version' => 'إصدار PHP',
                                            'server_software' => 'خادم الويب',
                                            'document_root' => 'مجلد الجذر',
                                            'current_time' => 'الوقت الحالي',
                                            'timezone' => 'المنطقة الزمنية',
                                            'memory_limit' => 'حد الذاكرة',
                                            'max_execution_time' => 'وقت التنفيذ الأقصى',
                                            'upload_max_filesize' => 'حجم الرفع الأقصى',
                                            'post_max_size' => 'حجم POST الأقصى'
                                        );
                                        echo $info_names[$key] ?? $key;
                                        ?>:
                                    </strong>
                                    <span class="text-muted"><?php echo $value; ?></span>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- أزرار الإجراءات -->
        <div class="row mt-4">
            <div class="col-12 text-center">
                <div class="btn-group" role="group">
                    <a href="index.php" class="btn btn-primary">
                        <i class="fas fa-home me-2"></i>الصفحة الرئيسية
                    </a>
                    <a href="test_database.php" class="btn btn-info">
                        <i class="fas fa-vial me-2"></i>اختبار قاعدة البيانات
                    </a>
                    <a href="auto_setup.php" class="btn btn-success">
                        <i class="fas fa-magic me-2"></i>إعداد تلقائي
                    </a>
                    <button onclick="location.reload()" class="btn btn-secondary">
                        <i class="fas fa-sync me-2"></i>تحديث
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحديث تلقائي كل دقيقة
        setTimeout(function() {
            location.reload();
        }, 60000);
        
        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.status-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
