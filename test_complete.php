<?php
/**
 * اختبار شامل للنظام بأسلوب Odoo
 * Complete Odoo-Style System Test
 */

session_start();

// تشغيل تلقائي إذا لم يكن مسجل دخول
if (!isset($_SESSION['user_id'])) {
    require_once 'go.php';
    exit();
}

$test_results = array();
$overall_status = 'success';

try {
    // اختبار 1: ملفات التكوين
    $test_results[] = array(
        'name' => 'ملفات التكوين',
        'status' => file_exists('config/database_config.php') ? 'success' : 'warning',
        'message' => file_exists('config/database_config.php') ? 'ملف التكوين موجود' : 'يتم استخدام التكوين البسيط'
    );
    
    // اختبار 2: نظام Odoo
    $odoo_loaded = false;
    if (file_exists('includes/odoo_registry.php')) {
        require_once 'includes/odoo_registry.php';
        require_once 'includes/odoo_menu.php';
        require_once 'includes/odoo_actions.php';
        $odoo_loaded = true;
    }
    
    $test_results[] = array(
        'name' => 'نظام Odoo',
        'status' => $odoo_loaded ? 'success' : 'error',
        'message' => $odoo_loaded ? 'نظام Odoo محمل بنجاح' : 'فشل في تحميل نظام Odoo'
    );
    
    // اختبار 3: سجل الوحدات
    if ($odoo_loaded) {
        $registry = OdooRegistry::getInstance();
        $modules = $registry->getModules();
        
        $test_results[] = array(
            'name' => 'سجل الوحدات',
            'status' => count($modules) > 0 ? 'success' : 'warning',
            'message' => 'تم تحميل ' . count($modules) . ' وحدة'
        );
        
        // اختبار 4: نظام القوائم
        $menu = OdooMenu::getInstance();
        $user_menus = $menu->getMenusForUser(array('admin'));
        
        $test_results[] = array(
            'name' => 'نظام القوائم',
            'status' => count($user_menus) > 0 ? 'success' : 'warning',
            'message' => 'تم تحميل ' . count($user_menus) . ' قائمة'
        );
        
        // اختبار 5: نظام الإجراءات
        $actions = OdooActions::getInstance();
        $window_actions = $actions->getActionsByType('window');
        
        $test_results[] = array(
            'name' => 'نظام الإجراءات',
            'status' => count($window_actions) > 0 ? 'success' : 'warning',
            'message' => 'تم تحميل ' . count($window_actions) . ' إجراء نافذة'
        );
    }
    
    // اختبار 6: قاعدة البيانات
    $db_status = 'error';
    $db_message = 'غير متصل';
    
    if (file_exists('config/database_config.php')) {
        require_once 'config/database_config.php';
        
        if (defined('DEMO_MODE') && DEMO_MODE) {
            $db_status = 'warning';
            $db_message = 'الوضع التجريبي مفعل';
        } else {
            try {
                require_once 'config/database_odoo.php';
                $db_status = 'success';
                $db_message = 'متصل بقاعدة البيانات';
            } catch (Exception $e) {
                $db_status = 'error';
                $db_message = 'خطأ في الاتصال: ' . $e->getMessage();
            }
        }
    }
    
    $test_results[] = array(
        'name' => 'قاعدة البيانات',
        'status' => $db_status,
        'message' => $db_message
    );
    
    // اختبار 7: الملفات الأساسية
    $required_files = array(
        'demo.php' => 'الصفحة الرئيسية',
        'pages/companies.php' => 'صفحة الشركات',
        'pages/customers.php' => 'صفحة العملاء',
        'pages/products.php' => 'صفحة المنتجات',
        'pages/invoices.php' => 'صفحة الفواتير',
        'assets/css/odoo-style.css' => 'ملف التصميم'
    );
    
    $missing_files = array();
    foreach ($required_files as $file => $description) {
        if (!file_exists($file)) {
            $missing_files[] = $description;
        }
    }
    
    $test_results[] = array(
        'name' => 'الملفات الأساسية',
        'status' => empty($missing_files) ? 'success' : 'warning',
        'message' => empty($missing_files) ? 'جميع الملفات موجودة' : 'ملفات مفقودة: ' . implode(', ', $missing_files)
    );
    
    // اختبار 8: الجلسة
    $session_data = array(
        'user_id' => isset($_SESSION['user_id']),
        'username' => isset($_SESSION['username']),
        'groups' => isset($_SESSION['groups']),
        'odoo_style' => isset($_SESSION['odoo_style'])
    );
    
    $session_ok = array_sum($session_data) >= 3;
    
    $test_results[] = array(
        'name' => 'بيانات الجلسة',
        'status' => $session_ok ? 'success' : 'warning',
        'message' => $session_ok ? 'بيانات الجلسة مكتملة' : 'بيانات الجلسة ناقصة'
    );
    
    // تحديد الحالة العامة
    foreach ($test_results as $result) {
        if ($result['status'] === 'error') {
            $overall_status = 'error';
            break;
        } elseif ($result['status'] === 'warning' && $overall_status !== 'error') {
            $overall_status = 'warning';
        }
    }
    
} catch (Exception $e) {
    $test_results[] = array(
        'name' => 'خطأ عام',
        'status' => 'error',
        'message' => $e->getMessage()
    );
    $overall_status = 'error';
}

// إحصائيات سريعة
$success_count = 0;
$warning_count = 0;
$error_count = 0;

foreach ($test_results as $result) {
    switch ($result['status']) {
        case 'success': $success_count++; break;
        case 'warning': $warning_count++; break;
        case 'error': $error_count++; break;
    }
}

$total_tests = count($test_results);
$success_percentage = round(($success_count / $total_tests) * 100);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار شامل - نظام ERP بأسلوب Odoo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/odoo-style.css" rel="stylesheet">
    <style>
        .test-card {
            transition: all 0.3s ease;
            border-left: 4px solid #dee2e6;
        }
        .test-card.success { border-left-color: #28a745; }
        .test-card.warning { border-left-color: #ffc107; }
        .test-card.error { border-left-color: #dc3545; }
        
        .status-icon.success { color: #28a745; }
        .status-icon.warning { color: #ffc107; }
        .status-icon.error { color: #dc3545; }
        
        .progress-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: bold;
            margin: 0 auto;
        }
        
        .odoo-badge {
            background: linear-gradient(45deg, #714B67, #8B5A8C);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <!-- العنوان الرئيسي -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h3 class="mb-0">
                                <i class="fas fa-vial me-2"></i>
                                اختبار شامل للنظام
                            </h3>
                            <span class="odoo-badge">
                                <i class="fas fa-cube me-1"></i>
                                Odoo Style
                            </span>
                        </div>
                    </div>
                    <div class="card-body text-center">
                        <div class="row align-items-center">
                            <div class="col-md-4">
                                <div class="progress-circle bg-<?php echo $overall_status === 'success' ? 'success' : ($overall_status === 'warning' ? 'warning' : 'danger'); ?> text-white">
                                    <?php echo $success_percentage; ?>%
                                </div>
                                <h4 class="mt-3 text-<?php echo $overall_status === 'success' ? 'success' : ($overall_status === 'warning' ? 'warning' : 'danger'); ?>">
                                    <?php 
                                    echo $overall_status === 'success' ? 'ممتاز' : ($overall_status === 'warning' ? 'جيد' : 'يحتاج إصلاح');
                                    ?>
                                </h4>
                            </div>
                            <div class="col-md-8">
                                <div class="row">
                                    <div class="col-4">
                                        <div class="text-success">
                                            <i class="fas fa-check-circle fa-2x"></i>
                                            <h5 class="mt-2"><?php echo $success_count; ?></h5>
                                            <small>نجح</small>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="text-warning">
                                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                                            <h5 class="mt-2"><?php echo $warning_count; ?></h5>
                                            <small>تحذير</small>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="text-danger">
                                            <i class="fas fa-times-circle fa-2x"></i>
                                            <h5 class="mt-2"><?php echo $error_count; ?></h5>
                                            <small>خطأ</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="progress mt-3" style="height: 20px;">
                                    <div class="progress-bar bg-success" style="width: <?php echo ($success_count / $total_tests) * 100; ?>%"></div>
                                    <div class="progress-bar bg-warning" style="width: <?php echo ($warning_count / $total_tests) * 100; ?>%"></div>
                                    <div class="progress-bar bg-danger" style="width: <?php echo ($error_count / $total_tests) * 100; ?>%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- نتائج الاختبارات -->
        <div class="row">
            <div class="col-12">
                <h5 class="mb-3">نتائج الاختبارات التفصيلية:</h5>
                <div class="row">
                    <?php foreach ($test_results as $index => $result): ?>
                        <div class="col-md-6 mb-3">
                            <div class="card test-card <?php echo $result['status']; ?>">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="me-3">
                                            <i class="fas fa-<?php 
                                                echo $result['status'] === 'success' ? 'check-circle' : 
                                                    ($result['status'] === 'warning' ? 'exclamation-triangle' : 'times-circle'); 
                                            ?> fa-2x status-icon <?php echo $result['status']; ?>"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1"><?php echo $result['name']; ?></h6>
                                            <small class="text-muted"><?php echo $result['message']; ?></small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- معلومات النظام -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle me-2"></i>معلومات النظام</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="list-unstyled">
                                    <li><strong>إصدار PHP:</strong> <?php echo PHP_VERSION; ?></li>
                                    <li><strong>نمط Odoo:</strong> <?php echo isset($_SESSION['odoo_style']) && $_SESSION['odoo_style'] ? 'مفعل' : 'غير مفعل'; ?></li>
                                    <li><strong>المستخدم:</strong> <?php echo $_SESSION['username'] ?? 'غير محدد'; ?></li>
                                    <li><strong>المجموعات:</strong> <?php echo isset($_SESSION['groups']) ? implode(', ', $_SESSION['groups']) : 'غير محدد'; ?></li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="list-unstyled">
                                    <li><strong>الوقت الحالي:</strong> <?php echo date('Y-m-d H:i:s'); ?></li>
                                    <li><strong>الوضع التجريبي:</strong> <?php echo isset($_SESSION['demo_mode']) && $_SESSION['demo_mode'] ? 'مفعل' : 'غير مفعل'; ?></li>
                                    <li><strong>الشركة:</strong> <?php echo $_SESSION['company_name'] ?? 'غير محدد'; ?></li>
                                    <li><strong>الوحدات:</strong> <?php echo isset($_SESSION['modules']) ? count($_SESSION['modules']) : '0'; ?></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- أزرار الإجراءات -->
        <div class="row mt-4">
            <div class="col-12 text-center">
                <div class="btn-group" role="group">
                    <a href="demo.php" class="btn btn-primary">
                        <i class="fas fa-home me-2"></i>الصفحة الرئيسية
                    </a>
                    <a href="go.php" class="btn btn-success">
                        <i class="fas fa-rocket me-2"></i>تشغيل فوري
                    </a>
                    <a href="status.php" class="btn btn-info">
                        <i class="fas fa-heartbeat me-2"></i>حالة النظام
                    </a>
                    <button onclick="location.reload()" class="btn btn-secondary">
                        <i class="fas fa-sync me-2"></i>إعادة الاختبار
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.test-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
        
        // تحديث تلقائي كل دقيقة
        setTimeout(function() {
            location.reload();
        }, 60000);
    </script>
</body>
</html>
