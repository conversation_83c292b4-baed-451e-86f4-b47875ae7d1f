<?php
/**
 * صفحة اختبار قاعدة البيانات والنماذج
 * Database and Models Test Page
 */

session_start();

// التحقق من وجود ملف التكوين
$config_exists = file_exists('config/database_config.php');
$database_connected = false;
$models_working = false;
$test_results = array();

if ($config_exists) {
    try {
        require_once 'config/database_config.php';
        require_once 'config/database_odoo.php';
        $database_connected = true;
        $test_results[] = array('status' => 'success', 'message' => 'تم الاتصال بقاعدة البيانات بنجاح');
        
        // اختبار النماذج
        require_once 'models/BaseModel.php';
        require_once 'models/ResCompany.php';
        require_once 'models/ResPartner.php';
        require_once 'models/ResUsers.php';
        
        $models_working = true;
        $test_results[] = array('status' => 'success', 'message' => 'تم تحميل النماذج بنجاح');
        
        // اختبار نموذج الشركات
        $company_model = new ResCompany();
        $companies = $company_model->search_read(array(), null, array('limit' => 5));
        $test_results[] = array('status' => 'success', 'message' => 'تم جلب ' . count($companies) . ' شركة من قاعدة البيانات');
        
        // اختبار نموذج الشركاء
        $partner_model = new ResPartner();
        $partners = $partner_model->search_read(array(), null, array('limit' => 5));
        $test_results[] = array('status' => 'success', 'message' => 'تم جلب ' . count($partners) . ' شريك من قاعدة البيانات');
        
        // اختبار نموذج المستخدمين
        $user_model = new ResUsers();
        $users = $user_model->search_read(array(), null, array('limit' => 5));
        $test_results[] = array('status' => 'success', 'message' => 'تم جلب ' . count($users) . ' مستخدم من قاعدة البيانات');
        
    } catch (Exception $e) {
        $test_results[] = array('status' => 'error', 'message' => 'خطأ: ' . $e->getMessage());
    }
} else {
    $test_results[] = array('status' => 'warning', 'message' => 'ملف التكوين غير موجود - يتم استخدام البيانات التجريبية');
}

// اختبار إنشاء سجل جديد
if ($models_working) {
    try {
        // اختبار إنشاء شريك جديد
        $partner_model = new ResPartner();
        $test_partner_id = $partner_model->create(array(
            'name' => 'شريك تجريبي - ' . date('Y-m-d H:i:s'),
            'email' => '<EMAIL>',
            'customer_rank' => 1,
            'active' => true
        ));
        
        if ($test_partner_id) {
            $test_results[] = array('status' => 'success', 'message' => 'تم إنشاء شريك تجريبي بنجاح (ID: ' . $test_partner_id . ')');
            
            // اختبار تحديث السجل
            $update_result = $partner_model->write(array($test_partner_id), array(
                'phone' => '+966501234567'
            ));
            
            if ($update_result) {
                $test_results[] = array('status' => 'success', 'message' => 'تم تحديث الشريك التجريبي بنجاح');
            }
            
            // اختبار حذف السجل
            $delete_result = $partner_model->unlink(array($test_partner_id));
            if ($delete_result) {
                $test_results[] = array('status' => 'success', 'message' => 'تم حذف الشريك التجريبي بنجاح');
            }
        }
        
    } catch (Exception $e) {
        $test_results[] = array('status' => 'error', 'message' => 'خطأ في اختبار العمليات: ' . $e->getMessage());
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار قاعدة البيانات - نظام ERP المحاسبي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/odoo-style.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-database me-2"></i>
                            اختبار قاعدة البيانات والنماذج
                        </h4>
                    </div>
                    
                    <div class="card-body">
                        <!-- حالة النظام -->
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <i class="fas fa-cog fa-3x <?php echo $config_exists ? 'text-success' : 'text-warning'; ?> mb-3"></i>
                                        <h5>ملف التكوين</h5>
                                        <span class="badge <?php echo $config_exists ? 'bg-success' : 'bg-warning'; ?>">
                                            <?php echo $config_exists ? 'موجود' : 'غير موجود'; ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <i class="fas fa-database fa-3x <?php echo $database_connected ? 'text-success' : 'text-danger'; ?> mb-3"></i>
                                        <h5>قاعدة البيانات</h5>
                                        <span class="badge <?php echo $database_connected ? 'bg-success' : 'bg-danger'; ?>">
                                            <?php echo $database_connected ? 'متصلة' : 'غير متصلة'; ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <i class="fas fa-code fa-3x <?php echo $models_working ? 'text-success' : 'text-danger'; ?> mb-3"></i>
                                        <h5>النماذج</h5>
                                        <span class="badge <?php echo $models_working ? 'bg-success' : 'bg-danger'; ?>">
                                            <?php echo $models_working ? 'تعمل' : 'لا تعمل'; ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- نتائج الاختبار -->
                        <h5 class="mb-3">نتائج الاختبار</h5>
                        <div class="list-group">
                            <?php foreach ($test_results as $result): ?>
                                <div class="list-group-item d-flex align-items-center">
                                    <?php if ($result['status'] == 'success'): ?>
                                        <i class="fas fa-check-circle text-success me-3"></i>
                                    <?php elseif ($result['status'] == 'warning'): ?>
                                        <i class="fas fa-exclamation-triangle text-warning me-3"></i>
                                    <?php else: ?>
                                        <i class="fas fa-times-circle text-danger me-3"></i>
                                    <?php endif; ?>
                                    
                                    <span><?php echo $result['message']; ?></span>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <!-- معلومات إضافية -->
                        <?php if ($database_connected && isset($companies)): ?>
                            <div class="mt-4">
                                <h5>بيانات الشركات من قاعدة البيانات</h5>
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>الاسم</th>
                                                <th>الكود</th>
                                                <th>الحالة</th>
                                                <th>تاريخ الإنشاء</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($companies as $company): ?>
                                                <tr>
                                                    <td><?php echo $company['id']; ?></td>
                                                    <td><?php echo $company['name']; ?></td>
                                                    <td><?php echo isset($company['code']) ? $company['code'] : '-'; ?></td>
                                                    <td>
                                                        <span class="badge <?php echo $company['active'] ? 'bg-success' : 'bg-secondary'; ?>">
                                                            <?php echo $company['active'] ? 'نشط' : 'غير نشط'; ?>
                                                        </span>
                                                    </td>
                                                    <td><?php echo isset($company['create_date']) ? date('Y-m-d H:i', strtotime($company['create_date'])) : '-'; ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <!-- إجراءات -->
                        <div class="mt-4">
                            <h5>الإجراءات المتاحة</h5>
                            <div class="btn-group" role="group">
                                <?php if (!$config_exists): ?>
                                    <a href="install_database.php" class="btn btn-primary">
                                        <i class="fas fa-database me-2"></i>
                                        تثبيت قاعدة البيانات
                                    </a>
                                <?php endif; ?>
                                
                                <a href="demo.php" class="btn btn-success">
                                    <i class="fas fa-home me-2"></i>
                                    الصفحة الرئيسية
                                </a>
                                
                                <a href="pages/companies.php" class="btn btn-info">
                                    <i class="fas fa-building me-2"></i>
                                    إدارة الشركات
                                </a>
                                
                                <button onclick="location.reload()" class="btn btn-secondary">
                                    <i class="fas fa-sync me-2"></i>
                                    إعادة الاختبار
                                </button>
                            </div>
                        </div>
                        
                        <!-- معلومات تقنية -->
                        <div class="mt-4">
                            <h5>معلومات تقنية</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="list-unstyled">
                                        <li><strong>إصدار PHP:</strong> <?php echo PHP_VERSION; ?></li>
                                        <li><strong>PDO متوفر:</strong> <?php echo class_exists('PDO') ? 'نعم' : 'لا'; ?></li>
                                        <li><strong>MySQL متوفر:</strong> <?php echo extension_loaded('mysql') || extension_loaded('mysqli') ? 'نعم' : 'لا'; ?></li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="list-unstyled">
                                        <li><strong>الذاكرة المتاحة:</strong> <?php echo ini_get('memory_limit'); ?></li>
                                        <li><strong>الوقت الحالي:</strong> <?php echo date('Y-m-d H:i:s'); ?></li>
                                        <li><strong>المنطقة الزمنية:</strong> <?php echo date_default_timezone_get(); ?></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحديث تلقائي كل 30 ثانية
        setTimeout(function() {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
