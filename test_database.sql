-- =============================================
-- اختبار قاعدة البيانات
-- =============================================

-- 1. اختبار استعلامات العملاء
SELECT '1. اختبار استعلامات العملاء' AS test_section;
SELECT id, name, phone, email FROM res_partner WHERE customer_rank > 0;

-- 2. اختبار استعلامات الموردين
SELECT '\n2. اختبار استعلامات الموردين' AS test_section;
SELECT id, name, phone, email FROM res_partner WHERE supplier_rank > 0;

-- 3. اختبار استعلامات المنتجات
SELECT '\n3. اختبار استعلامات المنتجات' AS test_section;
SELECT pt.id, pt.name, pt.list_price, pt.standard_price, 
       COUNT(pp.id) as variants, 
       COALESCE(SUM(sq.quantity), 0) as total_qty
FROM product_template pt
LEFT JOIN product_product pp ON pp.product_tmpl_id = pt.id
LEFT JOIN stock_quant sq ON sq.product_id = pp.id
GROUP BY pt.id, pt.name, pt.list_price, pt.standard_price;

-- 4. اختبار استعلامات المخزون
SELECT '\n4. اختبار استعلامات المخزون' AS test_section;
SELECT p.id, p.default_code, pt.name, sl.name as location, sq.quantity
FROM product_product p
JOIN product_template pt ON p.product_tmpl_id = pt.id
JOIN stock_quant sq ON sq.product_id = p.id
JOIN stock_location sl ON sl.id = sq.location_id;

-- 5. اختبار طرق الدفع
SELECT '\n5. اختبار طرق الدفع' AS test_section;
SELECT id, name, code, is_default, active 
FROM account_payment_method;

-- 6. اختبار طرق الشحن
SELECT '\n6. اختبار طرق الشحن' AS test_section;
SELECT id, name, delivery_type, fixed_price, active 
FROM delivery_carrier;

-- 7. اختبار مجموعات الصلاحيات
SELECT '\n7. اختبار مجموعات الصلاحيات' AS test_section;
SELECT g.id, g.name, COUNT(gu.uid) as user_count
FROM res_groups g
LEFT JOIN res_groups_users_rel gu ON g.id = gu.gid
GROUP BY g.id, g.name;

-- 8. اختبار التقارير المتاحة
SELECT '\n8. اختبار التقارير المتاحة' AS test_section;
SELECT id, name, model, report_type, report_name
FROM ir_actions_report;

-- 9. اختبار إعدادات الشركة
SELECT '\n9. اختبار إعدادات الشركة' AS test_section;
SELECT c.name, c.email, c.phone, cur.name as currency, 
       u.login as admin_user, u.email as admin_email
FROM res_company c
JOIN res_currency cur ON c.currency_id = cur.id
JOIN res_users u ON u.company_id = c.id
WHERE u.id = 1;

-- 10. اختبار إعدادات اللغة والتاريخ
SELECT '\n10. اختبار إعدادات اللغة والتاريخ' AS test_section;
SELECT name, code, date_format, time_format
FROM res_lang
WHERE active = 1;

-- رسالة نجاح
SELECT '\nتم تنفيذ اختبارات قاعدة البيانات بنجاح!' AS test_result;
