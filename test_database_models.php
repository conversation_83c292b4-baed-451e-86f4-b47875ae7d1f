<?php
/**
 * اختبار نماذج قاعدة البيانات
 * Test Database Models
 */

session_start();

// تشغيل تلقائي إذا لم يكن مسجل دخول
if (!isset($_SESSION['user_id'])) {
    require_once 'odoo_launcher.php';
    exit();
}

$test_results = array();
$overall_status = 'success';

try {
    // تحميل نظام Odoo
    require_once 'config/odoo_config.php';
    require_once 'config/odoo_database.php';
    require_once 'models/BaseModel.php';
    require_once 'models/ResCompany.php';
    require_once 'models/ResPartner.php';
    require_once 'models/ProductTemplate.php';
    
    $db = OdooDatabase::getInstance();
    
    // اختبار 1: اتصال قاعدة البيانات
    $test_results[] = array(
        'name' => 'اتصال قاعدة البيانات',
        'status' => 'success',
        'message' => $db->isDemoMode() ? 'الوضع التجريبي مفعل' : 'متصل بقاعدة البيانات',
        'details' => $db->isDemoMode() ? 'يتم استخدام بيانات تجريبية' : 'يتم استخدام قاعدة البيانات الحقيقية'
    );
    
    // اختبار 2: نموذج الشركات
    try {
        $company_model = new ResCompany($db);
        $companies = $company_model->search_read(array(array('active', '=', true)));
        
        $test_results[] = array(
            'name' => 'نموذج الشركات',
            'status' => 'success',
            'message' => 'تم تحميل ' . count($companies) . ' شركة',
            'details' => 'الشركات: ' . implode(', ', array_column($companies, 'name'))
        );
    } catch (Exception $e) {
        $test_results[] = array(
            'name' => 'نموذج الشركات',
            'status' => 'error',
            'message' => 'خطأ في تحميل الشركات: ' . $e->getMessage(),
            'details' => ''
        );
        $overall_status = 'error';
    }
    
    // اختبار 3: نموذج الشركاء
    try {
        $partner_model = new ResPartner($db);
        $partners = $partner_model->search_read(array(array('active', '=', true)));
        $customers = $partner_model->get_customers();
        $suppliers = $partner_model->get_suppliers();
        
        $test_results[] = array(
            'name' => 'نموذج الشركاء',
            'status' => 'success',
            'message' => 'تم تحميل ' . count($partners) . ' شريك (' . count($customers) . ' عميل، ' . count($suppliers) . ' مورد)',
            'details' => 'الشركاء: ' . implode(', ', array_slice(array_column($partners, 'name'), 0, 3)) . (count($partners) > 3 ? '...' : '')
        );
    } catch (Exception $e) {
        $test_results[] = array(
            'name' => 'نموذج الشركاء',
            'status' => 'error',
            'message' => 'خطأ في تحميل الشركاء: ' . $e->getMessage(),
            'details' => ''
        );
        $overall_status = 'error';
    }
    
    // اختبار 4: نموذج المنتجات
    try {
        $product_model = new ProductTemplate($db);
        $products = $product_model->search_read(array(array('active', '=', true)));
        $saleable = $product_model->get_saleable_products();
        $purchaseable = $product_model->get_purchaseable_products();
        
        $test_results[] = array(
            'name' => 'نموذج المنتجات',
            'status' => 'success',
            'message' => 'تم تحميل ' . count($products) . ' منتج (' . count($saleable) . ' للبيع، ' . count($purchaseable) . ' للشراء)',
            'details' => 'المنتجات: ' . implode(', ', array_slice(array_column($products, 'name'), 0, 3)) . (count($products) > 3 ? '...' : '')
        );
    } catch (Exception $e) {
        $test_results[] = array(
            'name' => 'نموذج المنتجات',
            'status' => 'error',
            'message' => 'خطأ في تحميل المنتجات: ' . $e->getMessage(),
            'details' => ''
        );
        $overall_status = 'error';
    }
    
    // اختبار 5: العلاقات بين النماذج
    try {
        $company_model = new ResCompany($db);
        $partner_model = new ResPartner($db);
        
        // اختبار العلاقة بين الشركة والشركاء
        $companies = $company_model->search_read(array(), null, array('limit' => 1));
        if (!empty($companies)) {
            $company_id = $companies[0]['id'];
            $company_partners = $partner_model->search_read(array(array('company_id', '=', $company_id)));
            
            $test_results[] = array(
                'name' => 'العلاقات بين النماذج',
                'status' => 'success',
                'message' => 'تم العثور على ' . count($company_partners) . ' شريك للشركة: ' . $companies[0]['name'],
                'details' => 'العلاقة بين الشركات والشركاء تعمل بشكل صحيح'
            );
        } else {
            $test_results[] = array(
                'name' => 'العلاقات بين النماذج',
                'status' => 'warning',
                'message' => 'لا توجد شركات لاختبار العلاقات',
                'details' => ''
            );
        }
    } catch (Exception $e) {
        $test_results[] = array(
            'name' => 'العلاقات بين النماذج',
            'status' => 'error',
            'message' => 'خطأ في اختبار العلاقات: ' . $e->getMessage(),
            'details' => ''
        );
        $overall_status = 'error';
    }
    
    // اختبار 6: عمليات CRUD
    try {
        $partner_model = new ResPartner($db);
        
        // اختبار البحث مع شروط متعددة
        $search_results = $partner_model->search_read(
            array(
                array('active', '=', true),
                array('customer_rank', '>', 0)
            ),
            null,
            array('order' => 'name ASC', 'limit' => 5)
        );
        
        $test_results[] = array(
            'name' => 'عمليات البحث المتقدمة',
            'status' => 'success',
            'message' => 'تم العثور على ' . count($search_results) . ' عميل نشط',
            'details' => 'البحث مع شروط متعددة والترتيب والحد الأقصى يعمل بشكل صحيح'
        );
    } catch (Exception $e) {
        $test_results[] = array(
            'name' => 'عمليات البحث المتقدمة',
            'status' => 'error',
            'message' => 'خطأ في البحث المتقدم: ' . $e->getMessage(),
            'details' => ''
        );
        $overall_status = 'error';
    }
    
    // اختبار 7: البيانات التجريبية
    try {
        $company_model = new ResCompany($db);
        $partner_model = new ResPartner($db);
        $product_model = new ProductTemplate($db);
        
        $demo_companies = $company_model->get_demo_data();
        $demo_partners = $partner_model->get_demo_data();
        $demo_products = $product_model->get_demo_data();
        
        $test_results[] = array(
            'name' => 'البيانات التجريبية',
            'status' => 'success',
            'message' => 'تم تحميل البيانات التجريبية: ' . count($demo_companies) . ' شركة، ' . count($demo_partners) . ' شريك، ' . count($demo_products) . ' منتج',
            'details' => 'جميع النماذج تحتوي على بيانات تجريبية شاملة'
        );
    } catch (Exception $e) {
        $test_results[] = array(
            'name' => 'البيانات التجريبية',
            'status' => 'error',
            'message' => 'خطأ في البيانات التجريبية: ' . $e->getMessage(),
            'details' => ''
        );
        $overall_status = 'error';
    }
    
    // اختبار 8: أداء النظام
    $start_time = microtime(true);
    
    try {
        $company_model = new ResCompany($db);
        $partner_model = new ResPartner($db);
        $product_model = new ProductTemplate($db);
        
        // تنفيذ عدة استعلامات
        for ($i = 0; $i < 10; $i++) {
            $company_model->search_read(array(array('active', '=', true)));
            $partner_model->search_read(array(array('customer_rank', '>', 0)));
            $product_model->search_read(array(array('sale_ok', '=', true)));
        }
        
        $end_time = microtime(true);
        $execution_time = round(($end_time - $start_time) * 1000, 2);
        
        $test_results[] = array(
            'name' => 'أداء النظام',
            'status' => $execution_time < 1000 ? 'success' : 'warning',
            'message' => 'تم تنفيذ 30 استعلام في ' . $execution_time . ' ميلي ثانية',
            'details' => 'متوسط وقت الاستعلام: ' . round($execution_time / 30, 2) . ' ميلي ثانية'
        );
    } catch (Exception $e) {
        $test_results[] = array(
            'name' => 'أداء النظام',
            'status' => 'error',
            'message' => 'خطأ في اختبار الأداء: ' . $e->getMessage(),
            'details' => ''
        );
        $overall_status = 'error';
    }
    
} catch (Exception $e) {
    $test_results[] = array(
        'name' => 'خطأ عام',
        'status' => 'error',
        'message' => $e->getMessage(),
        'details' => ''
    );
    $overall_status = 'error';
}

// إحصائيات سريعة
$success_count = 0;
$warning_count = 0;
$error_count = 0;

foreach ($test_results as $result) {
    switch ($result['status']) {
        case 'success': $success_count++; break;
        case 'warning': $warning_count++; break;
        case 'error': $error_count++; break;
    }
}

$total_tests = count($test_results);
$success_percentage = round(($success_count / $total_tests) * 100);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نماذج قاعدة البيانات - نظام ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --odoo-primary: #714B67;
            --odoo-secondary: #8B5A8C;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .test-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 2rem auto;
            max-width: 1200px;
        }
        
        .test-header {
            background: linear-gradient(45deg, var(--odoo-primary), var(--odoo-secondary));
            color: white;
            padding: 2rem;
            border-radius: 20px 20px 0 0;
            text-align: center;
        }
        
        .progress-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            font-weight: bold;
            margin: 0 auto 1rem;
            color: white;
        }
        
        .test-item {
            padding: 1.5rem;
            margin: 0.5rem 0;
            border-radius: 10px;
            border-left: 4px solid #dee2e6;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }
        
        .test-item.success { border-left-color: #28a745; background: #d4edda; }
        .test-item.warning { border-left-color: #ffc107; background: #fff3cd; }
        .test-item.error { border-left-color: #dc3545; background: #f8d7da; }
        
        .status-icon.success { color: #28a745; }
        .status-icon.warning { color: #ffc107; }
        .status-icon.error { color: #dc3545; }
        
        .test-details {
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-container">
            <!-- العنوان -->
            <div class="test-header">
                <div class="row align-items-center">
                    <div class="col-md-4">
                        <div class="progress-circle bg-<?php echo $overall_status === 'success' ? 'success' : ($overall_status === 'warning' ? 'warning' : 'danger'); ?>">
                            <?php echo $success_percentage; ?>%
                        </div>
                    </div>
                    <div class="col-md-8">
                        <h2><i class="fas fa-database me-2"></i>اختبار نماذج قاعدة البيانات</h2>
                        <p class="mb-0">فحص شامل لجميع نماذج البيانات والعلاقات</p>
                        <span class="badge bg-light text-dark mt-2">
                            <i class="fas fa-cube me-1"></i>
                            Odoo Style Database Models
                        </span>
                    </div>
                </div>
            </div>
            
            <!-- الإحصائيات -->
            <div class="p-4">
                <div class="row mb-4">
                    <div class="col-md-4 text-center">
                        <div class="text-success">
                            <i class="fas fa-check-circle fa-3x"></i>
                            <h4 class="mt-2"><?php echo $success_count; ?></h4>
                            <small>نجح</small>
                        </div>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="text-warning">
                            <i class="fas fa-exclamation-triangle fa-3x"></i>
                            <h4 class="mt-2"><?php echo $warning_count; ?></h4>
                            <small>تحذير</small>
                        </div>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="text-danger">
                            <i class="fas fa-times-circle fa-3x"></i>
                            <h4 class="mt-2"><?php echo $error_count; ?></h4>
                            <small>خطأ</small>
                        </div>
                    </div>
                </div>
                
                <!-- شريط التقدم -->
                <div class="progress mb-4" style="height: 20px;">
                    <div class="progress-bar bg-success" style="width: <?php echo ($success_count / $total_tests) * 100; ?>%"></div>
                    <div class="progress-bar bg-warning" style="width: <?php echo ($warning_count / $total_tests) * 100; ?>%"></div>
                    <div class="progress-bar bg-danger" style="width: <?php echo ($error_count / $total_tests) * 100; ?>%"></div>
                </div>
                
                <!-- نتائج الاختبارات -->
                <h5 class="mb-3">نتائج اختبار النماذج:</h5>
                <?php foreach ($test_results as $result): ?>
                    <div class="test-item <?php echo $result['status']; ?>">
                        <div class="d-flex align-items-start">
                            <div class="me-3">
                                <i class="fas fa-<?php 
                                    echo $result['status'] === 'success' ? 'check-circle' : 
                                        ($result['status'] === 'warning' ? 'exclamation-triangle' : 'times-circle'); 
                                ?> fa-2x status-icon <?php echo $result['status']; ?>"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1"><?php echo $result['name']; ?></h6>
                                <div><?php echo $result['message']; ?></div>
                                <?php if (!empty($result['details'])): ?>
                                    <div class="test-details"><?php echo $result['details']; ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
                
                <!-- معلومات النظام -->
                <div class="mt-4 p-3 bg-light rounded">
                    <h6><i class="fas fa-info-circle me-2"></i>معلومات النظام:</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <small>
                                <strong>وضع قاعدة البيانات:</strong> <?php echo $db->isDemoMode() ? 'تجريبي' : 'حقيقي'; ?><br>
                                <strong>إصدار PHP:</strong> <?php echo PHP_VERSION; ?><br>
                                <strong>المستخدم:</strong> <?php echo $_SESSION['username'] ?? 'غير محدد'; ?>
                            </small>
                        </div>
                        <div class="col-md-6">
                            <small>
                                <strong>الوقت:</strong> <?php echo date('Y-m-d H:i:s'); ?><br>
                                <strong>الشركة:</strong> <?php echo $_SESSION['company_name'] ?? 'غير محدد'; ?><br>
                                <strong>عدد الاختبارات:</strong> <?php echo $total_tests; ?>
                            </small>
                        </div>
                    </div>
                </div>
                
                <!-- أزرار الإجراءات -->
                <div class="text-center mt-4">
                    <div class="btn-group" role="group">
                        <a href="dashboard.php" class="btn btn-primary">
                            <i class="fas fa-home me-2"></i>لوحة التحكم
                        </a>
                        <a href="pages/companies.php" class="btn btn-success">
                            <i class="fas fa-building me-2"></i>الشركات
                        </a>
                        <a href="test_odoo_system.php" class="btn btn-info">
                            <i class="fas fa-cog me-2"></i>اختبار النظام
                        </a>
                        <button onclick="location.reload()" class="btn btn-secondary">
                            <i class="fas fa-sync me-2"></i>إعادة الاختبار
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const items = document.querySelectorAll('.test-item');
            items.forEach((item, index) => {
                item.style.opacity = '0';
                item.style.transform = 'translateX(20px)';
                setTimeout(() => {
                    item.style.transition = 'all 0.6s ease';
                    item.style.opacity = '1';
                    item.style.transform = 'translateX(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
