<?php
/**
 * اختبار نظام Odoo الجديد
 * Test New Odoo System
 */

session_start();

// تشغيل تلقائي إذا لم يكن مسجل دخول
if (!isset($_SESSION['user_id'])) {
    require_once 'odoo_launcher.php';
    exit();
}

$test_results = array();
$overall_status = 'success';

try {
    // اختبار 1: تحميل نظام Odoo
    $odoo_loaded = false;
    try {
        require_once 'config/odoo_config.php';
        require_once 'config/odoo_database.php';
        $odoo_loaded = true;
        
        $test_results[] = array(
            'name' => 'تحميل نظام Odoo',
            'status' => 'success',
            'message' => 'تم تحميل نظام Odoo بنجاح'
        );
    } catch (Exception $e) {
        $test_results[] = array(
            'name' => 'تحميل نظام Odoo',
            'status' => 'error',
            'message' => 'فشل في تحميل نظام Odoo: ' . $e->getMessage()
        );
    }
    
    // اختبار 2: قاعدة البيانات
    if ($odoo_loaded) {
        $db = OdooDatabase::getInstance();
        $demo_mode = $db->isDemoMode();
        
        $test_results[] = array(
            'name' => 'قاعدة البيانات',
            'status' => $demo_mode ? 'warning' : 'success',
            'message' => $demo_mode ? 'الوضع التجريبي مفعل' : 'متصل بقاعدة البيانات'
        );
    }
    
    // اختبار 3: الثوابت والإعدادات
    $constants_ok = defined('ODOO_SITE_NAME') && defined('ODOO_VERSION') && defined('ODOO_DB_HOST');
    
    $test_results[] = array(
        'name' => 'الثوابت والإعدادات',
        'status' => $constants_ok ? 'success' : 'error',
        'message' => $constants_ok ? 'جميع الثوابت محددة بشكل صحيح' : 'بعض الثوابت مفقودة'
    );
    
    // اختبار 4: نظام Odoo المتقدم
    $advanced_loaded = false;
    if (file_exists('includes/odoo_registry.php')) {
        try {
            require_once 'includes/odoo_registry.php';
            require_once 'includes/odoo_menu.php';
            require_once 'includes/odoo_actions.php';
            $advanced_loaded = true;
        } catch (Exception $e) {
            // تجاهل الأخطاء
        }
    }
    
    $test_results[] = array(
        'name' => 'نظام Odoo المتقدم',
        'status' => $advanced_loaded ? 'success' : 'warning',
        'message' => $advanced_loaded ? 'تم تحميل النظام المتقدم' : 'النظام الأساسي فقط'
    );
    
    // اختبار 5: الملفات الأساسية
    $required_files = array(
        'login.php' => 'صفحة تسجيل الدخول',
        'dashboard.php' => 'لوحة التحكم',
        'logout.php' => 'تسجيل الخروج',
        'odoo_launcher.php' => 'مشغل Odoo',
        'config/odoo_config.php' => 'تكوين Odoo',
        'config/odoo_database.php' => 'قاعدة بيانات Odoo'
    );
    
    $missing_files = array();
    foreach ($required_files as $file => $description) {
        if (!file_exists($file)) {
            $missing_files[] = $description;
        }
    }
    
    $test_results[] = array(
        'name' => 'الملفات الأساسية',
        'status' => empty($missing_files) ? 'success' : 'error',
        'message' => empty($missing_files) ? 'جميع الملفات موجودة' : 'ملفات مفقودة: ' . implode(', ', $missing_files)
    );
    
    // اختبار 6: الجلسة
    $session_data = array(
        'user_id' => isset($_SESSION['user_id']),
        'username' => isset($_SESSION['username']),
        'groups' => isset($_SESSION['groups']),
        'odoo_style' => isset($_SESSION['odoo_style']),
        'demo_data' => isset($_SESSION['demo_data'])
    );
    
    $session_ok = array_sum($session_data) >= 4;
    
    $test_results[] = array(
        'name' => 'بيانات الجلسة',
        'status' => $session_ok ? 'success' : 'warning',
        'message' => $session_ok ? 'بيانات الجلسة مكتملة' : 'بعض بيانات الجلسة مفقودة'
    );
    
    // اختبار 7: المجلدات
    $required_dirs = array('config', 'logs', 'cache', 'tmp', 'sessions', 'filestore');
    $missing_dirs = array();
    
    foreach ($required_dirs as $dir) {
        if (!file_exists($dir)) {
            $missing_dirs[] = $dir;
        }
    }
    
    $test_results[] = array(
        'name' => 'المجلدات المطلوبة',
        'status' => empty($missing_dirs) ? 'success' : 'warning',
        'message' => empty($missing_dirs) ? 'جميع المجلدات موجودة' : 'مجلدات مفقودة: ' . implode(', ', $missing_dirs)
    );
    
    // اختبار 8: إعدادات PHP
    $php_ok = version_compare(PHP_VERSION, '5.6.0', '>=');
    $extensions = array('pdo', 'json', 'session');
    $missing_extensions = array();
    
    foreach ($extensions as $ext) {
        if (!extension_loaded($ext)) {
            $missing_extensions[] = $ext;
        }
    }
    
    $test_results[] = array(
        'name' => 'إعدادات PHP',
        'status' => ($php_ok && empty($missing_extensions)) ? 'success' : 'warning',
        'message' => $php_ok ? 
            (empty($missing_extensions) ? 'PHP مُعد بشكل صحيح' : 'امتدادات مفقودة: ' . implode(', ', $missing_extensions)) :
            'إصدار PHP قديم: ' . PHP_VERSION
    );
    
    // تحديد الحالة العامة
    foreach ($test_results as $result) {
        if ($result['status'] === 'error') {
            $overall_status = 'error';
            break;
        } elseif ($result['status'] === 'warning' && $overall_status !== 'error') {
            $overall_status = 'warning';
        }
    }
    
} catch (Exception $e) {
    $test_results[] = array(
        'name' => 'خطأ عام',
        'status' => 'error',
        'message' => $e->getMessage()
    );
    $overall_status = 'error';
}

// إحصائيات سريعة
$success_count = 0;
$warning_count = 0;
$error_count = 0;

foreach ($test_results as $result) {
    switch ($result['status']) {
        case 'success': $success_count++; break;
        case 'warning': $warning_count++; break;
        case 'error': $error_count++; break;
    }
}

$total_tests = count($test_results);
$success_percentage = round(($success_count / $total_tests) * 100);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام Odoo - نظام ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --odoo-primary: #714B67;
            --odoo-secondary: #8B5A8C;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .test-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 2rem auto;
            max-width: 1200px;
        }
        
        .test-header {
            background: linear-gradient(45deg, var(--odoo-primary), var(--odoo-secondary));
            color: white;
            padding: 2rem;
            border-radius: 20px 20px 0 0;
            text-align: center;
        }
        
        .progress-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            font-weight: bold;
            margin: 0 auto 1rem;
            color: white;
        }
        
        .test-item {
            padding: 1rem;
            margin: 0.5rem 0;
            border-radius: 10px;
            border-left: 4px solid #dee2e6;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }
        
        .test-item.success { border-left-color: #28a745; background: #d4edda; }
        .test-item.warning { border-left-color: #ffc107; background: #fff3cd; }
        .test-item.error { border-left-color: #dc3545; background: #f8d7da; }
        
        .status-icon.success { color: #28a745; }
        .status-icon.warning { color: #ffc107; }
        .status-icon.error { color: #dc3545; }
        
        .odoo-badge {
            background: linear-gradient(45deg, var(--odoo-primary), var(--odoo-secondary));
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-container">
            <!-- العنوان -->
            <div class="test-header">
                <div class="row align-items-center">
                    <div class="col-md-4">
                        <div class="progress-circle bg-<?php echo $overall_status === 'success' ? 'success' : ($overall_status === 'warning' ? 'warning' : 'danger'); ?>">
                            <?php echo $success_percentage; ?>%
                        </div>
                    </div>
                    <div class="col-md-8">
                        <h2><i class="fas fa-cube me-2"></i>اختبار نظام Odoo</h2>
                        <p class="mb-0">فحص شامل لجميع مكونات النظام</p>
                        <span class="odoo-badge mt-2 d-inline-block">
                            Odoo Style ERP v<?php echo defined('ODOO_VERSION') ? ODOO_VERSION : '1.0'; ?>
                        </span>
                    </div>
                </div>
            </div>
            
            <!-- الإحصائيات -->
            <div class="p-4">
                <div class="row mb-4">
                    <div class="col-md-4 text-center">
                        <div class="text-success">
                            <i class="fas fa-check-circle fa-3x"></i>
                            <h4 class="mt-2"><?php echo $success_count; ?></h4>
                            <small>نجح</small>
                        </div>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="text-warning">
                            <i class="fas fa-exclamation-triangle fa-3x"></i>
                            <h4 class="mt-2"><?php echo $warning_count; ?></h4>
                            <small>تحذير</small>
                        </div>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="text-danger">
                            <i class="fas fa-times-circle fa-3x"></i>
                            <h4 class="mt-2"><?php echo $error_count; ?></h4>
                            <small>خطأ</small>
                        </div>
                    </div>
                </div>
                
                <!-- شريط التقدم -->
                <div class="progress mb-4" style="height: 20px;">
                    <div class="progress-bar bg-success" style="width: <?php echo ($success_count / $total_tests) * 100; ?>%"></div>
                    <div class="progress-bar bg-warning" style="width: <?php echo ($warning_count / $total_tests) * 100; ?>%"></div>
                    <div class="progress-bar bg-danger" style="width: <?php echo ($error_count / $total_tests) * 100; ?>%"></div>
                </div>
                
                <!-- نتائج الاختبارات -->
                <h5 class="mb-3">نتائج الاختبارات:</h5>
                <?php foreach ($test_results as $result): ?>
                    <div class="test-item <?php echo $result['status']; ?>">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="fas fa-<?php 
                                    echo $result['status'] === 'success' ? 'check-circle' : 
                                        ($result['status'] === 'warning' ? 'exclamation-triangle' : 'times-circle'); 
                                ?> fa-2x status-icon <?php echo $result['status']; ?>"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1"><?php echo $result['name']; ?></h6>
                                <small><?php echo $result['message']; ?></small>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
                
                <!-- معلومات النظام -->
                <div class="mt-4 p-3 bg-light rounded">
                    <h6><i class="fas fa-info-circle me-2"></i>معلومات النظام:</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <small>
                                <strong>إصدار PHP:</strong> <?php echo PHP_VERSION; ?><br>
                                <strong>نمط Odoo:</strong> <?php echo isset($_SESSION['odoo_style']) && $_SESSION['odoo_style'] ? 'مفعل' : 'غير مفعل'; ?><br>
                                <strong>المستخدم:</strong> <?php echo $_SESSION['username'] ?? 'غير محدد'; ?>
                            </small>
                        </div>
                        <div class="col-md-6">
                            <small>
                                <strong>الوقت:</strong> <?php echo date('Y-m-d H:i:s'); ?><br>
                                <strong>الوضع التجريبي:</strong> <?php echo isset($_SESSION['demo_mode']) && $_SESSION['demo_mode'] ? 'مفعل' : 'غير مفعل'; ?><br>
                                <strong>الشركة:</strong> <?php echo $_SESSION['company_name'] ?? 'غير محدد'; ?>
                            </small>
                        </div>
                    </div>
                </div>
                
                <!-- أزرار الإجراءات -->
                <div class="text-center mt-4">
                    <div class="btn-group" role="group">
                        <a href="dashboard.php" class="btn btn-primary">
                            <i class="fas fa-home me-2"></i>لوحة التحكم
                        </a>
                        <a href="odoo_launcher.php" class="btn btn-success">
                            <i class="fas fa-rocket me-2"></i>مشغل Odoo
                        </a>
                        <a href="status.php" class="btn btn-info">
                            <i class="fas fa-heartbeat me-2"></i>حالة النظام
                        </a>
                        <button onclick="location.reload()" class="btn btn-secondary">
                            <i class="fas fa-sync me-2"></i>إعادة الاختبار
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const items = document.querySelectorAll('.test-item');
            items.forEach((item, index) => {
                setTimeout(() => {
                    item.style.opacity = '1';
                    item.style.transform = 'translateX(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
